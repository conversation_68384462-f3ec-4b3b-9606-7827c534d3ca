package foeye

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestThreatIndexName(t *testing.T) {
	indexName := NewFoeyeTaskThreatsModel().IndexName()
	assert.Equal(t, "foeye_task_threats", indexName)
}

func TestThreatString(t *testing.T) {
	str := NewFoeyeTaskThreatsModel().String()
	assert.Equal(t, "", str)
}

func TestGetFileImportThreatListFields(t *testing.T) {
	fields := NewFoeyeTaskThreatsModel().GetThreatListFields()

	expectedFields := []map[string]string{
		{"name": "IP", "key": "ip"},
		{"name": "漏洞名称", "key": "common_title"},
		{"name": "CVE编号", "key": "cveId"},
		{"name": "漏洞类型", "key": "vulType"},
		{"name": "漏洞等级", "key": "level"},
		{"name": "漏洞地址", "key": "url"},
		{"name": "发现时间", "key": "createtime"},
		{"name": "通报时间", "key": "notice_time"},
	}

	assert.Equal(t, expectedFields, fields)
}

func TestGetFileImportThreatListDetailFields(t *testing.T) {
	fields := NewFoeyeTaskThreatsModel().GetThreatListDetailFields()

	expectedFields := []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "漏洞名称", "key": "common_title"},
		{"name": "CVE编号", "key": "cveId"},
		{"name": "漏洞类型", "key": "vulType"},
		{"name": "漏洞等级", "key": "level"},
		{"name": "漏洞地址", "key": "url"},
		{"name": "发现时间", "key": "createtime"},
		{"name": "通报时间", "key": "notice_time"},
		{"name": "上次扫描时间", "key": "lastupdatetime"},
		{"name": "备注信息", "key": "name"},
		{"name": "MAC地址", "key": "mac"},
		{"name": "地理位置", "key": "city"},
		{"name": "资产等级", "key": "asset_level"},
		{"name": "管理单元", "key": "company"},
	}

	assert.Equal(t, expectedFields, fields)
}
