package seeders

import (
	"fmt"
	"slices"
	"time"

	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/strategy"
)

// 定义显示名称映射
var displayNameMaps = map[string]map[string]string{
	"asset_merge": {
		"Ip":              "IP",
		"IpType":          "IP类型",
		"IpSegment":       "IP段",
		"MapIp":           "映射IP",
		"HostName":        "主机名",
		"Hostname":        "主机名",
		"EthName":         "网卡名",
		"Os":              "操作系统",
		"Kernel":          "内核",
		"DeviceId":        "设备ID",
		"DeviceName":      "设备名",
		"Model":           "型号",
		"Maker":           "制造商",
		"Sn":              "序列号",
		"Mac":             "MAC地址",
		"Product":         "组件",
		"Business":        "业务系统",
		"BusinessSystem":  "业务系统名称",
		"Oper":            "运维人员",
		"BusinessOwner":   "业务负责人",
		"MachineRoom":     "机房",
		"Status":          "资产状态",
		"Ports":           "端口",
		"MemorySize":      "内存大小",
		"MemoryUsageRate": "内存使用率",
		"CpuMaker":        "CPU厂商",
		"CpuBrand":        "CPU品牌",
		"CpuCount":        "CPU数量",
		"DiskCount":       "磁盘数量",
		"DiskSize":        "磁盘大小",
		"DiskUsageRate":   "磁盘使用率",
		"LoadAverage":     "负载",
		"IsPublic":        "是否外网IP",
		"JarPackageInfo":  "jar包信息",
	},
	"device_merge": {
		"Ip":              "IP",
		"Oper":            "运维人员",
		"HostName":        "主机名",
		"SN":              "序列号",
		"Os":              "操作系统",
		"Kernel":          "内核",
		"Model":           "型号",
		"Maker":           "制造商",
		"Mac":             "MAC地址",
		"MachineRoom":     "机房",
		"MemorySize":      "内存大小",
		"MemoryUsageRate": "内存使用率",
		"CpuMaker":        "CPU厂商",
		"CpuBrand":        "CPU品牌",
		"CpuCount":        "CPU数量",
		"DiskCount":       "磁盘数量",
		"DiskSize":        "磁盘大小",
		"DiskUsageRate":   "磁盘使用率",
		"LoadAverage":     "负载",
	},
	"vuln_merge": {
		"Ip":           "IP",
		"Ports":        "端口",
		"IsPoc":        "是否poc漏洞",
		"Url":          "漏洞地址",
		"Level":        "漏洞等级",
		"Name":         "漏洞名称",
		"VulType":      "漏洞类型",
		"Cve":          "cve编号",
		"Cnvd":         "cnvd编号",
		"Cnnvd":        "cnnvd编号",
		"HasExp":       "是否存在exp",
		"HasPoc":       "是否存在poc",
		"Status":       "漏洞流转状态",
		"Describe":     "描述",
		"Details":      "详情",
		"Hazard":       "危险性",
		"Suggestions":  "修复建议",
		"LastResponse": "最后一次响应",
	},
	"person_merge": {
		"Name":        "姓名",
		"EnglishName": "英文名",
		"Title":       "职位",
		"Mobile":      "手机号",
		"Email":       "邮箱",
		"Department":  "部门",
		"Status":      "在职状态",
		"Area":        "区域",
		"WorkNumber":  "工号",
	},
}

func MergeStrategySeeder() {
	var count int64
	err := strategy.NewStrategyModel().DB.Count(&count).Error
	if err != nil {
		fmt.Printf("MergeStrategySeeder get strategy count err: %+v \n", err)
		return
	}
	if count == 0 {
		strategies := make([]strategy.Strategy, 0)
		// 资产合并策略
		assetStrategies := initAssetMergeStrategy()
		strategies = append(strategies, assetStrategies...)
		// 漏洞合并策略
		vulnStrategies := initVulnMergeStrategy()
		strategies = append(strategies, vulnStrategies...)
		// 人员合并策略
		personStrategies := initPersonMergeStrategy()
		strategies = append(strategies, personStrategies...)
		// 设备合并策略
		deviceStrategies := initDeviceMergeStrategy()
		strategies = append(strategies, deviceStrategies...)

		strategyModel := strategy.NewStrategyModel()
		for _, v := range strategies {
			_, err := strategyModel.Create(v)
			if err != nil {
				fmt.Printf("create strategy failed, field: %s, err: %v \n", v.FieldName, err)
			}
		}
	}

	// 追加工号策略-人员策略
	AppendStrategyWorkNumberForPerson()
	// 追加序列号策略-设备策略
	AppendStrategySnForDevice()
	// 删除area、IpSegment、EthName字段-设备策略
	DeleteStrategyDeviceField()
	// 追加jar包信息策略-资产策略
	AppendStrategyJarPackageInfoForAsset()
	// 追加是否poc策略-漏洞策略
	AppendStrategyIsPocForVuln()

	// 追加策略
	fmt.Println("strategy create success, execute append strategy")
	AppendStrategy()
}

// AppendStrategy 追加策略到策略表
func AppendStrategy() {
	appendStrategies := make([]*strategy.Strategy, 0)
	// // 补充资产策略
	// appendStrategies = append(appendStrategies, appendDatasourceToStrategy(strategy.BusinessType_AssetMerge, 14, 15, 16, 17, 18, 20, 21)...)
	// // 补充设备策略
	// appendStrategies = append(appendStrategies, appendDatasourceToStrategy(strategy.BusinessType_DeviceMerge, 14, 15, 16, 17, 18, 20, 21)...)
	// // 补充漏洞策略
	// appendStrategies = append(appendStrategies, appendDatasourceToStrategy(strategy.BusinessType_VulnMerge, 1, 2, 3, 10, 14)...)
	// // 补充人员策略
	// appendStrategies = append(appendStrategies, appendDatasourceToStrategy(strategy.BusinessType_PersonMerge, 14, 15, 16, 17, 18, 20, 21)...)

	// 获取data_source表中的数据源
	model := data_source.NewSourceModel()
	existSources := make([]*data_source.Source, 0)
	err := model.Select("id", "has_asset_data", "has_vul_data", "has_personnel_data").Find(&existSources).Error
	if err != nil {
		fmt.Println("追加融合策略时获取数据源失败：", err)
		return
	}
	// 遍历数据源，将数据源的id添加到策略的SourcePriority中
	needAppendAssetSourceIds := make([]uint64, 0)
	needAppendVulnSourceIds := make([]uint64, 0)
	needAppendPersonSourceIds := make([]uint64, 0)
	for _, v := range existSources {
		if v.HasAssetData {
			needAppendAssetSourceIds = append(needAppendAssetSourceIds, v.Id)
		}
		if v.HasVulData {
			needAppendVulnSourceIds = append(needAppendVulnSourceIds, v.Id)
		}
		if v.HasPersonnelData {
			needAppendPersonSourceIds = append(needAppendPersonSourceIds, v.Id)
		}
	}
	if len(needAppendAssetSourceIds) > 0 {
		appendStrategies = append(appendStrategies, appendDatasourceToStrategy(strategy.BusinessType_AssetMerge, needAppendAssetSourceIds...)...)
		appendStrategies = append(appendStrategies, appendDatasourceToStrategy(strategy.BusinessType_DeviceMerge, needAppendAssetSourceIds...)...)
	}
	if len(needAppendVulnSourceIds) > 0 {
		appendStrategies = append(appendStrategies, appendDatasourceToStrategy(strategy.BusinessType_VulnMerge, needAppendVulnSourceIds...)...)
	}
	if len(needAppendPersonSourceIds) > 0 {
		appendStrategies = append(appendStrategies, appendDatasourceToStrategy(strategy.BusinessType_PersonMerge, needAppendPersonSourceIds...)...)
	}
	// 更新策略的SourcePriority
	for _, v := range appendStrategies {
		err := strategy.NewStrategyModel().UpdateColumns(v.Id, map[string]interface{}{"source_priority": v.SourcePriority})
		if err != nil {
			fmt.Printf("追加融合策略失败，策略id: %d，field: %s，错误: %v \n", v.Id, v.FieldName, err)
		}
	}
}

// AppendStrategyWorkNumberForPerson 追加工号策略
func AppendStrategyWorkNumberForPerson() {
	// 获取人员融合策略
	var count int64
	err := strategy.NewStrategyModel().DB.Where("business_type = ? And field_name = ?", strategy.BusinessType_PersonMerge, "WorkNumber").Count(&count).Error
	if err != nil {
		fmt.Println("获取人员融合策略失败：", err)
		return
	}
	if count > 0 {
		return
	}
	// 创建工号策略
	workNumberStrategy := strategy.Strategy{
		BusinessType:    strategy.BusinessType_PersonMerge,
		FieldName:       "WorkNumber",
		DisplayName:     getDisplayName(strategy.BusinessType_PersonMerge, "WorkNumber"),
		SourcePriority:  strategy.SourcePriority{"7": 1, "6": 2, "4": 3, "5": 4},
		UntrustedSource: strategy.UntrustedSource{},
		Version:         time.Now().UnixMilli(),
	}
	// 保存工号策略
	strategyModel := strategy.NewStrategyModel()
	_, err = strategyModel.Create(workNumberStrategy)
	if err != nil {
		fmt.Println("创建工号策略失败：", err)
	}
}

// AppendStrategySnForDevice 追加序列号策略
func AppendStrategySnForDevice() {
	// 获取设备融合策略
	var count int64
	err := strategy.NewStrategyModel().DB.Where("business_type = ? And field_name = ?", strategy.BusinessType_DeviceMerge, "SN").Count(&count).Error
	if err != nil {
		fmt.Println("获取设备融合策略失败：", err)
		return
	}
	if count > 0 {
		return
	}
	// 创建序列号策略
	snStrategy := strategy.Strategy{
		BusinessType:    strategy.BusinessType_DeviceMerge,
		FieldName:       "SN",
		DisplayName:     getDisplayName(strategy.BusinessType_DeviceMerge, "SN"),
		SourcePriority:  strategy.SourcePriority{"1": 1, "2": 1, "3": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "20": 1, "21": 1},
		UntrustedSource: strategy.UntrustedSource{},
		Version:         time.Now().UnixMilli(),
	}
	// 保存序列号策略
	strategyModel := strategy.NewStrategyModel()
	_, err = strategyModel.Create(snStrategy)
	if err != nil {
		fmt.Println("创建序列号策略失败：", err)
	}
	fmt.Println("设备创建序列号策略成功")
}

// AppendStrategyJarPackageInfoForAsset 追加jar包信息策略
func AppendStrategyJarPackageInfoForAsset() {
	// 获取资产融合策略
	var count int64
	err := strategy.NewStrategyModel().DB.Where("business_type = ? And field_name = ?", strategy.BusinessType_AssetMerge, "JarPackageInfo").Count(&count).Error
	if err != nil {
		fmt.Println("获取资产融合策略失败：", err)
		return
	}
	if count > 0 {
		return
	}
	// 创建jar包信息策略
	jarPackageInfoStrategy := strategy.Strategy{
		BusinessType:    strategy.BusinessType_AssetMerge,
		FieldName:       "JarPackageInfo",
		DisplayName:     getDisplayName(strategy.BusinessType_AssetMerge, "JarPackageInfo"),
		SourcePriority:  strategy.SourcePriority{"1": 1, "2": 1, "3": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "20": 1, "21": 1},
		UntrustedSource: strategy.UntrustedSource{},
		Version:         time.Now().UnixMilli(),
	}
	// 保存jar包信息策略
	strategyModel := strategy.NewStrategyModel()
	_, err = strategyModel.Create(jarPackageInfoStrategy)
	if err != nil {
		fmt.Println("创建jar包信息策略失败：", err)
	}
	fmt.Println("ip资产创建jar包信息策略成功")
}

// AppendStrategyIsPocForVuln 追加是否poc策略
func AppendStrategyIsPocForVuln() {
	// 获取漏洞融合策略
	var count int64
	err := strategy.NewStrategyModel().DB.Where("business_type = ? And field_name = ?", strategy.BusinessType_VulnMerge, "IsPoc").Count(&count).Error
	if err != nil {
		fmt.Println("获取漏洞融合策略失败：", err)
		return
	}
	if count > 0 {
		return
	}
	// 创建是否poc策略
	isPocStrategy := strategy.Strategy{
		BusinessType:    strategy.BusinessType_VulnMerge,
		FieldName:       "IsPoc",
		DisplayName:     getDisplayName(strategy.BusinessType_VulnMerge, "IsPoc"),
		SourcePriority:  strategy.SourcePriority{"7": 1, "6": 2, "3": 3, "8": 4, "12": 5},
		UntrustedSource: strategy.UntrustedSource{},
		Version:         time.Now().UnixMilli(),
	}
	// 保存是否poc策略
	strategyModel := strategy.NewStrategyModel()
	_, err = strategyModel.Create(isPocStrategy)
	if err != nil {
		fmt.Println("创建是否poc策略失败：", err)
	}
	fmt.Println("漏洞创建是否poc策略成功")
}

// DeleteStrategyDeviceField 删除设备策略中的area、IpSegment、EthName字段
func DeleteStrategyDeviceField() {
	strategyModel := strategy.NewStrategyModel()
	strategyModel.DB.Where("business_type = ? And field_name IN (?)", strategy.BusinessType_DeviceMerge, []string{"Area", "IpSegment", "EthName"}).Delete(&strategy.Strategy{})
}

// 追加数据源到策略，返回需要更新的策略
func appendDatasourceToStrategy(businessType string, datasourceIds ...uint64) []*strategy.Strategy {
	model := strategy.NewStrategyModel()
	existStrategies, err := model.ListAllDistinct(businessType)
	if err != nil {
		fmt.Println(err)
		return nil
	}
	updateStragegies := make([]*strategy.Strategy, 0)
	// 每个strategy都指向一个字段，所以需要遍历所有strategy，将datasourceId添加到strategy的SourcePriority中
	for _, strategy := range existStrategies {
		// 获取strategy的最大优先级
		maxPority := uint64(1)
		// 获取strategy的所有key，就是所有的datasourceId
		keys := make([]string, 0, len(strategy.SourcePriority))
		for k := range strategy.SourcePriority {
			keys = append(keys, k)
			if strategy.SourcePriority[k] > maxPority {
				maxPority = strategy.SourcePriority[k]
			}
		}
		// 优先级和不信任中都不包含要追加的datasourceId，就添加到strategy的SourcePriority中，优先级为maxPority+1
		needAppend := false
		for _, datasourceId := range datasourceIds {
			if !slices.Contains(keys, fmt.Sprintf("%d", datasourceId)) && !slices.Contains(strategy.UntrustedSource, fmt.Sprintf("%d", datasourceId)) {
				needAppend = true
				strategy.SourcePriority[fmt.Sprintf("%d", datasourceId)] = maxPority + 1
			}
		}
		if needAppend {
			updateStragegies = append(updateStragegies, strategy)
		}
	}
	return updateStragegies
}

func getDisplayName(businessType, fieldName string) string {
	if nameMap, ok := displayNameMaps[businessType]; ok {
		if displayName, exists := nameMap[fieldName]; exists {
			return displayName
		}
	}
	return fieldName
}

func initAssetMergeStrategy() []strategy.Strategy {
	now := time.Now().UnixMilli()
	strategies := make([]strategy.Strategy, 0)
	assetFields := []string{"IpSegment", "Hostname", "EthName", "Os", "Kernel", "Model", "Maker", "Sn", "Mac", "Product", "Business", "Oper", "MachineRoom", "Ports", "MemorySize", "MemoryUsageRate", "CpuMaker", "CpuBrand", "CpuCount", "DiskCount", "DiskSize", "DiskUsageRate", "LoadAverage"}
	for _, field := range assetFields {
		strategies = append(strategies, strategy.Strategy{
			BusinessType:    strategy.BusinessType_AssetMerge,
			FieldName:       field,
			DisplayName:     getDisplayName(strategy.BusinessType_AssetMerge, field),
			SourcePriority:  strategy.SourcePriority{"1": 1, "2": 1, "3": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "20": 1, "21": 1},
			UntrustedSource: strategy.UntrustedSource{},
			Version:         now,
		})
	}
	strategies = append(strategies, strategy.Strategy{
		BusinessType:    strategy.BusinessType_AssetMerge,
		FieldName:       "Status",
		DisplayName:     getDisplayName(strategy.BusinessType_AssetMerge, "Status"),
		SourcePriority:  strategy.SourcePriority{"7": 1, "1": 2, "2": 3, "10": 4, "6": 5, "3": 6, "5": 7, "11": 8, "8": 9, "13": 10, "9": 11, "14": 12, "15": 13, "16": 14, "17": 15, "18": 16, "20": 17, "21": 18},
		UntrustedSource: strategy.UntrustedSource{},
		Version:         now,
	})
	return strategies
}

func initDeviceMergeStrategy() []strategy.Strategy {
	now := time.Now().UnixMilli()
	strategies := make([]strategy.Strategy, 0)
	assetFields := []string{"Ip", "Oper", "Hostname", "Os", "Kernel", "Model", "Maker", "Mac", "MachineRoom", "MemorySize", "MemoryUsageRate", "CpuMaker", "CpuBrand", "CpuCount", "DiskCount", "DiskSize", "DiskUsageRate", "LoadAverage", "SN"}
	for _, field := range assetFields {
		strategies = append(strategies, strategy.Strategy{
			BusinessType:    strategy.BusinessType_DeviceMerge,
			FieldName:       field,
			DisplayName:     getDisplayName(strategy.BusinessType_DeviceMerge, field),
			SourcePriority:  strategy.SourcePriority{"1": 1, "2": 1, "3": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "20": 1, "21": 1},
			UntrustedSource: strategy.UntrustedSource{},
			Version:         now,
		})
	}
	return strategies
}

func initVulnMergeStrategy() []strategy.Strategy {
	now := time.Now().UnixMilli()
	strategies := make([]strategy.Strategy, 0)
	// 以下字段的策略与 name 字段的策略一致，"VulType", "Describe", "Details", "Hazard", "Suggestions", "LastResponse"
	vulFields := []string{"Ip", "Port", "Level", "Name", "HasExp", "HasPoc"}
	for _, field := range vulFields {
		strategies = append(strategies, strategy.Strategy{
			BusinessType:    strategy.BusinessType_VulnMerge,
			FieldName:       field,
			DisplayName:     getDisplayName(strategy.BusinessType_VulnMerge, field),
			SourcePriority:  strategy.SourcePriority{"7": 1, "6": 2, "3": 3, "8": 4, "12": 5},
			UntrustedSource: strategy.UntrustedSource{},
			Version:         now,
		})
	}
	return strategies
}

func initPersonMergeStrategy() []strategy.Strategy {
	now := time.Now().UnixMilli()
	strategies := make([]strategy.Strategy, 0)
	personFields := []string{"EnglishName", "Department", "Title", "Email"}
	for _, field := range personFields {
		strategies = append(strategies, strategy.Strategy{
			BusinessType:    strategy.BusinessType_PersonMerge,
			FieldName:       field,
			DisplayName:     getDisplayName(strategy.BusinessType_PersonMerge, field),
			SourcePriority:  strategy.SourcePriority{"4": 1, "5": 1, "6": 1, "7": 1},
			UntrustedSource: strategy.UntrustedSource{},
			Version:         now,
		})
	}

	personFields = []string{"Status", "WorkNumber"}
	for _, field := range personFields {
		strategies = append(strategies, strategy.Strategy{
			BusinessType:    strategy.BusinessType_PersonMerge,
			FieldName:       field,
			DisplayName:     getDisplayName(strategy.BusinessType_PersonMerge, field),
			SourcePriority:  strategy.SourcePriority{"7": 1, "6": 2, "4": 3, "5": 4},
			UntrustedSource: strategy.UntrustedSource{},
			Version:         now,
		})
	}

	return strategies
}
