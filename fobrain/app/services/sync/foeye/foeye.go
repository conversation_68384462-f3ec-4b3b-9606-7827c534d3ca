package foeye

import (
	"context"
	"fmt"
	"fobrain/fobrain/app/services/rule_infos_level"
	"fobrain/pkg/utils"
	"fobrain/pkg/utils/handle_es_bulk_error"
	"strings"
	"time"

	"github.com/olivere/elastic/v7"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"

	"fobrain/fobrain/app/services/node/foeye"
	"fobrain/fobrain/common/constant"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	assetses "fobrain/models/elastic/assets"
	"fobrain/models/elastic/poc"
	foeyees "fobrain/models/elastic/source/foeye"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
)

// SyncFoeye 同步foeye数据
func SyncFoeye(node *data_source.Node, taskInfo string) error {
	logs.GetSyncLogger().Infof("SyncFoeye start taskInfo:%s", taskInfo)
	assetTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())
	threatTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncThreat)).Int())

	//获取foeyeClient
	cli := foeye.NewFoeye()

	//设置foeyeClient请求的节点
	if err := cli.SetNode(node.Id); err != nil {
		return err
	}

	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncAsset)).Int() == data_sync_task.SyncAsset {
		//更新资产任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(assetTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("Foeye syncAssets UpdateStatusById StatusDoing err:%s", err.Error())
		}
		go syncAssets(constant.DefaultPage, cli, taskInfo, node, 0)
	}

	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncThreat)).Int() == data_sync_task.SyncThreat {
		//更新漏洞任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(threatTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("Foeye syncThreats UpdateStatusById StatusDoing err:%s", err.Error())
		}
		go syncThreats(constant.DefaultPage, cli, taskInfo, node, 0)
	}

	return nil
}

// syncAssets 同步资产
// @param [int64] otherTaskId 如果是同步任务资产则，通过此 ID 传递任务ID。
func syncAssets(page int, cli *foeye.Foeye, taskInfo string, node *data_source.Node, successCount int) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())
	logs.GetSyncLogger().Infof("SyncFoeye syncAssets start taskInfo:%d,taskChildId:%d", taskId, taskChildId)

	// 默认设置为3，智能判断
	networkType, err := node.GetNetworkType()
	if err != nil {
		logs.GetSyncLogger().Errorf("Foeye syncAssets GetNetworkType err:%s", err.Error())
	}
	personField := "name"
	if cli.GetConfig("person_field") != "" && cli.GetConfig("person_field") != nil {
		personField = cli.GetConfig("person_field").(string)
	}
	//获取资产
	assets, err := cli.GetAssets(page, constant.DefaultSize)
	if err != nil {
		logs.GetSyncLogger().Errorf("Foeye syncAssets GetAssets err:%s", err)

		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, "GetAssets "+err.Error())
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("Foeye syncAssets GetAssets Task UpdateFailById err:%s", taskErr.Error())
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, "GetAssets "+err.Error())
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("Foeye syncAssets GetAssets childTask UpdateFailById err:%s", childTaskErr.Error())
		}
		return
	}

	total := gjson.Get(assets, "total").Int() //数据总量
	result := gjson.Get(assets, "data.info")

	//子任务更新需要同步的数据总量
	if page == constant.DefaultPage { //仅第一次同步
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateById(taskChildId, data_sync_child_task.DataSyncChildTask{
			SyncDataTotal: int(total),
		})

		if err != nil {
			logs.GetSyncLogger().Errorf("Foeye syncAssets UpdateById SyncDataTotal err:%s", err.Error())
		}
	}

	if result.Exists() && total > 0 {
		assetTaskBulkRequest := es.GetEsClient().Bulk()
		assetProcessBulkRequest := es.GetEsClient().Bulk()
		// 遍历 _id 字段，并根据 id 添加新的字段数据
		var num = 0
		result.ForEach(func(key, value gjson.Result) bool {
			logs.GetSyncLogger().Debugf("SyncFoeye syncAssets value info:%s", value)
			ip := gjson.Get(value.String(), "ip").String()
			data, _ := sjson.Set(value.String(), "node_id", node.Id)
			areaId := node.AreaId
			if ip != "" {
				// 获取区域
				areaId, err = node.GetAreaByIp(ip)
				if err != nil {
					logs.GetSyncLogger().Errorf("Foeye syncAssets GetAreaByIp failed,ip:%s,nodeId:%d,err:%s", ip, node.Id, err)
					r := data_sync_child_task.NewDataSyncChildTaskFailRecord()
					r.Create(&data_sync_child_task.DataSyncChildTaskFailRecord{
						ChildTaskId:  taskChildId,
						DataType:     "ip",
						DataContent:  ip,
						FailedReason: err.Error(),
					})
				}
			}
			data, _ = sjson.Set(data, "area_id", areaId)

			assetId := fmt.Sprintf("%d_%d_%s", node.Id, areaId, ip)
			//写入任务库
			assetTask := foeyees.NewFoeyeTaskAssetsModel()
			assetTaskId := fmt.Sprintf("%d_%s", taskId, assetId)
			data, _ = sjson.Set(data, "task_id", taskId)
			data, _ = sjson.Set(data, "child_task_id", taskChildId)
			assetTaskReq := elastic.NewBulkIndexRequest().Index(assetTask.IndexName()).Id(assetTaskId).Doc(data)
			assetTaskBulkRequest = assetTaskBulkRequest.Add(assetTaskReq)

			//写入过程库
			assetProcess := assetses.NewProcessAssetsModel()
			assetProcessId := fmt.Sprintf("%d_%d_%s", taskId, taskChildId, assetId)
			assetProcess.Id = assetProcessId
			assetProcess.TaskDataId = assetTaskId

			assetProcess.NetworkType = networkType
			assetProcess.PersonField = personField

			assetProcess.Area = int(areaId)
			assetProcess.Node = node.Id
			assetProcess.Source = node.SourceId
			assetProcess.TaskId = taskId
			assetProcess.ChildTaskId = taskChildId
			assetProcess.Ip = ip
			assetProcess.Os = gjson.Get(value.String(), "os").String()
			assetProcess.Mac = gjson.Get(value.String(), "mac").String()
			assetProcess.BusinessSystem = func() string {
				s := gjson.Get(value.String(), "business_app").String()
				if s == "[]" {
					s = ""
				}
				return s
			}()
			assetProcess.Oper = gjson.Get(value.String(), "username").String()
			assetProcess.BusinessOwner = gjson.Get(value.String(), "username").String()
			assetProcess.MachineRoom = gjson.Get(value.String(), "computer_room").String()
			assetProcess.Status = 1
			if int(gjson.Get(value.String(), "state").Int()) == 0 {
				assetProcess.Status = 2
			}

			var product []string
			//处理组件
			var ruleInfos []*assetses.RuleInfo
			//端口
			var ports []*assetses.PortInfo
			gjson.Get(value.String(), "port_list").ForEach(func(k, v gjson.Result) bool {
				var portInfo assetses.PortInfo
				gjson.Get(v.String(), "rule_info").ForEach(func(key, val gjson.Result) bool {
					if gjson.Get(val.String(), "name").String() != "" && !utils.InArray(gjson.Get(val.String(), "name").String(), product) {
						product = append(product, gjson.Get(val.String(), "name").String())
					}
					var ruleInfo assetses.RuleInfo
					var description string
					var tags []string
					//组件名称
					if gjson.Get(val.String(), "name").String() != "" {
						ruleInfo.Product = gjson.Get(val.String(), "name").String()
						ruleInfo.Level = rule_infos_level.GetRuleInfoLevel(ruleInfo.Product)
					}
					//组件分类
					if gjson.Get(val.String(), "description").String() != "" {
						description = gjson.Get(val.String(), "description").String()
						tags = strings.Split(description, "|")
						if len(tags) > 1 {
							ruleInfo.FirstTag = tags[1]
							ruleInfo.SecondTag = tags[0]
						}
					}
					if ruleInfo.Product != "" {
						ruleInfos = append(ruleInfos, &ruleInfo)
					}
					return true
				})
				portInfo.Port = int(gjson.Get(v.String(), "port").Int())
				portInfo.Protocol = gjson.Get(v.String(), "protocol").String()
				portInfo.Title = gjson.Get(v.String(), "title").String()
				portInfo.Url = gjson.Get(v.String(), "link_url").String()
				ports = append(ports, &portInfo)

				return true
			})

			assetProcess.RuleInfos = ruleInfos
			assetProcess.Product = product
			assetProcess.Ports = ports
			lastResponseAt, _ := time.Parse(localtime.TimeFormat, gjson.Get(value.String(), "lastupdatetime").String())
			if lastResponseAt.IsZero() {
				lastResponseAt = time.Now()
			}
			assetProcess.LastResponseAt = localtime.NewLocalTime(lastResponseAt)
			ct := gjson.Get(value.String(), "createtime").String()
			if ct != "" {
				createTime, _ := time.Parse(localtime.TimeFormat, ct)
				assetProcess.CreatedAt = localtime.NewLocalTime(createTime)
			} else {
				assetProcess.CreatedAt = localtime.NewLocalTime(time.Now())
			}
			assetProcess.UpdatedAt = localtime.Now()
			assetProcessReq := elastic.NewBulkIndexRequest().Index(assetProcess.IndexName()).Id(assetProcessId).Doc(assetProcess)
			assetProcessBulkRequest = assetProcessBulkRequest.Add(assetProcessReq)
			num = num + 1
			return true
		})
		// 分页逻辑有问题，可能最后一页是空数据，或者第一页就空的，此处需要判断是不是真的有数据，否则会报空指针
		if num > 0 {
			assetProcessBulkReq, err := assetProcessBulkRequest.Refresh("true").Do(context.Background())

			if err != nil || assetProcessBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(assetses.NewProcessAssetsModel().IndexName(), err, assetProcessBulkReq)
				logs.GetSyncLogger().Errorf("Foeye syncAssets assetProcessBulkRequest.Do err:%s", errString)

				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("Foeye syncAssets assetProcessBulkRequest.Do task UpdateFailById err:%s", taskErr)
				}

				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("Foeye syncAssets assetProcessBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
				}
				return
			}
			assetTaskBulkReq, err := assetTaskBulkRequest.Do(context.Background())

			if err != nil || assetTaskBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(foeyees.NewFoeyeTaskAssetsModel().IndexName(), err, assetTaskBulkReq)
				logs.GetSyncLogger().Errorf("Foeye syncAssets assetTaskBulkRequest.Do err:%s", errString)

				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("Foeye syncAssets assetTaskBulkRequest.Do task UpdateFailById err:%s", taskErr)
				}

				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("Foeye syncAssets assetTaskBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
				}
				return
			}
			//更新成功数量
			err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(taskChildId, len(assetTaskBulkReq.Items))
			if err != nil {
				logs.GetSyncLogger().Errorf("Foeye syncAssets UpdateSyncDataSuccessTotal err:%s", err.Error())
			}
		}

	}

	if page*constant.DefaultSize <= int(total) && total > 0 {
		syncAssets(page+1, cli, taskInfo, node, successCount)
	} else {
		logs.GetSyncLogger().Infof("SyncFoeye syncAssets end taskInfo:%d,taskChildId:%d", taskId, taskChildId)
		//更新子任务完成状态
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(taskChildId, data_sync_child_task.StatusSuccess)
		if err != nil {
			logs.GetSyncLogger().Errorf("Foeye syncAssets UpdateStatusById StatusSuccess err:%s", err.Error())
		}
	}
}

// syncThreats 同步漏洞
func syncThreats(page int, cli *foeye.Foeye, taskInfo string, node *data_source.Node, successCount int) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncThreat)).Int())
	logs.GetSyncLogger().Infof("SyncFoeye syncThreats start taskInfo:%d,taskChildId:%d", taskId, taskChildId)

	//获取漏洞
	threats, err := cli.GetThreats(page, constant.DefaultSize)
	if err != nil {
		logs.GetSyncLogger().Errorf("Foeye syncThreats GetThreats err:%s", err)

		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, "GetThreats "+err.Error())
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("Foeye syncThreats GetThreats Task UpdateFailById err:%s", taskErr.Error())
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, "GetThreats "+err.Error())
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("Foeye syncThreats GetThreats childTask UpdateFailById err:%s", childTaskErr.Error())
		}
		return
	}

	total := gjson.Get(threats, "total").Int() //数据总量
	result := gjson.Get(threats, "data.info")

	//子任务更新需要同步的数据总量
	if page == constant.DefaultPage { //仅第一次同步
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateById(taskChildId, data_sync_child_task.DataSyncChildTask{
			SyncDataTotal: int(total),
		})

		if err != nil {
			logs.GetSyncLogger().Errorf("Foeye syncThreats UpdateById SyncDataTotal err:%s", err.Error())
		}
	}

	// client := redis.GetRedisClient()
	// rq := &queue.RedisQueue{Client: client}
	if result.Exists() && total > 0 && result.Raw != "null" {
		// var threatIds []map[string]interface{}
		threatTaskBulkRequest := es.GetEsClient().Bulk()
		threatProcessBulkRequest := es.GetEsClient().Bulk()

		// 遍历 _id 字段，并根据 id 添加新的字段数据
		var num = 0
		result.ForEach(func(key, value gjson.Result) bool {
			logs.GetSyncLogger().Debugf("SyncFoeye syncThreats value info:%s", value)
			id := gjson.Get(value.String(), "id").String()
			data, _ := sjson.Set(value.String(), "node_id", node.Id)
			data, _ = sjson.Set(data, "original_id", id)
			ip := gjson.Get(value.String(), "ip").String()
			areaId := node.AreaId
			if ip != "" {
				// 获取区域
				areaId, err = node.GetAreaByIp(ip)
				if err != nil {
					logs.GetSyncLogger().Errorf("Foeye syncThreats GetAreaByIp failed,ip:%s,nodeId:%d,err:%s", ip, node.Id, err)
					r := data_sync_child_task.NewDataSyncChildTaskFailRecord()
					r.Create(&data_sync_child_task.DataSyncChildTaskFailRecord{
						ChildTaskId:  taskChildId,
						DataType:     "ip",
						DataContent:  ip,
						FailedReason: err.Error(),
					})
				}
			}
			data, _ = sjson.Set(data, "area_id", areaId)

			threatId := fmt.Sprintf("%d_%d_%s_%d_%d", node.Id, areaId, id, taskId, taskChildId)
			//写入任务库
			threatTask := foeyees.NewFoeyeTaskThreatsModel()
			threatTaskId := fmt.Sprintf("%d_%s", taskId, threatId)
			data, _ = sjson.Set(data, "task_id", taskId)
			data, _ = sjson.Set(data, "child_task_id", taskChildId)
			threatTaskReq := elastic.NewBulkIndexRequest().Index(threatTask.IndexName()).Id(threatTaskId).Doc(data)
			threatTaskBulkRequest = threatTaskBulkRequest.Add(threatTaskReq)

			//写入过程库
			threatProcess := poc.NewProcessPocModel()
			threatProcess.Id = threatId
			threatProcess.OriginalId = id
			threatProcess.TaskDataId = threatTaskId
			threatProcess.TaskId = taskId
			threatProcess.ChildTaskId = taskChildId
			threatProcess.Area = areaId
			threatProcess.Node = node.Id
			threatProcess.Source = node.SourceId
			threatProcess.IsPoc = poc.YesPoc
			threatProcess.HasPoc = poc.YesPoc
			threatProcess.Ip = ip
			threatProcess.Port = int(gjson.Get(value.String(), "port").Int())
			threatProcess.Url = gjson.Get(value.String(), "url").String()
			threatProcess.Level = int(gjson.Get(value.String(), "level").Int())
			threatProcess.Name = gjson.Get(value.String(), "common_title").String()
			threatProcess.Cve = gjson.Get(value.String(), "cveId").String()
			threatProcess.HasExp = int(gjson.Get(value.String(), "has_exp").Int())
			threatProcess.Status = int(gjson.Get(value.String(), "state").Int())
			threatProcess.Describe = gjson.Get(value.String(), "common_description").String()
			threatProcess.Details = gjson.Get(value.String(), "common_impact").String()
			threatProcess.Suggestions = gjson.Get(value.String(), "recommandation").String()
			lastResponseAt, _ := time.Parse(localtime.TimeFormat, gjson.Get(value.String(), "lastupdatetime").String())
			if !lastResponseAt.IsZero() {
				threatProcess.LastResponseAt = localtime.NewLocalTime(lastResponseAt)
			}
			var level int
			switch gjson.Get(value.String(), "level").Int() {
			case 0:
				level = 1 // 低危
			case 1:
				level = 2 // 中危
			case 2:
				level = 3 // 高危
			case 3:
				level = 4 // 严重
			default:
				level = 5 // 未知
			}
			//漏洞等级（低危 1 中危 2 高危 3 严重 4 未知 5）
			threatProcess.Level = level
			threatProcess.VulType = gjson.Get(value.String(), "vulType").String()
			ct := gjson.Get(value.String(), "createtime").String()
			if ct != "" {
				createTime, _ := time.Parse(localtime.TimeFormat, ct)
				threatProcess.CreatedAt = localtime.NewLocalTime(createTime)
			} else {
				threatProcess.CreatedAt = localtime.NewLocalTime(time.Now())
			}
			threatProcess.UpdatedAt = localtime.Now()
			threatProcessReq := elastic.NewBulkIndexRequest().Index(threatProcess.IndexName()).Id(threatId).Doc(threatProcess)
			threatProcessBulkRequest = threatProcessBulkRequest.Add(threatProcessReq)
			// threatIds = append(threatIds, map[string]interface{}{"id": threatId, "is_poc": poc.YesPoc, "task_id": taskChildId})
			num = num + 1
			return true
		})
		if num > 0 {
			threatProcessBulkReq, err := threatProcessBulkRequest.Refresh("true").Do(context.Background())
			if err != nil || threatProcessBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(poc.NewProcessPocModel().IndexName(), err, threatProcessBulkReq)
				logs.GetSyncLogger().Errorf("Foeye syncThreats threatProcessBulkRequest.Do err:%s", errString)

				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("Foeye syncThreats threatProcessBulkRequest.Do task UpdateFailById err:%s", taskErr)
				}

				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("Foeye syncThreats threatProcessBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
				}
				return
			}

			threatTaskBulkReq, err := threatTaskBulkRequest.Do(context.Background())
			if err != nil || threatTaskBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(foeyees.NewFoeyeTaskThreatsModel().IndexName(), err, threatTaskBulkReq)
				logs.GetSyncLogger().Errorf("Foeye syncThreats threatTaskBulkRequest.Do err:%s", errString)

				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("Foeye syncThreats threatTaskBulkRequest.Do task UpdateFailById err:%s", taskErr)
				}

				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("Foeye syncThreats threatTaskBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
				}
				return
			}

			err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(taskChildId, len(threatTaskBulkReq.Items))
			if err != nil {
				logs.GetSyncLogger().Errorf("Foeye syncThreats UpdateSyncDataSuccessTotal err:%s", err.Error())
			}
		}
	}

	if page*constant.DefaultSize <= int(total) && total > 0 {
		syncThreats(page+1, cli, taskInfo, node, successCount)
	} else {
		logs.GetSyncLogger().Infof("SyncFoeye syncThreats end taskInfo:%d,taskChildId:%d", taskId, taskChildId)
		// // 发送任务结束标识
		// rq.Push(cfg.LoadQueue().VulnMergeQueue, []map[string]interface{}{{"task_id": taskChildId, "task_type": "sync", "total": successCount, "is_end": true}})
		//更新子任务完成状态
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(taskChildId, data_sync_child_task.StatusSuccess)
		if err != nil {
			logs.GetSyncLogger().Errorf("Foeye syncThreats UpdateStatusById StatusSuccess err:%s", err.Error())
		}
	}
}
