package assets

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"slices"
	"strconv"
	"strings"
	"time"

	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/network_areas"

	"github.com/olivere/elastic/v7"

	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/pkg/utils"
	pgidservice "fobrain/services/people_pgid"
)

const StatusOnLine = 1
const StatusOffLine = 2

const NetworkTypeInternal = 1 //内网
const NetworkTypeExternal = 2 //外网

type Business struct {
	System       string      `json:"system"`     // 业务系统
	SystemId     string      `json:"system_id"`  // 业务系统id
	Owner        string      `json:"owner"`      // 业务系统负责人
	OwnerId      string      `json:"owner_id"`   // 业务系统负责人id
	Department   []string    `json:"department"` // 负责人部门
	Source       string      `json:"source"`     // 来源,rule或者original,融合服务负责标记
	BusinessInfo interface{} `json:"business_info"`
	Addition     string      `json:"addition"` // 附加字段

	BusinessFrom         int `json:"business_from"`          // 业务系统来源 1资产IP导入 2数据源 3业务信息配置人工添加 4业务信息配置人工导入
	BusinessTrustedState int `json:"business_trusted_state"` // 业务系统可信状态

	PersonBase     []*PersonBase     `json:"person_base"`     // 业务系统负责人信息
	DepartmentBase []*DepartmentBase `json:"department_base"` // 业务系统部门信息
}

// 部门信息
type DepartmentBase struct {
	BusinessSystemId   string            `json:"business_system_id"`   // 业务系统
	BusinessSystemName string            `json:"business_system_name"` // 业务系统名称
	UserId             string            `json:"user_id"`              // 用户id
	UserName           string            `json:"user_name"`            // 用户名称
	Name               string            `json:"name"`                 // 部门名称,完整的部门名称，以/分隔
	Id                 uint64            `json:"id"`                   // 部门id
	Parents            []*DepartmentBase `json:"parents"`              // 部门父级
}

// 负责人信息
type PersonBase struct {
	Id         string            `json:"id"`         // 负责人id，来自人员台账
	Fid        string            `json:"fid"`        // 负责人fid，来自人员台账
	Name       string            `json:"name"`       // 负责人名称，来自人员台账
	Pgid       string            `json:"pgid"`       // 负责人pgid，来自动态计算
	FindInfo   []*PersonFindInfo `json:"find_info"`  // 查找信息，来自原始数据上报
	Department []*DepartmentBase `json:"department"` // 部门信息，来自人员台账或人工校准
}

func NewPersonBase() *PersonBase {
	return &PersonBase{
		FindInfo:   make([]*PersonFindInfo, 0),
		Department: make([]*DepartmentBase, 0),
	}
}

type PersonFindInfo struct {
	SourceId     uint64 `json:"source_id"`     // 来源id
	NodeId       uint64 `json:"node_id"`       // 节点id
	SourceValue  string `json:"source_value"`  // 来源字段值
	MappingField string `json:"mapping_field"` // 映射字段，name,english_name,email,phone,work_number
	FindCount    int    `json:"find_count"`    // 找到的人数
}

type PersonWithMapping struct {
	SourceId     uint64 `json:"source_id"`     // 来源id
	NodeId       uint64 `json:"node_id"`       // 节点id
	SourceValue  string `json:"source_value"`  // 来源字段值
	MappingField string `json:"mapping_field"` // 映射字段，name,english_name,email,phone,work_number
}

func (a *Business) Equal(b *Business) bool {
	if a == nil && b == nil {
		return true
	}
	if a == nil || b == nil {
		return false
	}
	if a.SystemId == b.SystemId {
		return true
	}
	if a.System == b.System && a.Owner == b.Owner {
		return true
	}
	return false
}

type (
	Assets struct {
		*es.BaseModel         `json:"-"`
		Product               []string                     `json:"product"`
		Id                    string                       `json:"id"`                       // ID
		Fid                   string                       `json:"fid"`                      // 资产唯一标识，IP+区域 唯一值
		FidHash               string                       `json:"fid_hash"`                 // 资产唯一标识，IP+区域 唯一值
		Area                  int                          `json:"area"`                     // 区域
		ProcessIds            []string                     `json:"process_ids"`              // 过程表ids
		SourceIds             []uint64                     `json:"source_ids"`               // 数据源ids-对应 mysql 的 `data_sources` 表的 ID
		NodeIds               []uint64                     `json:"node_ids"`                 // 节点ids-对应 mysql 的 `data_nodes` 表的 ID
		TaskDataIds           []string                     `json:"asset_task_ids"`           // 资产任务ids
		AllSourceIds          []uint64                     `json:"all_source_ids"`           // 所有数据源ids
		AllNodeIds            []uint64                     `json:"all_node_ids"`             // 所有节点ids
		AllTaskDataIds        []string                     `json:"all_asset_task_ids"`       // 所有任务ids
		AllProcessIds         []string                     `json:"all_process_ids"`          // 所有过程ids
		Ip                    string                       `json:"ip"`                       // IP
		IpType                int                          `json:"ip_type"`                  // IP类型(1是IPv4，2是IPv6,3是其他)
		IpSegment             []string                     `json:"ip_segment"`               // IP段
		IpSegmentSource       map[string]string            `json:"ip_segment_source"`        // IP段来源
		HostName              []string                     `json:"hostname"`                 // 主机名
		HostNameSource        map[string]string            `json:"hostname_source"`          // 主机名来源
		EthName               []string                     `json:"eth_name"`                 // 网卡名
		EthNameSource         map[string]string            `json:"eth_name_source"`          // 网卡名来源
		Os                    []string                     `json:"os"`                       // 操作系统
		OsSource              map[string]string            `json:"os_source"`                // 操作系统来源
		Kernel                []string                     `json:"kernel"`                   // 内核
		KernelSource          map[string]string            `json:"kernel_source"`            // 内核来源
		Model                 []string                     `json:"model"`                    // 型号
		ModelSource           map[string]string            `json:"model_source"`             // 型号来源
		Maker                 []string                     `json:"maker"`                    // 制造商
		MakerSource           map[string]string            `json:"maker_source"`             // 制造商来源
		Sn                    []string                     `json:"sn"`                       // 序列号
		SnSource              map[string]string            `json:"sn_source"`                // 序列号来源
		Mac                   []string                     `json:"mac"`                      // MAC地址
		MacSource             map[string]string            `json:"mac_source"`               // MAC地址来源
		RuleInfos             []*RuleInfo                  `json:"rule_infos"`               //组件信息
		RuleInfosSource       map[string][]*RuleInfo       `json:"rule_infos_source"`        //组件信息
		ProductSource         map[string]string            `json:"product_source"`           // 组件来源
		Business              []*Business                  `json:"business"`                 // 业务系统
		BusinessSource        map[string][]*Business       `json:"business_source"`          // 业务系统来源
		BusinessDepartment    []*DepartmentBase            `json:"business_department"`      // 业务系统部门
		OperSource            map[string]string            `json:"oper_source"`              // 运维人员来源
		OperDepartment        []*DepartmentBase            `json:"oper_department"`          // 运维人员部门
		OperInfo              []*PersonBase                `json:"oper_info"`                // 运维人员信息
		OperWithMapping       []*PersonWithMapping         `json:"oper_with_mapping"`        // 运维人员映射
		MachineRoom           []string                     `json:"machine_room"`             // 机房
		MachineRoomSource     map[string]string            `json:"machine_room_source"`      // 机房来源
		Status                int                          `json:"status"`                   // 状态 在线1 离线2
		StatusSource          map[string]int               `json:"status_source"`            // 状态来源
		Ports                 []*PortInfo                  `json:"ports"`                    // 端口
		PortsSource           map[string][]*PortInfo       `json:"ports_source"`             // 端口来源
		MemorySize            []string                     `json:"memory_size"`              // 内存大小
		MemorySizeSource      map[string]string            `json:"memory_size_source"`       // 内存大小来源
		MemoryUsageRate       []string                     `json:"memory_usage_rate"`        // 内存使用率
		MemoryUsageRateSource map[string]string            `json:"memory_usage_rate_source"` // 内存使用率来源
		CpuMaker              []string                     `json:"cpu_maker"`                // CPU厂商
		CpuMakerSource        map[string]string            `json:"cpu_maker_source"`         // CPU厂商来源
		CpuBrand              []string                     `json:"cpu_brand"`                // CPU品牌
		CpuBrandSource        map[string]string            `json:"cpu_brand_source"`         // CPU品牌来源
		CpuCount              []int                        `json:"cpu_count"`                // CPU数量
		CpuCountSource        map[string]int               `json:"cpu_count_source"`         // CPU数量来源
		DiskCount             []int                        `json:"disk_count"`               // 磁盘数量
		DiskCountSource       map[string]int               `json:"disk_count_source"`        // 磁盘数量来源
		DiskSize              []int                        `json:"disk_size"`                // 磁盘大小
		DiskSizeSource        map[string]int               `json:"disk_size_source"`         // 磁盘大小来源
		DiskUsageRate         []string                     `json:"disk_usage_rate"`          // 磁盘使用率
		DiskUsageRateSource   map[string]string            `json:"disk_usage_rate_source"`   // 磁盘使用率来源
		LoadAverage           []string                     `json:"load_average"`             // 负载
		LoadAverageSource     map[string]string            `json:"load_average_source"`      // 负载来源
		NetworkType           int                          `json:"network_type"`             // 网络类型(1是内网，2是外网，3是其他) 自己判断
		FusionRules           *es.FusionRules              `json:"fusion_rules"`             // 融合规则
		DeletedAt             *localtime.Time              `json:"deleted_at"`               // 删除时间 - 软删除时间-回收站列表展示的是存在这个字段内容的数据
		PurgedAt              *localtime.Time              `json:"purged_at"`                // 彻底删除时间 - 彻底删除时间-彻底删除数据
		CreatedAt             *localtime.Time              `json:"created_at"`               // 创建时间
		UpdatedAt             *localtime.Time              `json:"updated_at"`               // 更新时间
		LastResponseAt        *localtime.Time              `json:"last_response_at"`         // 最后一次响应时间，来自数据源上报，属于业务属性
		DataSourceResponseAt  *localtime.Time              `json:"data_source_response_at"`  // 数据源最后一次响应时间，来自数据源更新动作，属于审计属性
		IsDeviceExtracted     int                          `json:"is_device_extracted"`      // 是否参与了实体提取,1是未参与，2是参与了
		MergeCount            int                          `json:"merge_count"`              // 融合次数
		PersonLimit           []string                     `json:"person_limit"`             // 人员限制-全局统一字段，使用人员 fid 来限制数据权限
		PersonLimitHash       []string                     `json:"person_limit_hash"`        // 人员限制-全局统一字段，使用人员 fid_hash 来限制数据权限
		Tags                  []string                     `json:"tag"`
		TagsSource            map[string]string            `json:"tag_source"`
		PocNum                int64                        `json:"poc_num"`                 // 资产对应的漏洞数量
		PocNumUpdatedAt       *localtime.Time              `json:"poc_num_updated_at"`      // 资产对应的漏洞数量更新时间
		CustomFields          map[string]string            `json:"custom_fields"`           // 自定义字段
		JarPackageInfo        []*JarPackageInfo            `json:"jar_package_info"`        // jar包信息
		JarPackageInfoSource  map[string][]*JarPackageInfo `json:"jar_package_info_source"` // jar包信息来源

		OperStaffIds          []string `json:"oper_staff_ids"`          // 运维人员ids
		BusinessStaffIds      []string `json:"business_staff_ids"`      // 业务系统负责人ids
		OperDepartmentIds     []uint64 `json:"oper_department_ids"`     // 运维人员部门ids
		BusinessDepartmentIds []uint64 `json:"business_department_ids"` // 业务系统部门ids
	}

	MonitorAsset struct {
		Id              int             `json:"id"`
		Ip              string          `json:"ip"`               // IP
		Port            json.RawMessage `json:"ports"`            // 端口
		Protocol        json.RawMessage `json:"protocol"`         // 协议
		Product         json.RawMessage `json:"product"`          // 组件
		Device          string          `json:"device"`           // 设备
		Business        json.RawMessage `json:"business"`         // 业务系统
		BusinessOwner   json.RawMessage `json:"business_owner"`   // 业务系统负责人
		OperationsOwner json.RawMessage `json:"operations_owner"` // 运维负责人
	}
)

func (p *Assets) AppendSourceIds(ids ...uint64) {
	if len(p.SourceIds) == 0 {
		p.SourceIds = make([]uint64, 0)
	}
	p.SourceIds = append(p.SourceIds, ids...)
	p.SourceIds = utils.ListDistinct(p.SourceIds)
}

func (p *Assets) AppendNodeIds(ids ...uint64) {
	if len(p.NodeIds) == 0 {
		p.NodeIds = make([]uint64, 0)
	}
	p.NodeIds = append(p.NodeIds, ids...)
	p.NodeIds = utils.ListDistinct(p.NodeIds)
}

func (p *Assets) AppendTaskDataIds(ids ...string) {
	if len(p.TaskDataIds) == 0 {
		p.TaskDataIds = make([]string, 0)
	}
	p.TaskDataIds = append(p.TaskDataIds, ids...)
	p.TaskDataIds = utils.ListDistinct(p.TaskDataIds)
}

func (p *Assets) AppendProcessIds(ids ...string) {
	if len(p.ProcessIds) == 0 {
		p.ProcessIds = make([]string, 0)
	}
	p.ProcessIds = append(p.ProcessIds, ids...)
	p.ProcessIds = utils.ListDistinct(p.ProcessIds)
}

// IndexName 索引名
func (p *Assets) IndexName() string {
	return "asset"
}

func (p *Assets) String() string {
	if p == nil {
		return ""
	}
	return ""
}

func NewAssets() *Assets {
	return &Assets{}
}
func (p *Assets) NewKeywordQuery(keyword string, boolQuery *elastic.BoolQuery) *elastic.BoolQuery {
	escapedKeyword := fmt.Sprintf("*%s*", utils.Escape(keyword))
	boolQuery.Should(
		elastic.NewWildcardQuery("ip.keyword", escapedKeyword).CaseInsensitive(true),
		elastic.NewWildcardQuery("os", escapedKeyword).CaseInsensitive(true),
		elastic.NewWildcardQuery("product", escapedKeyword).CaseInsensitive(true),
		elastic.NewNestedQuery("rule_infos",
			elastic.NewWildcardQuery("rule_infos.product", escapedKeyword).CaseInsensitive(true),
		),
		elastic.NewWildcardQuery("ports.protocol", escapedKeyword).CaseInsensitive(true),
		elastic.NewWildcardQuery("ports.url.keyword", escapedKeyword).CaseInsensitive(true),
		elastic.NewWildcardQuery("ports.title.keyword", escapedKeyword).CaseInsensitive(true),
		elastic.NewWildcardQuery("ports.domain.keyword", escapedKeyword).CaseInsensitive(true),
		elastic.NewWildcardQuery("oper", escapedKeyword).CaseInsensitive(true),
		elastic.NewWildcardQuery("business.owner", escapedKeyword).CaseInsensitive(true),

		// 对 port 做精确匹配，用 TermQuery，且 keyword 要转成数字
		// 假设 keyword 是数字字符串，可以用 strconv.Atoi 转成 int64
		func() *elastic.TermQuery {
			portNum, err := strconv.ParseInt(keyword, 10, 64)
			if err != nil {
				// keyword 不是数字，不能匹配端口，返回一个不会命中的 TermQuery
				return elastic.NewTermQuery("ports.port", -1)
			}
			return elastic.NewTermQuery("ports.port", portNum)
		}(),
	).MinimumShouldMatch("1")

	return boolQuery
}

func (p *Assets) SampleHash() map[string]interface{} {
	return map[string]interface{}{
		"id":           p.Id,
		"area_id":      p.Area,
		"area":         network_areas.NetworkAreaName(uint64(p.Area)),
		"source_names": data_source.NewSourceModel().SourceNames(p.AllSourceIds),
		"node_names":   data_source.NodeNames(p.AllNodeIds),
		"ip":           p.Ip,
		"ip_type":      p.IpTypeDesc(),
		"ip_segment":   p.IpSegment,
		"hostname":     p.HostName,
		"eth_name":     p.EthName,
		"os":           p.Os,
		"kernel":       p.Kernel,
		"model":        p.Model,
		"maker":        p.Maker,
		"sn":           p.Sn,
		"mac":          p.Mac,
		//"product":             p.Product,
		"rule_infos": p.RuleInfos,
		"business": func() []*Business {
			if len(p.Business) == 0 {
				return make([]*Business, 0)
			}
			for _, business := range p.Business {
				if business == nil {
					continue
				}
				if business.PersonBase == nil {
					business.PersonBase = make([]*PersonBase, 0)
				}
				if business.DepartmentBase == nil {
					business.DepartmentBase = make([]*DepartmentBase, 0)
				}
				for _, base := range business.PersonBase {
					base.Pgid, _ = pgidservice.GetPgidById(base.Id)
					if base.FindInfo == nil {
						base.FindInfo = make([]*PersonFindInfo, 0)
					}
					if base.Department == nil {
						base.Department = make([]*DepartmentBase, 0)
					}
				}
			}
			return p.Business
		}(),
		"oper":                    p.OperStringSlice(),
		"oper_info":               DistinctOperInfo(p.OperInfo),
		"machine_room":            p.MachineRoom,
		"status":                  p.StatusDesc(),
		"ports":                   p.Ports,
		"memory_size":             p.MemorySize,
		"memory_usage_rate":       p.MemoryUsageRate,
		"cpu_maker":               p.CpuMaker,
		"cpu_brand":               p.CpuBrand,
		"cpu_count":               p.CpuCount,
		"disk_count":              p.DiskCount,
		"disk_size":               p.DiskSize,
		"disk_usage_rate":         p.DiskUsageRate,
		"load_average":            p.LoadAverage,
		"network_type":            p.NetworkTypeDesc(),
		"created_at":              p.CreatedAt,
		"updated_at":              p.UpdatedAt,
		"last_response_at":        p.LastResponseAt,
		"data_source_response_at": p.DataSourceResponseAt,
		"is_device_extracted":     p.IsDeviceExtracted,
		"merge_count":             p.MergeCount,
		"person_limit_hash":       p.PersonLimitHash,
		"tags":                    p.Tags,
		"oper_department":         p.OperDepartment,
		"business_department":     p.BusinessDepartment,
		"poc_num":                 p.PocNum,
		"poc_num_updated_at":      p.PocNumUpdatedAt,
		"custom_fields":           p.CustomFields,
		"jar_package_info":        p.JarPackageInfo,
	}
}
func (p *Assets) GenQueryNoDeletedAndPurged() *elastic.BoolQuery {
	return elastic.NewBoolQuery().
		MustNot(
			elastic.NewExistsQuery("deleted_at"),
			elastic.NewExistsQuery("purged_at"),
		)
}
func (p *Assets) RuleProduct() (res []string) {
	if len(p.RuleInfos) > 0 {
		for _, info := range p.RuleInfos {
			if info == nil {
				continue
			}
			res = append(res, info.Product)
		}
	}
	return res
}

func (p *Assets) PortInfos() (res [][]string) {
	if len(p.Ports) == 0 { // 检查 Ports 是否为空
		// 根据 NetworkType 的不同，填充不同数量的"无"
		switch p.NetworkType {
		case 1:
			res = append(res, []string{"", ""})
		case 2:
			res = append(res, []string{"", "", "", "", "", ""})
		default:
			res = append(res, []string{""})
		}
		return res
	}
	for _, port := range p.Ports {
		switch p.NetworkType {
		case 1: // 如果是内网展示什么
			ports := []string{
				strconv.Itoa(port.Port),
				port.Protocol,
			}
			res = append(res, ports)
		case 2: // 如果是外网展示内容
			ports := []string{
				strconv.Itoa(port.Port),
				port.Protocol,
				port.Url,
				port.Title,
				port.Domain,
				func() string {
					if port.Status == 0 {
						return "-"
					}
					return strconv.Itoa(port.Status)
				}(),
			}
			res = append(res, ports)
		default:
			res = append(res, []string{})
		}
	}
	return res
}

func (p *Assets) BusinessSystems() string {
	res := make([]string, 0)
	for _, source := range p.Business {
		if source == nil {
			continue
		}
		res = append(res, source.System)
	}

	return strings.Join(utils.Unique(res), ",")
}

func (p *Assets) BusinessOwners() string {
	res := make([]string, 0)
	for _, source := range p.Business {
		if source == nil {
			continue
		}
		for _, v := range source.PersonBase {
			if v.Name != "" {
				res = append(res, v.Name)
				continue
			}
			for _, t := range v.FindInfo {
				res = append(res, t.SourceValue)
			}
		}
	}
	return strings.Join(utils.Unique(res), ",")
}

func (p *Assets) BusinessDepartments() string {
	res := make([]string, 0)
	for _, source := range p.BusinessDepartment {
		if source == nil {
			continue
		}
		if source.Name != "" {
			res = append(res, source.Name)
			continue
		}
	}
	return strings.Join(utils.Unique(res), ",")
}
func (p *Assets) OperDepartments() string {
	res := make([]string, 0)
	for _, source := range p.OperDepartment {
		if source == nil {
			continue
		}
		if source.Name != "" {
			res = append(res, source.Name)
			continue
		}
	}
	return strings.Join(utils.Unique(res), ",")
}
func (p *Assets) OperStringSlice() []string {
	res := make([]string, 0)
	for _, source := range p.OperInfo {
		if source == nil {
			continue
		}
		if source.Id != "" {
			res = append(res, source.Name)
		} else {
			// 人工校准，仅校准部门信息
			if len(source.FindInfo) == 0 {
				continue
			}
			res = append(res, source.FindInfo[0].SourceValue)
		}
	}
	return res
}

func (p *Assets) OperString() string {
	res := p.OperStringSlice()
	return strings.Join(utils.Unique(res), ",")
}

func DistinctOperInfo(operInfo []*PersonBase) []*PersonBase {
	personIds := make([]string, 0)
	res := make([]*PersonBase, 0)
	for _, source := range operInfo {
		if source == nil {
			continue
		}
		if source.Id != "" {
			if !slices.Contains(personIds, source.Id) {
				personIds = append(personIds, source.Id)
				res = append(res, source)
			}
			source.Pgid, _ = pgidservice.GetPgidById(source.Id)
		} else {
			res = append(res, source)
		}
	}
	for _, item := range res {
		if item.FindInfo == nil {
			item.FindInfo = make([]*PersonFindInfo, 0)
		}
		if item.Department == nil {
			item.Department = make([]*DepartmentBase, 0)
		}
	}
	return res
}

func (p *Assets) IpTypeDesc() string {
	switch p.IpType {
	case 1:
		return "IPv4"
	case 2:
		return "IPv6"
	default:
		return "未知"
	}
}

func (p *Assets) StatusDesc() string {
	switch p.Status {
	case 1:
		return "在线"
	default:
		return "离线"
	}
}

func (p *Assets) GetProducts() string {
	// 提取 Product 并拼接成字符串
	var products []string
	for _, ruleInfo := range p.RuleInfos {
		if ruleInfo != nil {
			products = append(products, ruleInfo.Product)
		}
	}
	return strings.Join(products, ",")
}

func (p *Assets) NetworkTypeDesc() string {
	switch p.NetworkType {
	case 1:
		return "内网"
	case 2:
		return "外网"
	default:
		return "未知"
	}
}

func (p *Assets) Domains() (domains []string) {
	if p.Ports != nil {
		for _, port := range p.Ports {
			if port.Domain != "" {
				domains = append(domains, port.Domain)
			}
		}
	}

	return
}

// FindByQuery 根据条件搜索数据，性能较低，支持小数据量
// pageSize 限制最大为 300
func (p *Assets) FindByQuery(ctx context.Context, page, pageSize int, query *elastic.BoolQuery) ([]*Assets, error) {
	from := (page - 1) * pageSize
	searchResult, err := p.GetClient().Search().Index(p.IndexName()).Query(query).From(from).Size(pageSize).Sort("id", true).Do(ctx)
	if err != nil {
		return nil, err
	}
	total := searchResult.Hits.TotalHits.Value
	results := make([]*Assets, 0, total)
	if total == 0 {
		// 此处返回初始化后的 results，防止调用方直接循环结果导致空指针错误
		return results, nil
	}
	for _, hit := range searchResult.Hits.Hits {
		item, err := ConvertToAssetsModel(hit)
		if err != nil {
			logs.GetLogger().Warnf(fmt.Sprintf("资产信息解析失败,跳过,数据:%v,错误:%v", hit, err))
			continue
		}
		results = append(results, item)
	}
	return results, nil
}

// FindAllByQuery 根据条件查询所有数据，滚动查询，性能较高
func (p *Assets) FindAllByQuery(ctx context.Context, query *elastic.BoolQuery, nestedQuery *elastic.NestedQuery, fields ...string) ([]*Assets, int64) {
	scrollService := p.GetClient().Scroll().Index(p.IndexName())
	if query != nil {
		scrollService = scrollService.Query(query)
	}
	if nestedQuery != nil {
		scrollService = scrollService.Query(nestedQuery)
	}
	if len(fields) > 0 {
		scrollService = scrollService.FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...))
	}
	scrollService = scrollService.Scroll("1m").Size(1000).Sort("id", true)
	defer scrollService.Clear(context.Background())
	results := make([]*Assets, 0)
	var total int64
	for {
		searchResult, err := scrollService.Do(ctx)
		if errors.Is(err, io.EOF) {
			err = nil
			break
		}
		if err != nil || len(searchResult.Hits.Hits) == 0 {
			break
		}
		if len(searchResult.Hits.Hits) == 0 {
			break
		}
		total = searchResult.TotalHits()
		for _, hit := range searchResult.Hits.Hits {
			item, err := ConvertToAssetsModel(hit)
			if err != nil {
				logs.GetLogger().Warnf(fmt.Sprintf("资产信息解析失败,跳过,数据:%v,错误:%v", hit, err))
				continue
			}
			results = append(results, item)
		}
		// Update scrollService with new ScrollId
		//scrollService = p.GetClient().Scroll().ScrollId(searchResult.ScrollId).Scroll("1m")
	}
	return results, total
}

// ConvertToAssetsModel ES查询结果转换为Assets结构体
func ConvertToAssetsModel(hit *elastic.SearchHit) (*Assets, error) {
	item := NewAssets()
	err := json.Unmarshal(hit.Source, item)
	if err != nil {
		return nil, err
	}
	return item, nil
}

func (p *Assets) Insert(ctx context.Context, data *Assets) (*elastic.IndexResponse, error) {
	rsp, err := p.GetClient().Index().Index(p.IndexName()).BodyJson(data).Do(ctx)
	return rsp, err
}

func (p *Assets) First(ctx context.Context, ip, area string) (*Assets, error) {
	q := elastic.NewBoolQuery()
	q = q.Must(elastic.NewTermQuery("area", area), elastic.NewTermQuery("ip", ip))
	// 限制只返回一条数据即可
	searchResult, err := p.GetClient().Search().Index(p.IndexName()).Query(q).Size(1).Do(ctx)
	if err != nil {
		logs.GetLogger().Debugf("search assets error. IP: %s, Area:%s, error: %v,", ip, area, err)
		return nil, errors.New("search error")
	}
	if searchResult.Hits.TotalHits.Value == 0 {
		logs.GetLogger().Debugf("assets not found. IP: %s, Area:%s", ip, area)
		return nil, nil
	}
	item, err := ConvertToAssetsModel(searchResult.Hits.Hits[0])
	return item, err
}

func (p *Assets) Exists(ctx context.Context, query elastic.Query) bool {
	searchResult, err := p.GetClient().Search().Index(p.IndexName()).Query(query).Do(ctx)
	if err != nil {
		logs.GetLogger().Debugf("search assets error. query: %v, error: %v, ", query, err)
		return false
	}
	return searchResult.Hits.TotalHits.Value != 0
}

//	func (p *Assets) Exists(ctx context.Context, businessId string) bool {
//		q := elastic.NewBoolQuery()
//		q = q.Must(elastic.NewTermQuery("business.system_id", businessId))
//		searchResult, err := p.GetClient().Search().Index(p.IndexName()).Query(q).Do(ctx)
//		if err != nil {
//			logs.GetLogger().Debugf("search assets error. business: %s, error: %v, ", businessId, err)
//			return false
//		}
//		return searchResult.Hits.TotalHits.Value != 0
//	}
//
// UpdateOrCreate 更新或创建数据，传入id表示
func (p *Assets) UpdateOrCreate(ctx context.Context, data *Assets) (*elastic.UpdateResponse, error) {
	if data.Area < 1 || data.Ip == "" {
		return nil, errors.New("area or ip is empty")
	}
	if data.CreatedAt == nil {
		data.CreatedAt = localtime.NewLocalTime(time.Now())
	}
	data.UpdatedAt = localtime.NewLocalTime(time.Now())
	rsp, err := p.GetClient().Update().Index(p.IndexName()).DocAsUpsert(true).Doc(data).Do(ctx)
	return rsp, err
}

const (
	addValueToExemptRecordIdsScript = `
	if (ctx._source.exempt_record_ids == null) {
		ctx._source.exempt_record_ids = [];
	}
	def targetEntry = ctx._source.exempt_record_ids.find(entry -> entry.key == params.key);
	if (targetEntry == null) {
		ctx._source.exempt_record_ids.add([
			"key": params.key,
			"values": params.values
		]);
	} else {
		for (value in params.values) {
			if (!targetEntry.values.contains(value)) {
				targetEntry.values.add(value);
			}
		}
	}
`

	deleteValueFromExemptRecordIdsScript = `
		if (ctx._source.exempt_record_ids != null) {
			def targetEntry = ctx._source.exempt_record_ids.find(entry -> entry.key == params.key);
			if (targetEntry != null) {
				targetEntry.values.removeAll(params.values);
				if (targetEntry.values.isEmpty()) {
					ctx._source.exempt_record_ids.removeIf(entry -> entry.key == params.key);
				}
			}
		}
	`
)

// 使用updateByQuery追加exempt_record_ids指定的key的values值
func (p *Assets) AddValueToExemptRecordIds(dimensionsID int64, ids []int64, query *elastic.BoolQuery) (int64, error) {
	if len(ids) == 0 {
		return 0, nil
	}
	params := map[string]interface{}{
		"key":    dimensionsID,
		"values": ids,
	}
	updateByQuery, err := p.GetClient().UpdateByQuery(p.IndexName()).
		Query(query).
		Script(elastic.NewScript(addValueToExemptRecordIdsScript).Params(params)).
		//ProceedOnVersionConflict(). // 忽略版本冲突
		Slices("auto").
		Do(context.Background())
	if err != nil {
		return 0, err
	}
	return updateByQuery.Updated, nil
}

// 使用updateByQuery去掉exempt_record_ids指定的key的values值
func (p *Assets) DeleteValueFromExemptRecordIdsScript(dimensionsID int64, ids []int64) (int64, error) {
	if len(ids) == 0 {
		return 0, nil
	}
	interfaceIds := make([]interface{}, 0)
	for _, id := range ids {
		interfaceIds = append(interfaceIds, id)
	}

	params := map[string]interface{}{
		"key":    dimensionsID,
		"values": ids,
	}
	// 执行 Update By Query 操作
	updateByQuery, err := p.GetClient().UpdateByQuery(p.IndexName()).
		Query(elastic.NewNestedQuery("exempt_record_ids",
			elastic.NewBoolQuery().
				Must(elastic.NewMatchQuery("exempt_record_ids.key", dimensionsID)).
				Must(elastic.NewTermsQuery("exempt_record_ids.values", interfaceIds...)),
		)).
		Script(elastic.NewScript(deleteValueFromExemptRecordIdsScript).Params(params)).
		//ProceedOnVersionConflict(). // 忽略版本冲突
		Slices("auto").
		Do(context.Background())

	if err != nil {
		return 0, err
	}
	return updateByQuery.Updated, nil
}

// UpdateStatusByDataSourceResponseAt 更新资产状态为离线
// 如果数据源最后一次响应时间小于给定阈值，则更新资产状态为离线
// 同时更新updated_at字段为当前时间
func (p *Assets) UpdateStatusByDataSourceResponseAt(ctx context.Context, threshold time.Duration) (int64, error) {
	now := time.Now()
	// 截止时间
	cutoffTime := now.Add(-threshold)
	// 范围查询
	rangeQuery := elastic.NewRangeQuery("data_source_response_at").Lt(cutoffTime.Format("2006-01-02 15:04:05"))

	scriptSource := "ctx._source.status = params.offline_status; ctx._source.updated_at = params.current_time_str;"

	scriptParams := map[string]interface{}{
		"offline_status":   StatusOffLine, // Assuming StatusOffLine = 2 is defined
		"current_time_str": now.Format("2006-01-02 15:04:05"),
	}
	script := elastic.NewScript(scriptSource).Params(scriptParams)

	updateByQueryService := p.GetClient().UpdateByQuery(p.IndexName()).
		Query(rangeQuery).
		Script(script).
		Slices("auto").      // 批次大小
		Conflicts("proceed") // 冲突处理方式

	resp, err := updateByQueryService.Do(ctx)
	if err != nil {
		logs.GetLogger().Errorf("更新资产状态失败: %v", err)
		return 0, err
	}

	if resp == nil {
		logs.GetLogger().Warnf("更新资产状态失败: UpdateByQuery返回空响应")
		return 0, errors.New("更新资产状态失败: UpdateByQuery返回空响应")
	}

	logs.GetLogger().Infof("更新资产状态成功: 更新数据量：%d次，截止时间：%s", resp.Updated, cutoffTime.Format(time.RFC3339))
	return resp.Updated, nil
}
