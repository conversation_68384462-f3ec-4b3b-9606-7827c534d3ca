package bk_cmdb

import (
	"context"
	"fmt"
	"fobrain/pkg/utils/handle_es_bulk_error"
	"strings"
	"time"

	"github.com/google/uuid"

	"github.com/olivere/elastic/v7"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"

	"fobrain/fobrain/app/services/node/bk_cmdb"
	"fobrain/fobrain/app/services/sync"
	"fobrain/fobrain/common/constant"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	assetses "fobrain/models/elastic/assets"
	bkcmdbes "fobrain/models/elastic/source/bk_cmdb"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
)

// AssetProcessMap 映射到资产表 TODO
var AssetProcessMap = map[string]string{
	"Ip":         "bk_host_innerip",
	"Os":         "bk_os_name",
	"Sn":         "bk_sn",
	"Mac":        "bk_mac",
	"Oper":       "operator",
	"Status":     "bk_state",
	"MemorySize": "bk_mem",
	"CpuMaker":   "bk_cpu_module",
	"CpuBrand":   "bk_cpu_architecture",
	"CpuCount":   "bk_cpu",
	"DiskSize":   "bk_disk",
}

// SyncBKCmdb 同步蓝鲸数据
func SyncBKCmdb(node *data_source.Node, taskInfo string) error {
	logs.GetSyncLogger().Infof("SyncBKCmdb start taskInfo:%s", taskInfo)
	assetTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())
	peopleTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncPeople)).Int())

	//获取SyncBKCmdbClient
	cli := bk_cmdb.NewBkCmdb()
	//设置SyncBKCmdbClient请求的节点
	if err := cli.SetNode(node.Id); err != nil {
		return err
	}

	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncAsset)).Int() == data_sync_task.SyncAsset {
		//更新资产任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(assetTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("BkCmdb syncAssets UpdateStatusById StatusDoing err:%s", err.Error())
		}
		go syncAssets(constant.DefaultPageZero, cli, taskInfo, node, 0)
	}

	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncPeople)).Int() == data_sync_task.SyncPeople {
		//更新人员任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(peopleTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("BkCmdb syncEmployees UpdateStatusById StatusDoing err:%s", err.Error())
		}
		go syncEmployees(constant.DefaultPage, cli, taskInfo, node)
	}

	return nil
}

// syncAssets 同步资产
func syncAssets(page int, cli *bk_cmdb.BkCmdb, taskInfo string, node *data_source.Node, successCount int) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())
	logs.GetSyncLogger().Infof("SyncBKCmdb syncAssets start taskId:%d,taskChildId:%d", taskId, taskChildId)

	//获取资产
	assets, err := cli.GetAssets(page, constant.DefaultSize)
	if err != nil {
		logs.GetSyncLogger().Errorf("BkCmdb syncAssets GetAssets err:%s", err)

		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, err.Error())
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("BkCmdb syncAssets GetAssets Task UpdateFailById err:%s", taskErr.Error())
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, err.Error())
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("BkCmdb syncAssets GetAssets childTask UpdateFailById err:%s", childTaskErr.Error())
		}
		return
	}

	total := gjson.Get(assets, "data.count").Int() //数据总量
	result := gjson.Get(assets, "data.info")

	//子任务更新需要同步的数据总量
	if page == constant.DefaultPageZero { //仅第一次同步 win+linux 总数
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataTotal(taskChildId, int(total))
		if err != nil {
			logs.GetSyncLogger().Errorf("BkCmdb syncAssets UpdateSyncDataTotal err:%s", err.Error())
		}
	}

	// 默认设置为3，智能判断
	networkType, err := node.GetNetworkType()
	if err != nil {
		logs.GetSyncLogger().Errorf("BkCmdb syncAssets GetNetworkType err:%s", err.Error())
	}

	personField := "name"
	if cli.GetConfig("person_field") != "" && cli.GetConfig("person_field") != nil {
		personField = cli.GetConfig("person_field").(string)
	}

	if result.Exists() && total > 0 {
		assetTaskBulkRequest := es.GetEsClient().Bulk()
		assetProcessBulkRequest := es.GetEsClient().Bulk()
		var num = 0
		// 遍历 _id 字段，并根据 id 添加新的字段数据
		result.ForEach(func(key, value gjson.Result) bool {
			logs.GetSyncLogger().Debugf("SyncBKCmdb syncAssets value info:%s", value)
			data, _ := sjson.Set(value.String(), "node_id", node.Id)
			data, _ = sjson.Set(data, "area_id", node.AreaId)
			data, _ = sjson.Delete(data, "create_time")
			//获取业务系统
			bizIds := make([]int64, 0)
			bizName := make([]string, 0)
			businessIds, err := cli.GetBusinessIdByHost(gjson.Get(value.String(), "bk_host_id").String())
			if err != nil {
				logs.GetSyncLogger().Errorf("BkCmdb syncAssets GetBusinessIdByHost err:%s", err.Error())
			}
			gjson.Get(string(businessIds), "data").ForEach(func(_, value gjson.Result) bool {
				bizIds = append(bizIds, value.Get("bk_biz_id").Int())
				return true // 继续遍历
			})
			if len(bizIds) > 0 {
				businesses, err := cli.GetBusinessNameById(bizIds...)
				if err != nil {
					logs.GetSyncLogger().Errorf("BkCmdb syncAssets GetBusinessNameById err:%s", err.Error())
				}

				gjson.Get(string(businesses), "data.info").ForEach(func(_, value gjson.Result) bool {
					bizName = append(bizName, value.Get("bk_biz_name").String())
					return true
				})
				data, _ = sjson.Set(data, "business_system", businesses)
				data, _ = sjson.Set(data, "bk_biz_id", businessIds)
			}

			//写入任务库
			assetTask := bkcmdbes.NewBKCmdbTaskAssetsModel()
			assetTaskId := strings.ReplaceAll(uuid.New().String(), "-", "")
			data, _ = sjson.Set(data, "task_id", taskId)
			data, _ = sjson.Set(data, "child_task_id", taskChildId)
			assetTaskReq := elastic.NewBulkIndexRequest().Index(assetTask.IndexName()).Id(assetTaskId).Doc(data)
			assetTaskBulkRequest = assetTaskBulkRequest.Add(assetTaskReq)

			//写入过程库
			assetProcess := assetses.NewProcessAssetsModel()

			assetProcessId := strings.ReplaceAll(uuid.New().String(), "-", "")
			assetProcess.CreatedAt = localtime.NewLocalTime(time.Now())
			assetProcess.UpdatedAt = localtime.NewLocalTime(time.Now())
			assetProcess.Id = assetProcessId
			assetProcess.TaskId = taskId
			assetProcess.ChildTaskId = taskChildId
			areaId, _ := node.GetAreaByIp(gjson.Get(value.String(), "bk_host_innerip").String())
			assetProcess.Area = int(areaId)
			assetProcess.Node = node.Id
			assetProcess.Source = node.SourceId
			assetProcess.TaskId = taskId
			assetProcess.ChildTaskId = taskChildId
			assetProcess.Ip = gjson.Get(value.String(), "bk_host_innerip").String()
			assetProcess.NetworkType = networkType
			assetProcess.Os = gjson.Get(value.String(), "bk_os_name").String()
			assetProcess.Sn = gjson.Get(value.String(), "bk_sn").String()
			assetProcess.Mac = gjson.Get(value.String(), "bk_mac").String()
			assetProcess.Oper = gjson.Get(value.String(), "operator").String()

			if len(bizName) > 0 {
				assetProcess.BusinessSystem = bizName[0]
			}
			assetProcess.Status = 1
			if int(gjson.Get(value.String(), "bk_state").Int()) != 1 {
				assetProcess.Status = 2
			}

			assetProcess.MemorySize = gjson.Get(value.String(), "bk_mem").String()
			assetProcess.CpuMaker = gjson.Get(value.String(), "bk_cpu_module").String()
			assetProcess.CpuBrand = gjson.Get(value.String(), "bk_cpu_architecture").String()
			assetProcess.CpuCount = int(gjson.Get(value.String(), "bk_cpu").Int())
			assetProcess.DiskSize = int(gjson.Get(value.String(), "bk_disk").Int())
			assetProcess.TaskDataId = assetTaskId
			assetProcess.PersonField = personField

			assetProcessReq := elastic.NewBulkIndexRequest().Index(assetProcess.IndexName()).Id(assetProcessId).Doc(assetProcess)
			assetProcessBulkRequest = assetProcessBulkRequest.Add(assetProcessReq)

			num = num + 1

			return true
		})
		if num > 0 {
			assetProcessBulkReq, err := assetProcessBulkRequest.Refresh("true").Do(context.Background())
			if err != nil || assetProcessBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(assetses.NewProcessAssetsModel().IndexName(), err, assetProcessBulkReq)

				logs.GetSyncLogger().Errorf("BkCmdb syncAssets assetProcessBulkRequest.Do err:%s", errString)

				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("BkCmdb syncAssets assetProcessBulkRequest.Do task UpdateFailById err:%s", taskErr)
				}

				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("BkCmdb syncAssets assetProcessBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
				}
				return
			}

			assetTaskBulkReq, err := assetTaskBulkRequest.Do(context.Background())
			if err != nil || assetTaskBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(bkcmdbes.NewBKCmdbTaskAssetsModel().IndexName(), err, assetTaskBulkReq)
				logs.GetSyncLogger().Errorf("BkCmdb syncAssets assetTaskResponse.Do err:%s", errString)

				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("BkCmdb syncAssets assetTaskBulkRequest.Do task UpdateFailById err:%s", taskErr)
				}

				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("BkCmdb syncAssets assetTaskBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
				}
			}

			if err == nil {
				//更新成功数量
				err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(taskChildId, len(assetTaskBulkReq.Items))
				if err != nil {
					logs.GetSyncLogger().Errorf("BkCmdb syncAssets UpdateSyncDataSuccessTotal err:%s", err.Error())
				}
			}
		}
	}
	page = page + 1
	if page*constant.DefaultSize <= int(total) && total > 0 {
		syncAssets(page, cli, taskInfo, node, successCount)
	} else {
		logs.GetSyncLogger().Infof("SyncBKCmdb syncAssets end taskId:%d,taskChildId:%d", taskId, taskChildId)
		//更新子任务完成状态
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(taskChildId, data_sync_child_task.StatusSuccess)
		if err != nil {
			logs.GetSyncLogger().Errorf("BkCmdb syncAssets UpdateStatusById StatusSuccess err:%s", err.Error())
		}
	}
}

// syncEmployees 同步人员
func syncEmployees(page int, cli *bk_cmdb.BkCmdb, taskInfo string, node *data_source.Node) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncPeople)).Int())
	logs.GetSyncLogger().Infof("SyncBKCmdb syncEmployees start taskId:%d,taskChildId:%d", taskId, taskChildId)

	//获取人员
	employees, err := cli.GetEmployees(page, constant.DefaultSize)
	if err != nil {
		logs.GetSyncLogger().Errorf("BkCmdb syncEmployees GetLinuxEmployees err:%s", err)

		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, err.Error())
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("BkCmdb syncEmployees GetLinuxEmployees UpdateFailById Task err:%s", taskErr.Error())
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, err.Error())
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("BkCmdb syncEmployees GetLinuxEmployees UpdateFailById childTask err:%s", childTaskErr.Error())
		}
		return
	}

	total := gjson.Get(employees, "data.count").Int() //数据总量
	result := gjson.Get(employees, "data.results")

	//子任务更新需要同步的数据总量
	if page == constant.DefaultPage { //仅第一次同步
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateById(taskChildId, data_sync_child_task.DataSyncChildTask{
			SyncDataTotal: int(total),
		})

		if err != nil {
			logs.GetSyncLogger().Errorf("BkCmdb syncEmployees UpdateById SyncDataTotal err:%s", err.Error())
		}
	}

	// 获取unique_field,默认username+telephone
	uniqueField := cli.GetConfig("unique_field")
	if uniqueField == nil || uniqueField.(string) == "" {
		uniqueField = "username+telephone"
	}
	// client := redis.GetRedisClient()
	// rq := &queue.RedisQueue{Client: client}
	if result.Exists() && total > 0 {
		// var peopleIds []map[string]interface{}
		employeeTaskBulkRequest := es.GetEsClient().Bulk()
		employeeProcessBulkRequest := es.GetEsClient().Bulk()
		// 遍历 _id 字段，并根据 id 添加新的字段数据
		var num = 0
		result.ForEach(func(key, value gjson.Result) bool {
			logs.GetSyncLogger().Debugf("SyncBKCmdb syncEmployees value info:%s", value)
			data, _ := sjson.Set(value.String(), "node_id", node.Id)
			data, _ = sjson.Set(data, "area_id", node.AreaId)

			employeeId := strings.ReplaceAll(uuid.New().String(), "-", "")
			//写入任务库
			employeeTask := bkcmdbes.NewBKCmdbTaskEmployeesModel()
			employeeTaskId := fmt.Sprintf("%d_%s", taskId, employeeId)
			data, _ = sjson.Set(data, "task_id", taskId)
			assetTaskReq := elastic.NewBulkIndexRequest().Index(employeeTask.IndexName()).Id(employeeTaskId).Doc(data)
			employeeTaskBulkRequest = employeeTaskBulkRequest.Add(assetTaskReq)

			//写入过程库
			staffProcess := staff.NewProcessStaffModel()
			staffProcess.Id = strings.ReplaceAll(uuid.New().String(), "-", "")
			staffProcess.UniqueKey, _ = sync.GetUniqueKey("bk_cmdb", uniqueField.(string), data)
			staffProcess.TaskDataId = employeeTaskId
			staffProcess.TaskId = taskId
			staffProcess.ChildTaskId = taskChildId
			staffProcess.Area = int(node.AreaId)
			staffProcess.Node = node.Id
			staffProcess.Source = node.SourceId
			staffProcess.Name = gjson.Get(value.String(), "username").String()
			staffProcess.Mobile = gjson.Get(value.String(), "telephone").String()
			staffProcess.Email = gjson.Get(value.String(), "email").String()
			staffProcess.WorkNumber = func() string {
				workNumber := gjson.Get(value.String(), "extras.emp_code")
				if workNumber.Exists() {
					return workNumber.String()
				}
				return ""
			}()
			department := ""
			gjson.Get(value.String(), "departments").ForEach(func(k, v gjson.Result) bool {
				department += gjson.Get(v.String(), "name").String() + "/"
				return true
			})
			staffProcess.Department = strings.TrimSuffix(department, "/")
			staffProcess.Status = 1
			if gjson.Get(data, "status").String() != "NORMAL" {
				staffProcess.Status = 2
			}
			staffProcess.CreatedAt = localtime.NewLocalTime(time.Now())
			staffProcess.UpdatedAt = localtime.NewLocalTime(time.Now())
			employeeProcessReq := elastic.NewBulkIndexRequest().Index(staffProcess.IndexName()).Id(staffProcess.Id).Doc(staffProcess)
			employeeProcessBulkRequest = employeeProcessBulkRequest.Add(employeeProcessReq)

			num = num + 1
			return true
		})
		if num > 0 {
			employeeProcessBulkReq, err := employeeProcessBulkRequest.Refresh("true").Do(context.Background())
			if err != nil || employeeProcessBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(staff.NewProcessStaffModel().IndexName(), err, employeeProcessBulkReq)
				logs.GetSyncLogger().Errorf("BkCmdb syncEmployees employeeProcessBulkRequest.Do err:%s", errString)

				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("BkCmdb syncEmployees employeeProcessBulkRequest.Do task UpdateFailById err:%s", taskErr)
				}

				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("BkCmdb syncEmployees employeeProcessBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
				}
				return
			}

			employeeTaskBulkReq, err := employeeTaskBulkRequest.Do(context.Background())
			if err != nil || employeeTaskBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(bkcmdbes.NewBKCmdbTaskEmployeesModel().IndexName(), err, employeeTaskBulkReq)
				logs.GetSyncLogger().Errorf("BkCmdb syncEmployees employeeTaskBulkRequest.Do err:%s", errString)

				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("BkCmdb syncEmployees employeeTaskBulkRequest.Do task UpdateFailById err:%s", taskErr)
				}

				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("BkCmdb syncEmployees employeeTaskBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
				}
				return
			}
			if err == nil {
				//更新成功数量
				err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(taskChildId, len(employeeTaskBulkReq.Items))
				if err != nil {
					logs.GetSyncLogger().Errorf("BkCmdb syncEmployees UpdateSyncDataSuccessTotal err:%s", err.Error())
				}
				// logs.GetSyncLogger().Infof("BkCmdb syncEmployees peopleIds info:%v", peopleIds)

				// // 发送任务开始标识
				// rq.Push(cfg.LoadQueue().PersonMergeQueue, []map[string]interface{}{{"task_id": taskChildId, "task_type": "sync", "is_start": true}})
				// successCount := rq.Push(cfg.LoadQueue().PersonMergeQueue, peopleIds)
				// logs.GetSyncLogger().Infof("BkCmdb syncEmployees queue push successCount:%d, expectedCount:%d", successCount, len(peopleIds))
			}
		}

	}

	if page*constant.DefaultSize <= int(total) && total > 0 {
		syncEmployees(page+1, cli, taskInfo, node)
	} else {
		logs.GetSyncLogger().Infof("SyncBKCmdb syncEmployees end taskId:%d,taskChildId:%d", taskId, taskChildId)
		// // 发送任务结束标识
		// rq.Push(cfg.LoadQueue().PersonMergeQueue, []map[string]interface{}{{"task_id": taskChildId, "task_type": "sync", "total": total, "is_end": true}})
		//更新子任务完成状态
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(taskChildId, data_sync_child_task.StatusSuccess)
		if err != nil {
			logs.GetSyncLogger().Errorf("BkCmdb syncEmployees UpdateStatusById StatusSuccess err:%s", err.Error())
		}
	}
}
