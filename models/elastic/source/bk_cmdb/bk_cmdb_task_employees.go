package bk_cmdb

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/olivere/elastic/v7"
)

type (
	BKCmdbTaskEmployees struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`      //Task+NodeID+区域ID+id 作为唯一值去更新，用作记录每次任务拉取数据
		TaskId        string          `json:"task_id"` //
		ChildTaskId   string          `json:"child_task_id"`
		CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
		UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
	}
)

// IndexName 索引名
func (b *BKCmdbTaskEmployees) IndexName() string {
	return "bk_cmdb_task_employees"
}

func (b *BKCmdbTaskEmployees) String() string {
	if b == nil {
		return ""
	}
	return ""
}

func NewBKCmdbTaskEmployeesModel() *BKCmdbTaskEmployees {
	return &BKCmdbTaskEmployees{}
}
func (b *BKCmdbTaskEmployees) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("departments.name.keyword").
		Field("departments.full_name.keyword").
		Field("email.keyword").
		Field("qq.keyword").
		Field("telephone.keyword").
		Field("username.keyword").
		Field("display_name.keyword")

	return field
}

func (b *BKCmdbTaskEmployees) GetPeopleListFields() []map[string]string {
	return []map[string]string{
		{"name": "用户名", "key": "username"},
		{"name": "全名", "key": "display_name"},
		{"name": "手机号", "key": "telephone"},
		{"name": "邮箱", "key": "email"},
		{"name": "账户状态", "key": "status"},
		{"name": "组织", "key": "departments", "show_key": "full_name"},
		{"name": "职位", "key": "position"},
		{"name": "工号", "key": "extras", "show_key": "emp_code"},
	}
}

func (b *BKCmdbTaskEmployees) GetPeopleListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "用户名", "key": "username"},
		{"name": "全名", "key": "display_name"},
		{"name": "手机号", "key": "telephone"},
		{"name": "邮箱", "key": "email"},
		{"name": "账户状态", "key": "status"},
		{"name": "组织", "key": "departments", "show_key": "full_name"},
		{"name": "职位", "key": "position"},
		{"name": "工号", "key": "extras", "show_key": "emp_code"},
	}
}
