package changting_waf

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestChangtingWafTaskAssets_IndexName(t *testing.T) {
	assert.Equal(t, "changting_waf_task_assets", NewChangtingWafTaskAssetsModel().IndexName())
	assert.Equal(t, "", NewChangtingWafTaskAssetsModel().String())
}
func TestChangtingWafAssets_GetCTWafAssetListFields(t *testing.T) {
	threats := NewChangtingWafTaskAssetsModel()
	fields := threats.GetCTWafAssetListFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "服务器地址", "key": "ip_port", "show_value": "addrs", "all_key": "addrs"},
		{"name": "状态", "key": "is_enabled", "all_key": "is_enabled"},
		{"name": "站点名称", "key": "name", "all_key": "name"},
		{"name": "域名", "key": "server_names", "all_key": "server_names.*"},
		{"name": "备注", "key": "remark", "all_key": "remark"},
	}

	assert.Equal(t, expectedFields, fields, "字段不匹配")
}

func TestChangtingWafAssets_GetCTWafAssetListDetailFields(t *testing.T) {
	threats := NewChangtingWafTaskAssetsModel()
	fields := threats.GetCTWafAssetListDetailFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "服务器地址", "key": "ip_port", "show_value": "addrs", "all_key": "addrs"},
		{"name": "状态", "key": "is_enabled", "all_key": "is_enabled"},
		{"name": "站点名称", "key": "name", "all_key": "name"},
		{"name": "域名", "key": "server_names", "all_key": "server_names.*"},
		{"name": "备注", "key": "remark", "all_key": "remark"},
		{"name": "创建时间", "key": "create_time", "all_key": "create_time"},
	}

	assert.Equal(t, expectedFields, fields, "字段不匹配")
}
