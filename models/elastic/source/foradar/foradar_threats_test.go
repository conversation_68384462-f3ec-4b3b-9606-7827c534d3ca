package foradar

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestThreatIndexName(t *testing.T) {
	indexName := NewForadarTaskThreatsModel().IndexName()
	assert.Equal(t, "foradar_task_threats", indexName)
}

func TestThreatString(t *testing.T) {
	str := NewForadarTaskThreatsModel().String()
	assert.Equal(t, "", str)
}

func TestGetFileImportThreatListFields(t *testing.T) {
	fields := NewForadarTaskThreatsModel().GetThreatListFields()

	expectedFields := []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "漏洞名称", "key": "common_title"},
		//{"name": "漏洞分类", "key": "is_poc_scan"},
		{"name": "CVE编号", "key": "cve_id"},
		{"name": "漏洞类型", "key": "vul_type"},
		{"name": "漏洞等级", "key": "level"},
		{"name": "漏洞地址", "key": "url"},
		{"name": "发现时间", "key": "created_at"},
	}

	assert.Equal(t, expectedFields, fields)
}

func TestGetFileImportThreatListDetailFields(t *testing.T) {
	fields := NewForadarTaskThreatsModel().GetThreatListDetailFields()

	expectedFields := []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "漏洞名称", "key": "common_title"},
		//{"name": "漏洞分类", "key": "is_poc_scan"},
		{"name": "CVE编号", "key": "cve_id"},
		{"name": "漏洞类型", "key": "vul_type"},
		{"name": "漏洞等级", "key": "level"},
		{"name": "漏洞地址", "key": "url"},
		{"name": "发现时间", "key": "created_at"},
	}

	assert.Equal(t, expectedFields, fields)
}
