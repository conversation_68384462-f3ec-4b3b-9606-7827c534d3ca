package bk_cmdb

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestBKCmdbTaskAssets_IndexName(t *testing.T) {
	assert.Equal(t, "bk_cmdb_task_assets", NewBKCmdbTaskAssetsModel().IndexName())
	assert.Equal(t, "", NewBKCmdbTaskAssetsModel().String())
}

func TestGetBKCmdbAssetListFields(t *testing.T) {
	fields := NewBKCmdbTaskAssetsModel().GetAssetListFields()

	expectedFields := []map[string]string{
		{"name": "主机ID", "key": "bk_host_id"},
		{"name": "内网IP地址", "key": "bk_host_innerip"},
		{"name": "云区域", "key": "bk_cloud_id"},
		{"name": "主要维护人", "key": "operator"},
		{"name": "操作系统", "key": "bk_os_name"},
		{"name": "版本号", "key": "bk_os_version"},
		{"name": "设备SN", "key": "bk_sn"},
		{"name": "CPU厂商", "key": "bk_cpu_module"},
		{"name": "CPU品牌", "key": "bk_cpu_architecture"},
		{"name": "CPU核心数", "key": "bk_cpu"},
	}

	assert.Equal(t, expectedFields, fields)
}

func TestGetBKCmdbAssetListDetailFields(t *testing.T) {
	fields := NewBKCmdbTaskAssetsModel().GetAssetListDetailFields()

	expectedFields := []map[string]string{
		{"name": "主机ID", "key": "bk_host_id"},
		{"name": "内网IP地址", "key": "bk_host_innerip"},
		{"name": "云区域", "key": "bk_cloud_id"},
		{"name": "主要维护人", "key": "operator"},
		{"name": "操作系统", "key": "bk_os_name"},
		{"name": "版本号", "key": "bk_os_version"},
		{"name": "设备SN", "key": "bk_sn"},
		{"name": "CPU厂商", "key": "bk_cpu_module"},
		{"name": "CPU品牌", "key": "bk_cpu_architecture"},
		{"name": "CPU核心数", "key": "bk_cpu"},
		{"name": "操作系统类型", "key": "bk_os_type"}, //1:Linux;2:Windows;3:AIX
		{"name": "内网MAC地址", "key": "bk_mac"},
		{"name": "操作系统位数", "key": "bk_os_bit"},
	}

	assert.Equal(t, expectedFields, fields)
}
