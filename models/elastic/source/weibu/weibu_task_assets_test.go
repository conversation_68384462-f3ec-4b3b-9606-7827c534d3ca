package weibu

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestWeibuTaskAssets_IndexName(t *testing.T) {
	assert.Equal(t, "weibu_task_assets", NewWeibuTaskAssetsModel().IndexName())
	assert.Equal(t, "", NewWeibuTaskAssetsModel().String())
}

func TestWeibuTaskAssets_GetWeibuAssetListFields(t *testing.T) {
	m := NewWeibuTaskAssetsModel()
	fields := m.GetWeibuAssetListFields()

	assert.Equal(t, fields, []map[string]string{
		{"name": "告警主机", "key": "machine", "all_key": "machine"},
		{"name": "域名", "key": "machine_name", "all_key": "machine_name.*"},
		{"name": "服务", "key": "service", "show_value": "service_port", "all_key": "service_port.*.service"},
		{"name": "传输层协议", "key": "proto", "show_value": "service_port", "all_key": "service_port.*.proto"},
		{"name": "应用层协议", "key": "app_proto", "show_value": "service_port", "all_key": "service_port.*.app_proto"},
		{"name": "关联端口", "key": "port", "show_value": "service_port", "all_key": "service_port.*.port"},
	})
}

func TestWeibuTaskAssets_GetWeibuAssetListDetailFields(t *testing.T) {
	m := NewWeibuTaskAssetsModel()
	fields := m.GetWeibuAssetListDetailFields()

	assert.Equal(t, fields, []map[string]string{
		{"name": "告警主机", "key": "machine", "all_key": "machine"},
		{"name": "域名", "key": "machine_name", "all_key": "machine_name.*"},
		{"name": "服务", "key": "service", "show_value": "service_port", "all_key": "service_port.*.service"},
		{"name": "传输层协议", "key": "proto", "show_value": "service_port", "all_key": "service_port.*.proto"},
		{"name": "应用层协议", "key": "app_proto", "show_value": "service_port", "all_key": "service_port.*.app_proto"},
		{"name": "关联端口", "key": "port", "show_value": "service_port", "all_key": "service_port.*.port"},
		{"name": "服务名", "key": "service_name", "show_value": "service_port", "all_key": "service_port.*.service_name"},
		{"name": "关联域名", "key": "machine_name", "show_value": "service_port", "all_key": "service_port.*.machine_name.*"},
	})
}
