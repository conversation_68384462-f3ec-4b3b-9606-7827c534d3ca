package changting_waf

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/olivere/elastic/v7"
)

type (
	ChangtingWafTaskAssets struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`      // NODE+区域+IP 作为唯一值去更新，用作数据对比
		TaskId        string          `json:"task_id"` //
		ChildTaskId   string          `json:"child_task_id"`
		CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
		UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
	}
)

// IndexName 索引名
func (f *ChangtingWafTaskAssets) IndexName() string {
	return "changting_waf_task_assets"
}

func (f *ChangtingWafTaskAssets) String() string {
	if f == nil {
		return ""
	}
	return ""
}

func NewChangtingWafTaskAssetsModel() *ChangtingWafTaskAssets {
	return &ChangtingWafTaskAssets{}
}

func (f *ChangtingWafTaskAssets) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("name.keyword").
		Field("remark.keyword").
		Field("server_names.keyword").
		Field("addrs.ip_port.keyword")

	return field
}

func (f *ChangtingWafTaskAssets) GetCTWafAssetListFields() []map[string]string {
	return []map[string]string{
		{"name": "服务器地址", "key": "ip_port", "show_value": "addrs", "all_key": "addrs"},
		{"name": "状态", "key": "is_enabled", "all_key": "is_enabled"},
		{"name": "站点名称", "key": "name", "all_key": "name"},
		{"name": "域名", "key": "server_names", "all_key": "server_names.*"},
		{"name": "备注", "key": "remark", "all_key": "remark"},
	}
}

func (f *ChangtingWafTaskAssets) GetCTWafAssetListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "服务器地址", "key": "ip_port", "show_value": "addrs", "all_key": "addrs"},
		{"name": "状态", "key": "is_enabled", "all_key": "is_enabled"},
		{"name": "站点名称", "key": "name", "all_key": "name"},
		{"name": "域名", "key": "server_names", "all_key": "server_names.*"},
		{"name": "备注", "key": "remark", "all_key": "remark"},
		{"name": "创建时间", "key": "create_time", "all_key": "create_time"},
	}
}
