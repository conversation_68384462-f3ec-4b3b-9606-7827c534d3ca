package rizhiyi

import (
	"fmt"
	"fobrain/initialize/es"
	"fobrain/pkg/utils"

	"git.gobies.org/caasm/fobrain-components/utils/localtime"
	"github.com/olivere/elastic/v7"
)

func NewTaskAssetsModel() *TaskAssets {
	return &TaskAssets{}
}

type TaskAssets struct {
	*es.BaseModel `json:"-"`
	Id            string          `json:"id"`
	CreatedAt     *localtime.Time `json:"created_at"`
	UpdatedAt     *localtime.Time `json:"updated_at"`
}

func (a *TaskAssets) IndexName() string {
	return "rizhiyi_task_assets"
}

func (a *TaskAssets) String() string {
	if a == nil {
		return ""
	}
	return ""
}

func (a *TaskAssets) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("host_ip_address.keyword").
		Field("host_name.keyword").
		Field("device_type.keyword").
		Field("device_model.keyword").
		Field("operating_system.keyword").
		Field("company_or_brand.keyword").
		Field("network_location.keyword").
		Field("operation_and_maintenance_angle_a.keyword").
		Field("email.keyword").
		Field("business_team.keyword").
		Field("system_name.keyword").
		Field("generatorroom.keyword").
		Field("device_status.keyword").
		Field("system_status.keyword").
		Field("system_level.keyword")

	return field
}

func (a *TaskAssets) GetAssetListFields() []map[string]string {
	return []map[string]string{
		{"name": "设备类型", "key": "device_type", "all_key": "device_type"},
		{"name": "主机名", "key": "host_name", "all_key": "host_name"},
		{"name": "设备型号", "key": "device_model", "all_key": "device_model"},
		{"name": "IP地址", "key": "host_ip_address", "all_key": "host_ip_address"},
		{"name": "操作系统", "key": "operating_system", "all_key": "operating_system"},
		{"name": "公司或设备品牌", "key": "company_or_brand", "all_key": "company_or_brand"},
		{"name": "所在网络", "key": "network_location", "all_key": "network_location"},
		{"name": "运维 A 角", "key": "operation_and_maintenance_angle_a", "all_key": "operation_and_maintenance_angle_a"},
		{"name": "邮箱地址", "key": "email", "all_key": "email"},
		{"name": "业务团队", "key": "business_team", "all_key": "business_team"},
		{"name": "业务系统名称", "key": "system_name", "all_key": "system_name"},
		{"name": "机房", "key": "generatorroom", "all_key": "generatorroom"},
		{"name": "设备状态", "key": "device_status", "all_key": "device_status"},
		{"name": "业务系统状态", "key": "system_status", "all_key": "system_status"},
		{"name": "业务系统等级", "key": "system_level", "all_key": "system_level"},
	}
}

func (a *TaskAssets) GetAssetListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "设备类型", "key": "device_type", "all_key": "device_type"},
		{"name": "主机名", "key": "host_name", "all_key": "host_name"},
		{"name": "设备型号", "key": "device_model", "all_key": "device_model"},
		{"name": "IP地址", "key": "host_ip_address", "all_key": "host_ip_address"},
		{"name": "操作系统", "key": "operating_system", "all_key": "operating_system"},
		{"name": "公司或设备品牌", "key": "company_or_brand", "all_key": "company_or_brand"},
		{"name": "所在网络", "key": "network_location", "all_key": "network_location"},
		{"name": "运维 A 角", "key": "operation_and_maintenance_angle_a", "all_key": "operation_and_maintenance_angle_a"},
		{"name": "邮箱地址", "key": "email", "all_key": "email"},
		{"name": "业务团队", "key": "business_team", "all_key": "business_team"},
		{"name": "业务系统名称", "key": "system_name", "all_key": "system_name"},
		{"name": "机房", "key": "generatorroom", "all_key": "generatorroom"},
		{"name": "设备状态", "key": "device_status", "all_key": "device_status"},
		{"name": "业务系统状态", "key": "system_status", "all_key": "system_status"},
		{"name": "业务系统等级", "key": "system_level", "all_key": "system_level"},
	}
}
