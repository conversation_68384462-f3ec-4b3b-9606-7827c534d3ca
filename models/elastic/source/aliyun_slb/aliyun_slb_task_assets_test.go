package aliyun_slb

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestTaskAssets_IndexName(t *testing.T) {
	assert.Equal(t, "aliyun_slb_task_assets", NewTaskAssetsModel().IndexName())
	assert.Equal(t, "", NewTaskAssetsModel().String())
}

func TestAssets_GetAssetListFields(t *testing.T) {
	fields := NewTaskAssetsModel().GetAssetListFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "地址", "key": "Address", "all_key": "Address"},
		{"name": "地址类型", "key": "AddressType", "all_key": "AddressType"},
		{"name": "网络类型", "key": "NetworkType", "all_key": "NetworkType"},
		{"name": "实例ID", "key": "LoadBalancerId", "all_key": "LoadBalancerId"},
		{"name": "地域ID", "key": "RegionId", "all_key": "RegionId"},
		{"name": "实例的名称", "key": "LoadBalancerName", "all_key": "LoadBalancerName"},
		{"name": "创建时间", "key": "CreateTime", "all_key": "CreateTime"},
	}
	assert.Equal(t, expectedFields, fields, "字段不匹配")
}

func TestAssets_GetAssetListDetailFields(t *testing.T) {
	fields := NewTaskAssetsModel().GetAssetListDetailFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "地址", "key": "Address", "all_key": "Address"},
		{"name": "地址类型", "key": "AddressType", "all_key": "AddressType"},
		{"name": "网络类型", "key": "NetworkType", "all_key": "NetworkType"},
		{"name": "实例ID", "key": "LoadBalancerId", "all_key": "LoadBalancerId"},
		{"name": "地域ID", "key": "RegionId", "all_key": "RegionId"},
		{"name": "实例的名称", "key": "LoadBalancerName", "all_key": "LoadBalancerName"},
		{"name": "创建时间", "key": "CreateTime", "all_key": "CreateTime"},
	}
	assert.Equal(t, expectedFields, fields, "字段不匹配")
}
