{"settings": {"number_of_shards": 1, "number_of_replicas": 0, "blocks": {"read_only_allow_delete": "false"}, "max_result_window": "*********"}, "mappings": {"properties": {"accounts": {"type": "nested", "properties": {"centralization_account": {"type": "keyword"}, "id": {"type": "keyword"}, "last_login_at": {"type": "date", "format": "YYYY-MM-dd HH:mm:ss"}, "name": {"type": "keyword"}, "permission": {"type": "keyword"}, "shell": {"type": "keyword"}, "status": {"type": "long"}}}, "area": {"type": "integer"}, "asset_task_id": {"type": "keyword"}, "business_owner": {"type": "keyword"}, "business_system": {"type": "keyword"}, "child_task_id": {"type": "integer"}, "cpu_brand": {"type": "keyword"}, "cpu_count": {"type": "integer"}, "cpu_maker": {"type": "keyword"}, "created_at": {"type": "date", "format": "YYYY-MM-dd HH:mm:ss"}, "device_id": {"type": "keyword"}, "device_name": {"type": "keyword"}, "disk_count": {"type": "integer"}, "disk_size": {"type": "long"}, "disk_usage_rate": {"type": "keyword"}, "eth_name": {"type": "keyword"}, "hostname": {"type": "keyword"}, "id": {"type": "keyword"}, "ip": {"type": "ip", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ip_segment": {"type": "keyword"}, "kernel": {"type": "keyword"}, "last_response_at": {"type": "date", "format": "YYYY-MM-dd HH:mm:ss"}, "load_average": {"type": "keyword"}, "mac": {"type": "keyword"}, "machine_room": {"type": "keyword"}, "maker": {"type": "keyword"}, "map_ip": {"type": "ip", "ignore_malformed": true, "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "memory_size": {"type": "keyword"}, "memory_usage_rate": {"type": "keyword"}, "model": {"type": "keyword"}, "network_type": {"type": "integer"}, "node": {"type": "integer"}, "oper": {"type": "keyword"}, "os": {"type": "keyword"}, "ports": {"type": "nested", "properties": {"domain": {"type": "text", "fields": {"keyword": {"type": "keyword"}}}, "port": {"type": "long"}, "protocol": {"type": "keyword"}, "status": {"type": "long"}, "title": {"type": "text", "fields": {"keyword": {"type": "keyword"}}}, "url": {"type": "text", "fields": {"keyword": {"type": "keyword"}}}}}, "product": {"type": "keyword"}, "rule_infos": {"type": "nested", "properties": {"first_tag": {"type": "keyword"}, "product": {"type": "keyword"}, "second_tag": {"type": "keyword"}}}, "sn": {"type": "keyword"}, "source": {"type": "integer"}, "status": {"type": "integer"}, "task_id": {"type": "integer"}, "updated_at": {"type": "date", "format": "YYYY-MM-dd HH:mm:ss"}, "jar_package_info": {"type": "nested", "properties": {"jar_package_info": {"type": "nested", "properties": {"k": {"type": "keyword"}, "v": {"type": "keyword"}}}}}}}}