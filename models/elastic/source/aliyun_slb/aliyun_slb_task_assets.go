package aliyun_slb

import (
	"fmt"
	"fobrain/initialize/es"
	"git.gobies.org/caasm/fobrain-components/utils"
	"git.gobies.org/caasm/fobrain-components/utils/localtime"
	"github.com/olivere/elastic/v7"
)

type (
	TaskAssets struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`
		CreatedAt     *localtime.Time `json:"created_at"`
		UpdatedAt     *localtime.Time `json:"updated_at"`
	}
)

// IndexName 索引名
func (t *TaskAssets) IndexName() string {
	return "aliyun_slb_task_assets"
}

func (t *TaskAssets) String() string {
	if t == nil {
		return ""
	}
	return ""
}

func NewTaskAssetsModel() *TaskAssets {
	return &TaskAssets{}
}

func (asset *TaskAssets) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("Address").
		Field("LoadBalancerName")

	return field
}

func (asset *TaskAssets) GetAssetListFields() []map[string]string {
	return []map[string]string{
		{"name": "地址", "key": "Address", "all_key": "Address"},
		{"name": "地址类型", "key": "AddressType", "all_key": "AddressType"},
		{"name": "网络类型", "key": "NetworkType", "all_key": "NetworkType"},
		{"name": "实例ID", "key": "LoadBalancerId", "all_key": "LoadBalancerId"},
		{"name": "地域ID", "key": "RegionId", "all_key": "RegionId"},
		{"name": "实例的名称", "key": "LoadBalancerName", "all_key": "LoadBalancerName"},
		{"name": "创建时间", "key": "CreateTime", "all_key": "CreateTime"},
	}
}

func (asset *TaskAssets) GetAssetListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "地址", "key": "Address", "all_key": "Address"},
		{"name": "地址类型", "key": "AddressType", "all_key": "AddressType"},
		{"name": "网络类型", "key": "NetworkType", "all_key": "NetworkType"},
		{"name": "实例ID", "key": "LoadBalancerId", "all_key": "LoadBalancerId"},
		{"name": "地域ID", "key": "RegionId", "all_key": "RegionId"},
		{"name": "实例的名称", "key": "LoadBalancerName", "all_key": "LoadBalancerName"},
		{"name": "创建时间", "key": "CreateTime", "all_key": "CreateTime"},
	}
}
