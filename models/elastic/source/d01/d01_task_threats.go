package d01

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/olivere/elastic/v7"
)

type (
	TaskThreats struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`      // TaskId+NODE+区域+IP+Port 作为唯一值去更新，用作记录每次任务拉取数据
		TaskId        string          `json:"task_id"` //
		ChildTaskId   string          `json:"child_task_id"`
		CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
		UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
	}
)

// IndexName 索引名
func (f *TaskThreats) IndexName() string {
	return "d01_task_threats"
}

func (f *TaskThreats) String() string {
	if f == nil {
		return ""
	}
	return ""
}

func NewTaskThreatsModel() *TaskThreats {
	return &TaskThreats{}
}

func (f *TaskThreats) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("ip").
		Field("common_title.keyword").
		Field("vulType.keyword").
		Field("province.keyword")

	return field
}

func (f *TaskThreats) GetThreatListFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "漏洞名称", "key": "common_title"},
		{"name": "CVE编号", "key": "cveId"},
		{"name": "漏洞类型", "key": "vulType"},
		{"name": "漏洞等级", "key": "level"},
		{"name": "漏洞地址", "key": "url"},
		{"name": "发现时间", "key": "createtime"},
		{"name": "通报时间", "key": "notice_time"},
	}
}

func (f *TaskThreats) GetThreatListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "漏洞名称", "key": "common_title"},
		{"name": "CVE编号", "key": "cveId"},
		{"name": "漏洞类型", "key": "vulType"},
		{"name": "漏洞等级", "key": "level"},
		{"name": "漏洞地址", "key": "url"},
		{"name": "发现时间", "key": "createtime"},
		{"name": "通报时间", "key": "notice_time"},
		{"name": "上次扫描时间", "key": "lastupdatetime"},
		{"name": "备注信息", "key": "name"},
		{"name": "MAC地址", "key": "mac"},
		{"name": "地理位置", "key": "city"},
		{"name": "资产等级", "key": "asset_level"},
		{"name": "管理单元", "key": "company"},
	}
}
