package net_mapping

import "fobrain/fobrain/common/request"

// GetMappingGplotRequest 获取网络映射拓扑图请求参数
type GetMappingGplotRequest struct {
	Size       int    `json:"size" form:"size" uri:"size"`                      // 限制返回的映射关系数量，0表示不限制
	Keyword    string `json:"keyword" form:"keyword" uri:"keyword"`             // 搜索关键词（IP地址、IP:端口、域名）
	SearchType string `json:"search_type" form:"search_type" uri:"search_type"` // 搜索类型：ip、ip_port、domain
}

// GetMappingAssetsRequest 获取网络映射资产信息请求参数
type GetMappingAssetsRequest struct {
	Size                int    `json:"size" form:"size" uri:"size"`               // 限制映射关系数量
	Keyword             string `json:"keyword" form:"keyword" uri:"keyword"`      // 搜索关键词（IP或IP:端口）
	Field               string `json:"field" form:"field" uri:"field"  zh:"排序字段"` // 排序字段
	Order               string `json:"order" form:"order" uri:"order"  zh:"排序方式"` // 排序方式（ascend/descend）
	request.PageRequest        // 分页参数（Page、PerPage）
}

// GetMappingBaseInfoRequest 获取网络映射基础信息请求参数
type GetMappingBaseInfoRequest struct {
	Keyword string `json:"keyword" form:"keyword" uri:"keyword"` // 查询关键词（IP或IP:端口）
}

// GetMappingByBusinessRequest 根据业务系统获取网络映射拓扑图请求参数
type GetMappingByBusinessRequest struct {
	BusinessName string `json:"business_name" form:"business_name" uri:"business_name" validate:"required"` // 业务系统名称
	Size         int    `json:"size" form:"size" uri:"size"`                                                // 限制返回的映射关系数量，0表示不限制
}
