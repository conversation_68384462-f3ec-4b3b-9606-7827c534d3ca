package qt_cloud

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestQTCloudTaskThreats_IndexName(t *testing.T) {
	assert.Equal(t, "qt_cloud_task_threats", NewQTCloudTaskThreatsModel().IndexName())
	assert.Equal(t, "", NewQTCloudTaskThreatsModel().String())
}

func TestQTCloudTaskThreats_GetQTCloudThreatListFields(t *testing.T) {
	m := NewQTCloudTaskThreatsModel()
	fields := m.GetQTCloudThreatListFields()

	assert.Equal(t, fields, []map[string]string{
		{"name": "主机IP地址", "key": "displayIp"},
		{"name": "风险名称", "key": "vulName", "show_value": "details"},
		{"name": "危险程度", "key": "severity", "show_value": "details"}, //危险程度：0-信息；1-低危；2-中危；3-高危；4-危急 ）
		{"name": "外网IP地址", "key": "externalIp"},
		{"name": "内网IP地址", "key": "internalIp"},
		{"name": "主机名", "key": "hostname"},
		{"name": "首次监测时间", "key": "firstCheckTime", "show_value": "details"},
		{"name": "是否存在Poc", "key": "hasPoc", "show_value": "details"},
		{"name": "是否存在EXP", "key": "hasExp", "show_value": "details"},
		{"name": "CVE编号", "key": "cves", "show_value": "details"},
		{"name": "漏洞类型", "key": "category", "show_value": "details"},
	})
}

func TestQTCloudTaskThreats_GetQTCloudThreatListDetailFields(t *testing.T) {
	m := NewQTCloudTaskThreatsModel()
	fields := m.GetQTCloudThreatListDetailFields()

	assert.Equal(t, fields, []map[string]string{
		{"name": "主机IP地址", "key": "displayIp"},
		{"name": "风险名称", "key": "vulName", "show_value": "details"},
		{"name": "危险程度", "key": "severity", "show_value": "details"}, //危险程度：0-信息；1-低危；2-中危；3-高危；4-危急 ）	{"name": "外网IP", "key": "externalIp"},
		{"name": "内网IP地址", "key": "internalIp"},
		{"name": "主机名", "key": "hostname"},
		{"name": "首次监测时间", "key": "firstCheckTime", "show_value": "details"},
		{"name": "是否存在Poc", "key": "hasPoc", "show_value": "details"},
		{"name": "是否存在EXP", "key": "hasExp", "show_value": "details"},
		{"name": "CVE编号", "key": "cves", "show_value": "details"},
		{"name": "漏洞类型", "key": "category", "show_value": "details"}, //漏洞类型:0 SQL注入,1 未授权访问,2 敏感信息泄露,3 XML外部实体注入,4 跨站脚本攻击,5 不安全的反序列,6 客户端请求伪造,7 服务端请求伪造,8 命令执行,9 代码执行,10 任意文件上传,11 任意文件读取,12 拒绝服务攻击,13 目录遍历,14 恶意后门,15 本地提权,16 注入漏洞
		{"name": "是否内核漏洞", "key": "kernel", "show_value": "details"},
		{"name": "影响的应用", "key": "apps", "show_value": "details"},
		{"name": "修复影响", "key": "restartOpts", "show_value": "details"}, //：0-未知；1-无需重启；2-服务重启；3系统重启 （仅linux）
		{"name": "风险描述", "key": "desc", "show_value": "details"},
		{"name": "是否本地提权", "key": "localEscalation", "show_value": "details"},
		{"name": "是否远程执行", "key": "remote", "show_value": "details"},
		{"name": "受影响应用版本", "key": "appVersion", "show_value": "details"},
		{"name": "POC检测结果", "key": "pocCheckResults", "show_value": "details"},
	})
}
