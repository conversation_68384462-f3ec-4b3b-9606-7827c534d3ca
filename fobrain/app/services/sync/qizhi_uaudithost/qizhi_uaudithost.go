package qizhi_uaudithost

import (
	"context"
	"fmt"
	"fobrain/fobrain/app/services/rule_infos_level"
	"fobrain/pkg/utils/handle_es_bulk_error"
	"strconv"
	"time"

	"github.com/olivere/elastic/v7"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"

	"fobrain/fobrain/app/services/node/qizhi_uaudithost"
	"fobrain/fobrain/common/constant"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	assetses "fobrain/models/elastic/assets"
	qizhi_uaudithost2 "fobrain/models/elastic/source/qizhi_uaudithost"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils"
)

func SyncQiZhiUAuditHost(node *data_source.Node, taskInfo string) error {
	logs.GetSyncLogger().Infof("QiZhiUAuditHost start taskInfo:%s", taskInfo)

	assetTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())
	//threatTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncThreat)).Int())
	//获取foeyeClient
	cli := qizhi_uaudithost.NewQiZhiUAuditHost()

	//设置foeyeClient请求的节点
	if err := cli.SetNode(node.Id); err != nil {
		return err
	}
	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncAsset)).Int() == data_sync_task.SyncAsset {
		//更新资产任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(assetTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("QiZhiUAuditHost syncAssets UpdateStatusById StatusDoing err:%s", err.Error())
		}
		go syncAssets(constant.DefaultPage, cli, taskInfo, node, 0)
	}

	//if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncPeople)).Int() == data_sync_task.SyncPeople {
	//	//更新人员任务进行中
	//	err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(threatTaskId, data_sync_child_task.StatusDoing)
	//	if err != nil {
	//		logs.GetLogger().Errorf("QiZhiUAuditHost syncThreats UpdateStatusById StatusDoing err:%s", err.Error())
	//	}
	//	syncStaff(constant.DefaultPageZero, cli, taskInfo, node)
	//}
	return nil
}

// syncAssets 同步资产信息
func syncAssets(page int, cli *qizhi_uaudithost.QiZhiUAuditHost, taskInfo string, node *data_source.Node, successCount int) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())
	logs.GetSyncLogger().Infof("QiZhiUAuditHost syncAssets start taskInfo:%d,taskChildId:%d", taskId, taskChildId)

	// 默认设置为3，智能判断
	networkType, err := node.GetNetworkType()
	if err != nil {
		logs.GetSyncLogger().Errorf("QiZhiUAuditHost syncAssets GetNetworkType err:%s", err.Error())
	}
	personField := "name"
	if cli.GetConfig("person_field") != "" && cli.GetConfig("person_field") != nil {
		personField = cli.GetConfig("person_field").(string)
	}
	//获取资产
	assets, err := cli.GetAssets(page, cfg.GetInstance().SourceSync.QizhiUaudithostSize)
	if err != nil {
		logs.GetSyncLogger().Errorf("QiZhiUAuditHost syncAssets GetAssets err:%s", err)

		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, "GetAssets "+err.Error())
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("QiZhiUAuditHost syncAssets GetAssets Task UpdateFailById err:%s", taskErr.Error())
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, "GetAssets "+err.Error())
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("QiZhiUAuditHost syncAssets GetAssets childTask UpdateFailById err:%s", childTaskErr.Error())
		}
		return
	}
	total := gjson.Get(assets, "totalElements").Int()
	result := gjson.Get(assets, "content")

	if result.Exists() && len(result.Array()) > 0 {
		assetBulkRequest := es.GetEsClient().Bulk()
		assetTaskBulkRequest := es.GetEsClient().Bulk()
		assetProcessBulkRequest := es.GetEsClient().Bulk()
		var num = 0
		result.ForEach(func(key, value gjson.Result) bool {
			logs.GetSyncLogger().Debugf("QiZhiUAuditHost syncAssets value info:%s", value)
			data, _ := sjson.Set(value.String(), "node_id", node.Id)

			ip := gjson.Get(value.String(), "ip").String()
			if !utils.IsValidIP(ip) {
				return true
			}

			areaId := node.AreaId
			if ip != "" {
				// 获取区域
				areaId, err = node.GetAreaByIp(ip)
				if err != nil {
					logs.GetSyncLogger().Errorf("QiZhiUAuditHost syncAssets GetAreaByIp failed,ip:%s,nodeId:%d,err:%s", ip, node.Id, err)
					r := data_sync_child_task.NewDataSyncChildTaskFailRecord()
					r.Create(&data_sync_child_task.DataSyncChildTaskFailRecord{
						ChildTaskId:  taskChildId,
						DataType:     "ip",
						DataContent:  ip,
						FailedReason: err.Error(),
					})
				}
			}
			data, _ = sjson.Set(data, "area_id", areaId)

			//写入资产库
			asset := qizhi_uaudithost2.NewQiZhiUAuditHostAssetsModel()
			assetId := fmt.Sprintf("%d_%d_%s", node.Id, areaId, ip)
			assetReq := elastic.NewBulkIndexRequest().Index(asset.IndexName()).Id(assetId).Doc(data)
			assetBulkRequest = assetBulkRequest.Add(assetReq)

			//写入任务库
			assetTask := qizhi_uaudithost2.NewQiZhiUAuditHostTaskAssetsModel()
			assetTaskId := fmt.Sprintf("%d_%s", taskId, assetId)
			data, _ = sjson.Set(data, "task_id", taskId)
			data, _ = sjson.Set(data, "child_task_id", taskChildId)
			assetTaskReq := elastic.NewBulkIndexRequest().Index(assetTask.IndexName()).Id(assetTaskId).Doc(data)
			assetTaskBulkRequest = assetTaskBulkRequest.Add(assetTaskReq)

			//写入过程库
			assetProcess := assetses.NewProcessAssetsModel()
			assetProcessId := fmt.Sprintf("%d_%d_%s", taskId, taskChildId, assetId)
			assetProcess.Id = assetProcessId
			assetProcess.TaskDataId = assetTaskId

			assetProcess.NetworkType = networkType
			assetProcess.PersonField = personField

			assetProcess.Area = int(areaId)
			assetProcess.Node = node.Id
			assetProcess.Source = node.SourceId
			assetProcess.TaskId = taskId
			assetProcess.ChildTaskId = taskChildId
			assetProcess.Ip = ip
			assetProcess.Os = gjson.Get(value.String(), "sysType.name").String()
			assetProcess.Oper = gjson.Get(value.String(), "owner.userName").String()
			assetProcess.Status = 1 // 1在线 2离线
			if int(gjson.Get(value.String(), "state").Int()) == 1 {
				assetProcess.Status = 2
			}
			joinTime := gjson.Get(value.String(), "joinTime").String()
			if joinTime != "" {
				i64, _ := strconv.ParseInt(joinTime, 10, 64)
				t := time.UnixMilli(i64)
				createTime, _ := time.Parse(localtime.TimeFormat, t.Format("2006-01-02 15:04:05"))
				assetProcess.CreatedAt = localtime.NewLocalTime(createTime)
			} else {
				assetProcess.CreatedAt = localtime.NewLocalTime(time.Now())
			}
			updateTime := gjson.Get(value.String(), "updateTime").String()
			if updateTime != "" {
				i64, _ := strconv.ParseInt(updateTime, 10, 64)
				t := time.UnixMilli(i64)
				uTime, _ := time.Parse(localtime.TimeFormat, t.Format("2006-01-02 15:04:05"))
				assetProcess.UpdatedAt = localtime.NewLocalTime(uTime)
				assetProcess.LastResponseAt = localtime.NewLocalTime(uTime)
			} else {
				assetProcess.UpdatedAt = localtime.NewLocalTime(time.Now())
			}
			var product []string
			var ruleInfos []*assetses.RuleInfo
			var ports []*assetses.PortInfo
			services := gjson.Get(value.String(), "services").String()
			var portInfo assetses.PortInfo
			gjson.Get(services, "services").ForEach(func(key, val gjson.Result) bool {
				var ruleInfo assetses.RuleInfo
				if gjson.Get(val.String(), "proto").String() != "" {
					ruleInfo.Product = gjson.Get(val.String(), "proto").String()
					ruleInfo.Level = rule_infos_level.GetRuleInfoLevel(ruleInfo.Product)
					ruleInfos = append(ruleInfos, &ruleInfo)
				}
				portInfo.Port = int(gjson.Get(val.String(), "port").Int())
				portInfo.Protocol = gjson.Get(val.String(), "proto").String()
				portInfo.Status = int(gjson.Get(val.String(), "state").Int())
				ports = append(ports, &portInfo)
				return true
			})
			assetProcess.Product = product
			assetProcess.RuleInfos = ruleInfos
			assetProcess.Ports = ports

			assetProcessReq := elastic.NewBulkIndexRequest().Index(assetProcess.IndexName()).Id(assetProcessId).Doc(assetProcess)
			assetProcessBulkRequest = assetProcessBulkRequest.Add(assetProcessReq)
			num = num + 1
			return true
		})
		if num > 0 {
			assetProcessBulkReq, err := assetProcessBulkRequest.Refresh("true").Do(context.Background())
			dispose := errDispose(err, assetProcessBulkReq, "assetProcessBulkReq", taskId, taskChildId)
			if !dispose {
				return
			}

			assetBulkReq, err := assetBulkRequest.Refresh("true").Do(context.Background())
			dispose = errDispose(err, assetBulkReq, "assetBulkReq", taskId, taskChildId)
			if !dispose {
				return
			}

			assetTaskBulkReq, err := assetTaskBulkRequest.Refresh("true").Do(context.Background())
			dispose = errDispose(err, assetTaskBulkReq, "assetTaskBulkReq", taskId, taskChildId)
			if !dispose {
				return
			}

			if err == nil { //更新成功数量
				err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(taskChildId, len(assetTaskBulkReq.Items))
				if err != nil {
					logs.GetSyncLogger().Errorf("QiZhiUAuditHost syncAssets UpdateSyncDataSuccessTotal err:%s", err.Error())
				}
			}
		}
	}

	// 还有数据，再次发送请求
	if page*cfg.GetInstance().SourceSync.QizhiUaudithostSize <= int(total) && total > 0 && len(result.Array()) > 0 {
		time.Sleep(time.Duration(cfg.GetInstance().SourceSync.QizhiUaudithostTime) * time.Second)
		syncAssets(page+1, cli, taskInfo, node, successCount)
	} else {
		logs.GetSyncLogger().Infof("QiZhiUAuditHost syncAssets end taskInfo:%d,taskChildId:%d,total:%d", taskId, taskChildId, successCount)

		//子任务更新需要同步的数据总量
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateById(taskChildId, data_sync_child_task.DataSyncChildTask{
			SyncDataTotal: successCount,
		})

		if err != nil {
			logs.GetSyncLogger().Errorf("QiZhiUAuditHost syncAssets UpdateById SyncDataTotal err:%s", err.Error())
		}

		//更新子任务完成状态
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(taskChildId, data_sync_child_task.StatusSuccess)
		if err != nil {
			logs.GetSyncLogger().Errorf("QiZhiUAuditHost syncAssets UpdateStatusById StatusSuccess err:%s", err.Error())
		}
	}
}

// errDispose 错误处理
func errDispose(err error, req *elastic.BulkResponse, errStr string, taskId, taskChildId uint64) bool {
	if err != nil || req.Errors {
		errString := handle_es_bulk_error.HandleBulkResp("qizhi", err, req)
		logs.GetSyncLogger().Errorf("QiZhiUAuditHost syncAssets %s.Do err:%s", errStr, errString)

		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("QiZhiUAuditHost syncAssets %s.Do task UpdateFailById err:%s", errStr, taskErr)
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("QiZhiUAuditHost syncAssets %s.Do childTask UpdateFailById err:%s", errStr, childTaskErr.Error())
		}
		return false
	}
	return true
}

// syncStaff 同步人员信息
// 后续要使用oss中获取
func syncStaff(page int, cli *qizhi_uaudithost.QiZhiUAuditHost, taskInfo string, node *data_source.Node) {

}
