package changting_waf

import (
	"context"
	"fmt"
	"fobrain/pkg/utils/handle_es_bulk_error"
	"net"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"

	"github.com/olivere/elastic/v7"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"

	nodeCTWaf "fobrain/fobrain/app/services/node/changting_waf"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	assetses "fobrain/models/elastic/assets"
	ctWafModel "fobrain/models/elastic/source/changting_waf"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
)

// SyncChangtingWaf 同步数据
func SyncChangtingWaf(node *data_source.Node, taskInfo string) error {
	logs.GetSyncLogger().Infof("SyncChangtingWaf start taskInfo:%s", taskInfo)
	assetTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())

	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())

	callbackAssets := func(data string) error {
		count, err := handleAssets(data, node, assetTaskId, taskId)
		if err == nil { //更新成功数量
			err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(assetTaskId, count)
			if err != nil {
				logs.GetSyncLogger().Errorf("ChangtingWaf syncAssets UpdateSyncDataSuccessTotal err:%s", err.Error())
			}
		}
		return err
	}

	//获取ChangtingWaf Client
	cli := nodeCTWaf.NewChangtingWaf(callbackAssets, nil)

	//设置ChangtingWaf Client请求的节点
	if err := cli.SetNode(node.Id); err != nil {
		return err
	}

	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncAsset)).Int() == data_sync_task.SyncAsset {
		//更新资产任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(assetTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("ChangtingWaf syncAssets UpdateStatusById StatusDoing err:%s", err.Error())
		}
		go syncAssets(cli, taskInfo, node)
	}

	return nil
}

// syncAssets 同步资产
// @param [int64] otherTaskId 如果是同步任务资产则，通过此 ID 传递任务ID。
func syncAssets(cli *nodeCTWaf.ChangtingWaf, taskInfo string, node *data_source.Node) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())
	logs.GetSyncLogger().Infof("SyncChangtingWaf syncAssets start taskInfo:%d,taskChildId:%d", taskId, taskChildId)

	//获取资产
	total, err := cli.GetAssetsAll() // GetWeiBuAssetsAll 会调用handleAssets
	if err != nil {
		logs.GetSyncLogger().Errorf("ChangtingWaf syncAssets GetAssets err:%s", err)
		setTaskFailure("syncAssets", "GetAssets", taskId, err, taskChildId)
		return
	}

	//子任务更新需要同步的数据总量
	err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateById(taskChildId, data_sync_child_task.DataSyncChildTask{
		SyncDataTotal: int(total),
	})
	if err != nil {
		logs.GetSyncLogger().Errorf("ChangtingWaf syncAssets UpdateById SyncDataTotal err:%s", err.Error())
	}

	logs.GetSyncLogger().Infof("SyncChangtingWaf syncAssets end taskInfo:%d,taskChildId:%d", taskId, taskChildId)
	//更新子任务完成状态
	err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(taskChildId, data_sync_child_task.StatusSuccess)
	if err != nil {
		logs.GetSyncLogger().Errorf("ChangtingWaf syncAssets UpdateStatusById StatusSuccess err:%s", err.Error())
	}
}

func handleAssets(data string, node *data_source.Node, taskChildId, taskId uint64) (int, error) {
	// 默认设置为3，智能判断
	networkType, err := node.GetNetworkType()
	if err != nil {
		logs.GetSyncLogger().Errorf("ChangtingWaf syncAssets GetNetworkType err:%s", err.Error())
	}
	personField := "name"
	for _, config := range node.Configs {
		if config.Key == "person_field" {
			personField = config.Value
			break
		}
	}
	logs.GetSyncLogger().Infof("ChangtingWaf handleAssets data:%s", data)
	items := gjson.Get(data, "data.items")
	if len(items.Array()) == 0 {
		return 0, nil
	}
	count := 0
	if items.Exists() {
		count = int(len(items.Array()))
		assetTaskBulkRequest := es.GetEsClient().Bulk()
		assetProcessBulkRequest := es.GetEsClient().Bulk()
		// 遍历 _id 字段，并根据 id 添加新的字段数据
		var num = 0
		items.ForEach(func(key, value gjson.Result) bool {
			doc, _ := sjson.Set(value.String(), "node_id", node.Id)
			doc, _ = sjson.Set(doc, "area_id", node.AreaId)

			addrs := gjson.Get(value.String(), "addrs").Array()
			ipPort := ""
			//addrs两种格式：
			//1. "addrs": [
			//        "************:80"
			//    ],
			//2. "addrs": [
			//        {"ip_port":"************:80"}
			//    ],
			if len(addrs) > 0 {
				if gjson.Get(addrs[0].String(), "ip_port").String() != "" {
					ipPort = gjson.Get(addrs[0].String(), "ip_port").String()
				} else {
					ipPort = addrs[0].String()
				}
			}
			ip, _ := getIp(ipPort)
			areaId := node.AreaId

			doc, _ = sjson.Set(doc, "area_id", areaId)
			//写入任务库
			assetTask := ctWafModel.NewChangtingWafTaskAssetsModel()
			assetTaskId := strings.ReplaceAll(uuid.New().String(), "-", "")
			doc, _ = sjson.Set(doc, "task_id", taskId)
			doc, _ = sjson.Set(doc, "child_task_id", taskChildId)
			assetTaskReq := elastic.NewBulkIndexRequest().Index(assetTask.IndexName()).Id(assetTaskId).Doc(doc)
			assetTaskBulkRequest = assetTaskBulkRequest.Add(assetTaskReq)
			if ip != "" {
				// 获取区域
				areaId, err = node.GetAreaByIp(ip)
				if err != nil {
					logs.GetSyncLogger().Errorf("ChangtingWaf syncAssets GetAreaByIp failed,ip:%s,nodeId:%d,err:%s", ip, node.Id, err)
					r := data_sync_child_task.NewDataSyncChildTaskFailRecord()
					r.Create(&data_sync_child_task.DataSyncChildTaskFailRecord{
						ChildTaskId:  taskChildId,
						DataType:     "ip",
						DataContent:  ip,
						FailedReason: err.Error(),
					})
				}
			} else {
				logs.GetSyncLogger().Errorf("ChangtingWaf syncAssets ip is empty,ipPort:%s,nodeId:%d", ipPort, node.Id)
				r := data_sync_child_task.NewDataSyncChildTaskFailRecord()
				r.Create(&data_sync_child_task.DataSyncChildTaskFailRecord{
					ChildTaskId:  taskChildId,
					DataType:     "ip",
					DataContent:  ipPort,
					FailedReason: "ip is empty",
				})
				return true
			}

			//写入过程库
			assetProcessId := strings.ReplaceAll(uuid.New().String(), "-", "")
			assetProcess := SetProcessAssetFields(assetProcessId, assetTaskId, ipPort, areaId, node, value, networkType, personField, taskId, taskChildId)

			assetProcessReq := elastic.NewBulkIndexRequest().Index(assetProcess.IndexName()).Id(assetProcessId).Doc(assetProcess)
			assetProcessBulkRequest = assetProcessBulkRequest.Add(assetProcessReq)
			num = num + 1
			return true
		})
		if num > 0 {
			assetProcessBulkReq, err := assetProcessBulkRequest.Refresh("true").Do(context.Background())
			if err != nil || assetProcessBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(assetses.NewProcessAssetsModel().IndexName(), err, assetProcessBulkReq)
				logs.GetSyncLogger().Errorf("ChangtingWaf syncAssets assetProcessBulkRequest.Do err:%s", errString)
				return 0, err
			}

			assetTaskBulkReq, err := assetTaskBulkRequest.Do(context.Background())

			if err != nil || assetTaskBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(ctWafModel.NewChangtingWafTaskAssetsModel().IndexName(), err, assetTaskBulkReq)
				logs.GetSyncLogger().Errorf("ChangtingWaf syncAssets assetTaskBulkRequest.Do err:%s", errString)
				return 0, err
			}
		}
	}

	return count, nil
}

func SetProcessAssetFields(assetProcessId, assetTaskId, ipPort string, areaId uint64, node *data_source.Node, value gjson.Result, networkType int, personField string, taskId, taskChildId uint64) *assetses.ProcessAssets {
	ip, port := getIp(ipPort)
	assetProcess := assetses.NewProcessAssetsModel()
	assetProcess.Id = assetProcessId
	assetProcess.TaskId = taskId
	assetProcess.ChildTaskId = taskChildId
	assetProcess.TaskDataId = assetTaskId
	assetProcess.Area = int(areaId)
	assetProcess.Node = node.Id
	assetProcess.Source = node.SourceId
	assetProcess.TaskId = taskId
	assetProcess.ChildTaskId = taskChildId
	assetProcess.Ip = ip
	assetProcess.Status = 1
	assetProcess.NetworkType = networkType
	assetProcess.PersonField = personField

	//var product []string
	var ports []*assetses.PortInfo

	domains := gjson.Get(value.String(), "server_names").Array()
	domainArr := []string{}
	for _, domain := range domains {
		domainArr = append(domainArr, domain.String())
	}
	var product []string
	var portInfo assetses.PortInfo
	portInt, _ := strconv.Atoi(port)
	portInfo.Port = portInt

	ssl_protocols := gjson.Get(value.String(), "ssl_protocols").Array()
	var protocols []string
	for _, v := range ssl_protocols {
		protocols = append(protocols, v.String())
	}

	portInfo.Protocol = strings.Join(protocols, ",")
	portInfo.Domain = strings.Join(domainArr, ",")
	ports = append(ports, &portInfo)

	assetProcess.Product = product
	assetProcess.Ports = ports

	createTimeStr := gjson.Get(value.String(), "create_time").String()
	if createTimeStr == "" {
		assetProcess.CreatedAt = localtime.NewLocalTime(time.Now())
		assetProcess.UpdatedAt = localtime.NewLocalTime(time.Now())
	} else {
		createTimeInt, _ := strconv.Atoi(createTimeStr)
		tm := time.Unix(int64(createTimeInt), 0)
		assetProcess.CreatedAt = localtime.NewLocalTime(tm)
		assetProcess.UpdatedAt = localtime.NewLocalTime(tm)
	}

	return assetProcess
}

func setTaskFailure(category, operation string, taskId uint64, err error, taskChildId uint64) {
	//更新主任务状态为失败
	taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, operation+" "+err.Error())
	if taskErr != nil {
		logs.GetSyncLogger().Errorf("ChangtingWaf "+category+" "+operation+" Task UpdateFailById err:%s", taskErr.Error())
	}

	//更新子任务状态为失败
	childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, operation+" "+err.Error())
	if childTaskErr != nil {
		logs.GetSyncLogger().Errorf("ChangtingWaf "+category+" "+operation+" childTask UpdateFailById err:%s", childTaskErr.Error())
	}
}

func IsIP(addr string) bool {
	ip := net.ParseIP(addr)
	if ip != nil {
		return true
	}

	return false
}

func IsIPv4(addr string) bool {
	ip := net.ParseIP(addr)
	if ip != nil && ip.To4() != nil {
		return true
	}

	return false
}

func getIp(ipPort string) (string, string) {
	if ipPort == "" {
		return "", ""
	}
	strs := strings.Split(ipPort, ":")
	if len(strs) == 1 {
		return strs[0], ""
	}
	return strs[0], strs[1]
}
