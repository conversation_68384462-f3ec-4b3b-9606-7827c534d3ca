package poc_accessorys

import (
	"fobrain/initialize/mysql"
)

type PocAccessorys struct {
	mysql.BaseModel
	Path   string `json:"path" gorm:"path"`       // 附件地址
	Fid    string `json:"fid" gorm:"fid"`         // fid
	UserId uint64 `json:"user_id" gorm:"user_id"` // 用户id
	Source int64  `json:"source" gorm:"source"`   // 附件来源 1单条导入 2漏洞修复完成 3漏洞复测 4漏洞派发 5漏洞转交 6详情管理
	Name   string `json:"name" gorm:"name"`       // 附件名称
}

// TableName 表名
func (p *PocAccessorys) TableName() string {
	return "poc_accessorys"
}

func NewPocAccessorysModel() *PocAccessorys {
	return &PocAccessorys{BaseModel: mysql.BaseModel{DB: mysql.GetDbClient().Table((&PocAccessorys{}).TableName())}}
}

type UserPocAccessorys struct {
	PocAccessorys
	UserName string `json:"user_name"`
}

const (
	OneImportSource = 1 // 单条导入
	VulFinish       = 2 // 漏洞修复完成
	VulRetest       = 3 // 漏洞复测
	VulDistribute   = 4 // 漏洞派发
	VulCare         = 5 // 漏洞转交
	VulDetail       = 6 // 漏洞详情
)

// CreateItem 函数用于在数据库中创建一个新的 PocAccessorys 项
//
// 参数：
// item: 指向将要创建的 PocAccessorys 结构体指针
//
// 返回值：
// error: 如果创建成功，返回 nil；否则返回错误信息
func (p *PocAccessorys) CreateItem(item *PocAccessorys) error {
	return p.DB.Model(&PocAccessorys{}).Create(item).Error
}

// First 从数据库中获取第一个满足条件的 PocAccessorys 对象
//
// 参数：
// opts: 可选的数据库操作函数，用于修改查询行为
//
// 返回值：
// PocAccessorys: 查询到的第一个 PocAccessorys 对象
// error: 如果查询过程中发生错误，则返回错误信息；否则返回 nil
func (p *PocAccessorys) First(opts ...mysql.HandleFunc) (PocAccessorys, error) {
	query := p.DB.Table(p.TableName())
	for _, opt := range opts {
		opt(query)
	}
	var item PocAccessorys
	err := query.First(&item).Error
	if err != nil {
		return PocAccessorys{}, err
	}
	return item, nil
}

// List 函数用于从数据库中分页获取 PocAccessorys 列表
//
// 参数：
// page: 分页页码，从1开始
// size: 每页显示的数量
// opts: 可选的数据库操作函数，用于修改查询行为
//
// 返回值：
// []*PocAccessorys: 满足条件的 PocAccessorys 对象列表
// int64: 满足条件的 PocAccessorys 对象总数
// error: 如果查询过程中发生错误，则返回错误信息；否则返回 nil
func (p *PocAccessorys) List(page, size int, opts ...mysql.HandleFunc) ([]*PocAccessorys, int64, error) {
	query := p.DB.Table(p.TableName())
	for _, opt := range opts {
		opt(query)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if !mysql.IsPageAll(page, size) {
		query.Scopes(mysql.PageLimit(page, size))
	}

	var list = make([]*PocAccessorys, 0)
	if err := query.Find(&list).Error; err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

// UserPocAccessorysList 根据 fid 分页获取用户 PocAccessorys 列表
//
// 参数：
// fid: 过滤器ID
// page: 当前页码
// perPage: 每页显示的数量
//
// 返回值：
// []*UserPocAccessorys: 用户 PocAccessorys 对象列表
// int64: 用户 PocAccessorys 总数
// error: 错误信息，如果查询成功则返回 nil
func (p *PocAccessorys) UserPocAccessorysList(fid string, originalIds []string, page, perPage int) ([]*UserPocAccessorys, int64, error) {
	data := make([]*UserPocAccessorys, 0)
	var total int64
	query := p.DB.Model(&PocAccessorys{}).
		Select("poc_accessorys.*,users.username as user_name").
		Joins("LEFT JOIN users  ON poc_accessorys.user_id = users.id").
		Where("poc_accessorys.fid = ? or poc_accessorys.fid in (?)", fid, originalIds)

	query.Order("poc_accessorys.id desc")
	query.Count(&total)
	if total > 0 {
		query.Limit(perPage).Offset((page - 1) * perPage).Find(&data)
	}
	return data, total, nil
}

// Delete 函数用于从数据库中删除 PocAccessorys 对象
func (p *PocAccessorys) Delete(opts ...mysql.HandleFunc) error {
	query := p.DB.Table(p.TableName())
	for _, opt := range opts {
		opt(query)
	}
	return query.Delete(&PocAccessorys{}).Error
}
