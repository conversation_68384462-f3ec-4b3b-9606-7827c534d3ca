package assets

import (
	"context"
	"encoding/json"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	es_model "fobrain/models/elastic"
	"fobrain/models/mysql/strategy"

	"github.com/olivere/elastic/v7"
)

// 融合记录
type MergeRecords struct {
	*es.BaseModel     `json:"-"`
	Id                string                   `json:"id"`                  // ID  IP+区域 唯一值
	AssetId           string                   `json:"asset_id"`            // 结果表ID
	AssetRecordId     string                   `json:"asset_record_id"`     // 融合结果记录id
	Area              int                      `json:"area"`                // 区域
	Ip                string                   `json:"ip"`                  // IP
	Status            int                      `json:"status"`              // 状态 成功1 失败2
	Message           string                   `json:"message"`             // 失败信息
	SourceIds         []uint64                 `json:"source_ids"`          // 数据源ids
	AllSourceIds      []uint64                 `json:"all_source_ids"`      // 所有数据源ids
	NodeIds           []uint64                 `json:"node_ids"`            // 节点ids
	AllNodeIds        []uint64                 `json:"all_node_ids"`        // 所有节点ids
	AssetTaskIds      []string                 `json:"asset_task_ids"`      // 资产任务ids
	Strategies        []*strategy.Strategy     `json:"strategies"`          // 融合规则
	CreatedAt         *localtime.Time          `json:"created_at"`          // 创建时间
	UpdatedAt         *localtime.Time          `json:"updated_at"`          // 更新时间
	MergeMode         string                   `json:"merge_mode"`          // 融合模式,auto:自动模式,manual:手动模式(修改策略立即融合),calibration:手动校准
	BatchNo           string                   `json:"batch_no"`            // 批次号,仅用于手动模式(修改策略立即融合)
	AssetIds          []string                 `json:"asset_ids"`           // 受影响的资产 ID，用于手动校准
	IsDeviceExtracted int                      `json:"is_device_extracted"` // 是否参与了实体提取,1是未参与，2是参与了
	TiggerSourceId    uint64                   `json:"tigger_source_id"`    // 触发源ID
	FieldValInfoList  []*es_model.FieldValInfo `json:"field_val_info_list"` // 所有字段的采信信息
}

// IndexName 索引名
func (p *MergeRecords) IndexName() string {
	return "asset_merge_record"
}

func (p *MergeRecords) String() string {
	if p == nil {
		return ""
	}
	return ""
}

func NewMergeRecordsModel() *MergeRecords {
	return &MergeRecords{}
}

func ConvertToAssetMergeRecordModel(hit *elastic.SearchHit) (*MergeRecords, error) {
	item := NewMergeRecordsModel()
	err := json.Unmarshal(hit.Source, item)
	if err != nil {
		return nil, err
	}
	return item, nil
}

func (p *MergeRecords) Insert(ctx context.Context, data *MergeRecords) (*elastic.IndexResponse, error) {
	rsp, err := p.GetClient().Index().Index(p.IndexName()).BodyJson(data).Do(ctx)
	return rsp, err
}
