package dingtalk

import (
	"context"
	"fmt"
	"fobrain/pkg/utils/handle_es_bulk_error"
	"strings"
	syncpkg "sync"
	"time"

	"github.com/olivere/elastic/v7"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"

	"fobrain/fobrain/app/services/node/dingtalk"
	"fobrain/fobrain/app/services/sync"
	"fobrain/fobrain/common/constant"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	dingtalkes "fobrain/models/elastic/source/dingtalk"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
)

// SyncDingTalk 同步DingTalk数据
func SyncDingTalk(node *data_source.Node, taskInfo string) error {
	logs.GetSyncLogger().Infof("SyncDingTalk start taskInfo:%s", taskInfo)
	peopleTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncPeople)).Int())

	//获取dingTalkClient
	cli := dingtalk.NewDingtalk()
	//设置dingTalkClient请求的节点
	if err := cli.SetNode(node.Id); err != nil {
		return err
	}

	//更新人员任务为进行中
	err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(peopleTaskId, data_sync_child_task.StatusDoing)
	if err != nil {
		logs.GetSyncLogger().Errorf("DingTalk syncEmployees UpdateStatusById StatusDoing err:%s", err.Error())
	}

	go syncEmployees(constant.DefaultPageZero, cli, taskInfo, node, 0)
	return nil
}

// syncEmployees 同步人员
func syncEmployees(page int, cli *dingtalk.Dingtalk, taskInfo string, node *data_source.Node, successCount int) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncPeople)).Int())
	logs.GetSyncLogger().Infof("SyncDingTalk syncEmployees start taskId:%d,taskChildId:%d", taskId, taskChildId)

	//获取资产
	employees, err := cli.GetEmployees(page, constant.DefaultSize)
	if err != nil {
		logs.GetSyncLogger().Errorf("DingTalk syncEmployees GetEmployees err:%s", err)

		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, err.Error())
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("DingTalk syncEmployees GetEmployees UpdateFailById Task err:%s", taskErr.Error())
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, err.Error())
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("DingTalk syncEmployees GetEmployees UpdateFailById childTask err:%s", childTaskErr.Error())
		}
		return
	}

	result := gjson.Get(employees, "data_list")

	//子任务更新需要同步的数据总量
	total := len(result.Array())

	err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataTotal(taskChildId, total)
	if err != nil {
		logs.GetSyncLogger().Errorf("DingTalk syncEmployees UpdateSyncDataTotal err:%s", err.Error())
	}

	// 获取unique_field,默认name+mobile
	uniqueField := cli.GetConfig("unique_field")
	if uniqueField == nil || uniqueField.(string) == "" {
		uniqueField = "name+mobile"
	}
	// client := redis.GetRedisClient()
	// rq := &queue.RedisQueue{Client: client}
	if result.Exists() && total > 0 {
		// var peopleIds []map[string]interface{}
		employeeTaskBulkRequest := es.GetEsClient().Bulk()
		employeeProcessBulkRequest := es.GetEsClient().Bulk()
		cache := make(map[int]string)
		var lock syncpkg.RWMutex
		// 遍历 _id 字段，并根据 id 添加新的字段数据
		var num = 0
		// 添加并发控制，foreach 可以并发5个执行
		var wg syncpkg.WaitGroup
		var ch = make(chan struct{}, 5)
		result.ForEach(func(key, value gjson.Result) bool {
			ch <- struct{}{}
			wg.Add(1)
			go func(key, value gjson.Result) {
				defer func() {
					if reErr := recover(); reErr != nil {
						logs.GetSyncLogger().Errorf("DingTalk syncEmployees execEmployees taskId:%d,taskChildId:%d,page:%d > FAIL, 返回结果: %v", taskId, taskChildId, page, reErr)
					}
					<-ch
					wg.Done()
				}()
				logs.GetSyncLogger().Infof("SyncDingTalk syncEmployees value info:%s", value)
				//value===>employeeUserId
				//根据userId获取人员信息
				employeeInfo, err := cli.GetEmployeeInfo(value.String())
				if err != nil {
					//更新主任务状态为失败
					taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, err.Error())
					if taskErr != nil {
						logs.GetSyncLogger().Errorf("DingTalk syncEmployees GetEmployeeInfo UpdateFailById Task err:%s", taskErr.Error())
					}

					//更新子任务状态为失败
					childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, err.Error())
					if childTaskErr != nil {
						logs.GetSyncLogger().Errorf("DingTalk syncEmployees GetEmployeeInfo UpdateFailById childTask err:%s", childTaskErr.Error())
					}
					return
				}

				// 获取指定用户的所有父部门列表
				departments, err := cli.GetDepartments(value.String())
				if err != nil {
					if err.Error() != "部门不存在" {
						logs.GetSyncLogger().Errorf("DingTalk syncEmployees GetDepartments err:%s", err.Error())

						//更新主任务状态为失败
						taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, err.Error())
						if taskErr != nil {
							logs.GetSyncLogger().Errorf("DingTalk syncEmployees GetDepartments UpdateFailById Task err:%s", taskErr.Error())
						}

						//更新子任务状态为失败
						childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, err.Error())
						if childTaskErr != nil {
							logs.GetSyncLogger().Errorf("DingTalk syncEmployees GetDepartments UpdateFailById childTask err:%s", childTaskErr.Error())
						}
						return
					}
				}

				var depts []map[string]string
				parentList := gjson.Get(departments, "parent_list")
				if parentList.Exists() {
					parentList.ForEach(func(_, parentList gjson.Result) bool {
						tmp := make(map[string]string)
						parentList.Get("parent_dept_id_list").ForEach(func(_, deptId gjson.Result) bool {
							id := int(deptId.Int())
							lock.RLock()
							if detail, ok := cache[id]; ok {
								tmp[deptId.String()] = detail
								lock.RUnlock()
								return true
							}
							lock.RUnlock()
							//获取部门详情
							departmentDetails, err := cli.GetDepartmentDetails(id)
							if err != nil {
								logs.GetSyncLogger().Errorf("DingTalk syncEmployees GetDepartmentDetails err:%s", err.Error())

								//更新主任务状态为失败
								taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, err.Error())
								if taskErr != nil {
									logs.GetSyncLogger().Errorf("DingTalk syncEmployees GetDepartmentDetails UpdateFailById Task err:%s", taskErr.Error())
								}

								//更新子任务状态为失败
								childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, err.Error())
								if childTaskErr != nil {
									logs.GetSyncLogger().Errorf("DingTalk syncEmployees GetDepartmentDetails UpdateFailById childTask err:%s", childTaskErr.Error())
								}
								return false
							}
							lock.Lock()
							cache[id] = departmentDetails
							lock.Unlock()
							tmp[deptId.String()] = departmentDetails
							return true
						})

						depts = append(depts, tmp)
						return true
					})
				}

				data, _ := sjson.Set(employeeInfo, "node_id", node.Id)
				data, _ = sjson.Set(data, "area_id", node.AreaId)
				data, _ = sjson.Set(data, "departments", depts)
				if gjson.Get(data, "create_time").String() == "" {
					data, _ = sjson.Set(data, "create_time", time.Now().Format(time.RFC3339))
				}

				employeeId := fmt.Sprintf("%d_%d_%s", node.Id, node.AreaId, value.String())
				employeeName := fmt.Sprintf("%d_%d_%s", node.Id, node.AreaId, gjson.Get(employeeInfo, "name").String())
				//写入任务库
				employeeTaskModel := dingtalkes.NewDingtalkTaskEmployeesModel()
				employeeTaskId := fmt.Sprintf("%d_%s", taskId, employeeId)
				data, _ = sjson.Set(data, "task_id", taskId)
				data, _ = sjson.Set(data, "child_task_id", taskChildId)
				employeeTaskReq := elastic.NewBulkIndexRequest().Index(employeeTaskModel.IndexName()).Id(employeeTaskId).Doc(data)
				employeeTaskBulkRequest = employeeTaskBulkRequest.Add(employeeTaskReq)

				//写入过程库
				staffProcess := staff.NewProcessStaffModel()
				staffProcess.Id = employeeName
				staffProcess.UniqueKey, _ = sync.GetUniqueKey("dingtalk", uniqueField.(string), data)
				staffProcess.TaskDataId = employeeTaskId
				staffProcess.Area = int(node.AreaId)
				staffProcess.Node = node.Id
				staffProcess.Source = node.SourceId
				staffProcess.TaskId = taskId
				staffProcess.ChildTaskId = taskChildId
				staffProcess.Name = gjson.Get(data, "name").String()
				staffProcess.Title = gjson.Get(data, "title").String()
				staffProcess.Mobile = gjson.Get(data, "mobile").String()
				staffProcess.Email = gjson.Get(data, "email").String()
				staffProcess.WorkNumber = gjson.Get(data, "job_number").String()
				staffProcess.Status = 1
				//在职员工状态筛选，可以查询多个状态。不同状态之间使用英文逗号分隔。2：试用期；3：正式；5：待离职；-1：无状态
				if gjson.Get(data, "status").String() == "5" || gjson.Get(data, "status").String() == "-1" {
					staffProcess.Status = 2
				}
				department := ""
				gjson.Get(data, "departments").ForEach(func(k, v gjson.Result) bool {
					gjson.Parse(v.String()).ForEach(func(key, val gjson.Result) bool {
						department += gjson.Get(val.String(), "name").String() + "/"
						return true
					})
					department = strings.TrimSuffix(department, "/") + "&"
					return true
				})

				staffProcess.Department = strings.TrimSuffix(department, "&")
				staffProcess.CreatedAt = localtime.NewLocalTime(time.Now())

				employeeProcessReq := elastic.NewBulkIndexRequest().Index(staffProcess.IndexName()).Id(employeeName).Doc(staffProcess)
				employeeProcessBulkRequest = employeeProcessBulkRequest.Add(employeeProcessReq)
				num = num + 1
			}(key, value)
			return true
		})
		wg.Wait()
		if num > 0 {
			employeeProcessBulkReq, err := employeeProcessBulkRequest.Refresh("true").Do(context.Background())
			if err != nil || employeeProcessBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(staff.NewProcessStaffModel().IndexName(), err, employeeProcessBulkReq)
				logs.GetSyncLogger().Errorf("DingTalk syncEmployees employeeProcessBulkRequest.Do err:%s", errString)

				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("DingTalk syncEmployees employeeProcessBulkRequest.Do task UpdateFailById err:%s", taskErr)
				}

				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("DingTalk syncEmployees employeeProcessBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
				}
				return
			}

			employeeTaskBulkReq, err := employeeTaskBulkRequest.Refresh("true").Do(context.Background())
			if err != nil || employeeTaskBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(dingtalkes.NewDingtalkTaskEmployeesModel().IndexName(), err, employeeTaskBulkReq)
				logs.GetSyncLogger().Errorf("DingTalk syncEmployees employeeTaskBulkRequest.Do err:%s", errString)

				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("DingTalk syncEmployees employeeTaskBulkRequest.Do task UpdateFailById err:%s", taskErr)
				}

				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("DingTalk syncEmployees employeeTaskBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
				}
				return
			}
			if err == nil {
				err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(taskChildId, len(employeeTaskBulkReq.Items))
				if err != nil {
					logs.GetSyncLogger().Errorf("DingTalk syncEmployees UpdateSyncDataSuccessTotal err:%s", err.Error())
				}
				// logs.GetSyncLogger().Infof("DingTalk syncEmployees peopleIds info:%v", peopleIds)

				// // 发送任务开始标识
				// rq.Push(cfg.LoadQueue().PersonMergeQueue, []map[string]interface{}{{"task_id": taskChildId, "task_type": "sync", "is_start": true}})
				// successCount += rq.Push(cfg.LoadQueue().PersonMergeQueue, peopleIds)
				// logs.GetSyncLogger().Infof("DingTalk syncEmployees successCount:%d, expectedCount:%d", successCount, len(employeeTaskBulkReq.Items))
			}
		}
	}

	//下一次分页调用的offset值，当返回结果里没有next_cursor时，表示分页结束。
	if gjson.Get(employees, "next_cursor").Exists() {
		syncEmployees(int(gjson.Get(employees, "next_cursor").Int()), cli, taskInfo, node, successCount)
	} else {
		logs.GetSyncLogger().Infof("SyncDingTalk syncEmployees end taskId:%d,taskChildId:%d", taskId, taskChildId)
		//更新子任务完成状态
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(taskChildId, data_sync_child_task.StatusSuccess)
		if err != nil {
			logs.GetSyncLogger().Errorf("DingTalk syncEmployees UpdateStatusById StatusSuccess err:%s", err.Error())
		}
	}
}
