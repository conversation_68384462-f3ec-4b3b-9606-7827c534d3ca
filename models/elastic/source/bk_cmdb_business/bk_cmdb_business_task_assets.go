package bk_cmdb_business

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/olivere/elastic/v7"
)

type BKCmdbBusinessTaskAssets struct {
	*es.BaseModel `json:"-"`
	Id            string          `json:"id"`         // NODE+区域+IP 作为唯一值去更新，用作数据对比
	CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
	UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
}

func (b *BKCmdbBusinessTaskAssets) IndexName() string {
	return "bk_cmdb_business_task_assets"
}

func (b *BKCmdbBusinessTaskAssets) String() string {
	return ""
}

func NewBKCmdbBusinessTaskAssetsModel() *BKCmdbBusinessTaskAssets {
	return &BKCmdbBusinessTaskAssets{}
}

func (b *BKCmdbBusinessTaskAssets) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("bk_biz_name.keyword").
		Field("biz_sysmanager.keyword").
		Field("belong_org.keyword").
		Field("biz_business.keyword").
		Field("supplier.keyword").
		Field("app_brief.keyword")
	return field
}

func (b *BKCmdbBusinessTaskAssets) GetAssetListFields() []map[string]string {
	return []map[string]string{
		{"name": "ID", "key": "bk_biz_id", "all_key": "bk_biz_id"},
		{"name": "业务系统名称", "key": "bk_biz_name", "all_key": "bk_biz_name"},
		{"name": "运维人员", "key": "biz_sysmanager", "all_key": "biz_sysmanager"},
		{"name": "部门名称", "key": "belong_org", "all_key": "belong_org"},
		{"name": "业务系统负责人", "key": "biz_sysadminer", "all_key": "biz_sysadminer"},
		{"name": "供应商", "key": "supplier", "all_key": "supplier"},
		{"name": "时区", "key": "time_zone", "all_key": "time_zone"},
		{"name": "地址", "key": "main_page_url", "all_key": "main_page_url"},
		{"name": "摘要", "key": "app_brief", "all_key": "app_brief"},
	}
}

func (b *BKCmdbBusinessTaskAssets) GetAssetListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "ID", "key": "bk_biz_id", "all_key": "bk_biz_id"},
		{"name": "业务系统名称", "key": "bk_biz_name", "all_key": "bk_biz_name"},
		{"name": "运维人员", "key": "biz_sysmanager", "all_key": "biz_sysmanager"},
		{"name": "部门名称", "key": "belong_org", "all_key": "belong_org"},
		{"name": "业务系统负责人", "key": "biz_sysadminer", "all_key": "biz_sysadminer"},
		{"name": "供应商", "key": "supplier", "all_key": "supplier"},
		{"name": "时区", "key": "time_zone", "all_key": "time_zone"},
		{"name": "地址", "key": "main_page_url", "all_key": "main_page_url"},
		{"name": "摘要", "key": "app_brief", "all_key": "app_brief"},
	}
}
