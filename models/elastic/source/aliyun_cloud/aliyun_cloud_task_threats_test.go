package aliyun_cloud

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestAliYunCloudTaskThreats_IndexName(t *testing.T) {
	assert.Equal(t, "aliyun_cloud_task_threats", NewAliYunCloudTaskThreatsModel().IndexName())
	assert.Equal(t, "", NewAliYunCloudTaskThreatsModel().String())
}

func TestAliYunCloudThreats_GetAssetListFields(t *testing.T) {
	fields := NewAliYunCloudTaskThreatsModel().GetThreatListFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "CveId", "all_key": "CveId"},
		{"name": "访问者源IP地址", "all_key": "source_ip"},
		{"name": "漏洞类型", "all_key": "vul_type"},
		{"name": "漏洞名称", "all_key": "Title"},
		{"name": "漏洞等级", "all_key": "level"},
		{"name": "漏洞状态", "all_key": "status"},
		{"name": "漏洞修复紧急程度", "all_key": "necessity"},
	}
	assert.Equal(t, expectedFields, fields, "字段不匹配")
}

func TestAliYunCloudThreats_GetAssetListDetailFields(t *testing.T) {
	fields := NewAliYunCloudTaskThreatsModel().GetThreatListDetailFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "CveId", "all_key": "CveId"},
		{"name": "访问者源IP地址", "all_key": "source_ip"},
		{"name": "漏洞类型", "all_key": "vul_type"},
		{"name": "漏洞名称", "all_key": "Title"},
		{"name": "漏洞等级", "all_key": "level"},
		{"name": "漏洞状态", "all_key": "status"},
		{"name": "漏洞修复紧急程度", "all_key": "necessity"},
		{"name": "创建漏洞", "all_key": "create_ts_start"},
		{"name": "漏洞修改", "all_key": "modify_ts"},
		{"name": "最近更新时间", "all_key": "last_time"},
	}
	assert.Equal(t, expectedFields, fields, "字段不匹配")
}
