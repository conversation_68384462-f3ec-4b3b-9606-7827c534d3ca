package yuntu

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestTaskAssets_IndexName(t *testing.T) {
	assert.Equal(t, "yuntu_task_assets", NewTaskAssetsModel().IndexName())
	assert.Equal(t, "", NewTaskAssetsModel().String())
}

func TestAssets_GetAssetListFields(t *testing.T) {
	fields := NewTaskAssetsModel().GetAssetListFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "ID", "key": "id", "all_key": "id"},
		{"name": "IP", "key": "ip", "all_key": "ip"},
		{"name": "ASN名称", "key": "as_name", "all_key": "as_name"},
		{"name": "ASN编号", "key": "as_num", "all_key": "as_num"},
		{"name": "分组", "key": "bu", "all_key": "bu"},
		{"name": "创建时间", "key": "created_at", "all_key": "created_at"},
		{"name": "更新时间", "key": "updated_at", "all_key": "updated_at"},
		{"name": "最后发现时间", "key": "lastseen_at", "all_key": "lastseen_at"},
		{"name": "位置", "key": "location", "all_key": "location"},
		{"name": "存活端口数", "key": "live_port", "all_key": "live_port"},
		{"name": "状态", "key": "status", "all_key": "status"},
		{"name": "网段", "key": "subnet", "all_key": "subnet"},
		{"name": "IP版本", "key": "version", "all_key": "version"},
	}
	assert.Equal(t, expectedFields, fields, "字段不匹配")
}

func TestAssets_GetAssetListDetailFields(t *testing.T) {
	fields := NewTaskAssetsModel().GetAssetListDetailFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "ID", "key": "id", "all_key": "id"},
		{"name": "IP", "key": "ip", "all_key": "ip"},
		{"name": "ASN名称", "key": "as_name", "all_key": "as_name"},
		{"name": "ASN编号", "key": "as_num", "all_key": "as_num"},
		{"name": "分组", "key": "bu", "all_key": "bu"},
		{"name": "创建时间", "key": "created_at", "all_key": "created_at"},
		{"name": "更新时间", "key": "updated_at", "all_key": "updated_at"},
		{"name": "最后发现时间", "key": "lastseen_at", "all_key": "lastseen_at"},
		{"name": "位置", "key": "location", "all_key": "location"},
		{"name": "存活端口数", "key": "live_port", "all_key": "live_port"},
		{"name": "状态", "key": "status", "all_key": "status"},
		{"name": "网段", "key": "subnet", "all_key": "subnet"},
		{"name": "IP版本", "key": "version", "all_key": "version"},
	}
	assert.Equal(t, expectedFields, fields, "字段不匹配")
}
