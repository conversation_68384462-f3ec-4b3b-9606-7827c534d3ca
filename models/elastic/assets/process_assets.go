package assets

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"strings"

	"github.com/olivere/elastic/v7"

	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"fobrain/pkg/utils"
)

type (
	// PortInfo 端口信息
	PortInfo struct {
		Port     int    `json:"port"`     // 端口
		Protocol string `json:"protocol"` // 协议
		Status   int    `json:"status"`   // 状态
		Url      string `json:"url"`      // URL
		Domain   string `json:"domain"`   // Domain
		Title    string `json:"title"`    // Title
	}

	// AccountInfo 账号信息
	AccountInfo struct {
		Id                    string          `json:"id"`                     // ID
		Name                  string          `json:"name"`                   // Name
		Permission            string          `json:"permission"`             // 权限
		Shell                 string          `json:"shell"`                  // shell
		Status                int             `json:"status"`                 // 状态
		CentralizationAccount string          `json:"centralization_account"` // 集权账号
		LastLoginAt           *localtime.Time `json:"last_login_at"`          // 最后登录时间
	}
	// RuleInfo 组件信息
	RuleInfo struct {
		Product   string `json:"product"`    //组件名称
		FirstTag  string `json:"first_tag"`  //一级分类
		SecondTag string `json:"second_tag"` //二级分类
		Level     string `json:"level"`      //层级
	}

	KV struct {
		Key   string `json:"k"`
		Value string `json:"v"`
	}

	JarPackageInfo struct {
		JarPackageInfo []*KV `json:"jar_package_info"`
	}

	BusinessInfo struct {
		BusinessSystem string `json:"business_system"`
		BusinessOwner  string `json:"business_owner"`
	}

	ProcessAssets struct {
		*es.BaseModel   `json:"-"`
		Id              string            `json:"id"`                // ID  NODE+IP+区域+task_id+child_task_id 唯一值
		Area            int               `json:"area"`              // 区域
		Source          uint64            `json:"source"`            // 来源
		Node            uint64            `json:"node"`              // 节点
		TaskId          uint64            `json:"task_id"`           // 主任务ID
		ChildTaskId     uint64            `json:"child_task_id"`     // 子任务ID
		Ip              string            `json:"ip"`                // IP
		NetworkType     int               `json:"network_type"`      // 网络类型,1=内网,2=外网,3=智能判断
		IpSegment       string            `json:"ip_segment"`        // IP段
		MapIp           string            `json:"map_ip"`            // 映射IP
		HostName        string            `json:"hostname"`          // 主机名
		EthName         string            `json:"eth_name"`          // 网卡名
		Os              string            `json:"os"`                // 操作系统
		Kernel          string            `json:"kernel"`            // 内核
		DeviceId        string            `json:"device_id"`         // 设备ID
		DeviceName      string            `json:"device_name"`       // 设备名
		Model           string            `json:"model"`             // 型号
		Maker           string            `json:"maker"`             // 制造商
		Sn              string            `json:"sn"`                // 序列号
		Mac             string            `json:"mac"`               // MAC地址
		Product         []string          `json:"product"`           // 组件
		RuleInfos       []*RuleInfo       `json:"rule_infos"`        //组件信息
		BusinessSystem  string            `json:"business_system"`   // 业务系统
		Oper            string            `json:"oper"`              // 运维人员
		BusinessOwner   string            `json:"business_owner"`    // 业务负责人
		BusinessInfo    []*BusinessInfo   `json:"business_info"`     // 业务系统信息，支持一条IP多个业务系统的情况，可以代替BusinessSystem&BusinessOwner
		MachineRoom     string            `json:"machine_room"`      // 机房
		Status          int               `json:"status"`            // 状态  在线1 离线2
		Ports           []*PortInfo       `json:"ports"`             // 端口
		MemorySize      string            `json:"memory_size"`       // 内存大小
		MemoryUsageRate string            `json:"memory_usage_rate"` // 内存使用率
		CpuMaker        string            `json:"cpu_maker"`         // CPU厂商
		CpuBrand        string            `json:"cpu_brand"`         // CPU品牌
		CpuCount        int               `json:"cpu_count"`         // CPU数量
		DiskCount       int               `json:"disk_count"`        // 磁盘数量
		DiskSize        int               `json:"disk_size"`         // 磁盘大小
		DiskUsageRate   string            `json:"disk_usage_rate"`   // 磁盘使用率
		LoadAverage     string            `json:"load_average"`      // 负载
		Accounts        []*AccountInfo    `json:"accounts"`          // 账号
		IsPublic        bool              `json:"is_public"`         // 是否外网IP
		TaskDataId      string            `json:"asset_task_id"`     // 最后一次资产任务id
		CreatedAt       *localtime.Time   `json:"created_at"`        // 创建时间
		UpdatedAt       *localtime.Time   `json:"updated_at"`        // 更新时间
		LastResponseAt  *localtime.Time   `json:"last_response_at"`  // 最后一次响应时间
		PersonField     string            `json:"person_field"`      // 人员映射字段
		CustomFields    map[string]string `json:"custom_fields"`     // 自定义字段
		JarPackageInfo  []*JarPackageInfo `json:"jar_package_info"`  // jar包信息
	}
)

func (p ProcessAssets) GetSource() uint64 {
	return p.Source
}

func (p ProcessAssets) GetNode() uint64 {
	return p.Node
}

func (p ProcessAssets) GetTaskDataId() string {
	return p.TaskDataId
}

func (p ProcessAssets) GetId() string {
	return p.Id
}

func (a *PortInfo) Equal(b *PortInfo) bool {
	if a == nil && b == nil {
		return true
	}
	if a == nil || b == nil {
		return false
	}
	if a.Port == b.Port && a.Protocol == b.Protocol && a.Url == b.Url && a.Domain == b.Domain {
		return true // 端口、协议、URL、Domain都相同，认为是同一个端口
	}
	return false
}

// IndexName 索引名
func (p *ProcessAssets) IndexName() string {
	return "process_asset"
}

func (p *ProcessAssets) String() string {
	if p == nil {
		return ""
	}
	return ""
}

func NewProcessAssetsModel() *ProcessAssets {
	return &ProcessAssets{}
}

// GetAreaIpList 获取区域IP列表
func (p *ProcessAssets) GetAreaIpList() ([]map[string]any, error) {
	list := make([]map[string]any, 0, 5000)
	unique := make([]string, 0, 5000)
	l, err := es.All[ProcessAssets](5000, elastic.NewMatchAllQuery(), nil, "ip", "area")
	if err != nil {
		return nil, err
	}
	for _, item := range l {
		if !utils.ListContains(unique, fmt.Sprintf("%d_%s", item.Area, item.Ip)) {
			list = append(list, map[string]any{"area": item.Area, "ip": item.Ip})
			unique = append(unique, fmt.Sprintf("%d_%s", item.Area, item.Ip))
		}
	}
	return list, nil
}

// FindAllByQuery 根据条件查询所有数据，滚动查询，性能较高
func (p *ProcessAssets) FindAllByQuery(ctx context.Context, query *elastic.BoolQuery) ([]*ProcessAssets, int64) {
	scrollService := p.GetClient().Scroll().Index(p.IndexName()).Query(query).Scroll("1m").Size(1000).Sort("id", true)
	defer scrollService.Clear(context.Background())
	results := make([]*ProcessAssets, 0)
	var total int64
	for {
		searchResult, err := scrollService.Do(ctx)
		if errors.Is(err, io.EOF) {
			err = nil
			break
		}
		if err != nil || len(searchResult.Hits.Hits) == 0 {
			break
		}
		if len(searchResult.Hits.Hits) == 0 {
			break
		}
		total = searchResult.TotalHits()
		for _, hit := range searchResult.Hits.Hits {
			item, err := ConvertToProcessAssetsModel(hit)
			if err != nil {
				fmt.Printf("过程数据解析失败,跳过,数据:%v,错误:%v", hit, err)
				continue
			}
			results = append(results, item)
		}
		// Update scrollService with new ScrollId
		//scrollService = p.GetClient().Scroll().ScrollId(searchResult.ScrollId).Scroll("1m")
	}
	return results, total
}

func ConvertToProcessAssetsModel(hit *elastic.SearchHit) (*ProcessAssets, error) {
	item := NewProcessAssetsModel()
	err := json.Unmarshal(hit.Source, item)
	if err != nil {
		return nil, err
	}
	return item, nil
}

// BuildQuery 构造通用查询条件
func (p *ProcessAssets) BuildQuery(fields []string, minDocCount int, maxBuckets int) *elastic.TermsAggregation {
	// 构建字段组合的脚本
	scriptParts := make([]string, len(fields))
	for i, field := range fields {
		// 对每个字段添加空值检查
		scriptParts[i] = fmt.Sprintf("(doc['%s'].size() > 0 ? doc['%s'].value : '')", field, field)
	}

	// 使用 join 将所有部分用 '|' 连接起来
	script := strings.Join(scriptParts, " + '|' + ")

	// 构造聚合条件
	agg := elastic.NewTermsAggregation().
		Script(elastic.NewScript(script)).
		MinDocCount(minDocCount).
		Size(maxBuckets)

	return agg
}

// GetDuplicateCombinations 查询指定索引中，基于传入的字段列表和查询条件，
// 返回字段组合的去重统计结果及总文档数。
// - client: Elasticsearch 客户端
// - index: 索引名称
// - fields: 需要组合去重的字段列表
// - query: 查询条件
func (p *ProcessAssets) GetDuplicateCombinations(fields []string, query elastic.Query) ([]map[string]interface{}, int64, error) {
	const pageSize = 1000
	var afterKey map[string]interface{}
	var duplicates []map[string]interface{}
	var total int64
	client := p.GetClient()
	index := p.IndexName()
	for {
		// 构建 Composite Aggregation
		compositeAgg := elastic.NewCompositeAggregation().Size(pageSize)
		for _, field := range fields {
			termsSource := elastic.NewCompositeAggregationTermsValuesSource(field).Field(field).MissingBucket(true)
			compositeAgg = compositeAgg.Sources(termsSource)
		}
		if afterKey != nil {
			compositeAgg = compositeAgg.AggregateAfter(afterKey)
		}

		// 执行查询
		searchService := client.Search().Index(index).Size(0).Aggregation("duplicate_combinations", compositeAgg)
		if query != nil {
			searchService = searchService.Query(query)
		}
		res, err := searchService.Do(context.Background())
		if err != nil {
			return nil, total, err
		}

		// 获取总文档数
		if total == 0 && res.Hits != nil {
			total = res.Hits.TotalHits.Value
		}

		// 解析聚合结果
		compAggRes, found := res.Aggregations.Composite("duplicate_combinations")
		if !found {
			return nil, total, fmt.Errorf("aggregation result 'duplicate_combinations' not found")
		}

		for _, bucket := range compAggRes.Buckets {
			var keyParts []string
			for _, field := range fields {
				value, _ := bucket.Key[field].(string)
				keyParts = append(keyParts, value)
			}
			combinedKey := strings.Join(keyParts, "|")
			duplicates = append(duplicates, map[string]interface{}{
				"key":   combinedKey,
				"count": bucket.DocCount,
			})
		}

		if len(compAggRes.Buckets) < pageSize {
			break
		}
		afterKey = compAggRes.AfterKey
	}

	return duplicates, total, nil
}
