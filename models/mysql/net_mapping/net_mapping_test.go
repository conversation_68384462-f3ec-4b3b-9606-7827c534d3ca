package net_mapping

import (
	"context"
	"errors"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

func TestNetMapping_Create(t *testing.T) {
	netMapping := &NetMapping{
		DataSource:     "测试",
		BatchNo:        "xxx",
		FromArea:       1,
		FromIp:         "**************",
		FromPort:       "8080",
		FromDomain:     "abc.com",
		FromUrl:        "/a",
		FromProtocol:   "https",
		FromExistAlarm: false,
		ToArea:         2,
		ToIp:           "************",
		ToPort:         "8080",
		ToDomain:       "abc.com",
		ToUrl:          "/a",
		ToProtocol:     "https",
		ToExistAlarm:   false,
	}
	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `net_mappings` (`created_at`,`updated_at`,`data_source`,`batch_no`,`from_area`,`from_ip`,`from_port`,`from_domain`,`from_url`,`from_protocol`,`from_exist_alarm`,`to_area`,`to_ip`,`to_port`,`to_domain`,`to_url`,`to_protocol`,`to_exist_alarm`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	err := NewNetMappingModel().Create(netMapping)
	mockDb.Close()
	assert.Nil(t, err)
}

func TestNetMapping_CreateBatch(t *testing.T) {
	list := []*NetMapping{{}}
	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `net_mappings` (`created_at`,`updated_at`,`data_source`,`batch_no`,`from_area`,`from_ip`,`from_port`,`from_domain`,`from_url`,`from_protocol`,`from_exist_alarm`,`to_area`,`to_ip`,`to_port`,`to_domain`,`to_url`,`to_protocol`,`to_exist_alarm`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	count, err := NewNetMappingModel().CreateBatch(list)
	mockDb.Close()
	assert.Nil(t, err)
	assert.Equal(t, int64(1), count)
}

func TestNetMapping_UpdateBatch(t *testing.T) {
	list := []*NetMapping{
		&NetMapping{BaseModel: mysql.BaseModel{Id: 1}},
	}
	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `net_mappings` SET `batch_no`=?,`data_source`=?,`from_area`=?,`from_domain`=?,`from_exist_alarm`=?,`from_ip`=?,`from_port`=?,`from_protocol`=?,`from_url`=?,`to_area`=?,`to_domain`=?,`to_exist_alarm`=?,`to_ip`=?,`to_port`=?,`to_protocol`=?,`to_url`=? WHERE id = ?").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	err1 := NewNetMappingModel().UpdateBatch(list)

	mockDb.ExpectBegin()
	mockDb.ExpectExec("UPDATE `net_mappings` SET `batch_no`=?,`data_source`=?,`from_area`=?,`from_domain`=?,`from_exist_alarm`=?,`from_ip`=?,`from_port`=?,`from_protocol`=?,`from_url`=?,`to_area`=?,`to_domain`=?,`to_exist_alarm`=?,`to_ip`=?,`to_port`=?,`to_protocol`=?,`to_url`=? WHERE id = ?").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnError(errors.New("err"))
	_ = mockDb.ExpectationsWereMet()
	ere2 := NewNetMappingModel().UpdateBatch(list)
	mockDb.Close()
	assert.Nil(t, err1)
	assert.NotNil(t, ere2)
}

func TestNetMapping_ListByPage(t *testing.T) {
	page := 1
	size := 10
	keyword := "公网"

	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectQuery("SELECT * FROM `net_mapping_areas` WHERE `name` = ? ORDER BY `net_mapping_areas`.`id` LIMIT 1").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
	mockDb.ExpectQuery("SELECT count(*) FROM `net_mappings`").
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))
	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE from_ip LIKE ? OR data_source LIKE ? OR  from_port LIKE ? OR from_domain LIKE ? OR from_url LIKE ? OR from_protocol LIKE ? OR to_ip LIKE ? OR to_port LIKE ? OR to_domain LIKE ? OR to_url LIKE ? OR to_protocol LIKE ? OR from_area = ? OR to_area = ? ORDER BY created_at DESC LIMIT 10").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
			sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).
			AddRow(1))
	list, total, err := NewNetMappingModel().ListByPage(page, size, keyword)
	mockDb.Close()
	assert.Equal(t, list[0].Id, uint64(1))
	assert.Equal(t, total, int64(1))
	assert.Nil(t, err)
}

func TestNetMapping_ListByOpt(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE `id` = 1 ORDER BY created_at DESC").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
	list, err := NewNetMappingModel().ListByOpt(mysql.WithWhere("`id` = 1"))
	mockDb.Close()
	assert.Nil(t, err)
	assert.Equal(t, list[0].Id, uint64(1))
}

func TestNetMapping_Delete(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	mockDb.ExpectQuery("SELECT * FROM `net_mappings`").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
	mockDb.ExpectBegin()
	mockDb.ExpectExec("DELETE FROM `net_mappings` WHERE `net_mappings`.`id` = ?").
		WithArgs(sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	_, err1 := NewNetMappingModel().Delete(context.Background(), []uint64{}, "")

	mockDb.ExpectQuery("SELECT * FROM `net_mappings` WHERE id IN (?)").
		WithArgs(sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
	mockDb.ExpectBegin()
	mockDb.ExpectExec("net_mappings` WHERE id IN (?) AND `net_mappings`.`id` = ?").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.ExpectCommit()
	_, err2 := NewNetMappingModel().Delete(context.Background(), []uint64{1}, "")
	mockDb.Close()
	assert.Nil(t, err1)
	assert.Nil(t, err2)

}

func TestGetDomain(t *testing.T) {
	netMapping1 := &NetMapping{
		FromDomain: "a.com",
		ToDomain:   "b.com",
	}
	netMapping2 := &NetMapping{
		FromDomain: "",
		ToDomain:   "",
	}
	list1 := netMapping1.GetFromDomainList()
	list2 := netMapping1.GetToDomainList()
	list3 := netMapping2.GetFromDomainList()
	list4 := netMapping2.GetToDomainList()
	assert.Equal(t, len(list1), 1)
	assert.Equal(t, len(list2), 1)
	assert.Equal(t, len(list3), 0)
	assert.Equal(t, len(list4), 0)
}
