package weibu

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/pkg/cfg"
	"io"
	"strings"
	"time"

	"github.com/spf13/cast"
	"github.com/tidwall/gjson"

	"fobrain/fobrain/app/services/node"
)

const (
	UriCheckStatus = "%s://%s/api/v1/dashboard/status?%s"

	UriGetAssets = "%s://%s/api/v1/machine/list?%s"

	UriGetThreats = "%s://%s/api/v1/vulnerability/vulnerabilityList?%s"
)

type WeiBu struct {
	node.Client
	CallBackAssetsFunc  func(string) error
	CallBackThreatsFunc func(string) error
}

func NewWeiBu(callbackAssetsFunc, callbackThreatFunc func(data string) error) *WeiBu {
	return &WeiBu{
		Client:              node.NewDefaultClient(),
		CallBackAssetsFunc:  callbackAssetsFunc,
		CallBackThreatsFunc: callbackThreatFunc,
	}
}

// GetStatus 获取状态
func (f *WeiBu) GetStatus() error {
	_, err := f.GetResponse(UriCheckStatus, map[string]any{})
	if err != nil {
		return err
	}

	return nil
}

// GenerateSign 生成签名
func (f *WeiBu) GenerateSign(apiKey, apiSecret, timestamp string) string {
	// 创建 HMAC 对象，使用 SHA256 哈希算法和密钥
	h := hmac.New(sha256.New, []byte(apiSecret))

	// 写入要签名的数据
	h.Write([]byte(apiKey + timestamp))

	// 计算哈希值并获取签名
	signature := h.Sum(nil)

	// 对签名进行 Base64 编码
	encodedSignature := base64.URLEncoding.EncodeToString(signature)
	// 如果没有类似base64.URLEncoding.EncodeToString的方法，进行Base64编码后，请使用如下两行类似代码替换编码结果中的特殊字符
	//encodedSignature = strings.ReplaceAll(encodedSignature, "+", "-")
	//encodedSignature = strings.ReplaceAll(encodedSignature, "/", "_")
	encodedSignature = strings.TrimRight(encodedSignature, "=")

	return encodedSignature
}

func (f *WeiBu) GetSign() (string, string, string) {
	apiKey := cast.ToString(f.GetConfig("appkey"))
	appSecret := cast.ToString(f.GetConfig("appsecret"))

	currentTime := fmt.Sprintf("%d", time.Now().Unix())
	sign := f.GenerateSign(apiKey, appSecret, currentTime)
	return apiKey, currentTime, sign
}

func (f *WeiBu) GetResponse(uri string, params map[string]any) (string, error) {
	apiKey, currentTime, sign := f.GetSign()
	query := fmt.Sprintf("api_key=%s&auth_timestamp=%s&sign=%s", apiKey, currentTime, sign)
	apiUri := fmt.Sprintf(uri, cast.ToString(f.GetConfig("protocol")), cast.ToString(f.GetConfig("ip")), query)
	jsonData, err := json.Marshal(params)
	if err != nil {
		fmt.Println("Error encoding JSON data:", err)
		return "", err
	}

	resp, err := f.Client.Client.Post(apiUri, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	if resp.StatusCode != 200 || gjson.Get(string(body), "response_code").Int() != 0 {
		return "", errors.New(gjson.Get(string(body), "verbose_msg").String())
	}

	if !gjson.Get(string(body), "response_code").Exists() {
		return "", errors.New("节点认证失败")
	}
	return gjson.Get(string(body), "data").String(), nil
}

// GetWeiBuAssetsAll 获取所有资产数据
func (f *WeiBu) GetWeiBuAssetsAll() (int64, error) {
	// 发送请求
	return f.GetWeiBuAssetsPage(1, cfg.GetInstance().SourceSync.WeibuSize, map[string]interface{}{})
}

// GetWeiBuThreatsAll 获取所有威胁数据
func (f *WeiBu) GetWeiBuThreatsAll() (int64, error) {
	// 发送请求
	return f.GetWeiBuThreatsPage(1, cfg.GetInstance().SourceSync.WeibuSize, map[string]interface{}{})
}

// GetWeiBuAssetsPage 获取资产分页数据
func (f *WeiBu) GetWeiBuAssetsPage(page, perPage int, conditions map[string]interface{}) (int64, error) {
	params := map[string]any{
		"page": map[string]interface{}{
			"cur_page":  page,
			"page_size": perPage,
		},
		"condition": conditions,
	}
	// 发送请求
	data, err := f.GetResponse(UriGetAssets, params)
	if err != nil {
		return 0, err
	}

	total := gjson.Get(data, "page.total_num").Int()       //数据总量
	totalPage := gjson.Get(data, "page.total_pages").Int() //页数
	if f.CallBackAssetsFunc != nil && total > 0 {
		err = f.CallBackAssetsFunc(data)
		if err != nil {
			return total, err
		}
	}

	if page < int(totalPage) && total > 0 {
		time.Sleep(time.Duration(cfg.GetInstance().SourceSync.WeibuTime) * time.Second)
		return f.GetWeiBuAssetsPage(page+1, perPage, map[string]interface{}{})
	}

	return total, nil
}

// GetWeiBuThreatsPage 获取威胁分页数据
func (f *WeiBu) GetWeiBuThreatsPage(page, perPage int, conditions map[string]interface{}) (int64, error) {
	params := map[string]any{
		"page": map[string]interface{}{
			"cur_page":  page,
			"page_size": perPage,
		},
		"condition": conditions,
	}
	// 发送请求
	data, err := f.GetResponse(UriGetThreats, params)
	if err != nil {
		return 0, err
	}

	total := gjson.Get(data, "page.total_num").Int()       //数据总量
	totalPage := gjson.Get(data, "page.total_pages").Int() //页数
	if f.CallBackThreatsFunc != nil && total > 0 {
		err = f.CallBackThreatsFunc(data)
		if err != nil {
			return total, err
		}
	}

	if page < int(totalPage) && total > 0 {
		time.Sleep(time.Duration(cfg.GetInstance().SourceSync.WeibuTime) * time.Second)
		return f.GetWeiBuThreatsPage(page+1, perPage, map[string]interface{}{})
	}

	return total, nil
}
