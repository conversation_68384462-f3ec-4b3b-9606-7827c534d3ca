package seeders

import (
	"encoding/json"
	"errors"
	"fmt"
	responsePermission "fobrain/fobrain/app/response/permission"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/permission"
	"fobrain/pkg/utils"
	"strings"

	"gorm.io/gorm"
)

func MenuSeeder() {
	menusString := `[
    {
        "path": "/assetCenter",
        "name": "AssetCenter",
        "component": "LAYOUT",
        "redirect": "redirect",
        "meta": {
            "orderNo": 2,
            "icon": "layout-menu-assetCenter|svg",
            "title": "资产中心",
            "ignoreKeepAlive": false
        },
        "children": [
            {
                "path": "internalIP",
                "name": "InternalIP",
                "meta": {
                    "title": "内网资产",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-internalIP|svg",
                    "orderNo": 0
                },
                "component": "asset/internalIP/index.vue",
                "children": [
                    {
                        "path": "assetPortrait",
                        "name": "InternalPortrait",
                        "meta": {
                            "currentActiveMenu": "/assetCenter/internalIP",
                            "ignoreKeepAlive": false,
                            "icon": "layout-sliderMenu-internalIP|svg",
                            "hideMenu": true,
                            "dynamicLevel": 3,
                            "realPath": "/assetCenter/internalIP/assetPortrait",
                            "title": "内网IP资产画像"
                        },
                        "component": "asset/assetPortrait/index.vue",
                        "children": [
                            {
                                "meta": {
                                    "title": "导出"
                                },
                                "name": "export_vul_internal_portrait",
                                "type": 3,
                                "children": [
                                    {
                                        "meta": {
                                            "title": "漏洞操作"
                                        },
                                        "name": "漏洞导出",
                                        "path": "/api/v1/threat_center/export",
										"method": "GET",
                                        "type": 4
                                    }
                                ]
                            }
                        ]
                    },
                    {
						"meta": {
						  "title": "删除"
						},
						"name": "delete_internal",
						"type": 3,
						"buttonType": "delete",
						"children": [
						  {
							"meta": {
							  "title": "资产操作"
							},
							"name": "内网资产删除",
							"path": "/api/v1/internal_asset",
                            "method": "DELETE",
							"type": 4
						  },
                          {
							"meta": {
							  "title": "资产操作"
							},
							"name": "内网回收站资产删除",
							"path": "/api/v1/internal_asset/purge",
                            "method": "POST",
							"type": 4
						  }
						]
					},
				  	{
						"meta": {
						  "title": "导入"
						},
						"name": "import_internal",
						"type": 3,
						"children": [
						  {
							"meta": {
							  "title": "资产操作"
							},
							"name": "资产导入",
							"path": "/api/v1/task/sync/all",
                            "method": "GET",
							"type": 4
						  }
						]
				  	},
					{
						"meta": {
						  "title": "导出"
						},
						"name": "export_internal",
						"type": 3,
						"children": [
						  {
							"meta": {
							  "title": "资产操作"
							},
							"name": "内网资产导出",
							"path": "/api/v1/internal_asset/export",
                            "method": "GET",
							"type": 4
						  }
						]
					},
				  	{
						"meta": {
						  "title": "字段校准"
						},
						"name": "calibrate_fields_internal",
						"type": 3,
						"buttonType": "edit",
						"children": [
						  {
							"meta": {
							  "title": "资产操作"
							},
							"name": "内网资产字段校准",
							"path": "/api/v1/internal_asset/manual_calibration",
                            "method": "POST",
							"type": 4
						  }
						]
				  	},
                    {
                        "meta": {
                            "title": "查询"
                        },
                        "name": "query_asset",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "资产操作"
                                },
                                "name": "资产查询",
                                "path": "/api/v1/asset",
                                "method": "GET",
                                "type": 4
                            }
                        ]
                    }
                ]
            },
            {
                "path": "networkIp",
                "name": "NetworkIp",
                "redirect": "/assetCenter/networkIp/externalIp",
                "meta": {
                    "title": "互联网资产",
                    "ignoreKeepAlive": false,
                    "showMenu": false,
                    "icon": "layout-sliderMenu-networkIp|svg",
                    "orderNo": 1
                },
                "children": [
                    {
                        "path": "externalIp",
                        "name": "ExternalIp",
                        "meta": {
                            "currentActiveMenu": "/assetCenter/networkIp",
                            "title": "互联网资产",
                            "ignoreKeepAlive": false,
                            "showMenu": true,
                            "hideMenu": true,
                            "icon": "layout-sliderMenu-externalIp|svg"
                        },
                        "component": "asset/networkIp/index.vue",
                        "children": [
                            {
                                "path": "assetPortrait",
                                "name": "ExternalPortrait",
                                "meta": {
                                    "currentActiveMenu": "/assetCenter/networkIp",
                                    "ignoreKeepAlive": false,
                                    "icon": "ion:anewspaper-outline",
                                    "hideMenu": true,
                                    "dynamicLevel": 3,
                                    "realPath": "/assetCenter/networkIp/externalIp/assetPortrait",
                                    "title": "互联网资产画像"
                                },
                                "component": "asset/assetPortrait/index.vue",
                                "children": [
                                    {
                                        "meta": {
                                            "title": "导出"
                                        },
                                        "name": "export_vul_external_portrait",
                                        "type": 3,
                                        "children": [
                                            {
                                                "meta": {
                                                    "title": "漏洞操作"
                                                },
                                                "name": "漏洞导出",
                                                "path": "/api/v1/threat_center/export",
                                                "method": "GET",
                                                "type": 4
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_external",
                        "type": 3,
                        "buttonType": "delete",
                        "children": [
                            {
                                "meta": {
                                    "title": "资产操作"
                                },
                                "name": "互联网资产删除",
                                "path": "/api/v1/external_ip_asset",
                                "method": "DELETE",
                                "type": 4
                            },
                            {
                                "meta": {
                                    "title": "资产操作"
                                },
                                "name": "互联网资产回收站删除",
                                "path": "/api/v1/external_ip_asset/purge",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "导入"
                        },
                        "name": "import_external",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "资产操作"
                                },
                                "name": "资产导入",
                                "path": "/api/v1/task/sync/all",
                                "method": "GET",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "导出"
                        },
                        "name": "export_external",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "资产操作"
                                },
                                "name": "互联网资产导出",
                                "path": "/api/v1/external_ip_asset/export",
                                "method": "GET",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "字段校准"
                        },
                        "name": "calibrate_fields_external",
                        "type": 3,
                        "buttonType": "edit",
                        "children": [
                            {
                                "meta": {
                                    "title": "资产操作"
                                },
                                "name": "互联网资产字段校准",
                                "path": "/api/v1/external_ip_asset/manual_calibration",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "查询"
                        },
                        "name": "query_asset",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "资产操作"
                                },
                                "name": "资产查询",
                                "path": "/api/v1/asset",
                                "method": "GET",
                                "type": 4
                            }
                        ]
                    }
                ]
            },
            {
                "path": "physicalFacilities",
                "name": "PhysicalFacilities",
                "meta": {
                    "title": "实体设备",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-physicalFacilities|svg",
                    "orderNo": 2
                },
                "component": "asset/physicalFacilities/index.vue",
                "children": [
                    {
                        "path": "deveicePortrait",
                        "name": "DeveicePortrait",
                        "meta": {
                            "currentActiveMenu": "/assetCenter/physicalFacilities",
                            "ignoreKeepAlive": false,
                            "icon": "layout-sliderMenu-physicalFacilities|svg",
                            "hideMenu": true,
                            "dynamicLevel": 3,
                            "realPath": "/assetCenter/physicalFacilities/deveicePortrait",
                            "title": "实体画像"
                        },
                        "component": "asset/deveicePortrait/index.vue"
                    },
                    {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_facilities",
                        "type": 3,
                        "buttonType": "delete",
                        "children": [
                            {
                                "meta": {
                                    "title": "资产操作"
                                },
                                "name": "实体设备删除",
                                "path": "/api/v1/device",
                                "method": "DELETE",
                                "type": 4
                            },
                            {
                                "meta": {
                                    "title": "资产操作"
                                },
                                "name": "实体设备回收站删除",
                                "path": "/api/v1/device/purge",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "导出"
                        },
                        "name": "export_facilities",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "资产操作"
                                },
                                "name": "实体设备导出",
                                "path": "/api/v1/device/export",
                                "method": "GET",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "字段校准"
                        },
                        "name": "calibrate_fields_facilities",
                        "type": 3,
                        "buttonType": "edit",
                        "children": [
                            {
                                "meta": {
                                    "title": "资产操作"
                                },
                                "name": "实体设备字段校准",
                                "path": "/api/v1/device/manual_calibration",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "查询"
                        },
                        "name": "query_facilities",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "资产操作"
                                },
                                "name": "实体设备查询",
                                "path": "/api/v1/device",
                                "method": "GET",
                                "type": 4
                            }
                        ]
                    }
                ]
            },
            {
                "path": "personnelLedger",
                "name": "PersonnelLedger",
                "meta": {
                    "title": "人员台账",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-personnelLedger|svg",
                    "orderNo": 3
                },
                "component": "asset/personnelLedger/index.vue",
                "children": [
                    {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_ledger",
                        "type": 3,
                        "buttonType": "delete",
                        "children": [
                            {
                                "meta": {
                                    "title": "资产操作"
                                },
                                "name": "人员信息删除",
                                "path": "/api/v1/personnel_ledger",
                                "method": "DELETE",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_department",
                        "type": 3,
                        "buttonType": "delete",
                        "children": [
                            {
                                "meta": {
                                    "title": "资产操作"
                                },
                                "name": "部门信息删除",
                                "path": "/api/v1/personnel_departments",
                                "method": "DELETE",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "导入"
                        },
                        "name": "import_ledger",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "资产操作"
                                },
                                "name": "资产导入",
                                "path": "/api/v1/task/sync/all",
                                "method": "GET",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "添加"
                        },
                        "name": "add_department",
                        "type": 3,
                        "buttonType": "add",
                        "children": [
                            {
                                "meta": {
                                    "title": "资产操作"
                                },
                                "name": "部门信息添加",
                                "path": "/api/v1/personnel_departments",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "字段校准"
                        },
                        "name": "calibrate_fields_ledger",
                        "type": 3,
                        "buttonType": "edit",
                        "children": [
                            {
                                "meta": {
                                    "title": "资产操作"
                                },
                                "name": "人员信息字段校准",
                                "path": "/api/v1/personnel_ledger/manual_calibration",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "PGID设置"
                        },
                        "name": "pgid_setting_ledger",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "资产操作"
                                },
                                "name": "人员信息PGID设置",
                                "path": "/api/v1/settings/people_pgid",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "查询"
                        },
                        "name": "query_ledger",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "资产操作"
                                },
                                "name": "人员信息查询",
                                "path": "/api/v1/personnel_ledger",
                                "method": "GET",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "查询"
                        },
                        "name": "query_department",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "资产操作"
                                },
                                "name": "部门信息查询",
                                "path": "/api/v1/personnel_departments",
                                "method": "GET",
                                "type": 4
                            }
                        ]
                    }
                ]
            },
            {
                "path": "business",
                "name": "Business",
                "meta": {
                    "title": "业务系统",
                    "ignoreKeepAlive": true,
                    "icon": "layout-sliderMenu-business|svg",
                    "orderNo": 4
                },
                "component": "statisticalAnalysis/business/index.vue"
            },
            {
                "path": "businessSystem",
                "name": "BusinessSystem",
                "meta": {
                    "title": "业务系统",
                    "ignoreKeepAlive": true,
                    "icon": "layout-sliderMenu-business|svg",
                    "orderNo": 4
                },
                "component": "asset/businessSystem/index.vue",
                "children": [
                    {
                        "path": "businessPortrait",
                        "name": "BusinessPortrait",
                        "meta": {
                            "currentActiveMenu": "/assetCenter/businessSystem",
                            "ignoreKeepAlive": false,
                            "icon": "layout-sliderMenu-businessSystem|svg",
                            "hideMenu": true,
                            "dynamicLevel": 3,
                            "realPath": "/assetCenter/businessSystem/businessPortrait",
                            "title": "业务画像"
                        },
                        "component": "asset/businessPortrait/index.vue",
                        "children": [
                            {
                                "meta": {
                                    "title": "导出"
                                },
                                "name": "export_vul_business_system",
                                "type": 3
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "加入黑名单"
                        },
                        "name": "add_to_blacklist_business_system",
                        "type": 3,
                        "buttonType": "add"
                    },
                    {
                        "meta": {
                            "title": "添加"
                        },
                        "name": "add_business_assets_business_system",
                        "type": 3,
                        "buttonType": "add"
                    },
                    {
                        "meta": {
                            "title": "导入"
                        },
                        "name": "import_business_assets_business_system",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "导出"
                        },
                        "name": "export_business_assets_business_system",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "编辑"
                        },
                        "name": "edit_business_assets_business_system",
                        "type": 3,
                        "buttonType": "edit"
                    },
                    {
                        "meta": {
                            "title": "数据处理策略"
                        },
                        "name": "business_strategy_business_system",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "确认"
                        },
                        "name": "confirm_business_assets_business_system",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "移除黑名单"
                        },
                        "name": "remove_from_blacklist_business_system",
                        "type": 3,
                        "buttonType": "delete"
                    },
                     {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_business_system",
                        "type": 3,
                        "buttonType": "delete"
                    }
                ]
            },
            {
                "path": "departments",
                "name": "Departments",
                "meta": {
                    "title": "业务部门资产",
                    "ignoreKeepAlive": true,
                    "icon": "layout-sliderMenu-personnelLedger|svg",
                    "orderNo": 5
                },
                "component": "asset/departments/index.vue"
            },
            {
                "path": "assetClassification",
                "name": "AssetClassification",
                "meta": {
                    "title": "资产分类",
                    "ignoreKeepAlive": true,
                    "icon": "layout-sliderMenu-networkIp|svg",
                    "orderNo": 6
                },
                "component": "asset/assetClassification/index.vue"
            }
        ]
    },
    {
        "path": "/workbench",
        "name": "Workbench",
        "component": "LAYOUT",
        "redirect": "redirect",
        "meta": {
            "orderNo": 1,
            "icon": "layout-menu-workbench|svg",
            "title": "工作台",
            "hideMenu": false
        },
        "children": [
            {
                "path": "index",
                "name": "workSpaceIndex",
                "meta": {
                    "title": "工作台首页",
                    "ignoreKeepAlive": false,
                    "noSideBar": true,
                    "icon": "ion:settings-outline",
                    "orderNo": 0
                },
                "component": "/workbench/homePage/index.vue",
                "children": [
                    {
                        "meta": {
                            "title": "快捷入口跳入"
                        },
                        "name": "quick_access",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "设置豁免率"
                        },
                        "name": "set_rate",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "编辑计算配置项"
                        },
                        "name": "edit_calculation_config",
                        "type": 3,
                        "buttonType": "edit"
                    }
                ]
            },
            {
                "path": "exempt",
                "name": "Exempt",
                "meta": {
                    "title": "豁免",
                    "noSideBar": true,
                    "orderNo": 1
                },
                "component": "/workbench/homePage/components/Exempt/Exempt.vue",
                "children": [
                    {
                        "meta": {
                            "title": "豁免保存"
                        },
                        "name": "save_rate_workbench",
                        "type": 3
                    }
                ]
            }
        ]
    },
    {
        "path": "/assetCentralizedControl",
        "name": "AssetCentralizedControl",
        "component": "LAYOUT",
        "redirect": "redirect",
        "meta": {
            "orderNo": 10,
            "title": "集中管控",
            "ignoreKeepAlive": false,
            "icon": "layout-menu-assetCentralizedControl|svg",
            "hideMenu": false
        },
        "children": [
            {
                "path": "upgradePlatform",
                "name": "UpgradePlatform",
                "meta": {
                    "title": "上级升级平台",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-upgradePlatform|svg",
                    "hideMenu": false,
                    "orderNo": 0
                },
                "component": "/assetCentralizedControl/upgradePlatform/index.vue",
                "children": [
                    {
                        "meta": {
                            "title": "上传"
                        },
                        "name": "upload",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "下发"
                        },
                        "name": "issued",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "升级"
                        },
                        "name": "subProbe_upgrade",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_upgrade",
                        "buttonType": "delete",
                        "type": 3
                    }
                ]
            },
            {
                "path": "subUpgradePlatform",
                "name": "SubUpgradePlatform",
                "meta": {
                    "title": "下级升级平台",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-subUpgradePlatform|svg",
                    "hideMenu": false,
                    "orderNo": 1
                },
                "component": "/assetCentralizedControl/subUpgradePlatform/index.vue",
                "children": [
                    {
                        "meta": {
                            "title": "下载"
                        },
                        "name": "download_subUpgradePlatform",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "升级"
                        },
                        "name": "update_subUpgradePlatform",
                        "type": 3
                    }
                ]
            },
            {
                "path": "dataCollection",
                "name": "DataCollection",
                "meta": {
                    "title": "数据收集",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-dataAcquisition|svg",
                    "hideMenu": false,
                    "orderNo": 2
                },
                "component": "/groupManagement/dataCollection/index.vue",
                "children": [
                    {
                        "meta": {
                            "title": "添加"
                        },
                        "name": "add_node_dataCollection",
                        "type": 3,
                        "buttonType": "add"
                    },
                    {
                        "meta": {
                            "title": "编辑"
                        },
                        "name": "edit_node_dataCollection",
                        "type": 3,
                        "buttonType": "edit"
                    },
                    {
                        "meta": {
                            "title": "同步"
                        },
                        "name": "sync_upgrade_dataCollection",
                        "type": 3
                    }
                ]
            },
            {
                "path": "dataAppear",
                "name": "DataAppear",
                "meta": {
                    "title": "数据上报任务",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-dataAcquisition|svg",
                    "hideMenu": false,
                    "orderNo": 3
                },
                "component": "/groupManagement/dataAppear/index.vue",
                "children": [
                    {
                        "path": "detail",
                        "name": "DataAppearDetail",
                        "meta": {
                            "currentActiveMenu": "/assetCentralizedControl/dataAppear",
                            "title": "数据上报任务详情",
                            "ignoreKeepAlive": false,
                            "hideMenu": true
                        },
                        "component": "/groupManagement/dataAppear/detail/index.vue"
                    }
                ]
            },
            {
                "path": "dataCenter",
                "name": "DataCenter",
                "meta": {
                    "title": "数据中心",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-dataCenter|svg",
                    "hideMenu": false,
                    "orderNo": 4
                },
                "component": "/groupManagement/dataCenter/index.vue",
                "children": [
                    {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_dataCenter",
                        "type": 3,
                        "buttonType": "delete"
                    }
                ]
            }
        ]
    },
    {
        "path": "/dataGovernance",
        "name": "DataGovernance",
        "component": "LAYOUT",
        "redirect": "redirect",
        "meta": {
            "orderNo": 6,
            "icon": "layout-menu-DataGovernance|svg",
            "title": "数据治理",
            "ignoreKeepAlive": false
        },
        "children": [
            {
                "path": "dataAcquisition",
                "name": "DataAcquisition",
                "meta": {
                    "title": "数据采集管理",
                    "ignoreKeepAlive": true,
                    "icon": "layout-sliderMenu-dataAcquisition|svg",
                    "orderNo": 0
                },
                "component": "/dataGovernance/dataAcquisition/index.vue",
                "children": [
                    {
                        "path": "dataSync/",
                        "name": "DataSync",
                        "meta": {
                            "title": "数据同步",
                            "currentActiveMenu": "/dataGovernance/dataAcquisition",
                            "ignoreKeepAlive": false,
                            "icon": "ion:anewspaper-outline",
                            "hideMenu": true,
                            "dynamicLevel": 3,
                            "realPath": "/dataGovernance/dataAcquisition/dataSync"
                        },
                        "children": [
                            {
                                "meta": {
                                    "title": "立即同步"
                                },
                                "name": "sync_now",
                                "type": 3,
                                "children":  [
                                    {
                                        "meta": {
                                            "title": "同步操作"
                                        },
                                        "name": "立即同步操作",
                                        "path": "/api/v1/task/sync",
                                        "type": 4,
                                        "method": "GET"
                                    }
                                ]
                            },
                            {
                                "meta": {
                                    "title": "同步记录"
                                },
                                "name": "sync_records",
                                "type": 3
                            },
                            {
                                "meta": {
                                    "title": "校验节点状态"
                                },
                                "name": "validate_node_status",
                                "type": 3
                            },
                            {
                                "meta": {
                                    "title": "编辑"
                                },
                                "name": "edit_data_source",
                                "type": 3,
                                "buttonType": "edit",
                                "children": [
                                    {
                                        "meta": {
                                            "title": "同步操作"
                                        },
                                        "name": "编辑节点",
                                        "path": "/api/v1/data/node/frame",
                                        "type": 4,
                                        "method": "PUT"
                                    },
                                    {
                                        "meta": {
                                            "title": "同步操作"
                                        },
                                        "name": "编辑FOEye节点",
                                        "path": "/api/v1/data/node/foeye",
                                        "type": 4,
                                        "method": "PUT"
                                    },
                                    {
                                        "meta": {
                                            "title": "同步操作"
                                        },
                                        "name": "编辑D01节点",
                                        "path": "/api/v1/data/node/d01",
                                        "type": 4,
                                        "method": "PUT"
                                    },
                                    {
                                        "meta": {
                                            "title": "同步操作"
                                        },
                                        "name": "编辑华为云HK节点",
                                        "path": "/api/v1/data/node/huawei_hk_cloud",
                                        "type": 4,
                                        "method": "PUT"
                                    },
                                    {
                                        "meta": {
                                            "title": "同步操作"
                                        },
                                        "name": "编辑中移飞书节点",
                                        "path": "/api/v1/data/node/zhongyi/feishu",
                                        "type": 4,
                                        "method": "PUT"
                                    },
                                    {
                                        "meta": {
                                            "title": "同步操作"
                                        },
                                        "name": "编辑ForadarSass节点",
                                        "path": "/api/v1/data/node/foradar_saas",
                                        "type": 4,
                                        "method": "PUT"
                                    },
                                    {
                                        "meta": {
                                            "title": "同步操作"
                                        },
                                        "name": "编辑钉钉节点",
                                        "path": "/api/v1/data/node/dingtalk",
                                        "type": 4,
                                        "method": "PUT"
                                    },
                                    {
                                        "meta": {
                                            "title": "同步操作"
                                        },
                                        "name": "编辑青藤云节点",
                                        "path": "/api/v1/data/node/qty",
                                        "type": 4,
                                        "method": "PUT"
                                    },
                                    {
                                        "meta": {
                                            "title": "同步操作"
                                        },
                                        "name": "编辑蓝鲸CMDB节点",
                                        "path": "/api/v1/data/node/bk_cmdb",
                                        "type": 4,
                                        "method": "PUT"
                                    },
                                    {
                                        "meta": {
                                            "title": "同步操作"
                                        },
                                        "name": "编辑齐治堡垒机节点",
                                        "path": "/api/v1/data/node/qizhi/uaudithost",
                                        "type": 4,
                                        "method": "PUT"
                                    },
                                    {
                                        "meta": {
                                            "title": "同步操作"
                                        },
                                        "name": "编辑阿里云云盾节点",
                                        "path": "/api/v1/data/node/aliyun/cloud",
                                        "type": 4,
                                        "method": "PUT"
                                    },
                                    {
                                        "meta": {
                                            "title": "同步操作"
                                        },
                                        "name": "编辑微步节点",
                                        "path": "/api/v1/data/node/weibu",
                                        "type": 4,
                                        "method": "PUT"
                                    },
                                    {
                                        "meta": {
                                            "title": "同步操作"
                                        },  
                                        "name": "编辑马赫谷节点",
                                        "path": "/api/v1/data/node/mach_lake",
                                        "type": 4,
                                        "method": "PUT"
                                    },
                                    {
                                        "meta": {
                                            "title": "同步操作"
                                        },
                                        "name": "编辑XRay节点",
                                        "path": "/api/v1/data/node/x_ray",
                                        "type": 4,
                                        "method": "PUT"
                                    },
                                    {
                                        "meta": {
                                            "title": "同步操作"
                                        },
                                        "name": "编辑中移飞书节点",
                                        "path": "/api/v1/data/node/zhongyi/feishu",
                                        "type": 4,
                                        "method": "PUT"
                                    },
                                    {
                                        "meta": {
                                            "title": "同步操作"
                                        },
                                        "name": "编辑隆基节点",
                                        "path": "/api/v1/data/node/longi_waf",
                                        "type": 4,
                                        "method": "PUT"
                                    }
                                ]
                            },
                            {
                                "meta": {
                                    "title": "删除"
                                },
                                "name": "delete_data_source",
                                "type": 3,
                                "buttonType": "delete",
                                "children": [
                                    {
                                        "meta": {
                                            "title": "同步操作"
                                        },
                                        "name": "删除节点",
                                        "path": "/api/v1/data/node/frame",
                                        "type": 4,
                                        "method": "DELETE"
                                    },
                                    {
                                        "meta": {
                                            "title": "同步操作"
                                        },
                                        "name": "删除节点",
                                        "path": "/api/v1/data/nodes",
                                        "type": 4,
                                        "method": "DELETE"
                                    }
                                ]
                            },
                            {
                                "meta": {
                                    "title": "清除数据"
                                },
                                "name": "clear_data",
                                "type": 3
                            },
                            {
                                "meta": {
                                    "title": "重启"
                                },
                                "name": "restart_dataSync",
                                "type": 3
                            },
                            {
                                "meta": {
                                    "title": "关机"
                                },
                                "name": "shutdown_dataSync",
                                "type": 3
                            }
                        ],
                        "component": "/dataGovernance/dataAcquisition/components/dataSync/dataSync.vue"
                    },
                    {
                        "meta": {
                            "title": "高级配置"
                        },
                        "name": "advanced_config",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "高级配置操作",
                                "path": "/api/v1/settings/config",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "一键同步"
                        },
                        "name": "oneclick_sync",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "一键同步操作",
                                "path": "/api/v1/task/sync/all",
                                "type": 4,
                                "method": "GET"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "新增数据源"
                        },
                        "name": "add_data_source",
                        "type": 3,
                        "buttonType": "add",
                        "children": [
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "新增数据源",
                                "path": "/api/v1/data/source",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "添加节点"
                        },
                        "name": "add_node",
                        "type": 3,
                        "buttonType": "add",
                        "children": [
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "添加节点",
                                "path": "/api/v1/data/node/frame",
                                "type": 4,
                                "method": "POST"
                            },
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "添加FOEye节点",
                                "path": "/api/v1/data/node/foeye",
                                "type": 4,
                                "method": "POST"
                            },
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "添加D01节点",
                                "path": "/api/v1/data/node/d01",
                                "type": 4,
                                "method": "POST"
                            },
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "添加华为云HK节点",
                                "path": "/api/v1/data/node/huawei_hk_cloud",
                                "type": 4,
                                "method": "POST"
                            },
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "添加中移飞书节点",
                                "path": "/api/v1/data/node/zhongyi/feishu",
                                "type": 4,
                                "method": "POST"
                            },
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "添加ForadarSass节点",
                                "path": "/api/v1/data/node/foradar_saas",
                                "type": 4,
                                "method": "POST"
                            },
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "添加钉钉节点",
                                "path": "/api/v1/data/node/dingtalk",
                                "type": 4,
                                "method": "POST"
                            },
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "添加青藤云节点",
                                "path": "/api/v1/data/node/qty",
                                "type": 4,
                                "method": "POST"
                            },
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "添加蓝鲸CMDB节点",
                                "path": "/api/v1/data/node/bk_cmdb",
                                "type": 4,
                                "method": "POST"
                            },
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "添加齐治堡垒机节点",
                                "path": "/api/v1/data/node/qizhi/uaudithost",
                                "type": 4,
                                "method": "POST"
                            },
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "添加阿里云云盾节点",
                                "path": "/api/v1/data/node/aliyun/cloud",
                                "type": 4,
                                "method": "POST"
                            },
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "添加微步节点",
                                "path": "/api/v1/data/node/weibu",
                                "type": 4,
                                "method": "POST"
                            },
                            {
                                "meta": {
                                    "title": "同步操作"
                                },  
                                "name": "添加马赫谷节点",
                                "path": "/api/v1/data/node/mach_lake",
                                "type": 4,
                                "method": "POST"
                            },
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "添加XRay节点",
                                "path": "/api/v1/data/node/x_ray",
                                "type": 4,
                                "method": "POST"
                            },
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "添加中移飞书节点",
                                "path": "/api/v1/data/node/zhongyi/feishu",
                                "type": 4,
                                "method": "POST"
                            },
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "添加隆基节点",
                                "path": "/api/v1/data/node/longi_waf",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "文件导入"
                        },
                        "name": "file_import_dataAcquisition",
                        "type": 3
                    }
                ]
            },
            {
                "path": "dataFusion",
                "name": "DataFusion",
                "meta": {
                    "title": "数据融合管理",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-dataFusion|svg",
                    "orderNo": 1
                },
                "children": [
                    {
                        "meta": {
                            "title": "立即融合"
                        },
                        "name": "immediate_fusion",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "数据融合",
                                "path": "/api/v1/strategy/triggerMerge",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "编辑"
                        },
                        "name": "edit_data_fusion",
                        "type": 3,
                        "buttonType": "edit"
                    }
                ],
                "component": "/dataGovernance/dataFusion/index.vue"
            },
            {
                "path": "dataExtraction",
                "name": "DataExtraction",
                "meta": {
                    "title": "设备提取策略",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-dataExtraction|svg",
                    "orderNo": 2
                },
                "children": [
                    {
                        "meta": {
                            "title": "保存基础配置"
                        },
                        "name": "save_basic_config",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "DHCP操作",
                                "path": "/api/v1/strategy/device",
                                "type": 4,
                                "method": "PUT"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "编辑设备提取策略"
                        },
                        "name": "edit_data_extraction",
                        "type": 3,
                        "buttonType": "edit",
                        "children": [
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "设备提取策略设置",
                                "path": "/api/v1/strategy/create",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "立即提取"
                        },
                        "name": "extract_now",
                        "type": 3
                    }
                ],
                "component": "/dataGovernance/dataExtraction/index.vue"
            },
            {
                "path": "loopHoleConfigManagement",
                "name": "loopHoleConfigManagement",
                "meta": {
                    "title": "漏洞配置管理",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-loopholeManage|svg",
                    "orderNo": 3
                },
                "children": [
                    {
                        "meta": {
                            "title": "配置确认"
                        },
                        "name": "config_confirm",
                        "type": 3,
                        "buttonType": "edit",
                        "children": [
                            {
                                "meta": {
                                    "title": "同步操作"
                                },
                                "name": "优先级影响因子配置",
                                "path": "/api/v1/strategy/poc_settings_update",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "恢复默认"
                        },
                        "name": "restore_default",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "手动更新"
                        },
                        "name": "manual_update",
                        "type": 3
                    }
                ],
                "component": "/dataGovernance/loopHoleConfigManage/index.vue"
            }
        ]
    },
    {
        "path": "/settingInfo",
        "name": "SettingInfo",
        "component": "LAYOUT",
        "redirect": "redirect",
        "meta": {
            "orderNo": 9,
            "icon": "layout-menu-settingInfo|svg",
            "title": "信息配置"
        },
        "children": [
            {
                "path": "sysNetworkArea",
                "name": "SysNetworkArea",
                "meta": {
                    "title": "探测区域管理",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-networkArea|svg",
                    "orderNo": 0
                },
                "children": [
                    {
                        "meta": {
                            "title": "添加探测区域"
                        },
                        "name": "add_sys_area",
                        "type": 3,
                        "buttonType": "add",
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "添加探测区域",
                                "path": "/api/v1/settings/network_areas",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_sys_area",
                        "type": 3,
                        "buttonType": "delete",
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "删除探测区域",
                                "path": "/api/v1/settings/network_areas",
                                "type": 4,
                                "method": "DELETE"
                            }
                        ]
                    }
                ],
                "component": "/infoSetting/networkArea/index.vue"
            },
            {
                "path": "mappingRelationship",
                "name": "MappingRelationship",
                "meta": {
                    "title": "映射关系解析",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-mappingRelationship|svg",
                    "orderNo": 1
                },
                "component": "/infoSetting/mappingRelationship/index.vue",
                "children": [
                    {
                        "path": "recordList",
                        "name": "RecordList",
                        "meta": {
                            "title": "导入记录",
                            "ignoreKeepAlive": false,
                            "hideMenu": true,
                            "currentActiveMenu": "/settingInfo/mappingRelationship",
                            "realPath": "/settingInfo/mappingRelationship"
                        },
                        "component": "/infoSetting/mappingRelationship/components/recordList.vue"
                    },
                    {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_mapping_relationship",
                        "type": 3,
                        "buttonType": "delete"
                    },
                    {
                        "meta": {
                            "title": "新增映射类型"
                        },
                        "name": "add_mapping_type",
                        "type": 3,
                        "buttonType": "add",
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "新增映射类型",
                                "path": "/api/v1/net_mapping/area",
                                "type": 4,
                                "method": "PUT"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "导入"
                        },
                        "name": "import_mapping_relationship",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "单条导入",
                                "path": "/api/v1/net_mapping/create",
                                "type": 4,
                                "method": "POST"
                            },
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "文件导入",
                                "path": "/api/v1/net_mapping/import",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "资产中心联动更新"
                        },
                        "name": "asset_center_update",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "查看导入记录"
                        },
                        "name": "view_import_records",
                        "type": 3
                    }
                ]
            },
            {
                "path": "businessAssets",
                "name": "BusinessAssets",
                "meta": {
                    "title": "业务信息配置",
                    "ignoreKeepAlive": true,
                    "icon": "layout-sliderMenu-business|svg",
                    "orderNo": 2
                },
                "children": [
                    {
                        "meta": {
                            "title": "加入黑名单"
                        },
                        "name": "add_to_blacklist",
                        "type": 3,
                        "buttonType": "add",
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "加入黑名单",
                                "path": "/api/v1/business/blacklist",
                                "type": 4,
                                "method": "DELETE"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "添加"
                        },
                        "name": "add_business_assets",
                        "type": 3,
                        "buttonType": "add",
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "添加业务信息",
                                "path": "/api/v1/business",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "导入"
                        },
                        "name": "import_business_assets",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "导入业务信息",
                                "path": "/api/v1/business/import",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "导出"
                        },
                        "name": "export_business_assets",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "导出业务信息",
                                "path": "/api/v1/business/export",
                                "type": 4,
                                "method": "GET"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "编辑"
                        },
                        "name": "edit_business_assets",
                        "type": 3,
                        "buttonType": "edit",
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "编辑业务批量信息",
                                "path": "/api/v1/business/batch",
                                "type": 4,
                                "method": "PUT"
                            },
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "编辑业务信息",
                                "path": "/api/v1/business",
                                "type": 4,
                                "method": "PUT"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "数据处理策略"
                        },
                        "name": "business_strategy",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "数据策略更改",
                                "path": "/api/v1/business_strategy",
                                "type": 4,
                                "method": "PUT"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "确认"
                        },
                        "name": "confirm_business_assets",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "确认业务信息",
                                "path": "/api/v1/business/set_credible",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "移除黑名单"
                        },
                        "name": "remove_from_blacklist",
                        "type": 3,
                        "buttonType": "delete",
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "移除黑名单",
                                "path": "/api/v1/business/unconfirmed_list",
                                "type": 4,
                                "method": "DELETE"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_business_assets",
                        "type": 3,
                        "buttonType": "delete",
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "删除业务系统",
                                "path": "/api/v1/business/purge",
                                "type": 4,
                                "method": "DELETE"
                            }
                        ]
                    }
                ],
                "component": "/asset/businessAssets/index.vue"
            },
            {
                "path": "tags",
                "name": "Tags",
                "meta": {
                    "title": "标签规则管理",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-tag|svg",
                    "orderNo": 3
                },
                "children": [
                    {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_rule",
                        "type": 3,
                        "buttonType": "delete",
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "删除规则操作",
                                "path": "/api/v1/tag_rules",
                                "type": 4,
                                "method": "DELETE"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "添加规则"
                        },
                        "name": "add_rule",
                        "type": 3,
                        "buttonType": "add",
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "添加规则操作",
                                "path": "/api/v1/tag_rules",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "编辑"
                        },
                        "name": "edit_rule",
                        "type": 3,
                        "buttonType": "edit"
                    },
                    {
                        "meta": {
                            "title": "添加标签"
                        },
                        "name": "add_tag",
                        "type": 3,
                        "buttonType": "add",
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "添加标签操作",
                                "path": "/api/v1/tags",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    }
                ],
                "component": "/infoSetting/tags/index.vue"
            },
            {
                "path": "ipRanges",
                "name": "IpRanges",
                "meta": {
                    "title": "IP段管理",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-ipRanges|svg",
                    "orderNo": 4               
                },
                "children": [
                    {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_ip_ranges",
                        "type": 3,
                        "buttonType": "delete",
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "删除IP段操作",
                                "path": "/api/v1/ip_ranges",
                                "type": 4,
                                "method": "DELETE"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "添加"
                        },
                        "name": "add_ip_ranges",
                        "type": 3,
                        "buttonType": "add",
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "添加IP段操作",
                                "path": "/api/v1/ip_ranges",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "导入"
                        },
                        "name": "import_ip_ranges",
                        "type": 3          
                    },
                    {
                        "meta": {
                            "title": "导出"
                        },
                        "name": "export_ip_ranges",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "导出IP段操作",
                                "path": "/api/v1/ip_ranges/export",
                                "type": 4,
                                "method": "GET"
                            }
                        ]           
                    },
                    {
                        "meta": {
                            "title": "手动更新"
                        },
                        "name": "update_ip_ranges",
                        "type": 3                    
                    },
                    {
                        "meta": {
                            "title": "编辑"
                        },
                        "name": "edit_ip_ranges",
                        "type": 3,
                        "buttonType": "edit",
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "修改IP段操作",
                                "path": "/api/v1/ip_ranges",
                                "type": 4,
                                "method": "PUT"
                            }
                        ]
                    }
                ],
                "component": "/infoSetting/ipRanges/index.vue"
            },
            {
                "path": "customColumn",
                "name": "CustomColumn",
                "meta": {
                    "title": "自定义列配置",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-customColumn|svg",
                    "orderNo": 5               
                },
                "children": [
                    {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_custom_column",
                        "type": 3,
                        "buttonType": "delete",
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "删除自定义列操作",
                                "path": "/api/v1/custom_columns",
                                "type": 4,
                                "method": "DELETE"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "添加"
                        },
                        "name": "add_custom_column",
                        "type": 3,
                        "buttonType": "add",
                        "children": [
                            {
                                "meta": {
                                    "title": "信息配置操作"
                                },
                                "name": "添加自定义列操作",
                                "path": "/api/v1/custom_columns",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "编辑"
                        },
                        "name": "edit_custom_column",
                        "type": 3,
                        "buttonType": "edit"
                    }
                ],
                "component": "/infoSetting/customColumn/index.vue"
            }
        ]
    },
    {
        "path": "/loophole",
        "name": "Loophole",
        "component": "LAYOUT",
        "redirect": "redirect",
        "meta": {
            "orderNo": 4,
            "icon": "layout-menu-loopholeManage|svg",
            "ignoreKeepAlive": false,
            "title": "漏洞风险"
        },
        "children": [
            {
                "path": "loopholeRelevance",
                "name": "LoopholeRelevance",
                "meta": {
                    "title": "漏洞聚合",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-loopholeManage|svg",
                    "orderNo": 0
                },
                "component": "/loophole/loopholeRelevance/index.vue",
                "children": [
                    {
                        "path": "holeRelevanceDetail",
                        "name": "HoleRelevanceDetail",
                        "meta": {
                            "currentActiveMenu": "/loophole/loopholeRelevance",
                            "ignoreKeepAlive": false,
                            "icon": "layout-sliderMenu-internalIP|svg",
                            "hideMenu": true,
                            "dynamicLevel": 3,
                            "realPath": "/loophole/loopholeRelevance/holeRelevanceDetail",
                            "title": "漏洞关联详情"
                        },
                        "component": "/loophole/loopholeRelevance/components/holeRelevanceDetail.vue"
                    },
                    {
                        "meta": {
                            "title": "导出"
                        },
                        "name": "export_loophole_relevance",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "漏洞操作"
                                },
                                "name": "漏洞聚合导出",
                                "path": "/api/v1/threat_center/relevance/list/export",
                                "method": "GET",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "查询"
                        },
                        "name": "query_loophole_relevance",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "漏洞操作"
                                },
                                "name": "漏洞聚合查询",
                                "path": "/api/v1/threat_center/relevance/list",
                                "method": "GET",
                                "type": 4
                            }
                        ]
                    }
                ]
            },
            {
                "path": "loopholeManage",
                "name": "LoopholeManage",
                "meta": {
                    "title": "漏洞管理",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-loopholeManage|svg",
                    "orderNo": 1
                },
                "children": [
                    {
                        "meta": {
                            "title": "单条导入"
                        },
                        "name": "single_import_loophole_manage",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "漏洞操作"
                                },
                                "name": "单条漏洞导入",
                                "path": "/api/v1/threat_center",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "批量导入"
                        },
                        "name": "batch_import_loophole_manage",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "导出"
                        },
                        "name": "export_loophole_manage",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "漏洞操作"
                                },
                                "name": "漏洞导出",
                                "path": "/api/v1/threat_center/export",
                                "method": "GET",
                                "type": 4
                            }
                        ]
                    },
                    
                    {
                        "meta": {
                            "title": "派发"
                        },
                        "name": "paifa_loophole_manage",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "漏洞操作"
                                },
                                "name": "漏洞派发/转交",
                                "path": "/api/v1/threat_center/distribute",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "误报"
                        },
                        "name": "wubao_loophole_manage",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "漏洞操作"
                                },
                                "name": "漏洞批量操作",
                                "path": "/api/v1/threat_center/status_operate",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "无法修复"
                        },
                        "name": "wufaxiufu_loophole_manage",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "漏洞操作"
                                },
                                "name": "漏洞批量操作",
                                "path": "/api/v1/threat_center/status_operate",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "修复完成"
                        },
                        "name": "xiufuwancheng_loophole_manage",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "漏洞操作"
                                },
                                "name": "漏洞批量操作",
                                "path": "/api/v1/threat_center/status_operate",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "转交"
                        },
                        "name": "zhuanjiao_loophole_manage",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "漏洞操作"
                                },
                                "name": "漏洞派发/转交",
                                "path": "/api/v1/threat_center/distribute",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "延时"
                        },
                        "name": "yanshi_loophole_manage",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "漏洞操作"
                                },
                                "name": "漏洞批量操作",
                                "path": "/api/v1/threat_center/status_operate",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "催促"
                        },
                        "name": "cuicu_loophole_manage",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "漏洞操作"
                                },
                                "name": "漏洞批量操作",
                                "path": "/api/v1/threat_center/status_operate",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "复测"
                        },
                        "name": "fuce_loophole_manage",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "漏洞操作"
                                },
                                "name": "漏洞复测",
                                "path": "/api/v1/threat_center/retest",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "字段校准"
                        },
                        "name": "calibrate_fields_manage",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "漏洞操作"
                                },
                                "name": "漏洞字段校准",
                                "path": "/api/v1/threat_center/manual_calibration",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "漏洞自动派发"
                        },
                        "name": "loopthreat_auto_dispatch",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "漏洞操作"
                                },
                                "name": "漏洞自动派发",
                                "path": "/api/v1/threat_center/auto_dispatch",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_loophole_manage",
                        "type": 3,
                        "buttonType": "delete",
                        "children": [
                            {
                                "meta": {
                                    "title": "漏洞操作"
                                },
                                "name": "漏洞删除",
                                "path": "/api/v1/threat_center/delete_to_recycle_bin",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "查询"
                        },
                        "name": "query_loophole_manage",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "漏洞操作"
                                },
                                "name": "漏洞查询",
                                "path": "/api/v1/threat_center",
                                "method": "GET",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "上传附件"
                        },
                        "name": "upload_file_loophole",
                        "type": 3,
                        "buttonType": "add"
                    },
                    {
                        "meta": {
                            "title": "删除附件"
                        },
                        "name": "delete_file_loophole",
                        "type": 3,
                        "buttonType": "delete"
                    }
                ],
                "component": "/loophole/loopholeManage/index.vue"
            },
            {
                "path": "complianceRisks",
                "name": "ComplianceRisks",
                "meta": {
                    "title": "合规风险",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-complianceRisks|svg",
                    "orderNo": 2
                },
                "children": [
                    {
                        "meta": {
                            "title": "派发"
                        },
                        "name": "paifa_compliance_risks",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "合规风险操作"
                                },
                                "name": "合规风险派发/转交",
                                "path": "/api/v1/compliance_monitor_tasks/distribute",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "误报"
                        },
                        "name": "wubao_compliance_risks",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "合规风险操作"
                                },
                                "name": "合规风险批量操作",
                                "path": "/api/v1/compliance_monitor_tasks/status_operate",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "无法修复"
                        },
                        "name": "wufaxiufu_compliance_risks",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "合规风险操作"
                                },
                                "name": "合规风险批量操作",
                                "path": "/api/v1/compliance_monitor_tasks/status_operate",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "转交"
                        },
                        "name": "zhuanjiao_compliance_risks",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "合规风险操作"
                                },
                                "name": "合规风险派发/转交",
                                "path": "/api/v1/compliance_monitor_tasks/distribute",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "延时"
                        },
                        "name": "yanshi_compliance_risks",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "合规风险操作"
                                },
                                "name": "合规风险批量操作",
                                "path": "/api/v1/compliance_monitor_tasks/status_operate",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "催促"
                        },
                        "name": "cuicu_compliance_risks",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "合规风险操作"
                                },
                                "name": "合规风险批量操作",
                                "path": "/api/v1/compliance_monitor_tasks/status_operate",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "修复完成"
                        },
                        "name": "xiufuwancheng_compliance_risks",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "合规风险操作"
                                },
                                "name": "合规风险批量操作",
                                "path": "/api/v1/compliance_monitor_tasks/status_operate",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "复测"
                        },
                        "name": "fuce_compliance_risks",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "合规风险操作"
                                },
                                "name": "合规风险复测",
                                "path": "/api/v1/compliance_monitor_tasks/retest",
                                "method": "POST",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_compliance_risks",
                        "type": 3,
                        "buttonType": "delete",
                        "children": [
                            {
                                "meta": {
                                    "title": "合规风险操作"
                                },
                                "name": "合规风险删除",
                                "path": "/api/v1/compliance_monitor_tasks/records",
                                "method": "DELETE",
                                "type": 4
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "导出"
                        },
                        "name": "export_compliance_risks",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "合规风险操作"
                                },
                                "name": "合规风险导出",
                                "path": "/api/v1/compliance_monitor_tasks/records/export",
                                "method": "GET",
                                "type": 4
                            }
                        ]
                    }
                ],
                "component": "/riskCenter/complianceRisks/index.vue"
            }
        ]
    },
    {
        "path": "/taskCenter",
        "name": "TaskCenter",
        "component": "LAYOUT",
        "redirect": "redirect",
        "meta": {
            "orderNo": 5,
            "icon": "layout-menu-taskCenter|svg",
            "title": "任务中心",
            "ignoreKeepAlive": false
        },
        "children": [
            {
                "path": "dataSyncTask",
                "name": "DataSyncTask",
                "meta": {
                    "title": "数据同步任务",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-dataSyncTask|svg",
                    "orderNo": 0
                },
                "component": "/taskCenter/dataSyncTask/index.vue",
                "children": [
                    {
                        "meta": {
                            "title": "同步配置"
                        },
                        "name": "sync_config",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "再次同步"
                        },
                        "name": "resync_dataSync",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "下载"
                        },
                        "name": "download_dataSync",
                        "type": 3
                    }
                ]
            },
            {
                "path": "dataFusionTask",
                "name": "DataFusionTask",
                "meta": {
                    "title": "数据融合任务",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-dataSyncTask|svg",
                    "children": [

                    ],
                    "orderNo": 1
                },
                "component": "/taskCenter/dataFusionTask/index.vue",
                "children": [

                ]
            },
            {
                "path": "activeScan",
                "name": "ActiveScan",
                "meta": {
                    "title": "主动扫描任务",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-activeScan|svg",
                    "orderNo": 2
                },
                "children": [
                    {
                        "meta": {
                            "title": "编辑"
                        },
                        "name": "edit_scan",
                        "buttonType": "edit",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "新建任务"
                        },
                        "name": "create_task",
                        "buttonType": "add",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_active_scan",
                        "type": 3,
                        "buttonType": "delete"
                    },
                    {
                        "meta": {
                            "title": "再次执行"
                        },
                        "name": "rerun_active_scan",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "导出"
                        },
                        "name": "export_active_scan",
                        "type": 3
                    }
                ],
                "component": "/taskCenter/activeScan/index.vue"
            },
            {
                "path": "activeScanDetail/:task_id/:scan_type",
                "name": "ActiveScanDetail",
                "meta": {
                    "title": "主动扫描详情",
                    "currentActiveMenu": "/taskCenter/activeScan",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-activeScan|svg",
                    "hideMenu": true
                },
                "children": [
                    {
                        "meta": {
                            "title": "导出"
                        },
                        "name": "export_scan_detail",
                        "type": 3
                    }
                ],
                "component": "/taskCenter/activeScan/components/activeScanDetail/index.vue"
            },
            {
                "path": "childDataSyncTask",
                "name": "ChildDataSyncTask",
                "meta": {
                    "title": "数据同步子任务",
                    "currentActiveMenu": "/taskCenter/dataSyncTask",
                    "hideMenu": true,
                    "dynamicLevel": 0,
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-dataSyncTask|svg"
                },
                "component": "/taskCenter/dataSyncTask/components/taskChilTable.vue",
                "children": [

                ]
            },
            {
                "path": "dataSyncDetail",
                "name": "DataSyncDetail",
                "meta": {
                    "title": "数据同步任务详情",
                    "currentActiveMenu": "/taskCenter/dataSyncTask",
                    "ignoreKeepAlive": true,
                    "hideMenu": true,
                    "dynamicLevel": 0,
                    "realPath": "/taskCenter/dataSyncDetail"
                },
                "component": "/taskCenter/dataSyncTask/dataSyncDetail.vue"
            },
            {
                "path": "complianceMonitoring",
                "name": "ComplianceMonitoring",
                "meta": {
                    "title": "合规监测任务",
                    "icon": "layout-sliderMenu-complianceMonitoring|svg",
                    "ignoreKeepAlive": false,
                    "orderNo": 3
                },
                "component": "/taskCenter/complianceMonitoring/index.vue",
                "children": [
                    {
                        "path": "complianceMonitoringDetail/:id/:monitorId",
                        "name": "ComplianceMonitoringDetail",
                        "meta": {
                            "title": "合规监测详情",
                            "currentActiveMenu": "/taskCenter/complianceMonitoring",
                            "ignoreKeepAlive": true,
                            "hideMenu": true,
                            "dynamicLevel": 0,
                            "realPath": "/taskCenter/complianceMonitoring/complianceMonitoringDetail"
                        },
                        "children": [
                            {
                                "meta": {
                                    "title": "导出"
                                },
                                "name": "export_compliance_monitoring",
                                "type": 3
                            }
                        ],
                        "component": "/taskCenter/complianceMonitoring/components/monitoringDetail/index.vue"
                    },
                    {
                        "meta": {
                            "title": "新建监测规则"
                        },
                        "name": "create_monitoring_rule",
                        "type": 3,
                        "buttonType": "add"
                    },
                    {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_compliance_monitoring",
                        "type": 3,
                        "buttonType": "delete"
                    },
                    {
                        "meta": {
                            "title": "再次执行"
                        },
                        "name": "rerun_compliance_monitoring",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "编辑"
                        },
                        "name": "edit_compliance_monitoring",
                        "buttonType": "edit",
                        "type": 3
                    }
                ]
            }
        ]
    },
    {
        "path": "/setting",
        "name": "Setting",
        "component": "LAYOUT",
        "redirect": "redirect",
        "meta": {
            "orderNo": 100,
            "icon": "ion:settings-outline",
            "title": "系统管理",
            "isAdmin": true
        },
        "children": [
            {
                "path": "upgrade",
                "name": "Upgrade",
                "meta": {
                    "title": "系统升级",
                    "ignoreKeepAlive": false,
                    "orderNo": 0
                },
                "children": [
                    {
                        "meta": {
                            "title": "系统版本升级"
                        },
                        "name": "system_upgrade",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "系统版本升级",
                                "path": "/api/v1/system/upgrade/upload",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    }
                ],
                "component": "/setting/upgrade/index.vue"
            },
            {
                "path": "emailServer",
                "name": "EmailServer",
                "meta": {
                    "title": "邮件服务器配置",
                    "ignoreKeepAlive": false,
                    "orderNo": 1
                },
                "children": [
                    {
                        "meta": {
                            "title": "保存"
                        },
                        "name": "save_email",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "更改邮件服务配置",
                                "path": "/api/v1/settings/email",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "重置"
                        },
                        "name": "reset_email",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "重置邮件服务配置",
                                "path": "/api/v1/settings/email",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "开始测试"
                        },
                        "name": "start_testing",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "测试邮件服务配置",
                                "path": "/api/v1/settings/email/test",
                                "type": 4,
                                "method": "GET"
                            }
                        ]
                    }
                ],
                "component": "/setting/emailServer/index.vue"
            },
            {
                "path": "networkSetting",
                "name": "NetworkSetting",
                "meta": {
                    "title": "网络配置",
                    "ignoreKeepAlive": true,
                    "orderNo": 2
                },
                "children": [
                    {
                        "meta": {
                            "title": "确认"
                        },
                        "name": "confirm_networks",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "更改网络配置",
                                "path": "/api/v1/settings/networks",
                                "type": 4,
                                "method": "PUT"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "网络测试"
                        },
                        "name": "network_testing",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "网络测试",
                                "path": "/api/v1/settings/ping",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_host_config",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "删除",
                                "path": "/api/v1/settings/host_config",
                                "type": 4,
                                "method": "PUT"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "添加"
                        },
                        "name": "add_host_config",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "添加",
                                "path": "/api/v1/settings/host_config",
                                "type": 4,
                                "method": "PUT"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "编辑"
                        },
                        "name": "edit_host_config",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "编辑",
                                "path": "/api/v1/settings/host_config",
                                "type": 4,
                                "method": "PUT"
                            }
                        ]
                    }
                ],
                "component": "/setting/networkSetting/index.vue"
            },
            {
                "path": "scanBanTime",
                "name": "ScanBanTime",
                "meta": {
                    "title": "禁扫时间配置",
                    "ignoreKeepAlive": true,
                    "orderNo": 3
                },
                "children": [
                    {
                        "meta": {
                            "title": "确认"
                        },
                        "name": "confirm_ban",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "更改禁扫时间配置",
                                "path": "/api/v1/system/scan/ban",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    }
                ],
                "component": "/setting/scanBanTime/index.vue"
            },
            {
                "path": "blabkWhiteConfiguration",
                "name": "BlabkWhiteConfiguration",
                "meta": {
                    "title": "黑白名单配置",
                    "ignoreKeepAlive": true,
                    "orderNo": 4
                },
                "children": [
                    {
                        "meta": {
                            "title": "添加"
                        },
                        "name": "add_bwconfig",
                        "type": 3,
                        "buttonType": "add",
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "添加黑白名单",
                                "path": "/api/v1/system/black/white/conf",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_bwconfig",
                        "type": 3,
                        "buttonType": "delete",
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "删除黑白名单",
                                "path": "/api/v1/system/black/white/conf",
                                "type": 4,
                                "method": "DELETE"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "导出"
                        },
                        "name": "export_bwconfig",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "导出黑白名单",
                                "path": "/api/v1/system/black/white/export",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "导入"
                        },
                        "name": "import_bwconfig",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "导入黑白名单",
                                "path": "/api/v1/system/black/white/import",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    }
                ],
                "component": "/setting/blabkWhiteConfiguration/index.vue"
            },
            {
                "path": "grantAuthorization",
                "name": "GrantAuthorization",
                "meta": {
                    "title": "产品授权管理",
                    "ignoreKeepAlive": true,
                    "orderNo": 5
                },
                "children": [
                    {
                        "meta": {
                            "title": "立即激活"
                        },
                        "name": "active_product",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "激活产品",
                                "path": "/api/v1/settings/license",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    }
                ],
                "component": "/setting/grantAuthorization/index.vue"
            },
            {
                "path": "systemInfo",
                "name": "SystemInfo",
                "meta": {
                    "title": "系统信息配置",
                    "ignoreKeepAlive": true,
                    "orderNo": 6
                },
                "children": [
                    {
                        "meta": {
                            "title": "确认"
                        },
                        "name": "confirm_system_info",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "更改系统信息",
                                "path": "/api/v1/system/info/conf",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    }
                ],
                "component": "/setting/systemInfo/index.vue"
            },
            {
                "path": "userInfo",
                "name": "UserInfo",
                "meta": {
                    "title": "API Token配置",
                    "ignoreKeepAlive": true,
                    "orderNo": 7
                },
                "children": [
                    {
                        "meta": {
                            "title": "刷新"
                        },
                        "name": "refresh",
                        "type": 3
                    }
                ],
                "component": "/setting/userInfo/index.vue"
            },
            {
              "path": "ntpSync",
                "name": "NTPSync",
                "meta": {
                    "title": "NTP同步配置",
                    "ignoreKeepAlive": true,
                    "orderNo": 8
                },
                "children": [
                    {
                        "meta": {
                            "title": "保存"
                        },
                        "name": "save_ntp",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "立即同步"
                        },
                        "name": "sync_ntp",
                        "type": 3
                    }
                ],
                "component": "/setting/ntpSync/index.vue"
            },
            {
                "path": "user",
                "name": "User",
                "meta": {
                    "title": "用户权限管理",
                    "ignoreKeepAlive": false,
                    "orderNo": 9
                },
                "children": [
                    {
                        "meta": {
                            "title": "新增用户"
                        },
                        "name": "add_user",
                        "type": 3,
                        "buttonType": "add",
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "新增用户",
                                "path": "/api/v1/user_access/create_user",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "添加角色"
                        },
                        "name": "add_role",
                        "type": 3,
                        "buttonType": "add",
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "新增角色",
                                "path": "/api/v1/user_access/roles",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "启用"
                        },
                        "name": "enable_user",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "启用用户",
                                "path": "/api/v1/user_access/user_switch",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "禁用"
                        },
                        "name": "forbidden_user",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "禁用用户",
                                "path": "/api/v1/user_access/user_switch",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "编辑角色"
                        },
                        "name": "edit_role",
                        "type": 3,
                        "buttonType": "edit",
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "编辑角色",
                                "path": "/api/v1/user_access/roles",
                                "type": 4,
                                "method": "PUT"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "编辑用户"
                        },
                        "name": "edit_user",
                        "type": 3,
                        "buttonType": "edit",
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "编辑用户",
                                "path": "/api/v1/user_access/update_user",
                                "type": 4,
                                "method": "PUT"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "删除用户"
                        },
                        "name": "delete_user",
                        "type": 3,
                        "buttonType": "delete",
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "删除用户",
                                "path": "/api/v1/user_access/delete_user",
                                "type": 4,
                                "method": "DELETE"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "删除角色"
                        },
                        "name": "delete_role",
                        "type": 3,
                        "buttonType": "delete",
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "删除角色",
                                "path": "/api/v1/user_access/roles",
                                "type": 4,
                                "method": "DELETE"
                            }
                        ]
                    }
                ],
                "component": "/setting/user/index.vue"
            },
            {
                "path": "logger",
                "name": "Logger",
                "meta": {
                    "title": "日志审计",
                    "ignoreKeepAlive": false,
                    "orderNo": 10
                },
                "children": [
                    {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_logger",
                        "type": 3,
                        "buttonType": "delete",
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "删除用户日志",
                                "path": "/api/v1/audit_logs",
                                "type": 4,
                                "method": "DELETE"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "导出"
                        },
                        "name": "export_logger",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "导出用户日志",
                                "path": "/api/v1/audit_logs/export",
                                "type": 4,
                                "method": "GET"
                            },
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "导出系统日志",
                                "path": "/api/v1/system_logs/export",
                                "type": 4,
                                "method": "GET"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "日志外发"
                        },
                        "name": "logger_export",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "日志外发配置",
                                "path": "/api/v1/logs_qc_setting",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    }
                ],
                "component": "/setting/logger/index.vue"
            },
            {
                "path": "message",
                "name": "Message",
                "meta": {
                    "title": "消息推送",
                    "ignoreKeepAlive": false,
                    "orderNo": 11
                },
                "children": [
                    {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_message",
                        "type": 3,
                        "buttonType": "delete",
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "删除消息推送",
                                "path": "/api/v1/webhook",
                                "type": 4,
                                "method": "DELETE"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "确定"
                        },
                        "name": "confirm_message",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "消息推送设置",
                                "path": "/api/v1/webhook",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    }
                ],
                "component": "/setting/message/index.vue"
            },
            {
                "path": "sso",
                "name": "SSO",
                "meta": {
                    "title": "单点登录",
                    "ignoreKeepAlive": false,
                    "orderNo": 12
                },
                "children": [
                    {
                        "meta": {
                            "title": "确定"
                        },
                        "name": "confirm_sso",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "单点登录设置",
                                "path": "/api/v1/settings/sso",
                                "type": 4,
                                "method": "PUT"
                            }
                        ]
                    }
                ],
                "component": "/setting/sso/index.vue"
            },
            {
                "path": "dataBackup",
                "name": "DataBackup",
                "meta": {
                    "title": "数据备份",
                    "ignoreKeepAlive": false,
                    "orderNo": 13
                },
                "children": [
                    {
                        "path": "dataBackupRestore",
                        "name": "DataBackupRestore",
                        "meta": {
                            "title": "数据备份恢复",
                            "currentActiveMenu": "/setting/dataBackup",
                            "realPath": "/setting/dataBackup/dataBackupRestore",
                            "dynamicLevel": 3,
                            "ignoreKeepAlive": false,
                            "hideMenu": true
                        },
                        "component": "/setting/dataBackupRestore/index.vue",
                        "children":[
                            {
                                "meta": {
                                    "title": "保存"
                                },
                                "name": "update_data_backup_config",
                                "type": 3,
                                "children": [
                                    {
                                        "meta": {
                                            "title": "系统操作"
                                        },
                                        "name": "保存",
                                        "path": "/api/v1/system/backup/v2/config",
                                        "type": 4,
                                        "method": "PUT"
                                    }
                                ]
                            },
                            {
                                "meta": {
                                    "title": "还原"
                                },
                                "name": "restore_data_backup",
                                "type": 3,
                                "children": [
                                    {
                                        "meta": {
                                            "title": "系统操作"
                                        },
                                        "name": "还原",
                                        "path": "/api/v1/system/backup/v2/restore",
                                        "type": 4,
                                        "method": "POST"
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "数据备份"
                        },
                        "name": "backup_data",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "数据备份",
                                "path": "/api/v1/system/backup/export",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    },
                    {
                        "meta": {
                            "title": "数据恢复"
                        },
                        "name": "restore_data",
                        "type": 3,
                        "children": [
                            {
                                "meta": {
                                    "title": "系统操作"
                                },
                                "name": "数据恢复",
                                "path": "/api/v1/system/backup/upload",
                                "type": 4,
                                "method": "POST"
                            }
                        ]
                    }
                ],
                "component": "/setting/dataBackup/index.vue"
            }
        ]
    },
    {
        "path": "/statistical",
        "name": "Statistical",
        "component": "LAYOUT",
        "redirect": "redirect",
        "meta": {
            "orderNo": 3,
            "icon": "layout-menu-statistical|svg",
            "title": "统计分析",
            "hideMenu": false
        },
        "children": [
            {
                "path": "assetGraph",
                "name": "AssetGraph",
                "meta": {
                    "title": "资产图谱",
                    "ignoreKeepAlive": false,
                    "hideMenu": false,
                    "icon": "layout-sliderMenu-assetGraph|svg",
                    "orderNo": 0
                },
                "component": "/statisticalAnalysis/assetGraph/index.vue"
            },
            {
                "path": "externalMapping",
                "name": "ExternalMapping",
                "meta": {
                    "title": "内外网映射",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-externalMapping|svg",
                    "orderNo": 1
                },
                "component": "/statisticalAnalysis/externalMapping/index.vue"
            },
            {
                "path": "assetRelevance",
                "name": "AssetRelevance",
                "meta": {
                    "title": "资产映射",
                    "ignoreKeepAlive": false,
                    "icon": "layout-sliderMenu-externalMapping|svg",
                    "hideMenu": false,
                    "orderNo": 2
                },
                "children": [
                    {
                        "meta": {
                            "title": "导入"
                        },
                        "name": "import_asset_relevance",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "删除"
                        },
                        "name": "delete_asset_relevance",
                        "type": 3,
                        "buttonType": "delete"
                    },
                    {
                        "meta": {
                            "title": "编辑"
                        },
                        "name": "edit_asset_relevance",
                        "type": 3,
                        "buttonType": "edit"
                    },
                    {
                        "meta": {
                            "title": "添加映射关系"
                        },
                        "name": "add_asset_relevance",
                        "buttonType": "add",
                        "type": 3
                    },
                    {
                        "meta": {
                            "title": "修改字段"
                        },
                        "name": "edit_field",
                        "type": 3,
                        "buttonType": "edit"
                    }
                ],
                "component": "/statisticalAnalysis/assetRelevance/index.vue"
            }
        ]
    }
]`
	// 由前端提供完整的菜单按钮树，解析后递归入库，注意：每个项的name一定不能重复，一定不能重复，一定不能重复
	var menus = make([]responsePermission.Menus, 0)
	err := json.Unmarshal([]byte(menusString), &menus)
	if nil != err {
		fmt.Printf("MenuSeeder unmarshal json err:%+v \n", err)
		return
	}
	// 获取数据库中当前菜单的最大ID，对新增的数据做插入用
	type MaxId struct {
		Id uint64
	}
	var maxId MaxId
	mysql.GetDbClient().Model(&permission.Menus{}).Select("max(id) as id").Scan(&maxId)
	var parentId uint64 = 0
	var id = maxId.Id + 1
	// 递归入表
	do(menus, id, parentId)
	// 删除之前的首页数据"id": "1", "parent_id": "0", "title": "首页", "path": "/dashboard
	mysql.GetDbClient().Model(&permission.Menus{}).Where("path = ? and id = ? and title = ?", "/dashboard", 1, "首页").Delete(&permission.Menus{})
	// 删除废弃菜单
	mysql.GetDbClient().Model(&permission.Menus{}).Where("name = ? and title = ?", "single_operation", "单条操作").Delete(&permission.Menus{})
	mysql.GetDbClient().Model(&permission.Menus{}).Where("name = ? and title = ?", "batch_operation", "批量操作").Delete(&permission.Menus{})
	mysql.GetDbClient().Model(&permission.Menus{}).Where("name = ? and title = ?", "import_loophole_manage", "导入").Delete(&permission.Menus{})
	// 对系统默认角色二级管理员，普通用户添加默认权限
	addDefaultPermissionForSysRole()
}

// addDefaultPermissionForSysRole 为系统默认的二级管理员和普通用户添加权限，超级管理员不用添加，默认拥有所有权限
func addDefaultPermissionForSysRole() {
	var secondMenus = `AssetCenter
InternalIP
InternalPortrait
delete_internal
import_internal
export_internal
calibrate_fields_internal
NetworkIp
ExternalIp
ExternalPortrait
delete_external
import_external
export_external
calibrate_fields_external
PhysicalFacilities
DeveicePortrait
delete_facilities
export_facilities
calibrate_fields_facilities
PersonnelLedger
delete_ledger
import_ledger
add_department
calibrate_fields_ledger
Business
BusinessSystem
BusinessPortrait
Departments
AssetClassification
DataGovernance
DataAcquisition
DataSync
DataFusion
DataExtraction
loopHoleConfigManagement
SettingInfo
SysNetworkArea
MappingRelationship
RecordList
BusinessAssets
Tags
IpRanges
CustomColumn
Loophole
LoopholeRelevance
HoleRelevanceDetail
export_loophole_relevance
LoopholeManage
export_loophole_manage
single_import_loophole_manage
batch_import_loophole_manage
paifa_loophole_manage
wubao_loophole_manage
wufaxiufu_loophole_manage
yanshi_loophole_manage
zhuanjiao_loophole_manage
xiufuwancheng_loophole_manage
cuicu_loophole_manage
fuce_loophole_manage
calibrate_fields_manage
loopthreat_auto_dispatch
delete_loophole_manage
ComplianceRisks
paifa_compliance_risks
wubao_compliance_risks
wufaxiufu_compliance_risks
zhuanjiao_compliance_risks
yanshi_compliance_risks
cuicu_compliance_risks
xiufuwancheng_compliance_risks
fuce_compliance_risks
delete_compliance_risks
export_compliance_risks
TaskCenter
DataSyncTask
sync_config
DataFusionTask
ActiveScan
create_task
delete_active_scan
rerun_active_scan
export_active_scan
ActiveScanDetail
export_scan_detail
ChildDataSyncTask
DataSyncDetail
ComplianceMonitoring
ComplianceMonitoringDetail
export_compliance_monitoring
create_monitoring_rule
delete_compliance_monitoring
rerun_compliance_monitoring
Statistical
AssetGraph
ExternalMapping
AssetRelevance
import_asset_relevance
delete_asset_relevance
edit_asset_relevance
add_asset_relevance
edit_field
edit_scan
edit_compliance_monitoring
pgid_setting_ledger
resync_dataSync
download_dataSync
upload_file_loophole
delete_file_loophole`
	// 先删除旧数据，再执行插入
	mysql.GetDbClient().Model(&permission.RolesMenus{}).Where("role_id = 2").Delete(&permission.RolesMenus{})
	for _, r := range strings.Split(secondMenus, "\n") {
		r = strings.TrimSpace(r)
		// 根据菜单名称查菜单，拿ID
		var menu permission.Menus
		err := mysql.GetDbClient().Model(&permission.Menus{}).Where("name = ?", r).First(&menu).Error
		if nil != err {
			fmt.Printf("MenuSeeder query menus err:%+v \n", err)
			continue
		}
		mysql.GetDbClient().Model(&permission.RolesMenus{}).Create(&permission.RolesMenus{
			RoleId: 2,
			MenuId: menu.Id,
		})
	}
	var normalMenus = `AssetCenter
InternalIP
InternalPortrait
delete_internal
import_internal
export_internal
calibrate_fields_internal
NetworkIp
ExternalIp
ExternalPortrait
delete_external
import_external
export_external
calibrate_fields_external
PhysicalFacilities
DeveicePortrait
delete_facilities
export_facilities
calibrate_fields_facilities
Business
BusinessSystem
BusinessPortrait
AssetClassification
Loophole
LoopholeRelevance
HoleRelevanceDetail
export_loophole_relevance
LoopholeManage
export_loophole_manage
single_import_loophole_manage
paifa_loophole_manage
wubao_loophole_manage
wufaxiufu_loophole_manage
yanshi_loophole_manage
zhuanjiao_loophole_manage
xiufuwancheng_loophole_manage
cuicu_loophole_manage
fuce_loophole_manage
calibrate_fields_manage
loopthreat_auto_dispatch
delete_loophole_manage
ComplianceRisks
paifa_compliance_risks
wubao_compliance_risks
wufaxiufu_compliance_risks
zhuanjiao_compliance_risks
yanshi_compliance_risks
cuicu_compliance_risks
xiufuwancheng_compliance_risks
fuce_compliance_risks
delete_compliance_risks
export_compliance_risks
upload_file_loophole
delete_file_loophole`
	// 先删除旧数据，再执行插入
	mysql.GetDbClient().Model(&permission.RolesMenus{}).Where("role_id = 3").Delete(&permission.RolesMenus{})
	for _, r := range strings.Split(normalMenus, "\n") {
		r = strings.TrimSpace(r)
		var menu permission.Menus
		err := mysql.GetDbClient().Model(&permission.Menus{}).Where("name = ? or name = ?", r, r+"  ").First(&menu).Error
		if nil != err {
			fmt.Printf("MenuSeeder query menus err:%+v %s \n", err, r)
			continue
		}
		mysql.GetDbClient().Model(&permission.RolesMenus{}).Create(&permission.RolesMenus{
			RoleId: 3,
			MenuId: menu.Id,
		})
	}
	var auditorMenus = `Setting
    Logger
    delete_logger
    export_logger
    logger_export`
	// 先删除旧数据，再执行插入
	mysql.GetDbClient().Model(&permission.RolesMenus{}).Where("role_id = 4").Delete(&permission.RolesMenus{})
	for _, r := range strings.Split(auditorMenus, "\n") {
		r = strings.TrimSpace(r)
		var menu permission.Menus
		err := mysql.GetDbClient().Model(&permission.Menus{}).Where("name = ? or name = ?", r, r+"  ").First(&menu).Error
		if nil != err {
			fmt.Printf("MenuSeeder query menus err:%+v %s \n", err, r)
			continue
		}
		mysql.GetDbClient().Model(&permission.RolesMenus{}).Create(&permission.RolesMenus{
			RoleId: 4,
			MenuId: menu.Id,
		})
	}
}

// do 递归插入菜单数据
func do(menus []responsePermission.Menus, id, parentId uint64) uint64 {
	for _, r := range menus {
		var existingMenu permission.Menus
		var item = &permission.Menus{
			BaseDSL:   mysql.BaseDSL[permission.Menus]{Id: existingMenu.Id},
			Title:     r.Meta.Title,
			ParentId:  parentId,
			Path:      r.Path,
			Method:    r.Method,
			Icon:      r.Meta.Icon,
			Component: r.Component,
			Name:      r.Name,
			Status:    1,
			Redirect:  r.Redirect,
		}
		if r.Meta.Sort != nil {
			item.Sort = *r.Meta.Sort
		} else {
			item.Sort = 9999
		}
		// 前端用来批量选择按钮用
		if r.ButtonType != nil && r.ButtonType.ButtonType != nil {
			item.ButtonType = *r.ButtonType.ButtonType
		}
		// 解析菜单的类型，如果前端给的数据里面已经配置了，并且值是3，就不用计算
		// 如果一个菜单他有children，并且子里面全是type==3和type==4，则他的type为2，末级页面
		// 如果一个菜单他有children，并且里面有type!=3的，则他的type为1，非末级页面，即目录
		var needCalcType = false
		if r.MenuType == nil || r.Type == nil {
			needCalcType = true
		} else if *r.Type < 3 {
			needCalcType = true
		} else {
			item.Type = *r.Type
		}
		if needCalcType {
			if len(r.Children) == 0 {
				item.Type = 2
			} else if len(r.Children) > 0 {
				item.Type = 1
				var allType3 = true
				for _, child := range r.Children {
					if child.MenuType == nil || child.Type == nil {
						allType3 = false
						break
					} else if *child.Type < 3 {
						allType3 = false
						break
					}
				}
				if allType3 {
					item.Type = 2
				}
			}
		}
		// 前端用，部分菜单多了一个层级嵌套，用来隐藏下级，直接跳转到下级用
		if r.Meta.HideMenu != nil && *r.Meta.HideMenu.HideMenu {
			item.HideMenu = 1
		}
		// 三级，四级页面用，用来表示当前菜单树上哪个菜单是激活的
		if r.Meta.CurrentActiveMenu != nil {
			item.CurrentActiveMenu = *r.Meta.CurrentActiveMenu.CurrentActiveMenu
		}
		// 忽略vue组件的keepalive属性
		if r.Meta.IgnoreKeepAlive != nil && *r.Meta.IgnoreKeepAlive.IgnoreKeepAlive {
			item.IgnoreKeepAlive = 1
		}
		if r.Meta.DynamicLevel != nil && *r.Meta.DynamicLevel.DynamicLevel > 0 {
			item.DynamicLevel = *r.Meta.DynamicLevel.DynamicLevel
		}
		// 前端用，隐藏侧边栏用，部分页面需要隐藏侧边栏
		if r.Meta.NoSideBar != nil && *r.Meta.NoSideBar.NoSideBar {
			item.NoSideBar = 1
		}
		if r.Meta.RealPath != nil {
			item.RealPath = *r.Meta.RealPath.RealPath
		}
		// 特别注意，菜单的name一定不重复，此处以name为唯一条件，查询，如果已经有了，则更新，因为seeder没有版本记录，每次都是全量执行
		if err := mysql.GetDbClient().Where("name = ?", r.Name).First(&existingMenu).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				item.Id = id
				err := mysql.GetDbClient().Create(item).Error
				if err != nil {
					fmt.Printf("MenuSeeder create item err: %+v \n", err)
				}
				id += 1
			}

		} else {
			// 记录存在，检查并更新 Name 字段
			var itemMap = make(map[string]interface{})
			itemMap, err = utils.StructToMap(item, "json")
			delete(itemMap, "children")
			delete(itemMap, "id,string")
			delete(itemMap, "id")
			delete(itemMap, "created_at")
			delete(itemMap, "updated_at")
			delete(itemMap, "parent_id,string")
			delete(itemMap, "parent_id")
			mysql.GetDbClient().Model(&existingMenu).Updates(itemMap)
			item.Id = existingMenu.Id
		}
		if len(r.Children) > 0 {
			id = do(r.Children, id, item.Id)
		}
	}
	return id
}
