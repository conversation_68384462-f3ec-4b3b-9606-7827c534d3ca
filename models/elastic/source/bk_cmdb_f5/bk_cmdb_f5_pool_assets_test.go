package bk_cmdb_f5

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestBkCmdbF5PoolAssets_IndexName(t *testing.T) {
	assert.Equal(t, "bk_cmdb_f5_pool_task_assets", NewBkCmdbF5PoolTaskAssetsModel().IndexName())
	assert.Equal(t, "", NewBkCmdbF5PoolTaskAssetsModel().String())
}

func TestBkCmdbF5PoolAssets_GetAssetListFields(t *testing.T) {
	fields := NewBkCmdbF5PoolTaskAssetsModel().GetAssetListFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "ID", "key": "bk_inst_id", "all_key": "bk_inst_id"},
		{"name": "实例名", "key": "bk_inst_name", "all_key": "bk_inst_name"},
		{"name": "健康检查", "key": "health_monitors", "all_key": "health_monitors"},
		{"name": "负载均衡算法", "key": "lb_method", "all_key": "lb_method"},
		{"name": "成员数量", "key": "member_quantity", "all_key": "member_quantity"},
	}
	assert.Equal(t, expectedFields, fields, "字段不匹配")
}

func TestBkCmdbF5PoolAssets_GetAssetListDetailFields(t *testing.T) {
	fields := NewBkCmdbF5PoolTaskAssetsModel().GetAssetListDetailFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "ID", "key": "bk_inst_id", "all_key": "bk_inst_id"},
		{"name": "实例名", "key": "bk_inst_name", "all_key": "bk_inst_name"},
		{"name": "健康检查", "key": "health_monitors", "all_key": "health_monitors"},
		{"name": "负载均衡算法", "key": "lb_method", "all_key": "lb_method"},
		{"name": "成员数量", "key": "member_quantity", "all_key": "member_quantity"},
	}
	assert.Equal(t, expectedFields, fields, "字段不匹配")
}
