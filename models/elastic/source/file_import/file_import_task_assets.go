package file_import

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"fobrain/models/elastic/assets"
	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/olivere/elastic/v7"
)

type FileImportTaskAssets struct {
	*es.BaseModel   `json:"-"`
	Id              string             `json:"id"` // ID  NODE+IP+区域 唯一值
	TaskId          string             `json:"task_id"`
	ChildTaskId     string             `json:"child_task_id"`
	Area            int                `json:"area"`              // 区域
	Source          int                `json:"source"`            // 来源
	Node            int                `json:"node"`              // 节点
	Ip              string             `json:"ip"`                // IP
	IpType          int                `json:"ip_type"`           // IP类型(1是IPv4，2是IPv6)
	IpSegment       string             `json:"ip_segment"`        // IP段
	HostName        string             `json:"hostname"`          // 主机名
	EthName         string             `json:"eth_name"`          // 网卡名
	Os              string             `json:"os"`                // 操作系统
	Kernel          string             `json:"kernel"`            // 内核
	DeviceId        string             `json:"device_id"`         // 设备ID
	DeviceName      string             `json:"device_name"`       // 设备名
	Model           string             `json:"model"`             // 型号
	Maker           string             `json:"maker"`             // 制造商
	Sn              string             `json:"sn"`                // 序列号
	Mac             string             `json:"mac"`               // MAC地址
	Product         []string           `json:"product"`           // 组件
	BusinessSystem  string             `json:"business_system"`   // 业务系统
	Oper            string             `json:"oper"`              // 运维人员
	BusinessOwner   string             `json:"business_owner"`    // 业务负责人
	MachineRoom     string             `json:"machine_room"`      // 机房
	Status          int                `json:"status"`            // 状态
	Ports           *[]assets.PortInfo `json:"ports"`             // 端口
	MemorySize      string             `json:"memory_size"`       // 内存大小
	MemoryUsageRate string             `json:"memory_usage_rate"` // 内存使用率
	CpuMaker        string             `json:"cpu_maker"`         // CPU厂商
	CpuBrand        string             `json:"cpu_brand"`         // CPU品牌
	CpuCount        int                `json:"cpu_count"`         // CPU数量
	DiskCount       int                `json:"disk_count"`        // 磁盘数量
	DiskSize        int                `json:"disk_size"`         // 磁盘大小
	DiskUsageRate   int                `json:"disk_usage_rate"`   // 磁盘使用率
	LoadAverage     string             `json:"load_average"`      // 负载
	IsPublic        bool               `json:"is_public"`         // 是否外网IP
	CreatedAt       *localtime.Time    `json:"created_at"`        // 创建时间
	UpdatedAt       *localtime.Time    `json:"updated_at"`        // 更新时间
	CustomFields    map[string]string  `json:"custom_fields"`     // 自定义字段
}

// IndexName 索引名
func (f *FileImportTaskAssets) IndexName() string {
	return "file_import_task_assets"
}

func (f *FileImportTaskAssets) String() string {
	if f == nil {
		return ""
	}
	return ""
}

func NewFileImportTaskAssetsModel() *FileImportTaskAssets {
	return &FileImportTaskAssets{}
}

func (f *FileImportTaskAssets) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("ip.keyword").
		Field("ports.port.keyword").
		Field("ports.title.keyword").
		Field("ports.protocol.keyword").
		Field("ports.domain.keyword").
		Field("ports.url.keyword").
		Field("ports.status.keyword")
	return field
}
func (f *FileImportTaskAssets) GetAssetListFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "IP段", "key": "ip_segment"},
		{"name": "操作系统", "key": "os"},
		{"name": "端口", "key": "port", "show_value": "ports"},
		{"name": "协议", "key": "protocol", "show_value": "ports"},
		{"name": "URL", "key": "url", "show_value": "ports"},
		{"name": "网站标题", "key": "title", "show_value": "ports"},
		{"name": "域名", "key": "domain", "show_value": "ports"},
		{"name": "状态码", "key": "status", "show_value": "ports"},
		{"name": "MAC地址", "key": "mac"},
		{"name": "组件", "key": "product"},
		{"name": "业务系统", "key": "business_system"},
		{"name": "运维人员", "key": "oper"},
		{"name": "业务负责人", "key": "business_owner"},
		{"name": "机房", "key": "machine_room"},
		{"name": "资产状态", "key": "on_line_status"},
	}
}

func (f *FileImportTaskAssets) GetAssetListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "IP类型", "key": "ip_type"},
		{"name": "IP段", "key": "ip_segment"},
		{"name": "主机名", "key": "hostname"},
		{"name": "网卡名", "key": "eth_name"},
		{"name": "操作系统", "key": "os"},
		{"name": "内核", "key": "kernel"},
		{"name": "端口", "key": "port", "show_value": "ports"},
		{"name": "协议", "key": "protocol", "show_value": "ports"},
		{"name": "URL", "key": "url", "show_value": "ports"},
		{"name": "网站标题", "key": "title", "show_value": "ports"},
		{"name": "域名", "key": "domain", "show_value": "ports"},
		{"name": "状态码", "key": "status", "show_value": "ports"},
		{"name": "MAC地址", "key": "mac"},
		{"name": "组件", "key": "product"},
		{"name": "业务系统", "key": "business_system"},
		{"name": "运维人员", "key": "oper"},
		{"name": "业务负责人", "key": "business_owner"},
		{"name": "机房", "key": "machine_room"},
		{"name": "资产状态", "key": "on_line_status"},
	}
}
