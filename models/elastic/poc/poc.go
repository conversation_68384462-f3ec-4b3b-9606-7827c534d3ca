package poc

import (
	"context"
	"encoding/json"
	"fmt"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/network_areas"
	pgidservice "fobrain/services/people_pgid"
	"io"
	"net"
	"net/url"
	"regexp"
	"sort"
	"strconv"
	"strings"

	"go-micro.dev/v4/logger"

	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/poc_settings"

	"github.com/olivere/elastic/v7"
	"github.com/pkg/errors"

	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"fobrain/pkg/utils"
)

type (
	Poc struct {
		*es.BaseModel        `json:"-"`
		Id                   string                   `json:"id"`                      // id  区域+IP+端口+? 唯一值
		Fid                  string                   `json:"fid"`                     // fid  cve+cnvd+cnnvd+url
		FidHash              string                   `json:"fid_hash"`                // fid_hash  cve+cnvd+cnnvd+url
		OriginalIds          []string                 `json:"original_ids"`            // 原始id
		OriginalIdsSource    map[string]string        `json:"original_ids_source"`     // 原始id-数据源映射
		Area                 uint64                   `json:"area"`                    // 区域-【唯一】
		ProcessIds           []string                 `json:"process_ids"`             // 过程表ids-【补充】
		SourceIds            []uint64                 `json:"source_ids"`              // 数据源ids-【补充】
		NodeIds              []uint64                 `json:"node_ids"`                // 节点ids-【补充】
		TaskDataIds          []string                 `json:"poc_task_ids"`            // 漏洞任务ids-【补充】
		AllSourceIds         []uint64                 `json:"all_source_ids"`          // 所有数据源ids-【补充】
		AllNodeIds           []uint64                 `json:"all_node_ids"`            // 所有节点ids-【补充】
		AllTaskDataIds       []string                 `json:"all_poc_task_ids"`        // 所有任务ids-【补充】
		AllProcessIds        []string                 `json:"all_process_ids"`         // 所有过程ids-【补充】
		Ip                   string                   `json:"ip"`                      // ip-【唯一】
		IpSource             map[string]string        `json:"ip_source"`               // ip-数据源映射-【补充】
		IpType               int                      `json:"ip_type"`                 // IP类型(1是IPv4，2是IPv6,3是其他)
		NetworkType          int                      `json:"network_type"`            // 网络类型(1是内网，2是外网，3是其他)
		Port                 int                      `json:"port"`                    // 端口-【策略】
		PortSource           map[string]int           `json:"port_source"`             // 端口-数据源映射-【补充】
		IsPoc                int                      `json:"is_poc"`                  // 是否poc漏洞 -【唯一】1是 2否
		IsPocSource          map[string]int           `json:"is_poc_source"`           // 是否poc漏洞-数据源映射-【补充】
		Url                  string                   `json:"url"`                     //  漏洞地址 -【唯一】
		UrlSource            map[string]string        `json:"url_source"`              //  漏洞地址-数据源映射-【补充】
		Level                int                      `json:"level"`                   //  漏洞等级 低危：1 中危：2 高危：3 严重：4 未知：5-【策略】
		LevelSource          map[string]int           `json:"level_source"`            //  漏洞等级-数据源映射-【补充】
		Cve                  string                   `json:"cve"`                     // cve编号-【唯一】
		CveSource            map[string]string        `json:"cve_source"`              // cve编号-数据源映射-【补充】
		Cnvd                 string                   `json:"cnvd"`                    // cnvd编号-【唯一】
		CnvdSource           map[string]string        `json:"cnvd_source"`             // cnvd编号-数据源映射-【补充】
		Cnnvd                string                   `json:"cnnvd"`                   // cnnvd编号-【唯一】
		CnnvdSource          map[string]string        `json:"cnnvd_source"`            // cnnvd编号-数据源映射-【补充】
		HasExp               int                      `json:"has_exp"`                 // 是否存在exp，1是，2否，其他未知
		HasExpSource         map[string]int           `json:"has_exp_source"`          // 是否存在exp-数据源映射-【补充】
		HasPoc               int                      `json:"has_poc"`                 // 是否存在poc-【策略】优先级高没有的取存在exp的
		HasPocSource         map[string]int           `json:"has_poc_source"`          // 是否存在poc-数据源映射-【补充】
		Status               int                      `json:"status"`                  // 状态-【唯一】漏洞流转状态
		StatusSource         map[string]interface{}   `json:"status_source"`           // 状态-数据源映射-【补充】
		Person               map[string]string        `json:"person"`                  // 修复负责人
		PersonInfo           []*assets.PersonBase     `json:"person_info"`             // 修复负责人信息
		PersonDepartment     []*assets.DepartmentBase `json:"person_department"`       // 修复负责人部门
		CcPerson             map[string]string        `json:"cc_person"`               // 抄送人
		Name                 string                   `json:"name"`                    // 漏洞名称-【策略】
		NameSource           map[string]string        `json:"name_source"`             // 漏洞名称-数据源映射-【补充】
		VulType              string                   `json:"vulType"`                 // 漏洞类型-【策略】同漏洞名称策略保持一致
		VulTypeSource        map[string]string        `json:"vulType_source"`          // 漏洞类型-数据源映射-【补充】
		Describe             string                   `json:"describe"`                // 描述-【策略】同漏洞名称策略保持一致
		DescribeSource       map[string]string        `json:"describe_source"`         // 描述-数据源映射-【补充】
		Details              string                   `json:"details"`                 // 详情-【策略】同漏洞名称策略保持一致
		DetailsSource        map[string]string        `json:"details_source"`          // 详情-数据源映射-【补充】
		Hazard               string                   `json:"hazard"`                  // 危险性-【策略】同漏洞名称策略保持一致
		HazardSource         map[string]string        `json:"hazard_source"`           // 危险性-数据源映射-【补充】
		Suggestions          string                   `json:"suggestions"`             // 修复建议-【策略】同漏洞名称策略保持一致
		SuggestionsSource    map[string]string        `json:"suggestions_source"`      // 修复建议-数据源映射-【补充】
		LastResponse         string                   `json:"last_response"`           // 最后一次响应-【策略】同漏洞名称策略保持一致
		LastResponseSource   map[string]string        `json:"last_response_source"`    // 最后一次响应-数据源映射-【补充】
		CreatedAt            *localtime.Time          `json:"created_at"`              // 创建时间
		UpdatedAt            *localtime.Time          `json:"updated_at"`              // 更新时间
		MergeCount           int                      `json:"merge_count"`             // 融合次数
		PersonLimit          []string                 `json:"person_limit"`            // 人员限制-全局统一字段，使用人员 fid 来限制数据权限
		PersonLimitHash      []string                 `json:"person_limit_hash"`       // 人员限制-全局统一字段，使用人员 fid_hash 来限制数据权限
		LastResponseAt       *localtime.Time          `json:"last_response_at"`        // 最后一次响应时间
		LastResponseAtSource map[string]string        `json:"last_response_at_source"` // 最后一次响应时间-数据源映射
		CustomFields         map[string]string        `json:"custom_fields"`           // 自定义字段

		RiskNum            int                      `json:"risk_num"`            // 风险值
		RepairPriority     string                   `json:"repair_priority"`     // 修复优先级
		Business           []*assets.Business       `json:"business"`            // 业务系统
		BusinessDepartment []*assets.DepartmentBase `json:"business_department"` // 业务系统部门
		Oper               []string                 `json:"oper"`                // 运维人员
		OperInfo           []*assets.PersonBase     `json:"oper_info"`           // 运维人员信息
		OperDepartment     []*assets.DepartmentBase `json:"oper_department"`     // 运维人员部门
		BusinessNamesTmp   []string                 `json:"business_name_tmp"`   // 业务系统名称-临时

		DeletedAt *localtime.Time `json:"deleted_at"` // 删除时间

		OperStaffIds          []string `json:"oper_staff_ids"`          // 运维人员ids
		BusinessStaffIds      []string `json:"business_staff_ids"`      // 业务系统负责人ids
		OperDepartmentIds     []uint64 `json:"oper_department_ids"`     // 运维人员部门ids
		BusinessDepartmentIds []uint64 `json:"business_department_ids"` // 业务系统部门ids
		RepairDepartmentIds   []uint64 `json:"repair_department_ids"`   // 漏洞修复人部门ids

		HostName         []string        `json:"hostname"`           //主机名
		LimitDate        *localtime.Time `json:"limit_date"`         //修复时间要求,来自状态流转
		ProcessedTime    *localtime.Time `json:"processed_time"`     //状态变为已处理的时间
		StatusChangeTime *localtime.Time `json:"status_change_time"` //状态变更时间
	}
)

const (
	PocStatusOfNew         = 0  // 新增
	PocStatusOfStillExist  = 1  // 复现.
	PocStatusOfBeRepair    = 10 // 待修复
	PocStatusOfForward     = 11 // 待修复-转交
	PocStatusOfDelay       = 12 // 延时
	PocStatusOfTimeout     = 13 // 超时
	PocStatusOfReRepairing = 14 // 复测中
	PocStatusOfNoRepair    = 15 // 复测未通过

	PocOperateOfUrge        = 16 // 催促操作
	PocStatusOfWaitRetest   = 17 // 待复测
	PocStatusOfRepaired     = 30 // 复测通过
	PocStatusOfErrorReport  = 40 // 误报
	PocStatusOfCantRepaired = 41 // 无法修复

	NetworkTypeInternal = 1 //内网
	NetworkTypeExternal = 2 //外网

)

// poc状态
var PocStatusRelations = map[int]string{
	PocStatusOfNew:          "新增",
	PocStatusOfStillExist:   "复现",
	PocStatusOfBeRepair:     "待修复",
	PocStatusOfForward:      "待修复",
	PocStatusOfDelay:        "延时",
	PocStatusOfTimeout:      "超时",
	PocStatusOfReRepairing:  "复测中",
	PocStatusOfNoRepair:     "复测未通过",
	PocStatusOfRepaired:     "复测通过",
	PocStatusOfErrorReport:  "误报",
	PocStatusOfCantRepaired: "无法修复",
	PocStatusOfWaitRetest:   "待复测",

	PocOperateOfUrge: "催促",
}

// 可以转为复测中的状态
var CanReTestStatus = map[int]string{
	//PocStatusOfNew:        "新增",
	//PocStatusOfStillExist: "复现",
	//PocStatusOfBeRepair:   "待修复",
	//PocStatusOfForward:    "待修复",
	//PocStatusOfDelay:      "延时",
	//PocStatusOfTimeout:    "超时",
	PocStatusOfWaitRetest: "待复测",
}

// 不可以往回改的状态
var CanNotReturnStatus = map[int]string{
	PocStatusOfRepaired: "复测通过",
	//PocStatusOfErrorReport:  "误报",
	//PocStatusOfCantRepaired: "无法修复",
	PocStatusOfWaitRetest: "待复测",
}

func (p *Poc) AppendSourceIds(ids ...uint64) {
	if len(p.SourceIds) == 0 {
		p.SourceIds = make([]uint64, 0)
	}
	p.SourceIds = append(p.SourceIds, ids...)
	p.SourceIds = utils.ListDistinct(p.SourceIds)
}

func (p *Poc) AppendNodeIds(ids ...uint64) {
	if len(p.NodeIds) == 0 {
		p.NodeIds = make([]uint64, 0)
	}
	p.NodeIds = append(p.NodeIds, ids...)
	p.NodeIds = utils.ListDistinct(p.NodeIds)
}

func (p *Poc) AppendTaskDataIds(ids ...string) {
	if len(p.TaskDataIds) == 0 {
		p.TaskDataIds = make([]string, 0)
	}
	p.TaskDataIds = append(p.TaskDataIds, ids...)
	p.TaskDataIds = utils.ListDistinct(p.TaskDataIds)
}

func (p *Poc) AppendProcessIds(ids ...string) {
	if len(p.ProcessIds) == 0 {
		p.ProcessIds = make([]string, 0)
	}
	p.ProcessIds = append(p.ProcessIds, ids...)
	p.ProcessIds = utils.ListDistinct(p.ProcessIds)
}

// IndexName 索引名
func (p *Poc) IndexName() string {
	return "poc"
}

func (p *Poc) String() string {
	if p == nil {
		return ""
	}
	return ""
}

func NewPoc() *Poc {
	return &Poc{}
}

// LevelDesc
// 漏洞登记描述
func (p *Poc) LevelDesc() string {
	switch p.Level {
	case 1:
		return "低危"
	case 2:
		return "中危"
	case 3:
		return "高危"
	case 4:
		return "严重"
	default:
		return "未知"
	}
}

func (p *Poc) StatusDesc() string {
	if v, ok := PocStatusRelations[p.Status]; ok {
		return v
	}

	return "未知"

	//switch p.Status {
	//case 1:
	//	return "新增"
	//case 2:
	//	return "复现"
	//case 3:
	//	return "修复"
	//case 4:
	//	return "未修复"
	//case 5:
	//	return "误报"
	//default:
	//	return "未知"
	//}
}

func (p *Poc) HasExpDesc() string {
	switch p.HasExp {
	case 1:
		return "是"
	case 2:
		return "否"
	default:
		return "未知"
	}
}

func (p *Poc) PersonName() string {
	if p.Person != nil {
		person := p.Person
		return person["name"]
	}
	return ""
}

func (p *Poc) PersonFid() string {
	if p.Person != nil {
		person := p.Person
		return person["fid"]
	}
	return ""
}

func (p *Poc) CcPersonName() string {
	if p.CcPerson != nil {
		person := p.CcPerson
		return person["name"]
	}
	return ""
}

func (p *Poc) CcPersonFid() string {
	if p.CcPerson != nil {
		person := p.CcPerson
		return person["fid"]
	}
	return ""
}

func (p *Poc) HasPocDesc() string {
	switch p.HasPoc {
	case 1:
		return "是"
	case 2:
		return "否"
	default:
		return "未知"
	}
}

// AreaDesc
// 区域的解释方法，预留，后面在做修改
func (p *Poc) AreaDesc() string {
	return fmt.Sprintf("%d", p.Area)
}

func (p *Poc) IsPocDesc() string {
	switch p.IsPoc {
	case 1:
		return "是"
	case 2:
		return "否"
	default:
		return "未知"
	}
}
func (p *Poc) SampleHash() map[string]interface{} {
	if len(p.Business) > 0 {
		for _, value := range p.Business {
			if value != nil && value.BusinessInfo != nil {
				//增加业务系统可信状态展示到内外网资产
				businessInfo, ok := value.BusinessInfo.(map[string]interface{})
				businessInfoBusinessName, _ := businessInfo["business_name"].(string)
				if ok && len(businessInfoBusinessName) > 0 { //存在业务系统
					businessInfo["reliability"] = businessInfo["status"]
					value.BusinessInfo = businessInfo //回填
				}
			}
		}
	}
	return map[string]interface{}{
		"id":               p.Id,
		"name":             p.Name,
		"original_ids":     p.OriginalIds,
		"source_names":     data_source.NewSourceModel().SourceNames(p.AllSourceIds),
		"all_node_ids":     p.AllNodeIds,
		"node_names":       data_source.NodeNames(p.AllNodeIds),
		"ip":               p.Ip,
		"port":             p.Port,
		"area":             network_areas.NetworkAreaName(p.Area),
		"hostname":         p.HostName,
		"url":              CheckUrl(p.Url),
		"vulType":          p.VulType,
		"describe":         p.Describe,
		"level":            p.LevelDesc(),
		"status":           p.StatusDesc(),
		"statusCode":       p.Status,
		"cve":              p.Cve,
		"cnvd":             p.Cnvd,
		"cnnvd":            p.Cnnvd,
		"hasExp":           p.HasExpDesc(),
		"hasPoc":           p.HasPocDesc(),
		"isPoc":            p.IsPocDesc(),
		"created_at":       p.TimeFormat(p.CreatedAt),
		"updated_at":       p.TimeFormat(p.UpdatedAt),
		"deleted_at":       p.TimeFormat(p.DeletedAt),
		"details":          p.Details,
		"hazard":           p.Hazard,
		"suggestions":      p.Suggestions,
		"last_response":    p.LastResponse,
		"last_response_at": p.LastResponseAt,
		"person_name":      p.PersonName(),
		"person_fid":       utils.Md5Hash(p.PersonFid()),
		"person_pgid": func() string {
			pgid, err := pgidservice.GetPgidByFid(p.PersonFid())
			if err != nil {
				return ""
			}
			return pgid
		}(),
		"person_info": func() []*assets.PersonBase {
			if p.PersonInfo == nil {
				return make([]*assets.PersonBase, 0)
			}
			for _, person := range p.PersonInfo {
				person.Pgid, _ = pgidservice.GetPgidById(person.Id)
			}
			return p.PersonInfo
		}(),
		"person_department": p.PersonDepartment,
		"cc_person_name":    p.CcPersonName(),
		"cc_person_fid":     utils.Md5Hash(p.CcPersonFid()),
		"cc_person_pgid": func() string {
			pgid, err := pgidservice.GetPgidByFid(p.CcPersonFid())
			if err != nil {
				return ""
			}
			return pgid
		}(),

		"risk_num":        p.RiskNum,
		"repair_priority": p.RepairPriority,
		"business": func() []*assets.Business {
			for _, business := range p.Business {
				for _, personBase := range business.PersonBase {
					personBase.Pgid, _ = pgidservice.GetPgidById(personBase.Id)
				}
			}
			return p.Business
		}(),
		"business_department": p.BusinessDepartment,
		"oper":                p.Oper,
		"oper_info":           assets.DistinctOperInfo(p.OperInfo),
		"oper_department":     p.OperDepartment,
		"fid":                 p.Fid,
		"business_name_tmp":   p.BusinessNamesTmp,
		"custom_fields":       p.CustomFields,
		"limit_date":          p.LimitDate.Format(utils.DateTimeLayout),
		"processed_time":      p.ProcessedTime.Format(utils.DateTimeLayout),
		"status_change_time":  p.StatusChangeTime.Format(utils.DateTimeLayout),
	}
}

func ConvertToPoc(hit *elastic.SearchHit) (*Poc, error) {
	poc := NewPoc()
	err := json.Unmarshal(hit.Source, poc)
	if err != nil {
		return nil, err
	}
	return poc, nil
}

func CheckUrl(urlStr string) string {
	// 判断是否是uuid,fe43b43bc440690e2dac053d
	uuidRegex := regexp.MustCompile(`^[0-9a-f]{16,}$`)
	if uuidRegex.MatchString(urlStr) {
		return ""
	}

	re := regexp.MustCompile(`^-?\d+$`)
	if re.MatchString(urlStr) {
		return ""
	}

	// 支持的URL scheme
	validSchemes := []string{
		"http://", "https://", "ftp://", "sftp://",
		"ssh://", "tcp://", "udp://",
	}

	// 检查是否以支持的scheme开头
	hasValidScheme := false
	for _, scheme := range validSchemes {
		if strings.HasPrefix(strings.ToLower(urlStr), scheme) {
			hasValidScheme = true
			break
		}
	}

	urlTemp := urlStr
	// 如果没有scheme,尝试添加http://
	if !hasValidScheme {
		// IP:PORT 格式检查
		ipPortRegex := regexp.MustCompile(`^(\d{1,3}\.){3}\d{1,3}(:\d{1,5})?$`)
		if ipPortRegex.MatchString(urlStr) {
			return urlStr
		}

		// 纯IP地址检查
		ipRegex := regexp.MustCompile(`^(\d{1,3}\.){3}\d{1,3}$`)
		if ipRegex.MatchString(urlStr) {
			return urlStr
		}

		urlTemp = "http://" + urlStr
	}

	// 尝试解析URL
	parsedURL, err := url.Parse(urlTemp)
	if err != nil {
		return ""
	}

	// 检查是否有host
	if parsedURL.Host == "" {
		return ""
	}

	return urlStr
}

func (p *Poc) TimeFormat(localTime *localtime.Time) string {
	if localTime == nil {
		return ""
	}

	return localTime.String()
}

func (p *Poc) NewKeywordQuery(keyword string, boolQuery *elastic.BoolQuery) *elastic.BoolQuery {
	escapedKeyword := fmt.Sprintf("*%s*", utils.Escape(keyword))
	boolQuery.Should(
		elastic.NewWildcardQuery("ip.keyword", escapedKeyword).CaseInsensitive(true),
		elastic.NewWildcardQuery("name.keyword", escapedKeyword).CaseInsensitive(true),
		elastic.NewWildcardQuery("vulType", escapedKeyword).CaseInsensitive(true),
	).MinimumShouldMatch("1")
	return boolQuery
}

// 获取网络类型，1=内网,2=公网,3=other
func (p *Poc) GetNetworkType(ip string) int {
	// 先判断字符串格式，排除格式错误的情况
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return 3
	}
	if parsedIP.IsPrivate() {
		return 1
	}
	return 2
}

func (p *Poc) GetPocLevelNum(settings *poc_settings.PocSettingObj) int {
	if settings.PocLevelEnable == 0 {
		return 1
	}

	switch p.Level {
	case 1:
		return settings.PocLevelLow
	case 2:
		return settings.PocLevelMedium
	case 3:
		return settings.PocLevelHigh
	case 4:
		return settings.PocLevelVeryHigh
	default:
		return 1
	}
}

func (p *Poc) GetPocFoundStatus(settings *poc_settings.PocSettingObj) int {
	if settings.PocFoundTimesEnable == 0 {
		return 1
	}

	if p.Status == PocStatusOfStillExist {
		return settings.PocFoundTimesMulti
	}
	return settings.PocFoundTimesFirst
}

func (p *Poc) GetHavePoc(settings *poc_settings.PocSettingObj) int {
	if settings.PocNumEnable == 0 {
		return 1
	}

	if p.HasPoc == 1 {
		return settings.PocNumHave
	}
	return settings.PocNumNone
}

func (p *Poc) GetHaveExp(settings *poc_settings.PocSettingObj) int {
	if settings.PocExpNumEnable == 0 {
		return 1
	}

	if p.HasExp == 1 {
		return settings.PocExpNumHave
	}
	return settings.PocExpNumNone
}

func (p *Poc) GetInternet(settings *poc_settings.PocSettingObj) int {
	if settings.AssetInternetEnable == 0 {
		return 1
	}

	ipType := p.GetNetworkType(p.Ip)
	if ipType == 2 {
		return settings.AssetInternetYes
	}
	return settings.AssetInternetNo
}

func (p *Poc) processInterface(value interface{}) (int, error) {
	switch v := value.(type) {
	case float64:
		return int(v), nil
	case string:
		return strconv.Atoi(v)
	case int:
		return v, nil
	default:
		return 0, errors.New("invalid type")
	}
}

func (p *Poc) GetBusiness(settings *poc_settings.PocSettingObj) int {
	if settings.AssetBusinessEnable == 0 {
		return 1
	}

	//查询业务系统重要度
	weight := 99
	for _, business := range p.Business {
		if business == nil || business.BusinessInfo == nil {
			continue
		}
		businessInfo, ok := business.BusinessInfo.(map[string]interface{})
		if !ok {
			continue
		}
		attr, ok := businessInfo["assets_attribute"].(map[string]interface{})
		if !ok {
			continue
		}
		if v, ok := attr["important_types"].(float64); ok {
			newWeight := int(v)
			if weight > newWeight {
				weight = newWeight
			}
		}
	}

	switch weight {
	case 1:
		return settings.AssetBusinessVery
	case 2:
		return settings.AssetBusinessImportant
	case 3:
		return settings.AssetBusinessNormal
	default:
		return settings.AssetBusinessNormal
	}
}

func (p *Poc) GetSameBusiness(settings *poc_settings.PocSettingObj) int {
	if settings.AssetSameBusinessEnable == 0 {
		return 1
	}

	businessNames := make([]string, 0, len(p.Business))
	for _, business := range p.Business {
		if business == nil || business.System == "" {
			continue
		}
		businessNames = append(businessNames, business.System)
	}
	if len(businessNames) == 0 {
		return 1
	}

	//查询业务系统漏洞数量
	query := elastic.NewBoolQuery()
	q := elastic.NewNestedQuery("business",
		elastic.NewBoolQuery().Must(
			elastic.NewTermsQueryFromStrings("business.system", businessNames...),
		),
	)
	query.Must(q)
	count, err := p.SearchAssetsPocCount(p.Name, query)
	if err != nil {
		count = 0
	}

	switch count {
	case 0, 1:
		return settings.AssetSameBusinessOne
	case 2:
		return settings.AssetSameBusinessTwo
	default:
		return settings.AssetSameBusinessThreeGe
	}
}

func (p *Poc) SearchAssetsPocCount(pocName string, additionQueries ...elastic.Query) (int64, error) {
	var esQuery elastic.Query
	// 创建布尔查询
	boolQuery := elastic.NewBoolQuery()
	//去除回收站数据
	boolQuery.Must(elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("deleted_at"), elastic.NewExistsQuery("purged_at")))
	// 排除 "create_at" 不为空的文档
	boolQuery.MustNot(
		elastic.NewExistsQuery("create_at"),
	)

	for _, q := range additionQueries {
		boolQuery = boolQuery.Must(q)
	}
	// 构造最终查询
	esQuery = boolQuery

	sourceContext := elastic.NewFetchSourceContext(true).Include("id", "ip")
	// 执行查询
	client := es.GetEsClient()
	searchService := client.Scroll().
		Index(assets.NewAssets().IndexName()). // 设置索引
		Query(esQuery).                        // 使用构建的查询
		Size(1000).
		FetchSourceContext(sourceContext).
		Scroll("1m")
	var total int64
	defer searchService.Clear(context.Background())
	// 执行查询
	searchResult, err := searchService.Do(context.TODO())
	if err != nil || err == io.EOF {
		if err == io.EOF {
			err = nil
		}
		return 0, err
	}

	for {
		// 检查是否有数据
		if searchResult.Hits == nil || searchResult.Hits.Hits == nil || len(searchResult.Hits.Hits) == 0 {
			break
		}
		ips := make([]interface{}, 0)
		for _, hit := range searchResult.Hits.Hits {
			asset := assets.Assets{}
			err = json.Unmarshal(hit.Source, &asset)
			if err != nil {
				logger.Errorf("Error unmarshalling asset json: %v", err)
				continue
			}
			ips = append(ips, asset.Ip)
		}
		pocQuery := elastic.NewBoolQuery()
		if pocName != "" {
			pocQuery = pocQuery.Must(elastic.NewTermQuery("name.keyword", pocName).CaseInsensitive(true))
		}
		pocQuery = pocQuery.Must(elastic.NewBoolQuery().
			Must(elastic.NewTermsQuery("ip", ips...)))

		pocSearch, err := es.GetEsClient().Search().
			Index(p.IndexName()).Query(pocQuery).
			Size(0).Do(context.TODO())
		if err != nil {
			logger.Errorf("Error searching poc: %v", err)
			continue
		}
		if pocSearch.Hits != nil && pocSearch.Hits.TotalHits != nil && pocSearch.Hits.TotalHits.Value > 0 {
			total += pocSearch.Hits.TotalHits.Value
		}
		// 如果没有更多的数据需要滚动查询，则退出循环
		if searchResult.ScrollId == "" {
			break
		}

		// 获取下一批数据
		searchResult, err = searchService.Do(context.TODO())
		if err == io.EOF {
			break
		}
		if err != nil {
			logger.Errorf("Error scrolling poc: %v", err)
			break
		}
	}

	return total, nil
}

func (p *Poc) GetRepairPriority(settings *poc_settings.PocSettingObj, riskNum int) string {
	p00, _ := GetPocRePairNum(settings.VulRepairPriorityP0)
	if p00 <= riskNum {
		return "p0"
	}

	p10, p11 := GetPocRePairNum(settings.VulRepairPriorityP1)
	if p10 <= riskNum && p11 >= riskNum {
		return "p1"
	}

	p20, p21 := GetPocRePairNum(settings.VulRepairPriorityP2)
	if p20 <= riskNum && p21 >= riskNum {
		return "p2"
	}

	p30, p31 := GetPocRePairNum(settings.VulRepairPriorityP3)
	if p30 <= riskNum && p31 >= riskNum {
		return "p3"
	}
	return "p3"
}

func GetPocRePairNum(priority string) (int, int) {
	p0 := strings.Split(priority, ",")
	p00, _ := strconv.Atoi(p0[0])
	p01, _ := strconv.Atoi(p0[1])
	return p00, p01
}

type PocStatsInfo struct {
	PendingCount int64 `json:"pending_count"`
	FixedCount   int64 `json:"fixed_count"`
	IgnoredCount int64 `json:"ignored_count"`
}

func PocStats() (*PocStatsInfo, error) {
	// 定义聚合
	// 为漏洞结果设置为各种状态：新增-0、复现-1、待修-10、待修复（待复测）-11、待修复
	// （延时）-12、待修复（超时未修复）-13、待修复（复测中）-14、待修复（复测未通过）
	// -15、已修复（复测通过）-30、忽略（误报）-40、忽略（无法修复）-41。
	// 状态：0-9代表新增复现类型的状态，10-29代表待修复状态，30-39代表已修复状态，
	// 40-49代表忽略状态。

	// issue:3554
	//待处理计算是状态为包括新增、复现、待修复、待复测、复测中、复测未通过、延时、超时未修复、
	aggPendingQuery := elastic.NewBoolQuery()
	aggPendingQuery = aggPendingQuery.MustNot(elastic.NewExistsQuery("deleted_at"))
	aggPendingQuery = aggPendingQuery.Must(elastic.NewTermsQuery("status", 0, 1, 10, 11, 12, 13, 14, 15, 17))
	aggPendingRepair := elastic.NewFilterAggregation().Filter(aggPendingQuery)
	// 已处理：包括复测通过、误报、无法修复
	aggFixedQuery := elastic.NewBoolQuery()
	aggFixedQuery = aggFixedQuery.MustNot(elastic.NewExistsQuery("deleted_at"))
	aggFixedQuery = aggFixedQuery.Must(elastic.NewTermsQuery("status", 30, 40, 41))
	aggFixed := elastic.NewFilterAggregation().Filter(aggFixedQuery)
	// 回收站
	aggIgnoredQuery := elastic.NewBoolQuery()
	aggIgnoredQuery = aggIgnoredQuery.Must(elastic.NewExistsQuery("deleted_at"))
	aggIgnored := elastic.NewFilterAggregation().Filter(aggIgnoredQuery)

	// 执行查询
	searchResult, err := es.GetEsClient().Search().
		Index(NewPoc().IndexName()).
		Size(0). // 不需要返回文档
		Aggregation("pending", aggPendingRepair).
		Aggregation("fixed", aggFixed).
		Aggregation("ignored", aggIgnored).
		Do(context.TODO())

	if err != nil {
		return nil, fmt.Errorf("failed to execute search query: %w", err)
	}

	// 提取聚合结果
	counts := &PocStatsInfo{}

	if agg, found := searchResult.Aggregations.Filter("pending"); found {
		counts.PendingCount = agg.DocCount
	}

	if agg, found := searchResult.Aggregations.Filter("fixed"); found {
		counts.FixedCount = agg.DocCount
	}

	if agg, found := searchResult.Aggregations.Filter("ignored"); found {
		counts.IgnoredCount = agg.DocCount
	}
	return counts, nil
}

type PocOverviewInfo struct {
	LevelInfo          []PocAggInfo `json:"level_info"`           // 漏洞风险等级分布
	RepairPriorityInfo []PocAggInfo `json:"repair_priority_info"` // 漏洞修复优先级分布
	StatusInfo         []PocAggInfo `json:"status_info"`          // 漏洞修复状态分布
	NetworkTypeInfo    []PocAggInfo `json:"network_type_info"`    //
	RecyclePocOverviewInfo
}

type RecyclePocOverviewInfo struct {
	RecycleLevelInfo          []PocAggInfo `json:"recycle_level_info"`
	RecycleRepairPriorityInfo []PocAggInfo `json:"recycle_repair_priority_info"`
	RecycleStatusInfo         []PocAggInfo `json:"recycle_status_info"`
	RecycleNetworkTypeInfo    []PocAggInfo `json:"recycle_network_type_info"`
}

type PocAggInfo struct {
	Field string `json:"field"`
	Count int64  `json:"count"`
}

func PocOverview() (*PocOverviewInfo, error) {
	searchResult, err := es.GetEsClient().Search().
		Index(NewPoc().IndexName()).
		Query(elastic.NewBoolQuery()).
		Size(0).
		Aggregation("network_type_count", elastic.NewTermsAggregation().Field("network_type").Size(10)).
		Aggregation("status_count", elastic.NewTermsAggregation().Field("status").Size(20)).
		Aggregation("level_count", elastic.NewTermsAggregation().Field("level").Size(10)).
		Aggregation("repair_priority_count", elastic.NewTermsAggregation().Field("repair_priority.keyword").Size(10)).
		Do(context.TODO())

	if err != nil {
		return nil, fmt.Errorf("failed to execute search query: %v", err)
	}

	var result = PocOverviewInfo{
		LevelInfo: []PocAggInfo{
			{Field: "低危", Count: 0},
			{Field: "中危", Count: 0},
			{Field: "高危", Count: 0},
			{Field: "严重", Count: 0},
			{Field: "未知", Count: 0},
		},
		StatusInfo: []PocAggInfo{},
		NetworkTypeInfo: []PocAggInfo{
			{Field: "内网", Count: 0},
			{Field: "外网", Count: 0},
			{Field: "其他", Count: 0},
		},
		RepairPriorityInfo: []PocAggInfo{
			{Field: "p0", Count: 0},
			{Field: "p1", Count: 0},
			{Field: "p2", Count: 0},
			{Field: "p3", Count: 0},
		},
	}

	for _, v := range PocStatusRelations {
		result.StatusInfo = append(result.StatusInfo, PocAggInfo{
			Field: v,
			Count: 0,
		})
	}

	// 解析 network_type_count 聚合结果
	networkTypeAgg, found := searchResult.Aggregations.Terms("network_type_count")
	if found {
		for _, bucket := range networkTypeAgg.Buckets {
			fmt.Printf("--Network Type: %v, Count: %d\n", bucket.Key, bucket.DocCount)
			netType, err := utils.TypeAssertToInt(bucket.Key)
			if err != nil {
				continue
			}

			netDesc := ""
			if netType == NetworkTypeInternal {
				netDesc = "内网"
			} else if netType == NetworkTypeExternal {
				netDesc = "外网"
			} else if netType == 3 {
				netDesc = "其他"
			}
			for i, v := range result.NetworkTypeInfo {
				if v.Field == netDesc {
					result.NetworkTypeInfo[i].Count = bucket.DocCount
				}
			}
		}
	}

	// 解析 status_count 聚合结果
	statusAgg, found := searchResult.Aggregations.Terms("status_count")
	if found {
		for _, bucket := range statusAgg.Buckets {
			fmt.Printf("--Status: %v, Count: %d\n", bucket.Key, bucket.DocCount)
			status, err := utils.TypeAssertToInt(bucket.Key)
			if err != nil {
				continue
			}
			field := PocStatusRelations[status]
			for i, v := range result.StatusInfo {
				if v.Field == field {
					result.StatusInfo[i].Count = bucket.DocCount
				}
			}
		}
	}

	// 解析 level_count 聚合结果
	levelAgg, found := searchResult.Aggregations.Terms("level_count")
	if found {
		for _, bucket := range levelAgg.Buckets {
			fmt.Printf("--Level: %v, Count: %d\n", bucket.Key, bucket.DocCount)
			level, err := utils.TypeAssertToInt(bucket.Key)
			if err != nil {
				continue
			}
			tmp := &Poc{Level: level}
			field := tmp.LevelDesc()
			for i, v := range result.LevelInfo {
				if v.Field == field {
					result.LevelInfo[i].Count = bucket.DocCount
				}
			}
		}
	}

	// 解析 repair_priority_count 聚合结果
	repairPriorityAgg, found := searchResult.Aggregations.Terms("repair_priority_count")
	if found {
		for _, bucket := range repairPriorityAgg.Buckets {
			fmt.Printf("--repairPriority: %v, Count: %d\n", bucket.Key, bucket.DocCount)
			for i, v := range result.RepairPriorityInfo {
				if v.Field == fmt.Sprint(bucket.Key) {
					result.RepairPriorityInfo[i].Count = bucket.DocCount
				}
			}

		}
	}
	sort.Slice(result.RepairPriorityInfo, func(i, j int) bool {
		return result.RepairPriorityInfo[i].Field < result.RepairPriorityInfo[j].Field
	})

	return &result, nil
}

func RecyclePocOverview() (*RecyclePocOverviewInfo, error) {

	searchResult, err := es.GetEsClient().Search().
		Index(NewPoc().IndexName()).
		Size(0).
		Aggregation("network_type_count", elastic.NewTermsAggregation().Field("network_type").Size(10)).
		Aggregation("status_count", elastic.NewTermsAggregation().Field("status").Size(10)).
		Aggregation("level_count", elastic.NewTermsAggregation().Field("level").Size(10)).
		Aggregation("repair_priority_count", elastic.NewTermsAggregation().Field("repair_priority.keyword").Size(10)).
		Do(context.TODO())

	if err != nil {
		return nil, fmt.Errorf("failed to execute search query: %v", err)
	}

	var result = RecyclePocOverviewInfo{
		RecycleLevelInfo: []PocAggInfo{
			{Field: "低危", Count: 0},
			{Field: "中危", Count: 0},
			{Field: "高危", Count: 0},
			{Field: "严重", Count: 0},
			{Field: "未知", Count: 0},
		},
		RecycleStatusInfo: []PocAggInfo{},
		RecycleNetworkTypeInfo: []PocAggInfo{
			{Field: "内网", Count: 0},
			{Field: "外网", Count: 0},
			{Field: "其他", Count: 0},
		},
		RecycleRepairPriorityInfo: []PocAggInfo{
			{Field: "p0", Count: 0},
			{Field: "p1", Count: 0},
			{Field: "p2", Count: 0},
			{Field: "p3", Count: 0},
		},
	}

	for _, v := range PocStatusRelations {
		result.RecycleStatusInfo = append(result.RecycleStatusInfo, PocAggInfo{
			Field: v,
			Count: 0,
		})
	}

	// 解析 network_type_count 聚合结果
	networkTypeAgg, found := searchResult.Aggregations.Terms("network_type_count")
	if found {
		for _, bucket := range networkTypeAgg.Buckets {
			fmt.Printf("--Network Type: %v, Count: %d\n", bucket.Key, bucket.DocCount)
			netType, err := utils.TypeAssertToInt(bucket.Key)
			if err != nil {
				continue
			}

			netDesc := ""
			if netType == NetworkTypeInternal {
				netDesc = "内网"
			} else if netType == NetworkTypeExternal {
				netDesc = "外网"
			} else if netType == 3 {
				netDesc = "其他"
			}
			for i, v := range result.RecycleNetworkTypeInfo {
				if v.Field == netDesc {
					result.RecycleNetworkTypeInfo[i].Count = bucket.DocCount
				}
			}
		}
	}

	// 解析 status_count 聚合结果
	statusAgg, found := searchResult.Aggregations.Terms("status_count")
	if found {
		for _, bucket := range statusAgg.Buckets {
			fmt.Printf("--Status: %v, Count: %d\n", bucket.Key, bucket.DocCount)
			status, err := utils.TypeAssertToInt(bucket.Key)
			if err != nil {
				continue
			}
			field := PocStatusRelations[status]
			for i, v := range result.RecycleStatusInfo {
				if v.Field == field {
					result.RecycleStatusInfo[i].Count = bucket.DocCount
				}
			}
		}
	}

	// 解析 level_count 聚合结果
	levelAgg, found := searchResult.Aggregations.Terms("level_count")
	if found {
		for _, bucket := range levelAgg.Buckets {
			fmt.Printf("--Level: %v, Count: %d\n", bucket.Key, bucket.DocCount)
			level, err := utils.TypeAssertToInt(bucket.Key)
			if err != nil {
				continue
			}
			tmp := &Poc{Level: level}
			field := tmp.LevelDesc()
			for i, v := range result.RecycleLevelInfo {
				if v.Field == field {
					result.RecycleLevelInfo[i].Count = bucket.DocCount
				}
			}
		}
	}

	// 解析 repair_priority_count 聚合结果
	repairPriorityAgg, found := searchResult.Aggregations.Terms("repair_priority_count")
	if found {
		for _, bucket := range repairPriorityAgg.Buckets {
			fmt.Printf("--repairPriority: %v, Count: %d\n", bucket.Key, bucket.DocCount)
			for i, v := range result.RecycleRepairPriorityInfo {
				if v.Field == fmt.Sprint(bucket.Key) {
					result.RecycleRepairPriorityInfo[i].Count = bucket.DocCount
				}
			}

		}
	}
	sort.Slice(result.RecycleRepairPriorityInfo, func(i, j int) bool {
		return result.RecycleRepairPriorityInfo[i].Field < result.RecycleRepairPriorityInfo[j].Field
	})

	return &result, nil
}

// PocIpNums  获取资产bug数量
func PocIpNums(ip []interface{}) (map[string]int64, error) {
	data := make(map[string]int64)
	boolQuery := elastic.NewBoolQuery()
	if len(ip) > 0 {
		boolQuery = boolQuery.Must(elastic.NewTermsQuery("ip", ip...))
	}
	agg := elastic.NewTermsAggregation().Field("ip").Size(1000000).ShardSize(1000000)
	searchResult, err := es.GetEsClient().Search().
		Index(NewPoc().IndexName()).
		Query(boolQuery).
		Aggregation("ip_aggs", agg).
		Do(context.TODO())
	if err != nil {
		return nil, err
	}
	aggResult, found := searchResult.Aggregations.Terms("ip_aggs")
	if found {
		for _, bucket := range aggResult.Buckets {
			data[bucket.Key.(string)] = bucket.DocCount
		}
	}
	return data, nil
}

// 示例：name.keyword
func (p *Poc) FieldDistinctTotal(field, keyword string) (int, error) {
	query := elastic.NewBoolQuery()
	if keyword != "" {
		query.Must(elastic.NewWildcardQuery(field, "*"+keyword+"*"))
	}
	// 构建搜索服务
	aggName := "unique_" + field + "_count"
	searchService := p.GetClient().Search().
		Query(query).
		Index(p.IndexName()). // 指定索引名称
		Size(0).              // 不需要返回实际的文档数据
		Aggregation(aggName, elastic.NewCardinalityAggregation().Field(field))

	// 执行查询
	searchResult, err := searchService.Do(context.Background())
	if err != nil {
		return 0, errors.WithMessage(err, "Error executing search query")
	}

	// 解析结果
	agg, found := searchResult.Aggregations.Cardinality(aggName)
	if !found {
		return 0, errors.New("Aggregation not found")

	}
	fmt.Printf("Unique name count: %d\n", int(*agg.Value))
	return int(*agg.Value), nil
}

type NameIPAggregationListResult struct {
	Name          string
	Level         int
	DocCount      int64
	UniqueIPCount int64
}

// 数用于执行 Elasticsearch 查询，按 name.keyword 字段聚合数据，并计算每个聚合桶中的文档数量、唯一 IP 数量和最大 level 值。函数支持根据关键词过滤 name.keyword 字段，并返回一个包含每个聚合桶信息的结果列表。
func (p *Poc) NameIPAggregationList(keyword string) ([]NameIPAggregationListResult, error) {
	var results []NameIPAggregationListResult
	ctx := context.Background()

	field := "name.keyword"

	query := elastic.NewBoolQuery()
	if keyword != "" {
		query.Must(elastic.NewWildcardQuery(field, "*"+keyword+"*"))
	}

	compositeAgg := elastic.NewCompositeAggregation().
		Size(10000).
		Sources(
			elastic.NewCompositeAggregationTermsValuesSource(field).Field(field),
		)

	uniqueIPCountAgg := elastic.NewCardinalityAggregation().Field("ip")
	compositeAgg.SubAggregation("unique_ip_count", uniqueIPCountAgg)

	maxLevelAgg := elastic.NewMaxAggregation().Field("level")
	compositeAgg.SubAggregation("max_level", maxLevelAgg)

	searchResult, err := p.GetClient().Search().
		Index(p.IndexName()).
		Query(query).
		Size(0).
		Aggregation("name_count", compositeAgg).
		Do(ctx)

	if err != nil {
		return nil, fmt.Errorf("failed to execute search query: %v", err)
	}

	agg, found := searchResult.Aggregations.Composite("name_count")
	if !found {
		return nil, fmt.Errorf("no name_count aggregation found")
	}

	for _, bucket := range agg.Buckets {
		name, _ := bucket.Key[field].(string)
		docCount := bucket.DocCount
		uniqueIPCount := int64(0)
		maxLevel := int64(0)

		if uniqueIPAgg, found := bucket.Aggregations.Cardinality("unique_ip_count"); found {
			uniqueIPCount = int64(*uniqueIPAgg.Value)
		}

		if maxLevelAgg, found := bucket.Aggregations.Max("max_level"); found {
			maxLevel = int64(*maxLevelAgg.Value)
		}

		results = append(results, NameIPAggregationListResult{
			Name:          name,
			Level:         int(maxLevel),
			DocCount:      docCount,
			UniqueIPCount: uniqueIPCount,
		})
	}

	return results, nil
}

type PocAggregationListResult struct {
	Key      string
	DocCount int64
}

func (p *Poc) PocAggregationList(keyword, field string) ([]PocAggregationListResult, error) {
	var results []PocAggregationListResult

	query := elastic.NewBoolQuery()
	if keyword != "" {
		query.Must(elastic.NewWildcardQuery(field, "*"+keyword+"*"))
	}

	fieldAgg := elastic.NewTermsAggregation().Field(field).Size(10000)

	searchResult, err := p.GetClient().Search().Index(p.IndexName()).
		Query(query).Size(0).
		Aggregation("field_count", fieldAgg).
		Do(context.Background())

	if err != nil {
		return nil, fmt.Errorf("failed to execute search query: %v", err)
	}

	agg, found := searchResult.Aggregations.Terms("field_count")
	if !found {
		return nil, fmt.Errorf("no field_count aggregation found")
	}

	for _, bucket := range agg.Buckets {
		key := bucket.Key.(string)
		docCount := bucket.DocCount

		results = append(results, PocAggregationListResult{
			Key:      key,
			DocCount: docCount,
		})
	}

	return results, nil
}

func (p *Poc) CheckPocStatus(ids []interface{}) ([]string, error) {
	pocIds := make([]string, 0)
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermsQuery("id", ids...))
	boolQuery.Must(elastic.NewTermQuery("status", 10))
	scroll := es.GetEsClient().Scroll().
		Index(p.IndexName()).
		Query(boolQuery).
		Size(1000).
		Scroll("5m")
	defer scroll.Clear(context.Background())
	for {
		searchResult, err := scroll.Do(context.Background())
		if err == io.EOF || elastic.IsNotFound(err) {
			break
		}
		if searchResult.Hits.TotalHits.Value == 0 {
			break
		}

		if len(searchResult.Hits.Hits) == 0 {
			break
		}
		for _, hit := range searchResult.Hits.Hits {
			pocSource := Poc{}
			err = json.Unmarshal(hit.Source, &pocSource)
			if err != nil {
				logger.Error("poc json unmarshal error", err.Error())
				continue
			}
			pocIds = append(pocIds, pocSource.Id)
		}
	}
	return pocIds, nil
}

func (p *Poc) BusinessSystems() string {
	res := make([]string, 0)
	for _, source := range p.Business {
		res = append(res, source.System)
	}

	return strings.Join(utils.Unique(res), ",")
}

func (p *Poc) BusinessOwners() string {
	res := make([]string, 0)
	for _, source := range p.Business {
		for _, v := range source.PersonBase {
			if v.Name != "" {
				res = append(res, v.Name)
				continue
			}
			for _, t := range v.FindInfo {
				res = append(res, t.SourceValue)
			}
		}
	}
	return strings.Join(utils.Unique(res), ",")
}

func (p *Poc) BusinessDepartments() string {
	res := make([]string, 0)
	for _, source := range p.Business {
		for _, v := range source.DepartmentBase {
			if v.Name != "" {
				res = append(res, v.Name)
				continue
			}
		}
	}
	return strings.Join(utils.Unique(res), ",")
}

func (p *Poc) OperInfos() string {
	res := make([]string, 0)
	for _, source := range p.OperInfo {
		if source.Name != "" {
			res = append(res, source.Name)
			continue
		}
		for _, t := range source.FindInfo {
			res = append(res, t.SourceValue)
		}
	}
	return strings.Join(utils.Unique(res), ",")
}

func (p *Poc) OperInfoDepartment() string {
	res := make([]string, 0)
	for _, source := range p.OperDepartment {
		if source.Name != "" {
			res = append(res, source.Name)
			continue
		}
	}
	return strings.Join(utils.Unique(res), ",")
}
