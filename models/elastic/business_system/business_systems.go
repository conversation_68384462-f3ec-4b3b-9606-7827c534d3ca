package business_system

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"reflect"
	"strings"
	"sync"
	"time"

	"github.com/olivere/elastic/v7"
	"github.com/pkg/errors"

	"fobrain/fobrain/app/request/asset_center"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	filtrate "fobrain/models/elastic"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/helper"
	"fobrain/pkg/utils"
)

// 定义一个通用的map类型，用于存储value到label的映射
type attrMap map[int]string

const (
	TrustStatusYes     = 1 //可信状态 1可信 2可疑 3不可信
	TrustStatusYesOrNo = 2
	TrustStatusNo      = 3

	SourceStatusHandleAndImport = 1 //  数据来源 1手动校准/文件导入 2数据源采集 3资产属性提取
	SourceStatusDataSource      = 2
	SourceStatusAsset           = 3
)

var SourceStatusMap = attrMap{
	SourceStatusHandleAndImport: "手动校准/文件导入",
	SourceStatusDataSource:      "数据源采集",
	SourceStatusAsset:           "资产属性提取",
}

// ContinuityLevel 连续性级别
var ContinuityLevel = attrMap{
	1: "一级",
	2: "二级",
	3: "三级",
	4: "四级",
	5: "五级",
}

// Parent 父级部门
type Parent struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

// Department 部门
type Department struct {
	ID      string   `json:"id"`
	Name    string   `json:"name"`
	Parents []Parent `json:"parents"`
}

// BusinessSystems 业务系统结构体
type BusinessSystems struct {
	*es.BaseModel   `json:"-"`
	Id              string          `json:"id"`        //id
	TagId           uint64          `json:"tag_id"`    //id
	Status          int             `json:"status"`    //可信状态 1可信 2可疑 3不可信
	From            int             `json:"from"`      //数据来源 1手动校准/文件导入 2数据源采集 3资产属性提取
	FromMark        int             `json:"from_mark"` //来源标记 用于来源更小粒度描述 1资产IP导入 2数据源 3业务信息配置人工添加 4业务信息配置人工导入
	IntranetIps     []string        `json:"intranet_ips" zh:"内网"`
	InternetIps     []string        `json:"internet_ips" zh:"互联网"`
	ContinuityLevel int             `json:"continuity_level" zh:"连续性级别"` // 1一级 2二级 3三级 4四级 5五级
	UseMark         string          `json:"use_mark" form:"use_mark" validate:"omitempty" zh:"规划用途"`
	Fid             string          `json:"fid"`              //唯一标记 未融合先使用 mid+BusinessName
	BusinessName    string          `json:"business_name"`    //业务系统名称
	Address         string          `json:"address"`          //业务系统地址
	AssetsAttribute AssetsAttribute `json:"assets_attribute"` //资产属性
	SystemVersion   string          `json:"system_version"`   //系统版本
	CreatedAt       *localtime.Time `json:"created_at"`       // 创建时间
	DeletedAt       *localtime.Time `json:"deleted_at"`       // 创建时间
	UpdatedAt       *localtime.Time `json:"updated_at"`       // 更新时间

	PersonBase     []*assets.PersonBase     `json:"person_base"`     // 业务系统负责人信息
	DepartmentBase []*assets.DepartmentBase `json:"department_base"` // 业务系统部门信息
	CustomFields   map[string]interface{}   `json:"custom_fields"`   //自定义字段
}

// AssetsAttribute 资产属性结构体
type AssetsAttribute struct {
	IsGj           *int `json:"is_gj"`           //是否关基设施：''-空/ 1-是/ 2-否
	IsXc           *int `json:"is_xc"`           //是否信创：''- 空/ 1-是/ 2- 否
	PurchaseType   *int `json:"purchase_type"`   //采购类型：''-空/ 1-自研/ 2-外包/ 3-第三方采购
	ImportantTypes *int `json:"important_types"` //重要性 ''-空/ 1-非常重要/ 2- 重要/ 3- 一般
	InsuranceLevel *int `json:"insurance_level"` //等保级别: ''- 空/ 1-一级/2- 二级/3- 三级
	OperatingEnv   *int `json:"operating_env"`   //运行环境 1-生产环境/2-开发环境
	RunningState   *int `json:"running_state"`   //运行状态 1-运行中/ 0-已下线
}

var allBusinessSystems = make(map[string]*assets.Business)
var allBusinessError = false
var lockAllBusinessSystems sync.RWMutex

func (b *BusinessSystems) IndexName() string {
	return "business_systems"
}

func NewBusinessSystems() *BusinessSystems {
	return &BusinessSystems{}
}

// FindAllByQuery 根据条件查询所有数据，滚动查询，性能较高
func (b *BusinessSystems) FindAllByQuery(ctx context.Context, query elastic.Query, fields []string) ([]*BusinessSystems, int64) {
	scrollService := b.GetClient().Scroll().Index(b.IndexName()).Query(query).Scroll("1m").Size(1000).Sort("id", true)
	defer scrollService.Clear(context.Background())
	results := make([]*BusinessSystems, 0)
	var total int64
	for {
		if len(fields) > 0 {
			scrollService.FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...))
		}
		searchResult, err := scrollService.Do(ctx)
		if errors.Is(err, io.EOF) {
			err = nil
			break
		}
		if err != nil || len(searchResult.Hits.Hits) == 0 {
			break
		}
		if len(searchResult.Hits.Hits) == 0 {
			break
		}
		total = searchResult.TotalHits()
		for _, hit := range searchResult.Hits.Hits {
			item, err := convertToBusinessModel(hit)
			if err != nil {
				logs.GetLogger().Warnf(fmt.Sprintf("资产信息解析失败,跳过,数据:%v,错误:%v", hit, err))
				continue
			}
			results = append(results, item)
		}
		// Update scrollService with new ScrollId
		//scrollService = b.GetClient().Scroll().ScrollId(searchResult.ScrollId).Scroll("1m")
	}
	return results, total
}

// convertToAssetsModel ES查询结果转换为Assets结构体
func convertToBusinessModel(hit *elastic.SearchHit) (*BusinessSystems, error) {
	item := NewBusinessSystems()
	err := json.Unmarshal(hit.Source, item)
	if err != nil {
		return nil, err
	}
	return item, nil
}

// UpdateDeletedAt 更新删除标记
func (b *BusinessSystems) UpdateDeletedAt(ids []string, keyword string, isDelete bool) error {
	var deleteTime *localtime.Time
	boolQuery := elastic.NewBoolQuery()
	if len(keyword) > 0 {
		boolQuery.Must(b.NewKeywordQuery(keyword))
	}
	if len(ids) > 0 {
		boolQuery.Must(elastic.NewTermsQueryFromStrings("fid", ids...))
	}
	// 构建脚本更新 deleted_at 字段
	if isDelete { //标记删除
		//获取业务系统ID
		if len(ids) == 0 {
			businesses, _ := b.FindAllByQuery(context.Background(), boolQuery, []string{})
			for _, bus := range businesses {
				ids = append(ids, bus.Id)
			}
		}
		q := elastic.NewNestedQuery("business",
			elastic.NewBoolQuery().Must(
				elastic.NewTermsQueryFromStrings("business.system_id", ids...),
			),
		)
		exists := assets.NewAssets().Exists(context.Background(), q)
		if exists {
			return fmt.Errorf("业务系统有相关资产，请先转交")
		}
		deleteTime = localtime.NewLocalTime(time.Now())
	}
	script := elastic.NewScriptInline("ctx._source.deleted_at = params.deleted_at").
		Param("deleted_at", deleteTime)

	// 执行 UpdateByQuery
	_, err := b.GetClient().UpdateByQuery(b.IndexName()).Query(boolQuery).Script(script).Refresh("true").Do(context.TODO())
	if err != nil {
		return err
	}
	return nil
}

func (b *BusinessSystems) NewKeywordQuery(keyword string) elastic.Query {
	//构建模糊查询 支持普通数据和nested数据
	// 对于普通字段使用QueryStringQuery
	regularQuery := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("address").
		Field("business_name") // 添加业务系统名称字段

	// 对于嵌套字段使用NestedQuery
	nestedPersonQuery := elastic.NewNestedQuery("person_base",
		elastic.NewBoolQuery().Should(
			elastic.NewWildcardQuery("person_base.name", fmt.Sprintf("*%s*", utils.Escape(keyword))),
		))

	// 对于部门嵌套字段使用NestedQuery
	nestedDepartmentQuery := elastic.NewNestedQuery("department_base",
		elastic.NewBoolQuery().Should(
			elastic.NewWildcardQuery("department_base.name", fmt.Sprintf("*%s*", utils.Escape(keyword))),
		))

	// 组合普通查询和嵌套查询
	return elastic.NewBoolQuery().Should(regularQuery, nestedPersonQuery, nestedDepartmentQuery)
}

// DeleteByIds 根据ID批量删除
func (b *BusinessSystems) DeleteByIds(ids []string, keyword string) error {
	boolQuery := elastic.NewBoolQuery()
	if len(ids) > 0 {
		boolQuery.Must(elastic.NewTermsQueryFromStrings("fid", ids...))
	}
	// 查询 deleted_at 字段存在且不为空字符串
	//boolQuery.Must(elastic.NewExistsQuery("deleted_at"))
	//if len(keyword) > 0 {
	//	boolQuery.Must(b.NewKeywordQuery(keyword))
	//}
	err := es.Delete[BusinessSystems](boolQuery)
	if err != nil {
		return err
	}
	return nil
}

func (b *BusinessSystems) GetById(ctx context.Context, id string) (*BusinessSystems, error) {
	q := elastic.NewBoolQuery()
	q = q.Must(elastic.NewTermQuery("id", id))
	searchResult, err := b.GetClient().Search().Index(b.IndexName()).Size(1).Query(q).Do(ctx)
	if err != nil {
		logs.GetLogger().Warnf("search business error. Ids:%s, error: %v,", id, err)
		return nil, errors.New("search error")
	}
	if searchResult.Hits.TotalHits.Value == 0 {
		logs.GetLogger().Debugf("business not found. Ids:%s", id)
		return nil, nil
	}
	hit := searchResult.Hits.Hits[0]
	item := NewBusinessSystems()
	err = json.Unmarshal(hit.Source, item)
	if err != nil {
		return nil, err
	}
	return item, nil
}

func (b *BusinessSystems) GetAssetBusinessStructById(ctx context.Context, id string) (*assets.Business, error) {
	logger := logs.GetLogger()
	q := elastic.NewBoolQuery()
	q = q.Must(elastic.NewTermQuery("id", id))
	searchResult, err := b.GetClient().Search().Index(b.IndexName()).Size(1).Query(q).Do(ctx)
	if err != nil {
		logger.Warnf("search business error. Ids:%s, error: %v,", id, err)
		return nil, errors.New("search error")
	}
	if searchResult.Hits.TotalHits.Value == 0 {
		logger.Debugf("business not found. Ids:%s", id)
		return nil, nil
	}
	hit := searchResult.Hits.Hits[0]
	return convertToAssetBusiness(hit)
}

func (b *BusinessSystems) GetAssetBusinessStructByIds(ctx context.Context, ids []string) ([]*assets.Business, error) {
	if allBusinessError {
		err := b.CacheAllBusinessSystems(ctx)
		if err != nil {
			return nil, err
		}
	}
	var businesses []*assets.Business
	lockAllBusinessSystems.RLock()
	defer lockAllBusinessSystems.RUnlock()
	for _, id := range ids {
		business, ok := allBusinessSystems[id]
		if !ok {
			continue
		}
		businesses = append(businesses, business)
	}
	return businesses, nil
}
func (b *BusinessSystems) CacheAllBusinessSystems(ctx context.Context) error {
	lockAllBusinessSystems.Lock()
	defer lockAllBusinessSystems.Unlock()
	q := &elastic.MatchAllQuery{}
	rows, err := es.All[BusinessSystems](1000, q, []elastic.Sorter{
		elastic.NewFieldSort("id").Desc(),
	})
	if err != nil {
		allBusinessError = true
		return errors.Wrap(err, "cache all business systems error")
	}
	allBusinessSystems = make(map[string]*assets.Business)
	for _, bs := range rows {
		assetBusiness := &assets.Business{
			SystemId:             bs.Id,
			System:               bs.BusinessName,
			BusinessInfo:         bs,
			PersonBase:           bs.PersonBase,
			DepartmentBase:       bs.DepartmentBase,
			BusinessTrustedState: bs.Status,
		}
		allBusinessSystems[bs.Id] = assetBusiness
	}
	allBusinessError = false
	return nil
}

// convertToAssetBusiness 转换为资产业务模型
func convertToAssetBusiness(b *elastic.SearchHit) (*assets.Business, error) {
	if b == nil || b.Source == nil {
		return nil, errors.New("search result is nil")
	}
	item := NewBusinessSystems()
	err := json.Unmarshal(b.Source, item)
	if err != nil {
		return nil, err
	}
	assetBusiness := &assets.Business{
		SystemId:             item.Id,
		System:               item.BusinessName,
		BusinessInfo:         item,
		PersonBase:           item.PersonBase,
		DepartmentBase:       item.DepartmentBase,
		BusinessTrustedState: item.Status,
	}
	return assetBusiness, nil
}

func (b *BusinessSystems) UpdateAssetsByBusiness(ctx context.Context, business *BusinessSystems) error {
	query := elastic.NewNestedQuery("business",
		elastic.NewBoolQuery().Must(
			elastic.NewTermQuery("business.system_id", business.Id),
		),
	)
	assetBusiness := &assets.Business{
		SystemId:             business.Id,
		System:               business.BusinessName,
		BusinessInfo:         business,
		PersonBase:           business.PersonBase,
		DepartmentBase:       business.DepartmentBase,
		BusinessTrustedState: business.Status,
	}
	// 使用 Script 来更新 `business.owner` 字段
	// 2. 使用 UpdateByQuery API 来同时查询和更新文档
	script := elastic.NewScript(`
    ctx._source.business.removeIf(item -> item.system_id == params.system_id);
    ctx._source.business.add(params.business);
`).Param("business", assetBusiness).Param("system_id", business.Id)
	_, err := es.GetEsClient().UpdateByQuery(assets.NewAssets().IndexName()).
		Query(query).
		Script(script).
		Refresh("true").
		Do(ctx)
	if err != nil {
		return err
	}
	return nil
}

// 修改函数签名以返回脚本和参数
func generateUpdateScript(fields map[string]interface{}) (string, map[string]interface{}) {
	var scriptParts []string
	params := make(map[string]interface{})

	for fieldName, newValue := range fields {
		paramName := strings.ReplaceAll(fieldName, ".", "_")
		switch v := newValue.(type) {
		case string:
			// 字符串需要转义
			scriptParts = append(scriptParts, fmt.Sprintf(`ctx._source.%s = '%s';`, fieldName, strings.ReplaceAll(v, "'", "\\'")))
		case []string:
			// 数组转为 JSON
			jsonBytes, _ := json.Marshal(v)
			scriptParts = append(scriptParts, fmt.Sprintf(`ctx._source.%s = %s;`, fieldName, string(jsonBytes)))
		case nil:
			scriptParts = append(scriptParts, fmt.Sprintf(`ctx._source.%s = null;`, fieldName))
		case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64:
			// 数字类型直接使用值
			scriptParts = append(scriptParts, fmt.Sprintf(`ctx._source.%s = %v;`, fieldName, v))
		case *int, *int8, *int16, *int32, *int64, *uint, *uint8, *uint16, *uint32, *uint64, *float32, *float64:
			// 指针数字类型，需要解引用
			scriptParts = append(scriptParts, fmt.Sprintf(`ctx._source.%s = %v;`, fieldName, reflect.ValueOf(v).Elem().Interface()))
		default:
			// 复杂对象使用 params
			params[paramName] = v
			scriptParts = append(scriptParts, fmt.Sprintf(`ctx._source.%s = params.%s;`, fieldName, paramName))
		}
	}

	script := strings.Join(scriptParts, "\n")
	return script, params
}

// CreateOrUpdate 创建或更新
func (b *BusinessSystems) CreateOrUpdate(business *BusinessSystems) error {
	err := es.CreateOrUpdate[BusinessSystems](*business)
	if err != nil {
		return err
	}
	return nil
}

func (b *BusinessSystems) GetByNames(ctx context.Context, names []string) ([]*BusinessSystems, error) {
	if allBusinessError {
		err := b.CacheAllBusinessSystems(ctx)
		if err != nil {
			return nil, err
		}
	}
	var results = make([]*BusinessSystems, 0)
	lockAllBusinessSystems.RLock()
	defer lockAllBusinessSystems.RUnlock()
	for _, business := range allBusinessSystems {
		if utils.ListContains[string](names, business.System) {
			bi := business.BusinessInfo.(*BusinessSystems)
			results = append(results, bi)
		}
	}
	return results, nil
}
func (b *BusinessSystems) GetByName(ctx context.Context, name string) (*BusinessSystems, error) {
	q := elastic.NewBoolQuery()
	q = q.Must(elastic.NewTermQuery("business_name", name))
	searchResult, err := b.GetClient().Search().Index(b.IndexName()).Size(1).Query(q).Do(ctx)
	if err != nil {
		return nil, err
	}
	if searchResult.Hits.TotalHits.Value == 0 {
		logs.GetLogger().Warnf("business not found. Name:%s,", name)
		return nil, nil
	}
	item, err := convertToBusinessModel(searchResult.Hits.Hits[0])
	return item, err
}

func (b *BusinessSystems) GetBusinessByName(ctx context.Context, name string) (*assets.Business, error) {
	if allBusinessError || len(allBusinessSystems) == 0 {
		err := b.CacheAllBusinessSystems(ctx)
		if err != nil {
			return nil, err
		}
	}
	lockAllBusinessSystems.RLock()
	defer lockAllBusinessSystems.RUnlock()
	for _, business := range allBusinessSystems {
		if business.System == name {
			return business, nil
		}
	}
	return nil, nil
}

// SetStatus 更新可信标记
func (b *BusinessSystems) SetStatus(ids []string, keyword string, opStatus int, searchCondition []string, status int) error {
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermQuery("status", opStatus))

	if status == TrustStatusYes { //标记为可信部门必须存在（默认：存在数据一定存在业务系统名称）
		// 构建 Script
		script := elastic.NewScript("doc['department_base.name.keyword'].value != ''") // 创建 Script 对象

		// 构建 Nested 查询
		nestedQuery := elastic.NewNestedQuery(
			"department_base",
			elastic.NewBoolQuery().Must(
				elastic.NewExistsQuery("department_base.name.keyword"), // 判断字段存在

				elastic.NewScriptQuery(script), // 判断字段值不为空字符串
			),
		)

		// 将 Nested 查询加入 BoolQuery
		boolQuery.Must(nestedQuery)
	}

	if len(keyword) > 0 {
		boolQuery.Must(b.NewKeywordQuery(keyword))
	}

	if len(ids) > 0 {
		boolQuery.Must(elastic.NewTermsQueryFromStrings("fid", ids...))
	}

	if len(searchCondition) > 0 {
		conditions, err := filtrate.ParseQueryConditions(searchCondition)
		if err != nil {
			return err
		}

		for _, condition := range conditions {
			boolQuery = filtrate.BuildBoolQuery(condition.Field, condition.OperationTypeString, condition.LogicalConnective, condition.Value, boolQuery)
		}
	}

	script := elastic.NewScriptInline("ctx._source.status = params.status").
		Param("status", status)

	// 执行 UpdateByQuery
	_, err := b.GetClient().UpdateByQuery(b.IndexName()).Query(boolQuery).Script(script).Refresh("true").Do(context.TODO())
	if err != nil {
		return err
	}
	return nil
}

func (b *BusinessSystems) StatisticsStatus() (map[int64]int64, error) {
	var result = make(map[int64]int64)
	query := elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("deleted_at"))

	agg := elastic.NewTermsAggregation().Field("status").Size(20)
	searchResult, err := es.GetEsClient().Search().
		Index(NewBusinessSystems().IndexName()).
		Query(query).
		Aggregation("status_counts", agg).
		Do(context.TODO())
	if err != nil {
		return result, errors.WithMessage(err, "es search BusinessSystems failed")
	}

	aggResult, found := searchResult.Aggregations.Terms("status_counts")
	if found {
		for _, bucket := range aggResult.Buckets {
			status, _ := bucket.Key.(float64)
			count := bucket.DocCount
			result[int64(status)] = count
		}
	} else {
		return result, nil
	}

	return result, nil
}

// CredibleList 查询可信列表
func (b *BusinessSystems) CredibleList(ids []string, keyword string, status int) (*asset_center.BusinessSystemCredibleResponse, error) {
	credibleResponse := &asset_center.BusinessSystemCredibleResponse{}

	totalQuery := elastic.NewBoolQuery().Must(elastic.NewTermQuery("status", status))

	if len(keyword) > 0 {
		totalQuery.Must(b.NewKeywordQuery(keyword))
	}

	if len(ids) > 0 {
		totalQuery.Must(elastic.NewTermsQueryFromStrings("fid", ids...))
	}

	// 查询全部的数据
	res, err := b.GetClient().Count(b.IndexName()).Query(totalQuery).Do(context.TODO())
	if err != nil {
		return nil, fmt.Errorf("error querying total count: %v", err)
	}
	credibleResponse.CheckedTotal = res

	// 查询部门存在的数据
	passQuery := elastic.NewBoolQuery().Must(elastic.NewTermQuery("status", status))
	passQuery = passQuery.Must(elastic.NewNestedQuery("department_base", elastic.NewBoolQuery().Must(elastic.NewExistsQuery("department_base.name.keyword"))))
	if len(keyword) > 0 {
		passQuery.Must(b.NewKeywordQuery(keyword))
	}

	if len(ids) > 0 {
		passQuery.Must(elastic.NewTermsQueryFromStrings("fid", ids...))
	}

	res, err = b.GetClient().Count(b.IndexName()).Query(passQuery).Do(context.TODO())
	if err != nil {
		return nil, fmt.Errorf("error querying pass count: %v", err)
	}
	credibleResponse.CheckedPass = res

	credibleResponse.CheckedUnpass = credibleResponse.CheckedTotal - credibleResponse.CheckedPass

	return credibleResponse, nil
}

// 修改 BatchUpdate 方法
func (b *BusinessSystems) BatchUpdate(ctx context.Context, query elastic.Query, params *asset_center.BusinessSystemsRequest) error {
	fieldsToUpdate := make(map[string]interface{})
	if params.AssetsAttribute.OperatingEnv != nil {
		fieldsToUpdate["assets_attribute.operating_env"] = *params.AssetsAttribute.OperatingEnv
	}
	if params.AssetsAttribute.PurchaseType != nil {
		fieldsToUpdate["assets_attribute.purchase_type"] = *params.AssetsAttribute.PurchaseType
	}
	if params.AssetsAttribute.ImportantTypes != nil {
		fieldsToUpdate["assets_attribute.important_types"] = *params.AssetsAttribute.ImportantTypes
	}
	if params.AssetsAttribute.InsuranceLevel != nil {
		fieldsToUpdate["assets_attribute.insurance_level"] = *params.AssetsAttribute.InsuranceLevel
	}
	if params.AssetsAttribute.IsGj != nil {
		fieldsToUpdate["assets_attribute.is_gj"] = *params.AssetsAttribute.IsGj
	}
	if params.AssetsAttribute.IsXc != nil {
		fieldsToUpdate["assets_attribute.is_xc"] = *params.AssetsAttribute.IsXc
	}

	if len(params.PersonIds) > 0 {
		mapOper := make(map[string][]*assets.PersonWithMapping)
		for _, personId := range params.PersonIds {
			mapOper["id"] = append(mapOper["id"], &assets.PersonWithMapping{
				SourceId:     0,
				NodeId:       0,
				SourceValue:  personId,
				MappingField: "id",
			})
		}
		personResult, departmentResult, err := helper.GetPersonInfoByList(mapOper)
		if err != nil {
			return err
		}
		fieldsToUpdate["person_base"] = personResult
		fieldsToUpdate["department_base"] = departmentResult
	}
	script, values := generateUpdateScript(fieldsToUpdate)
	_, err := es.GetEsClient().UpdateByQuery(b.IndexName()).
		Query(query).
		Script(func() *elastic.Script {
			if len(values) > 0 {
				return elastic.NewScript(script).Params(values)
			}
			return elastic.NewScript(script)
		}()).
		Refresh("true").
		Do(ctx)
	if err != nil {
		return err
	}
	return nil
}

// DepartmentList 获取部门列表及统计
func (b *BusinessSystems) DepartmentList(ctx context.Context, departmentName string) ([]struct {
	DepartmentName string `json:"department_name"`
	Count          int64  `json:"count"`
}, error) {
	// 构建基础查询
	query := elastic.NewBoolQuery()

	// 构建nested查询
	if departmentName != "" {
		nestedQuery := elastic.NewNestedQuery(
			"department_base",
			elastic.NewBoolQuery().Must(
				elastic.NewWildcardQuery("department_base.name.keyword", fmt.Sprintf("*%s*", departmentName)),
			),
		)
		query.Must(nestedQuery)
	}

	// 使用nested aggregation
	nestedAgg := elastic.NewNestedAggregation().Path("department_base")
	termsAgg := elastic.NewTermsAggregation().
		Field("department_base.name.keyword").
		Size(10000)

	nestedAgg.SubAggregation("department_names", termsAgg)

	searchResult, err := es.GetEsClient().Search().
		Index(b.IndexName()).
		Query(query).
		Aggregation("nested_departments", nestedAgg).
		Do(ctx)
	if err != nil {
		return nil, fmt.Errorf("search department list failed: %w", err)
	}

	// 处理聚合结果
	departments := make([]struct {
		DepartmentName string `json:"department_name"`
		Count          int64  `json:"count"`
	}, 0)

	// 先获取nested aggregation的结果
	nestedResult, found := searchResult.Aggregations.Nested("nested_departments")
	if !found {
		return departments, nil
	}

	// 再获取terms aggregation的结果
	termsResult, found := nestedResult.Terms("department_names")
	if !found {
		return departments, nil
	}

	// 处理结果
	for _, bucket := range termsResult.Buckets {
		departmentName, ok := bucket.Key.(string)
		if !ok || departmentName == "" {
			continue
		}
		departments = append(departments, struct {
			DepartmentName string `json:"department_name"`
			Count          int64  `json:"count"`
		}{
			DepartmentName: departmentName,
			Count:          bucket.DocCount,
		})
	}

	return departments, nil
}

// RefreshBusiness 更新业务系统的配置信息
// 支持两种模式：
// 1. 传入businessIds：更新指定业务系统的配置信息
// 2. 不传入businessIds：更新所有业务系统的配置信息
func (b *BusinessSystems) RefreshBusiness(ctx context.Context, businessIds []string, refreshFields []string) error {
	// 参数检查
	if ctx == nil {
		ctx = context.Background()
	}
	if len(refreshFields) == 0 {
		return nil
	}
	for _, field := range refreshFields {
		if !utils.InArray(field, []string{"ip", "staff"}) {
			return fmt.Errorf("invalid refresh field: %s", field)
		}
	}
	// 日志记录开始
	logs.GetLogger().Infof("开始更新业务系统的内外网IP信息")

	// 构建查询条件
	query := elastic.NewBoolQuery()

	// 处理业务系统ID
	if len(businessIds) > 0 {
		// 指定了业务系统ID，只处理指定的业务系统
		logs.GetLogger().Infof("将处理指定的 %d 个业务系统", len(businessIds))

		// 分批处理业务系统ID，每批最多1000个
		// 这是为了避免Elasticsearch terms查询超过1000个值的限制
		termsQueryBatchSize := 1000
		totalBatches := (len(businessIds) + termsQueryBatchSize - 1) / termsQueryBatchSize

		// 如果只有一批，直接使用terms查询
		if totalBatches == 1 {
			businessIdInterfaces := make([]interface{}, len(businessIds))
			for i, id := range businessIds {
				businessIdInterfaces[i] = id
			}
			query = query.Must(elastic.NewTermsQuery("fid", businessIdInterfaces...))
		} else {
			// 如果有多批，使用should查询组合多个terms查询
			shouldQueries := make([]elastic.Query, 0, totalBatches)

			for i := 0; i < len(businessIds); i += termsQueryBatchSize {
				end := i + termsQueryBatchSize
				if end > len(businessIds) {
					end = len(businessIds)
				}

				batchIds := businessIds[i:end]
				businessIdInterfaces := make([]interface{}, len(batchIds))
				for j, id := range batchIds {
					businessIdInterfaces[j] = id
				}

				termsQuery := elastic.NewTermsQuery("fid", businessIdInterfaces...)
				shouldQueries = append(shouldQueries, termsQuery)
			}

			// 组合所有批次的查询
			query = query.Must(elastic.NewBoolQuery().Should(shouldQueries...).MinimumShouldMatch("1"))
			logs.GetLogger().Infof("业务系统ID较多，已分成 %d 批进行处理", totalBatches)
		}
	} else {
		// 未指定业务系统ID，将处理所有业务系统
		logs.GetLogger().Infof("未指定业务系统ID，将处理所有业务系统的IP信息")
	}

	// 批处理大小
	batchSize := 1000

	// 初始化滚动查询
	scrollService := b.GetClient().Scroll(b.IndexName()).
		Query(query).
		Size(batchSize).
		Scroll("1m")

	// 处理计数
	processedCount := 0

	// 循环处理每批业务系统
	for {
		// 执行滚动查询
		result, err := scrollService.Do(ctx)
		if err == io.EOF {
			// 所有数据已处理完毕
			break
		}
		if err != nil {
			logs.GetLogger().Errorf("查询业务系统失败: %v", err)
			return err
		}

		// 如果没有查询到数据，结束处理
		if len(result.Hits.Hits) == 0 {
			break
		}

		// 将查询结果转换为业务系统对象
		businessBatch := make([]*BusinessSystems, 0, len(result.Hits.Hits))
		for _, hit := range result.Hits.Hits {
			business, err := convertToBusinessModel(hit)
			if err != nil {
				logs.GetLogger().Errorf("转换业务系统数据失败: %v", err)
				continue
			}
			businessBatch = append(businessBatch, business)
		}

		// 处理当前批次的业务系统
		if len(businessBatch) > 0 {
			// 并发处理参数
			maxConcurrent := 5  // 最大并发数
			subBatchSize := 100 // 每个协程处理的业务系统数量

			// 并发控制
			var wg sync.WaitGroup
			semaphore := make(chan struct{}, maxConcurrent) // 信号量控制并发
			errChan := make(chan error, maxConcurrent)      // 错误通道

			// 分批并发处理
			for i := 0; i < len(businessBatch); i += subBatchSize {
				end := i + subBatchSize
				if end > len(businessBatch) {
					end = len(businessBatch)
				}

				// 获取当前子批次的业务系统
				subBatch := businessBatch[i:end]

				// 添加到等待组
				wg.Add(1)

				// 获取信号量，控制并发数
				semaphore <- struct{}{}

				// 启动协程处理当前子批次
				go func(subBatch []*BusinessSystems) {
					// 完成后释放信号量和等待组
					defer func() {
						<-semaphore
						wg.Done()
					}()

					// 执行处理函数
					// 收集所有需要更新的字段，然后一次性更新
					if err := b.updateBusinessBatch(ctx, subBatch, refreshFields); err != nil {
						errChan <- err
					}
				}(subBatch)
			}

			// 等待所有协程完成
			wg.Wait()
			close(errChan)

			// 检查是否有错误
			for err := range errChan {
				if err != nil {
					logs.GetLogger().Errorf("并发处理业务系统IP失败: %v", err)
					return err
				}
			}

			// 更新处理进度
			processedCount += len(businessBatch)
			logs.GetLogger().Infof("已处理 %d 个业务系统", processedCount)
		}
	}

	// 处理完成日志
	if processedCount == 0 {
		logs.GetLogger().Infof("未找到需要处理的业务系统")
	} else {
		logs.GetLogger().Infof("完成业务系统的IP信息更新，共处理 %d 个业务系统", processedCount)
	}

	return nil
}

// updateBusinessBatch 一次性批量更新业务系统数据
// 根据 refreshFields 参数决定需要更新哪些字段，并一次性进行批量更新
// 避免多个函数分别更新同一份数据导致的效率问题
func (b *BusinessSystems) updateBusinessBatch(ctx context.Context, businesses []*BusinessSystems, refreshFields []string) error {
	// 参数检查
	if len(businesses) == 0 || len(refreshFields) == 0 {
		return nil
	}

	// 创建批量更新对象
	bulk := b.GetClient().Bulk().Index(b.IndexName())
	updateCount := 0

	// 处理每个业务系统
	for _, business := range businesses {
		// 收集需要更新的字段
		updateFields := make(map[string]interface{})

		// 如果需要更新IP信息
		if utils.InArray("ip", refreshFields) {
			// 获取业务系统关联的资产信息
			intranetIps, internetIps := b.getBusinessRelatedIPs(ctx, business.Id)

			// 比较内网IP列表是否发生变化
			// 使用集合比较，忽略元素顺序
			intranetChanged := !compareStringSlicesAsSet(intranetIps, business.IntranetIps)
			if intranetChanged {
				updateFields["intranet_ips"] = intranetIps
				updateFields["updated_at"] = localtime.NewLocalTime(time.Now())
			}

			// 比较外网IP列表是否发生变化
			internetChanged := !compareStringSlicesAsSet(internetIps, business.InternetIps)
			if internetChanged {
				updateFields["internet_ips"] = internetIps
				updateFields["updated_at"] = localtime.NewLocalTime(time.Now())
			}
		}

		// 如果需要更新人员信息
		if utils.InArray("staff", refreshFields) {
			personBase := business.PersonBase
			departmentBase := business.DepartmentBase
			var err error

			// 收集人员映射信息
			mapOper := make(map[string][]*assets.PersonWithMapping)
			for _, person := range business.PersonBase {
				findInfo := person.FindInfo
				if len(findInfo) > 0 {
					for _, info := range findInfo {
						mapOper[info.MappingField] = append(mapOper[info.MappingField], &assets.PersonWithMapping{
							SourceId:     info.SourceId,
							NodeId:       info.NodeId,
							SourceValue:  info.SourceValue,
							MappingField: info.MappingField,
						})
					}
				}
			}

			// 获取人员信息
			if len(mapOper) > 0 {
				// 根据人员ID查询人员信息
				personBase, departmentBase, err = helper.GetPersonInfoByList(mapOper)
				if err != nil {
					logs.GetLogger().Errorf("【updateBusinessBatch】获取人员信息失败，%+v", err)
				}
			} else {
				// 获取部门信息
				for _, dep := range business.DepartmentBase {
					department := helper.GetDepartment(dep.Id, "", "", business.BusinessName, business.Id)
					if department != nil {
						departmentBase = append(departmentBase, department)
					}
				}
				// departmentBase 去重避免重复
				seen := make(map[uint64]bool)
				tmp := make([]*assets.DepartmentBase, 0, len(departmentBase))

				// 倒序遍历，保留靠后的项
				for i := len(departmentBase) - 1; i >= 0; i-- {
					dept := departmentBase[i]
					if !seen[dept.Id] {
						seen[dept.Id] = true
						tmp = append(tmp, dept)
					}
				}
				departmentBase = tmp
			}

			// 分别比较人员和部门列表是否发生变化
			// 人员列表比较
			personChanged := !comparePersonList(personBase, business.PersonBase)
			if personChanged {
				updateFields["person_base"] = personBase
				updateFields["updated_at"] = localtime.NewLocalTime(time.Now())
			}
			// 部门列表比较
			departmentChanged := !compareDepartmentList(departmentBase, business.DepartmentBase)
			if departmentChanged {
				updateFields["department_base"] = departmentBase
				updateFields["updated_at"] = localtime.NewLocalTime(time.Now())
			}
			if departmentBase == nil {
				updateFields["state"] = TrustStatusNo
			}
			// 旧数据有部门 新数据没部门
			if business.DepartmentBase != nil && departmentBase == nil {
				updateFields["state"] = TrustStatusYesOrNo
			}
		}

		// 如果有需要更新的字段，添加到批量更新队列
		if len(updateFields) > 0 {
			bulk.Add(elastic.NewBulkUpdateRequest().
				Id(business.Id).
				Doc(updateFields))
			updateCount++
		}

		// 每500条执行一次批量更新，避免请求过大
		if updateCount >= 500 {
			_, err := bulk.Do(ctx)
			if err != nil {
				return fmt.Errorf("批量更新业务系统数据失败: %w", err)
			}
			// 重置批量更新
			bulk = b.GetClient().Bulk().Index(b.IndexName())
			updateCount = 0
		}
	}

	// 执行剩余的批量更新
	if updateCount > 0 {
		_, err := bulk.Do(ctx)
		if err != nil {
			return fmt.Errorf("批量更新业务系统数据失败: %w", err)
		}
	}

	return nil
}

// getBusinessRelatedIPs 获取业务系统关联的内外网IP
// comparePersonList 比较两个人员列表是否相同
// 根据人员的关键字段进行比较，忽略顺序
func comparePersonList(list1, list2 []*assets.PersonBase) bool {
	// 如果长度不同，则列表一定不同
	if len(list1) != len(list2) {
		return false
	}

	// 创建第一个列表的映射，使用人员ID作为键
	personMap := make(map[string]*assets.PersonBase, len(list1))
	for _, person := range list1 {
		// 使用ID作为唯一标识
		personMap[person.Id] = person
	}

	// 检查第二个列表中的每个人员
	for _, person2 := range list2 {
		// 在第一个列表中查找对应的人员
		person1, exists := personMap[person2.Id]
		if !exists {
			// 如果人员不存在，则列表不同
			return false
		}

		// 比较关键字段
		if person1.Name != person2.Name || person1.Fid != person2.Fid {
			return false
		}

		// 比较FindInfo列表
		if !comparePersonFindInfo(person1.FindInfo, person2.FindInfo) {
			return false
		}

		// 比较Department列表
		if !compareDepartmentList(person1.Department, person2.Department) {
			return false
		}
	}

	// 所有人员都匹配，列表相同
	return true
}

// comparePersonFindInfo 比较两个人员查找信息列表是否相同
func comparePersonFindInfo(list1, list2 []*assets.PersonFindInfo) bool {
	// 如果长度不同，则列表一定不同
	if len(list1) != len(list2) {
		return false
	}

	// 创建第一个列表的映射，使用组合键
	findInfoMap := make(map[string]struct{}, len(list1))
	for _, info := range list1 {
		// 创建一个唯一标识字符串
		key := fmt.Sprintf("%d:%d:%s:%s", info.SourceId, info.NodeId, info.SourceValue, info.MappingField)
		findInfoMap[key] = struct{}{}
	}

	// 检查第二个列表中的每个查找信息
	for _, info := range list2 {
		// 创建唯一标识字符串
		key := fmt.Sprintf("%d:%d:%s:%s", info.SourceId, info.NodeId, info.SourceValue, info.MappingField)

		// 检查是否存在
		if _, exists := findInfoMap[key]; !exists {
			return false
		}
	}

	// 所有查找信息都匹配，列表相同
	return true
}

// compareDepartmentList 比较两个部门列表是否相同
// 根据部门的关键字段进行比较，忽略顺序
func compareDepartmentList(list1, list2 []*assets.DepartmentBase) bool {
	// 如果长度不同，则列表一定不同
	if len(list1) != len(list2) {
		return false
	}

	// 创建第一个列表的映射，使用部门ID作为键
	deptMap := make(map[uint64]*assets.DepartmentBase, len(list1))
	for _, dept := range list1 {
		deptMap[dept.Id] = dept
	}

	// 检查第二个列表中的每个部门
	for _, dept2 := range list2 {
		// 在第一个列表中查找对应的部门
		dept1, exists := deptMap[dept2.Id]
		if !exists {
			// 如果部门不存在，则列表不同
			return false
		}

		// 比较关键字段
		if dept1.Name != dept2.Name ||
			dept1.BusinessSystemId != dept2.BusinessSystemId ||
			dept1.BusinessSystemName != dept2.BusinessSystemName ||
			dept1.UserId != dept2.UserId ||
			dept1.UserName != dept2.UserName {
			return false
		}

		// 递归比较父部门列表
		if !compareDepartmentList(dept1.Parents, dept2.Parents) {
			return false
		}
	}

	// 所有部门都匹配，列表相同
	return true
}

// compareStringSlicesAsSet 比较两个字符串切片是否包含相同的元素（忽略顺序）
// 将切片视为集合进行比较，只关注元素的存在性而不关注顺序
func compareStringSlicesAsSet(slice1, slice2 []string) bool {
	// 如果长度不同，则集合一定不同
	if len(slice1) != len(slice2) {
		return false
	}

	// 创建第一个切片的元素集合
	set1 := make(map[string]struct{}, len(slice1))
	for _, item := range slice1 {
		set1[item] = struct{}{}
	}

	// 检查第二个切片的每个元素是否都在集合中
	for _, item := range slice2 {
		// 如果有元素不在集合中，则集合不同
		if _, exists := set1[item]; !exists {
			return false
		}
	}

	// 所有元素都匹配，集合相同
	return true
}

func (b *BusinessSystems) getBusinessRelatedIPs(ctx context.Context, businessId string) ([]string, []string) {
	// 创建嵌套查询，只查询与指定业务系统关联的资产
	assetQuery := elastic.NewBoolQuery().Must(
		elastic.NewNestedQuery("business",
			elastic.NewBoolQuery().Must(
				elastic.NewTermsQuery("business.system_id", businessId),
			),
		),
	)

	// 只获取必要字段，减少数据传输量
	assetFields := []string{"ip", "id", "network_type", "business"}
	assetModel := assets.NewAssets()
	assetList, _ := assetModel.FindAllByQuery(ctx, assetQuery, nil, assetFields...)

	// 初始化IP列表
	intranetIps := []string{}
	internetIps := []string{}

	// 如果有关联资产，处理IP信息
	if len(assetList) > 0 {
		// 使用map去重，提高效率
		intranetIpsMap := make(map[string]struct{})
		internetIpsMap := make(map[string]struct{})

		// 根据网络类型分类IP
		for _, asset := range assetList {
			if asset.Ip == "" {
				continue
			}

			switch asset.NetworkType {
			case 1: // 内网资产
				intranetIpsMap[asset.Ip] = struct{}{}
			case 2: // 外网资产
				internetIpsMap[asset.Ip] = struct{}{}
			}
		}

		// 直接创建新的切片，预分配容量减少内存分配
		intranetIps = make([]string, 0, len(intranetIpsMap))
		internetIps = make([]string, 0, len(internetIpsMap))

		// 将map转换为切片
		for ip := range intranetIpsMap {
			intranetIps = append(intranetIps, ip)
		}
		for ip := range internetIpsMap {
			internetIps = append(internetIps, ip)
		}
	}

	return intranetIps, internetIps
}
