{"settings": {"number_of_shards": 1, "number_of_replicas": 0, "blocks": {"read_only_allow_delete": "false"}, "max_result_window": 100000000}, "mappings": {"dynamic_templates": [{"custom_fields_template": {"path_match": "custom_fields.*", "mapping": {"type": "keyword"}}}, {"disable_other_objects": {"match_mapping_type": "object", "path_unmatch": "custom_fields.*", "mapping": {"enabled": false}}}], "properties": {"all_asset_task_ids": {"type": "keyword"}, "all_node_ids": {"type": "keyword"}, "all_process_ids": {"type": "keyword"}, "all_source_ids": {"type": "keyword"}, "area": {"type": "integer"}, "asset_task_ids": {"type": "keyword"}, "business": {"type": "nested", "properties": {"department_base": {"type": "nested", "properties": {"id": {"type": "long"}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "parents": {"type": "nested", "properties": {"id": {"type": "long"}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "user_id": {"type": "keyword"}, "user_name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "user_id": {"type": "keyword"}, "user_name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "owner": {"type": "keyword"}, "person_base": {"type": "nested", "properties": {"department": {"type": "nested", "properties": {"id": {"type": "long"}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "parents": {"type": "nested", "properties": {"id": {"type": "long"}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "user_id": {"type": "keyword"}, "user_name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "user_id": {"type": "keyword"}, "user_name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "fid": {"type": "keyword"}, "find_info": {"type": "nested", "properties": {"find_count": {"type": "long"}, "mapping_field": {"type": "keyword"}, "node_id": {"type": "keyword"}, "source_id": {"type": "keyword"}, "source_value": {"type": "keyword"}}}, "id": {"type": "keyword"}, "name": {"type": "keyword"}}}, "system": {"type": "keyword"}}}, "business_department": {"type": "nested", "properties": {"business_system_id": {"type": "keyword"}, "business_system_name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "id": {"type": "long"}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "parents": {"type": "nested", "properties": {"business_system_id": {"type": "keyword"}, "business_system_name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "id": {"type": "long"}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "user_id": {"type": "keyword"}, "user_name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "user_id": {"type": "keyword"}, "user_name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "business_department_ids": {"type": "keyword"}, "business_staff_ids": {"type": "keyword"}, "cpu_brand": {"type": "keyword"}, "cpu_count": {"type": "integer"}, "cpu_maker": {"type": "keyword"}, "created_at": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "custom_fields": {"type": "object", "dynamic": "true"}, "data_source_response_at": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "deleted_at": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "disk_count": {"type": "integer"}, "disk_size": {"type": "long"}, "disk_usage_rate": {"type": "keyword"}, "eth_name": {"type": "keyword"}, "exempt_record_ids": {"type": "nested", "properties": {"key": {"type": "integer"}, "values": {"type": "integer"}}}, "fid": {"type": "keyword"}, "fid_hash": {"type": "keyword"}, "hostname": {"type": "keyword"}, "id": {"type": "keyword"}, "ip": {"type": "ip", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ip_segment": {"type": "keyword"}, "ip_type": {"type": "integer"}, "is_device_extracted": {"type": "integer"}, "kernel": {"type": "keyword"}, "last_response_at": {"type": "date", "format": "YYYY-MM-dd HH:mm:ss"}, "load_average": {"type": "keyword"}, "mac": {"type": "keyword"}, "machine_room": {"type": "keyword"}, "maker": {"type": "keyword"}, "memory_size": {"type": "keyword"}, "memory_usage_rate": {"type": "keyword"}, "merge_count": {"type": "integer"}, "model": {"type": "keyword"}, "network_type": {"type": "integer"}, "node_ids": {"type": "keyword"}, "oper": {"type": "keyword"}, "oper_department": {"type": "nested", "properties": {"id": {"type": "long"}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "parents": {"type": "nested", "properties": {"id": {"type": "long"}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "user_id": {"type": "keyword"}, "user_name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "user_id": {"type": "keyword"}, "user_name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "oper_department_ids": {"type": "keyword"}, "oper_info": {"type": "nested", "properties": {"department": {"type": "nested", "properties": {"id": {"type": "long"}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "parents": {"type": "nested", "properties": {"id": {"type": "long"}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "user_id": {"type": "keyword"}, "user_name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "user_id": {"type": "keyword"}, "user_name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "fid": {"type": "keyword"}, "find_info": {"type": "nested", "properties": {"find_count": {"type": "long"}, "mapping_field": {"type": "keyword"}, "node_id": {"type": "keyword"}, "source_id": {"type": "keyword"}, "source_value": {"type": "keyword"}}}, "id": {"type": "keyword"}, "name": {"type": "keyword"}}}, "oper_staff_ids": {"type": "keyword"}, "os": {"type": "keyword"}, "person_limit": {"type": "keyword"}, "person_limit_hash": {"type": "keyword"}, "ports": {"properties": {"domain": {"type": "text", "fields": {"keyword": {"type": "keyword"}}}, "port": {"type": "long"}, "protocol": {"type": "keyword"}, "status": {"type": "long"}, "title": {"type": "text", "fields": {"keyword": {"type": "keyword"}}}, "url": {"type": "text", "fields": {"keyword": {"type": "keyword"}}}}}, "proactive_task_id": {"type": "keyword"}, "process_ids": {"type": "keyword"}, "product": {"type": "keyword"}, "rule_infos": {"type": "nested", "properties": {"first_tag": {"type": "keyword"}, "product": {"type": "keyword"}, "second_tag": {"type": "keyword"}}}, "sn": {"type": "keyword"}, "source_ids": {"type": "keyword"}, "status": {"type": "integer"}, "tag": {"type": "keyword"}, "updated_at": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "jar_package_info": {"type": "nested", "properties": {"jar_package_info": {"type": "nested", "properties": {"k": {"type": "keyword"}, "v": {"type": "keyword"}}}}}}}}