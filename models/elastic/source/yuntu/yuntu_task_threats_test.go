package yuntu

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestTaskThreats_IndexName(t *testing.T) {
	assert.Equal(t, "yuntu_task_threats", NewTaskThreatsModel().IndexName())
	assert.Equal(t, "", NewTaskThreatsModel().String())
}

func TestThreats_GetThreatListFields(t *testing.T) {
	fields := NewTaskThreatsModel().GetThreatListFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "IP地址", "key": "assets"},
		{"name": "ID", "key": "id"},
		{"name": "资产类型", "key": "assets_type"},
		{"name": "漏洞等级", "key": "severity"},
		{"name": "状态", "key": "status"},
		{"name": "目标", "key": "target"},
		{"name": "漏洞标题", "key": "title"},
		{"name": "披露时间", "key": "disclosure_date", "show_value": "vuln"},
		{"name": "漏洞分类", "key": "category", "show_value": "vuln"},
	}
	assert.Equal(t, expectedFields, fields, "字段不匹配")
}

func TestThreats_GetThreatListDetailFields(t *testing.T) {
	fields := NewTaskThreatsModel().GetThreatListDetailFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "IP地址", "key": "assets"},
		{"name": "ID", "key": "id"},
		{"name": "资产类型", "key": "assets_type"},
		{"name": "漏洞等级", "key": "severity"},
		{"name": "状态", "key": "status"},
		{"name": "目标", "key": "target"},
		{"name": "漏洞标题", "key": "title"},
		{"name": "披露时间", "key": "disclosure_date", "show_value": "vuln"},
		{"name": "漏洞分类", "key": "category", "show_value": "vuln"},
	}
	assert.Equal(t, expectedFields, fields, "字段不匹配")
}
