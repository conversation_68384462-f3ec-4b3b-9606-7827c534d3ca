package d01

import (
	"context"
	"fmt"
	"fobrain/fobrain/app/services/node/foeye"
	"fobrain/fobrain/app/services/rule_infos_level"
	syncFoeye "fobrain/fobrain/app/services/sync/foeye"
	"fobrain/fobrain/app/services/sync/proactive_public"
	"fobrain/fobrain/common/constant"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	pb "fobrain/mergeService/proto"
	assetses "fobrain/models/elastic/assets"
	"fobrain/models/elastic/poc"
	"fobrain/models/elastic/source/d01"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_task"
	"fobrain/models/mysql/proactive_task_node_relations"
	"fobrain/models/mysql/task"
	"fobrain/pkg/utils"
	"fobrain/pkg/utils/handle_es_bulk_error"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/olivere/elastic/v7"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
)

type SyncTask struct {
	cli                       *foeye.Foeye
	node                      *data_source.Node
	proactiveTaskNodeRelation *proactive_task_node_relations.ProactiveTaskNodeRelations
	defaultPage               int
	proactiveTask             *task.ProactiveTasks
}

// NewSyncTask
// @Summary 同步 D01 任务维度数据
//
//	@Param taskInfo map true map[string]any{
//		"task_id": "proactive_task 表的Id, 通过proactive task 表中的任务类型来处理需要同步资产还是同步漏洞，还是两者一起同步",
//		"proactive_task_node_relation_id": "节点ID",
//	}
func NewSyncTask(node *data_source.Node, taskInfo map[string]any) error {
	cli := foeye.NewFoeye()
	err := cli.SetNode(node.Id)
	if err != nil {
		return err
	}

	proactiveTask, err := task.NewProactiveTasks().FindById(int64(taskInfo["task_id"].(uint64)))
	if err != nil {
		return err
	}

	proactiveTaskNodeRelation, err := proactive_task_node_relations.FindById(int64(taskInfo["proactive_task_node_relation_id"].(int)))
	if err != nil {
		return err
	}

	sync := SyncTask{
		cli:                       cli,
		proactiveTask:             proactiveTask,
		proactiveTaskNodeRelation: proactiveTaskNodeRelation,
		defaultPage:               constant.DefaultPage,
		node:                      node,
	}

	// 同步资产 或者 漏洞
	switch proactiveTask.TaskType {
	case 1:
		go sync.syncTaskAsset(sync.defaultPage, proactiveTaskNodeRelation.NodeTaskId)
	case 2:
		go sync.syncTaskThreat(sync.defaultPage, proactiveTaskNodeRelation.NodeTaskId)
	default:
		go sync.syncTaskAsset(sync.defaultPage, proactiveTaskNodeRelation.NodeTaskId)
		go sync.syncTaskThreat(sync.defaultPage, proactiveTaskNodeRelation.NodeTaskId)
	}

	proactiveTask.SyncStatus = task.SyncStatusSyncing
	task.NewProactiveTasks().Save(proactiveTask)

	return nil
}

// syncTaskAsset
// @Summary 同步 d01 任务资产
func (sync SyncTask) syncTaskAsset(page int, nodeTaskId int) {
	// 获取任务资产
	assets, err := sync.cli.GetTaskAssets(page, constant.DefaultSize, nodeTaskId)
	if err != nil {
		logs.GetLogger().Infof("sync d01 task asset error: %v", err.Error())
		return
	}

	// 获取资产总数
	total := gjson.Get(assets, "total").Int()

	// 如果是第一页，更新资产总数
	if page == constant.DefaultPage {
		if err := proactive_task_node_relations.UpdateAssetSum(sync.proactiveTaskNodeRelation.Id, int(total)); err != nil {
			logs.GetLogger().Infof("sync d01 task asset - save proactiveTaskNodeRelation Asset sum error: %v", err.Error())
		}
	}

	// 解析数据
	result := gjson.Get(assets, "data.info")
	if total <= 0 {
		proactive_task_node_relations.UpdateNodeDataSyncAssetStatus(sync.proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncSuccessStatus)
		return
	}

	assetsTaskIndex := d01.NewTaskAssetsModel().IndexName()
	assetsFinishIndex := d01.NewFinishedAssetsModel().IndexName()
	isV2 := sync.cli.IsFoeyeV2()
	if isV2 {
		assetsTaskIndex = d01.D01V2SourceTaskAssetsIndex
		assetsFinishIndex = d01.D01V2FinishedAssets
	}

	sourceTaskBulkRequest := es.GetEsClient().Bulk()
	assetTaskBulkRequest := es.GetEsClient().Bulk()
	assetProcessBulkRequest := es.GetEsClient().Bulk()

	// 遍历资产信息，逐个处理
	result.ForEach(func(key, value gjson.Result) bool {
		data, _ := sjson.Set(value.String(), "node_id", sync.node.Id)
		data, _ = sjson.Set(data, "area_id", sync.node.AreaId)

		assetId := strings.ReplaceAll(uuid.New().String(), "-", "")
		sourceTask := sync.handleSourceTask(data_sync_task.SyncAsset, value.String(), assetId)
		sourceTaskReq := elastic.NewBulkIndexRequest().Index(assetsTaskIndex).Id(assetId).Doc(sourceTask)
		sourceTaskBulkRequest = sourceTaskBulkRequest.Add(sourceTaskReq)

		assetTaskId := strings.ReplaceAll(uuid.New().String(), "-", "")
		data, _ = sjson.Set(data, "proactive_task_id", sync.proactiveTask.Id)
		assetTaskReq := elastic.NewBulkIndexRequest().Index(assetsFinishIndex).Id(assetTaskId).Doc(data)
		assetTaskBulkRequest = assetTaskBulkRequest.Add(assetTaskReq)

		assetProcessId := strings.ReplaceAll(uuid.New().String(), "-", "")
		assetProcessReq := prepareAssetProcessRequest(isV2, sync, assetProcessId, value)
		assetProcessBulkRequest = assetProcessBulkRequest.Add(assetProcessReq)

		return true
	})

	// 执行 Bulk 请求并处理错误
	if err := processBulkRequests(assetProcessBulkRequest, assetTaskBulkRequest); err != nil {
		logs.GetLogger().Errorf("d01 syncAssets bulk request error: %s", err)
		proactive_task_node_relations.UpdateNodeDataSyncAssetStatus(sync.proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncFailStatus)
		return
	}
	if err := sync.handleBulkRequest(sourceTaskBulkRequest, "sourceTaskBulkRequest"); err != nil {
		return
	}

	// 更新成功状态
	proactive_task_node_relations.UpdateNodeDataSyncAssetStatus(sync.proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncSuccessStatus)
	logs.GetLogger().Info("d01 task syncAssets success")

	// 递归处理分页
	if page*constant.DefaultSize <= int(total) {
		sync.syncTaskAsset(page+1, nodeTaskId)
	} else {
		// 发送任务结束标识
		mergeResult, err := pb.GetProtoClient().TriggerMergeForAsset(context.Background(), &pb.TriggerMergeForAssetRequest{
			TriggerMergeBaseRequest: &pb.TriggerMergeBaseRequest{
				SourceId:     sync.node.SourceId,
				NodeId:       sync.node.Id,
				TriggerEvent: "主动扫描任务结束",
				TaskId:       fmt.Sprint(sync.proactiveTask.Id + mysql.ProactiveTaskIdAdd),
				ChildTaskId:  fmt.Sprint(sync.proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd),
			},
			DataRangeByTask: &pb.DataRangeByTask{
				TaskId:      fmt.Sprint(sync.proactiveTask.Id + mysql.ProactiveTaskIdAdd),
				ChildTaskId: fmt.Sprint(sync.proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd),
				NodeId:      sync.node.Id,
			},
		}, pb.ClientWithAddress)
		if err != nil {
			logs.GetLogger().Errorf("d01 proactive task end Send to TriggerMergeForAssetRequest error:%v", err)
		}
		if !mergeResult.Success {
			logs.GetLogger().Errorf("d01 proactive task end Send to TriggerMergeForAssetRequest Message error:%v", mergeResult.Message)
		}
	}
}

func (sync SyncTask) handleSourceTask(syncType int64, sourceTask, assetId string) string {
	if syncType == data_sync_task.SyncAsset {
		sourceTask, _ = sjson.Set(sourceTask, "asset_task_id", assetId)
	} else if syncType == data_sync_task.SyncThreat {
		sourceTask, _ = sjson.Set(sourceTask, "poc_task_id", assetId)
		sourceTask, _ = sjson.Set(sourceTask, "original_id", gjson.Get(sourceTask, "id").String())
	}

	sourceTask, _ = sjson.Set(sourceTask, "source", sync.node.SourceId)
	sourceTask, _ = sjson.Set(sourceTask, "source_id", sync.node.SourceId)
	sourceTask, _ = sjson.Set(sourceTask, "node_id", sync.node.Id)
	sourceTask, _ = sjson.Set(sourceTask, "node", sync.node.Id)
	sourceTask, _ = sjson.Set(sourceTask, "area_id", sync.node.AreaId)
	sourceTask, _ = sjson.Set(sourceTask, "area", sync.node.AreaId)
	sourceTask, _ = sjson.Set(sourceTask, "task_id", sync.proactiveTask.Id+mysql.ProactiveTaskIdAdd)
	sourceTask, _ = sjson.Set(sourceTask, "child_task_id", sync.proactiveTaskNodeRelation.Id+mysql.ProactiveTaskIdAdd)
	sourceTask, _ = sjson.Set(sourceTask, "sync_created_at", localtime.NewLocalTime(time.Now()))
	sourceTask, _ = sjson.Set(sourceTask, "sync_updated_at", localtime.NewLocalTime(time.Now()))
	return sourceTask
}

// 准备资产处理请求
func prepareAssetProcessRequest(isV2 bool, sync SyncTask, assetProcessId string, value gjson.Result) *elastic.BulkIndexRequest {
	assetProcess := assetses.NewProcessAssetsModel()
	assetProcess.Id = assetProcessId
	assetProcess.Area = int(sync.node.AreaId)
	assetProcess.Node = sync.node.Id
	assetProcess.Source = sync.node.SourceId
	assetProcess.TaskId = sync.proactiveTask.Id + mysql.ProactiveTaskIdAdd
	assetProcess.ChildTaskId = sync.proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd
	assetProcess.Ip = gjson.Get(value.String(), "ip").String()
	assetProcess.Os = gjson.Get(value.String(), "os").String()
	assetProcess.Mac = gjson.Get(value.String(), "mac").String()
	assetProcess.BusinessSystem = gjson.Get(value.String(), "business_app").String()
	assetProcess.Oper = gjson.Get(value.String(), "username").String()
	assetProcess.BusinessOwner = gjson.Get(value.String(), "username").String()
	assetProcess.MachineRoom = gjson.Get(value.String(), "computer_room").String()
	assetProcess.Status = proactive_public.GetStatus(gjson.Get(value.String(), "state").Int())

	// 处理端口和产品信息
	if isV2 {
		assetProcess.RuleInfos, assetProcess.Ports = syncFoeye.ExtractPortsAndProductsV2(value)
	} else {
		assetProcess.Product, assetProcess.RuleInfos, assetProcess.Ports = extractPortsAndProducts(value)
	}

	// 处理时间
	assetProcess.CreatedAt = proactive_public.GetCreateTime(value)
	assetProcess.UpdatedAt = proactive_public.GetUpdateTime(value)

	return elastic.NewBulkIndexRequest().Index(assetProcess.IndexName()).Id(assetProcessId).Doc(assetProcess)
}

// 提取端口和产品信息
func extractPortsAndProducts(value gjson.Result) ([]string, []*assetses.RuleInfo, []*assetses.PortInfo) {
	var ruleInfos []*assetses.RuleInfo
	var ports []*assetses.PortInfo
	var product []string

	gjson.Get(value.String(), "port_list").ForEach(func(k, v gjson.Result) bool {
		var portInfo assetses.PortInfo
		gjson.Get(v.String(), "rule_info").ForEach(func(key, val gjson.Result) bool {

			productName := val.Get("name").String()
			if productName != "" && !utils.InArray(productName, product) {
				product = append(product, productName)
			}

			var ruleInfo assetses.RuleInfo
			var description string
			var tags []string
			//组件名称
			if gjson.Get(val.String(), "name").String() != "" {
				ruleInfo.Product = gjson.Get(val.String(), "name").String()
				ruleInfo.Level = rule_infos_level.GetRuleInfoLevel(ruleInfo.Product)
			}
			//组件分类
			if gjson.Get(val.String(), "description").String() != "" {
				description = gjson.Get(val.String(), "description").String()
				tags = strings.Split(description, "|")
				if len(tags) > 1 {
					ruleInfo.FirstTag = tags[1]
					ruleInfo.SecondTag = tags[0]
				}
			}
			if ruleInfo.Product != "" {
				ruleInfos = append(ruleInfos, &ruleInfo)
			}
			return true
		})
		portInfo.Port = int(v.Get("port").Int())
		portInfo.Protocol = v.Get("protocol").String()
		portInfo.Title = v.Get("title").String()
		portInfo.Url = v.Get("link_url").String()
		ports = append(ports, &portInfo)
		return true
	})

	return product, ruleInfos, ports
}

// 处理 Bulk 请求并返回错误
func processBulkRequests(assetProcessBulkRequest, assetTaskBulkRequest *es.SafeBulkService) error {
	assetProcessBulkReq, err := assetProcessBulkRequest.Refresh("true").Do(context.Background())
	if err != nil || assetProcessBulkReq.Errors {
		return proactive_public.ExtractBulkErrors(assetProcessBulkReq)
	}

	assetTaskBulkReq, err := assetTaskBulkRequest.Do(context.Background())
	if err != nil || assetTaskBulkReq.Errors {
		return proactive_public.ExtractBulkErrors(assetTaskBulkReq)
	}

	return nil
}

// syncTaskThreat
// @Summary 同步 d01 任务漏洞
func (sync SyncTask) syncTaskThreat(page int, nodeTaskId int) {
	// 获取漏洞数据
	threats, err := sync.cli.GetTaskThreats(page, constant.DefaultSize, nodeTaskId)
	if err != nil {
		logs.GetLogger().Errorf("d01 syncThreats GetThreats err:%s", err)
		return
	}

	total := gjson.Get(threats, "total").Int() // 数据总量
	result := gjson.Get(threats, "data.info")

	// 如果是第一页，更新漏洞总数
	if page == constant.DefaultPage {
		if err := proactive_task_node_relations.UpdateThreatSum(sync.proactiveTaskNodeRelation.Id, int(total)); err != nil {
			logs.GetLogger().Errorf("sync d01 task threat - save proactiveTaskNodeRelation Asset sum error: %v", err.Error())
		}
	}

	if total <= 0 {
		proactive_task_node_relations.UpdateNodeDataSyncThreatStatus(sync.proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncSuccessStatus)
		return
	}
	threatTaskIndex := d01.NewTaskThreatsModel().IndexName()
	threatsFinishIndex := d01.NewFinishedThreatsModel().IndexName()
	if sync.cli.IsFoeyeV2() {
		threatTaskIndex = d01.D01V2SourceTaskThreatsIndex
		threatsFinishIndex = d01.D01V2FinishedThreats
	}

	if result.Exists() {
		threatIds := make([]map[string]interface{}, 0)
		threatProcessBulkRequest := es.GetEsClient().Bulk()
		threatTaskBulkRequest := es.GetEsClient().Bulk()
		sourceThreatTaskBulkRequest := es.GetEsClient().Bulk()

		// 遍历 _id 字段，并根据 id 添加新的字段数据
		result.ForEach(func(key, value gjson.Result) bool {
			data, _ := sjson.Set(value.String(), "node_id", sync.node.Id)
			data, _ = sjson.Set(data, "area_id", sync.node.AreaId)

			// 写入资产库
			threatId := strings.ReplaceAll(uuid.New().String(), "-", "")
			threatTaskId := strings.ReplaceAll(uuid.New().String(), "-", "")
			data, _ = sjson.Set(data, "proactive_task_id", sync.proactiveTask.Id)
			threatTaskReq := elastic.NewBulkIndexRequest().Index(threatsFinishIndex).Id(threatTaskId).Doc(data)
			threatTaskBulkRequest = threatTaskBulkRequest.Add(threatTaskReq)

			// 生成漏洞处理模型
			threatProcess := generateThreatProcess(sync, value, threatId)
			threatProcessReq := elastic.NewBulkIndexRequest().Index(threatProcess.IndexName()).Id(threatId).Doc(threatProcess)
			threatProcessBulkRequest = threatProcessBulkRequest.Add(threatProcessReq)

			sourceTask := sync.handleSourceTask(data_sync_task.SyncThreat, value.String(), threatId)
			sourceTask, _ = sjson.Set(sourceTask, "id", threatId)
			sourceThreatTaskReq := elastic.NewBulkIndexRequest().Index(threatTaskIndex).Id(threatId).Doc(sourceTask)
			sourceThreatTaskBulkRequest = sourceThreatTaskBulkRequest.Add(sourceThreatTaskReq)

			threatIds = append(threatIds, map[string]interface{}{"id": threatId, "is_poc": poc.YesPoc, "task_id": fmt.Sprintf("%s_%s", "task_threat_sync", strconv.Itoa(int(sync.proactiveTask.Id)))})
			return true
		})

		if err := sync.handleBulkRequest(threatProcessBulkRequest, "threatProcessBulkRequest"); err != nil {
			return
		}

		if err := sync.handleBulkRequest(threatTaskBulkRequest, "threatTaskBulkRequest"); err != nil {
			return
		}

		if err := sync.handleBulkRequest(sourceThreatTaskBulkRequest, "sourceThreatTaskBulkRequest"); err != nil {
			return
		}
		//if successCount := rq.Push(cfg.LoadQueue().VulnMergeQueue, threatIds); successCount > 0 {
		//	logs.GetSyncLogger().Infof("d01 syncThreats successCount:%d, expectedCount:%d", successCount, len(threatIds))
		//}
	}

	// 分页继续同步
	if page*constant.DefaultSize <= int(total) && total > 0 {
		sync.syncTaskThreat(page+1, nodeTaskId)
	} else {
		proactive_task_node_relations.UpdateNodeDataSyncThreatStatus(sync.proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncSuccessStatus)
		// 发送任务结束标识
		mergeResult, err := pb.GetProtoClient().TriggerMergeForVuln(context.Background(), &pb.TriggerMergeForVulnRequest{
			TriggerMergeBaseRequest: &pb.TriggerMergeBaseRequest{
				SourceId:     sync.node.SourceId,
				NodeId:       sync.node.Id,
				TriggerEvent: "主动扫描任务结束",
				TaskId:       fmt.Sprint(sync.proactiveTask.Id + mysql.ProactiveTaskIdAdd),
				ChildTaskId:  fmt.Sprint(sync.proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd),
			},
			DataRangeByTask: &pb.DataRangeByTask{
				TaskId:      fmt.Sprint(sync.proactiveTask.Id + mysql.ProactiveTaskIdAdd),
				ChildTaskId: fmt.Sprint(sync.proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd),
				NodeId:      sync.node.Id,
			},
		}, pb.ClientWithAddress)

		if err != nil {
			logs.GetLogger().Errorf("proactive task end Send to TriggerMergeRequest error:%v", err)
		}
		if !mergeResult.Success {
			logs.GetLogger().Errorf("proactive task end Send to TriggerMergeRequest Message error:%v", mergeResult.Message)
		}
	}
}

// generateThreatProcess 生成 threatProcess 结构体并设置其属性
func generateThreatProcess(sync SyncTask, value gjson.Result, threatId string) poc.ProcessPoc {
	threatProcess := poc.NewProcessPocModel()
	threatProcess.Id = threatId
	threatProcess.OriginalId = gjson.Get(value.String(), "id").String()
	threatProcess.TaskId = sync.proactiveTask.Id + mysql.ProactiveTaskIdAdd
	threatProcess.ChildTaskId = sync.proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd
	threatProcess.Area = sync.node.AreaId
	threatProcess.Node = sync.node.Id
	threatProcess.Source = sync.node.SourceId
	threatProcess.IsPoc = poc.YesPoc
	threatProcess.Ip = gjson.Get(value.String(), "ip").String()
	threatProcess.Port = int(gjson.Get(value.String(), "port").Int())
	threatProcess.Url = gjson.Get(value.String(), "url").String()
	threatProcess.Name = gjson.Get(value.String(), "common_title").String()
	threatProcess.Cve = gjson.Get(value.String(), "cveId").String()
	threatProcess.HasExp = int(gjson.Get(value.String(), "has_exp").Int())
	threatProcess.Status = int(gjson.Get(value.String(), "state").Int())
	threatProcess.Describe = gjson.Get(value.String(), "common_description").String()
	threatProcess.Details = gjson.Get(value.String(), "common_impact").String()
	threatProcess.Suggestions = gjson.Get(value.String(), "recommandation").String()
	lastResponseAt := proactive_public.ParseTimeOrNow(gjson.Get(value.String(), "last_response").String())
	threatProcess.LastResponseAt = lastResponseAt

	threatProcess.Level = proactive_public.MapLevel(gjson.Get(value.String(), "level").Int())
	threatProcess.VulType = gjson.Get(value.String(), "vulType").String()

	ct := gjson.Get(value.String(), "createtime").String()
	threatProcess.CreatedAt = proactive_public.ParseTimeOrNow(ct)

	updateTime := gjson.Get(value.String(), "lastupdatetime").String()
	threatProcess.UpdatedAt = proactive_public.ParseTimeOrNow(updateTime)

	return *threatProcess
}

// handleBulkRequest 处理批量请求并检查错误
func (sync SyncTask) handleBulkRequest(bulkRequest *es.SafeBulkService, requestName string) error {
	bulkResp, err := bulkRequest.Refresh("true").Do(context.Background())
	if err != nil || bulkResp.Errors {
		errString := handle_es_bulk_error.HandleBulkResp(requestName, err, bulkResp)
		e := proactive_task_node_relations.UpdateNodeDataSyncThreatStatus(sync.proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncFailStatus)
		if e != nil {
			logs.GetLogger().Errorf("UpdateNodeDataSyncThreatStatus error:%v", e)
		}
		return fmt.Errorf("%s Do err: %v", requestName, errString)
	}
	return nil
}
