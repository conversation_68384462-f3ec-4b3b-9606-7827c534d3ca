package bk_cmdb

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestBKCmdbTaskEmployees_IndexName(t *testing.T) {
	assert.Equal(t, "bk_cmdb_task_employees", NewBKCmdbTaskEmployeesModel().IndexName())
	assert.Equal(t, "", NewBKCmdbTaskEmployeesModel().String())
}

func TestGetBKCmdbPeopleListFields(t *testing.T) {
	fields := NewBKCmdbTaskEmployeesModel().GetPeopleListFields()

	expectedFields := []map[string]string{
		{"name": "用户名", "key": "username"},
		{"name": "全名", "key": "display_name"},
		{"name": "手机号", "key": "telephone"},
		{"name": "邮箱", "key": "email"},
		{"name": "账户状态", "key": "status"},
		{"name": "组织", "key": "departments", "show_key": "full_name"},
		{"name": "职位", "key": "position"},
		{"name": "工号", "key": "extras", "show_key": "emp_code"},
	}

	assert.Equal(t, expectedFields, fields)
}

func TestGetBKCmdbPeopleListDetailFields(t *testing.T) {
	fields := NewBKCmdbTaskEmployeesModel().GetPeopleListDetailFields()

	expectedFields := []map[string]string{
		{"name": "用户名", "key": "username"},
		{"name": "全名", "key": "display_name"},
		{"name": "手机号", "key": "telephone"},
		{"name": "邮箱", "key": "email"},
		{"name": "账户状态", "key": "status"},
		{"name": "组织", "key": "departments", "show_key": "full_name"},
		{"name": "职位", "key": "position"},
		{"name": "工号", "key": "extras", "show_key": "emp_code"},
	}

	assert.Equal(t, expectedFields, fields)
}
