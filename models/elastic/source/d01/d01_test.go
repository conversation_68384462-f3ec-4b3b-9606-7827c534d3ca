package d01

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestD01Threats_IndexName(t *testing.T) {
	assert.Equal(t, "d01_task_assets", NewTaskAssetsModel().IndexName())
	assert.Equal(t, "", NewTaskAssetsModel().String())
}

// 测试资产列表字段
func TestAssets_GetFoeyeAssetListFields(t *testing.T) {
	assets := NewTaskAssetsModel()
	fields := assets.GetAssetListFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "端口", "key": "port", "show_value": "port_list"},
		{"name": "协议", "key": "protocol", "show_value": "port_list"},
		{"name": "组件", "key": "rule_info", "show_value": "port_list", "show_key": "name"},
		{"name": "域名", "key": "domain"},
		{"name": "操作系统", "key": "os"},
		{"name": "MAC地址", "key": "mac"},
	}

	assert.Equal(t, expectedFields, fields, "资产列表字段不匹配")
}

// 测试资产详情列表字段
func TestAssets_GetFoeyeAssetListDetailFields(t *testing.T) {
	assets := NewTaskAssetsModel()
	fields := assets.GetAssetListDetailFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "端口", "key": "port", "show_value": "port_list"},
		{"name": "协议", "key": "protocol", "show_value": "port_list"},
		{"name": "组件", "key": "rule_info", "show_value": "port_list", "show_key": "name"},
		{"name": "域名", "key": "domain"},
		{"name": "操作系统", "key": "os"},
		{"name": "MAC地址", "key": "mac"},
		{"name": "资产标识", "key": "is_honeypot/is_fraud"},
		{"name": "风险属性", "key": "threat_status"},
		{"name": "网站截图", "key": "website_image"},
		{"name": "RDP截图", "key": "rdp_image"},
		{"name": "发现时间", "key": "createtime"},
		{"name": "上次扫描时间", "key": "lastupdatetime"},
		{"name": "网站标题", "key": "website_title"},
		{"name": "备注信息", "key": "name"},
		{"name": "地理位置", "key": "city"},
		{"name": "资产等级", "key": "asset_level"},
		{"name": "机房信息", "key": "computer_room"},
		{"name": "管理单元", "key": "company"},
		{"name": "业务系统", "key": "business_app"},
		{"name": "负责人", "key": "username"},
		{"name": "电话", "key": "manager_mobile"},
		{"name": "邮箱", "key": "manager_email"},
	}

	assert.Equal(t, expectedFields, fields, "资产详情列表字段不匹配")
}

// 测试威胁列表字段
func TestThreats_GetThreatListFields(t *testing.T) {
	threats := NewTaskThreatsModel()
	fields := threats.GetThreatListFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "漏洞名称", "key": "common_title"},
		{"name": "CVE编号", "key": "cveId"},
		{"name": "漏洞类型", "key": "vulType"},
		{"name": "漏洞等级", "key": "level"},
		{"name": "漏洞地址", "key": "url"},
		{"name": "发现时间", "key": "createtime"},
		{"name": "通报时间", "key": "notice_time"},
	}

	assert.Equal(t, expectedFields, fields, "威胁列表字段不匹配")
}

// 测试威胁详情列表字段
func TestThreats_GetThreatListDetailFields(t *testing.T) {
	threats := NewTaskThreatsModel()
	fields := threats.GetThreatListDetailFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "漏洞名称", "key": "common_title"},
		{"name": "CVE编号", "key": "cveId"},
		{"name": "漏洞类型", "key": "vulType"},
		{"name": "漏洞等级", "key": "level"},
		{"name": "漏洞地址", "key": "url"},
		{"name": "发现时间", "key": "createtime"},
		{"name": "通报时间", "key": "notice_time"},
		{"name": "上次扫描时间", "key": "lastupdatetime"},
		{"name": "备注信息", "key": "name"},
		{"name": "MAC地址", "key": "mac"},
		{"name": "地理位置", "key": "city"},
		{"name": "资产等级", "key": "asset_level"},
		{"name": "管理单元", "key": "company"},
	}

	assert.Equal(t, expectedFields, fields, "威胁详情列表字段不匹配")
}
