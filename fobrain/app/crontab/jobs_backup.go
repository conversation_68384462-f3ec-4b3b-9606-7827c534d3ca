package crontab

import (
	"context"
	"fmt"
	"strings"
	"time"

	"fobrain/fobrain/app/services/backup"
	"fobrain/fobrain/logs"
	mcron "fobrain/models/mysql/crontab"
	"fobrain/pkg/cfg"
	"git.gobies.org/fobrain/unibackup/pkg/types"
)

// CheckBackupSchedule 检查备份调度（每分钟执行一次）
func (j *Job) CheckBackupSchedule(_ *Cron, _ *mcron.CrontabModel) string {
	if !backup.IsEnabled() {
		return "备份服务未启用"
	}

	// 只检查融合任务，不阻止备份任务之间的并行执行
	if backup.IsMergeTaskRunning() {
		return "融合任务正在运行，跳过备份检查"
	}

	config, err := backup.GetBackupConfig()
	if err != nil {
		return fmt.Sprintf("获取备份配置失败: %v", err)
	}

	now := time.Now()
	result := []string{}

	// 检查是否需要执行分组全量备份（独立检查，不受增量备份影响）
	if shouldExecuteFullBackup(config, now) && !backup.IsBackupTaskRunning(types.BackupTypeArchival) {
		if taskID := executeGroupFullBackup(config); taskID != "" {
			result = append(result, fmt.Sprintf("分组全量备份已启动: %s", taskID))
			// 更新上次全量备份时间
			backup.UpdateLastBackupTime(backup.BackupLastFullTimeKey, now)
		}
	}

	// 检查是否需要执行分组增量备份（独立检查，不受全量备份影响）
	if shouldExecuteIncrBackup(config, now) && !backup.IsBackupTaskRunning(types.BackupTypeChainIncremental) {
		if taskID := executeGroupIncrBackup(config); taskID != "" {
			result = append(result, fmt.Sprintf("分组增量备份已启动: %s", taskID))
			// 更新上次增量备份时间
			backup.UpdateLastBackupTime(backup.BackupLastIncrTimeKey, now)
		}
	}

	if len(result) == 0 {
		return "无需执行备份"
	}

	return strings.Join(result, "; ")
}

// shouldExecuteFullBackup 判断是否应该执行分组全量备份
func shouldExecuteFullBackup(config *backup.BackupConfig, now time.Time) bool {
	// 如果从未执行过，则执行
	if config.LastFullTime == nil {
		return true
	}

	// 检查是否超过间隔时间
	intervalDuration := time.Duration(config.FullIntervalDays) * 24 * time.Hour
	return now.Sub(*config.LastFullTime) >= intervalDuration
}

// shouldExecuteIncrBackup 判断是否应该执行分组增量备份
func shouldExecuteIncrBackup(config *backup.BackupConfig, now time.Time) bool {
	// 如果从未执行过，则执行
	if config.LastIncrTime == nil {
		return true
	}

	// 检查是否超过间隔时间
	intervalDuration := time.Duration(config.IncrIntervalHours) * time.Hour
	return now.Sub(*config.LastIncrTime) >= intervalDuration
}

// executeGroupFullBackup 执行分组全量备份
func executeGroupFullBackup(config *backup.BackupConfig) string {
	manager := backup.GetManager()
	if manager == nil {
		return ""
	}

	// 构建分组全量备份请求
	req := buildGroupBackupRequest(types.BackupTypeArchival, "定时分组全量备份", config)
	if len(req.Sources) == 0 {
		return ""
	}

	// 执行分组全量备份
	taskID, err := manager.BackupAllAsync(context.Background(), req)
	if err != nil {
		logs.GetLogger().Errorf("分组全量备份失败: %v", err)
		return ""
	}

	return taskID
}

// executeGroupIncrBackup 执行分组增量备份
func executeGroupIncrBackup(config *backup.BackupConfig) string {
	manager := backup.GetManager()
	if manager == nil {
		return ""
	}

	// 构建分组增量备份请求
	req := buildGroupBackupRequest(types.BackupTypeChainIncremental, "定时分组增量备份", config)
	if len(req.Sources) == 0 {
		return ""
	}

	// 执行分组增量备份
	taskID, err := manager.BackupAllAsync(context.Background(), req)
	if err != nil {
		logs.GetLogger().Errorf("分组增量备份失败: %v", err)
		return ""
	}

	return taskID
}

// buildGroupBackupRequest 构建分组备份请求
func buildGroupBackupRequest(backupType types.BackupType, description string, config *backup.BackupConfig) types.BackupAllRequest {
	var sources []types.BackupRequest

	// 默认添加MySQL备份
	sources = append(sources, types.BackupRequest{
		SourceType:  types.MySQL,
		SourceName:  cfg.LoadMysql().Database,
		BackupType:  backupType,
		Description: fmt.Sprintf("%s-MySQL", description),
	})

	// 默认添加ES备份
	sources = append(sources, types.BackupRequest{
		SourceType:  types.Elasticsearch,
		SourceName:  "main_cluster",
		BackupType:  backupType,
		Description: fmt.Sprintf("%s-ES", description),
	})

	return types.BackupAllRequest{
		Sources:          sources,
		Description:      description,
		Atomic:           true,
		CleanupOnFailure: true,
	}
}

// CheckBackupLockStatus 检查备份锁状态（每5分钟执行一次）
func (j *Job) CheckBackupLockStatus(_ *Cron, _ *mcron.CrontabModel) string {
	if !backup.IsEnabled() {
		return "备份服务未启用"
	}

	// 清理过期的任务状态缓存
	backup.ClearTaskStatusCache()

	return "备份锁状态检查完成"
}

// RegisterBackupScheduleCheck 注册备份调度检查定时任务
func RegisterBackupScheduleCheck() *mcron.CrontabModel {
	return NewSchedulerJob("CheckBackupSchedule", "0 * * * * *", "CheckBackupSchedule", "")
}

// RegisterBackupLockStatusCheck 注册备份锁状态检查定时任务
func RegisterBackupLockStatusCheck() *mcron.CrontabModel {
	return NewSchedulerJob("CheckBackupLockStatus", "0 */5 * * * *", "CheckBackupLockStatus", "")
}
