package mach_lake

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestMachLakeTaskAssets_IndexName(t *testing.T) {
	assert.Equal(t, "mach_lake_task_assets", NewMachLakeTaskAssetsModel().IndexName())
	assert.Equal(t, "", NewMachLakeTaskAssetsModel().String())
}

func TestMachLakeTaskAssets_GetMachLakeAssetListFields(t *testing.T) {
	m := NewMachLakeTaskAssetsModel()
	fields := m.GetMachLakeAssetListFields()

	assert.Equal(t, fields, []map[string]string{
		{"name": "IP地址", "key": "ipAddress", "all_key": "ipAddress"},
		{"name": "Ｍac地址", "key": "macAddress", "all_key": "macAddress"},
		{"name": "内外网", "key": "ipLocality", "all_key": "ipLocality"},
		{"name": "总字节数", "key": "totalBytes", "all_key": "totalBytes"},
		{"name": "总数据包", "key": "totalPackets", "all_key": "totalPackets"},
	})
}

func TestMachLakeTaskAssets_GetMachLakeAssetListDetailFields(t *testing.T) {
	m := NewMachLakeTaskAssetsModel()
	fields := m.GetMachLakeAssetListDetailFields()

	assert.Equal(t, fields, []map[string]string{
		{"name": "IP地址", "key": "ipAddress", "all_key": "ipAddress"},
		{"name": "Ｍac地址", "key": "macAddress", "all_key": "macAddress"},
		{"name": "内外网", "key": "ipLocality", "all_key": "ipLocality"},
		{"name": "总字节数", "key": "totalBytes", "all_key": "totalBytes"},
		{"name": "总数据包", "key": "totalPackets", "all_key": "totalPackets"},
	})
}
