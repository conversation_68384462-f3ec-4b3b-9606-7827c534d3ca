package bk_cmdb_custom_module_property

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/olivere/elastic/v7"
)

type (
	BkCmdbCustomTaskEcsAssets struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`         // NODE+区域+IP 作为唯一值去更新，用作数据对比
		CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
		UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
	}
)

// IndexName 索引名
func (b *BkCmdbCustomTaskEcsAssets) IndexName() string {
	return "bk_cmdb_cloud_ecs_task_assets"
}

func (b *BkCmdbCustomTaskEcsAssets) String() string {
	if b == nil {
		return ""
	}
	return ""
}

func NewBkCmdbCustomTaskEcsAssetsModel() *BkCmdbCustomTaskEcsAssets {
	return &BkCmdbCustomTaskEcsAssets{}
}

func (b *BkCmdbCustomTaskEcsAssets) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("PrivateIpAddress.keyword").
		Field("OrgName.keyword").
		Field("CPU.keyword").
		Field("bk_inst_name.keyword").
		Field("biz.keyword")
	return field
}

func (b *BkCmdbCustomTaskEcsAssets) GetAssetListFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "PrivateIpAddress", "all_key": "PrivateIpAddress"},
		{"name": "CPU", "key": "ecs_cpu", "all_key": "ecs_cpu"},
		{"name": "组织架构名称", "key": "OrgName", "all_key": "OrgName"},
		{"name": "名称", "key": "biz", "all_key": "biz"},
		{"name": "内存", "key": "ecs_memory", "all_key": "ecs_memory"},
	}
}

func (b *BkCmdbCustomTaskEcsAssets) GetAssetListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "PrivateIpAddress", "all_key": "PrivateIpAddress"},
		{"name": "CPU", "key": "ecs_cpu", "all_key": "ecs_cpu"},
		{"name": "组织架构名称", "key": "OrgName", "all_key": "OrgName"},
		{"name": "名称", "key": "biz", "all_key": "biz"},
		{"name": "内存", "key": "ecs_memory", "all_key": "ecs_memory"},
	}
}
