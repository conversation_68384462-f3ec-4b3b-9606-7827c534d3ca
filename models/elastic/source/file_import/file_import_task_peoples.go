package file_import

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/olivere/elastic/v7"
)

type (
	FileImportTaskPeoples struct {
		*es.BaseModel `json:"-"`
		Id            string         `json:"id"`           //  NODE+人名+区域 唯一值
		TaskId        string         `json:"task_id"`      //
		Area          int            `json:"area"`         // 区域
		Source        int            `json:"source"`       // 来源
		Node          int            `json:"node"`         // 节点
		Name          string         `json:"name"`         // 姓名
		EnglishName   string         `json:"english_name"` // 英文名
		Title         string         `json:"title"`        // 职位
		Mobile        string         `json:"mobile"`       // 手机号
		Email         string         `json:"email"`        // 邮箱
		Department    string         `json:"department"`   // 部门
		WorkNumber    string         `json:"work_number"`  // 工号
		Status        string         `json:"status"`       // 状态
		CreatedAt     localtime.Time `json:"created_at"`   // 创建时间
		UpdatedAt     localtime.Time `json:"updated_at"`   // 更新时间
	}
)

// IndexName 索引名
func (f *FileImportTaskPeoples) IndexName() string {
	return "file_import_task_peoples"
}

func (f *FileImportTaskPeoples) String() string {
	if f == nil {
		return ""
	}
	return ""
}

func NewFileImportTaskPeoplesModel() *FileImportTaskPeoples {
	return &FileImportTaskPeoples{}
}

func (f *FileImportTaskPeoples) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("title.keyword").
		Field("email.keyword").
		Field("name.keyword").
		Field("mobile.keyword")

	return field
}

func (f *FileImportTaskPeoples) GetPeopleListFields() []map[string]string {
	return []map[string]string{
		{"name": "姓名", "key": "name"},
		{"name": "电话号码", "key": "mobile"},
		{"name": "企业邮箱", "key": "email"},
		{"name": "职位", "key": "title"},
		{"name": "部门", "key": "department"},
		{"name": "工号", "key": "work_number"},
	}
}

func (f *FileImportTaskPeoples) GetPeopleListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "姓名", "key": "name"},
		{"name": "英文名", "key": "english_name"},
		{"name": "职位", "key": "title"},
		{"name": "电话号码", "key": "mobile"},
		{"name": "企业邮箱", "key": "email"},
		{"name": "部门", "key": "department"},
		{"name": "工号", "key": "work_number"},
		{"name": "在职状态", "key": "status"},
	}
}
