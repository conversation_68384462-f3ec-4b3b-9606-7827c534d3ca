package bk_cmdb

import (
	"bytes"
	"context"
	testcommon "fobrain/fobrain/tests/common_test"
	httputil "fobrain/pkg/http"
	"io/ioutil"
	"net/http"
	"testing"
	"time"

	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"
	"github.com/olivere/elastic/v7"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"

	"fobrain/fobrain/app/services/node"
)

func TestBkCmdbCustomBusiness_GetVul(t *testing.T) {
	cli := NewBkCmdbCustomBusiness()
	opera := node.NewMockOpera() // 使用 MockOpera
	handler := func(original map[string]interface{}) map[string]interface{} {
		return original
	}
	err := cli.GetVul(context.TODO(), opera, handler)
	assert.NoError(t, err)
}

func TestBkCmdbCustomBusiness_CustomGetAssetsBusiness(t *testing.T) {
	cli := NewBkCmdbCustomBusiness()
	if err := cli.SetConfig(map[string]any{
		"account": "admin", "password": "aabbccddee", "ip": "127.0.0.1:8099", "protocol": "http",
	}); err != nil {
		t.Errorf(err.Error())
	}

	resp := http.Response{
		StatusCode: http.StatusOK,
		Body:       ioutil.NopCloser(bytes.NewBufferString(`{"code":0,"permission":null,"result":true,"request_id":"091a38b2d1134627a0c9114477fd11bf","message":"success","data":{"count":1,"info":[{"PrivateIpAddress":"127.0.0.1"}]}}`)),
	}
	httpClient := httputil.NewHttpClient(cli.Client.Client)
	time.Sleep(time.Millisecond * 300)
	defer gomonkey.ApplyMethodReturn(httpClient, "Post", &resp, nil).Reset()

	// 调用测试方法
	list, total, err := cli.CustomGetAssetsBusiness(1, 1, 0)
	assert.NoError(t, err)
	assert.Equal(t, int64(1), total)
	assert.NotNil(t, list)
}

func TestBkCmdbCustomBusiness_GetAssets(t *testing.T) {
	mockDb := testcommon.InitSqlMock()
	defer mockDb.Close()

	// Setup miniredis
	s, err := miniredis.Run()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer s.Close()

	// Create a redis client with the miniredis address
	redisClient := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	testcommon.SetRedisClient(redisClient)

	// 使用 AnyQuery() 来匹配所有SQL查询，避免正则表达式匹配问题
	mockDb.ExpectQuery("").
		WillReturnRows(mockDb.NewRows([]string{"id", "name"}).AddRow(1, "name"))

	mockDb.ExpectQuery("").
		WillReturnRows(mockDb.NewRows([]string{"id", "name"}).AddRow(1, "name"))

	// 添加更多期望的查询来匹配测试中可能的数据库调用
	mockDb.ExpectQuery("").
		WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(0))

	mockDb.ExpectQuery("").
		WillReturnRows(mockDb.NewRows([]string{"id", "weight"}).AddRow(1, 1))

	mockDb.ExpectQuery("").
		WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(0))

	mockDb.ExpectQuery("").
		WillReturnRows(mockDb.NewRows([]string{"id", "weight"}).AddRow(1, 1))

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()

	mockServer.Register("/staff/_search", []*elastic.SearchHit{
		{
			Id: "1",
			Source: []byte(`{
				  "fid": "1"
			}`),
		},
		{
			Id: "2",
			Source: []byte(`{
				  "fid": "2"
			}`),
		},
	})
	mockServer.Register("/_search/scroll", []*elastic.SearchHit{})
	mockServer.Register("/business_systems/_search", []*elastic.SearchHit{})

	cli := NewBkCmdbCustomBusiness()

	// 创建一个自定义的 mock opera，避免类型断言问题
	opera := &MockOperaWithStrings{}

	handler := func(original map[string]interface{}) map[string]interface{} {
		return original
	}
	result := gjson.Result{
		Type: 5,
		Raw:  `[{"hold_source_code":"1","go_live_date":"2022-10-31","bk_biz_developer":"","bk_biz_maintainer":"aa","create_time":"2022-07-09T00:48:32.142+08:00","deploy_mode":"depl_02","operator":"","bk_biz_name":"ITOM控平台","last_time":"2024-11-25T10:57:06.634+08:00","life_cycle":null,"dev_language":"dev_l01","dev_framework":"dev_e_03","biz_sysmanager":"zgliang","default":0,"bk_biz_productor":"","biz_itsm":true,"supplier":"科技","department_manager":"zql","app_category":"app_07","app_domain":"app_08","biz_status":"RegistNewAppStatus02","biz_developer":"","bk_biz_tester":"","en_full_name":"IT Management","build_mode":"build_mode_01","expiring_date":"","access_by_external":"False","app_brief":"运维基础监动化调度平台等。","bk_biz_id":2,"language":"1","biz_business":"zhaihyingjun","time_zone":"Asia/Shanghai","main_page_url":"https://a.com","biz_sysadminer":"zhayingjun","access_by_mobile":"False","bk_supplier_account":"0","belong_org":"IT运营组"}]`,
	}
	time.Sleep(time.Millisecond * 300)

	patches := gomonkey.ApplyMethodReturn(cli, "CustomGetAssetsBusiness", result, int64(201), nil)
	var originalDatas []map[string]interface{}

	patches.ApplyFuncReturn(cli.execBusinessData, originalDatas, int64(201), nil)
	patches.ApplyMethodReturn(opera, "BatchUpdateSourceAsset", nil)
	patches.ApplyMethodReturn(opera, "UpdateSyncDataTotal", nil)
	patches.ApplyMethodReturn(opera, "UpdateSyncDataSuccessTotal", nil)

	err = cli.GetAssets(context.TODO(), opera, handler)
	patches.Reset()
	assert.NoError(t, err)
}

// MockOperaWithStrings 是一个返回正确字符串类型的 mock opera
type MockOperaWithStrings struct {
	Status    int
	UUID      string
	Logs      []string
	Process   int
	Total     int
	SyncSData []map[string]interface{}
	SyncPData []map[string]interface{}
}

func (m *MockOperaWithStrings) GetParamByKey(key string) any {
	switch key {
	case "app_code":
		return "test_app_code"
	case "app_secret":
		return "test_app_secret"
	case "account":
		return "test_account"
	case "ip":
		return "127.0.0.1"
	case "protocol":
		return "http"
	case "person_field":
		return "name"
	default:
		return key
	}
}

func (m *MockOperaWithStrings) UpdateSyncDataTotal(syncType int, total int) error {
	m.Total = total
	return nil
}

func (m *MockOperaWithStrings) UpdateSyncDataSuccessTotal(syncType int, num int) error {
	return nil
}

func (m *MockOperaWithStrings) SetStatus(syncType int, status int, msg string) error {
	m.Status = status
	m.Logs = append(m.Logs, msg)
	return nil
}

func (m *MockOperaWithStrings) SetProcess(syncType int, process int) error {
	m.Process = process
	return nil
}

func (m *MockOperaWithStrings) BatchUpdateAsset(sourceData, processData, processDeviceData []map[string]interface{}) error {
	m.SyncSData = append(m.SyncSData, sourceData...)
	m.SyncPData = append(m.SyncPData, processData...)
	return nil
}

func (m *MockOperaWithStrings) BatchUpdateVul(sourceData, processData []map[string]interface{}) error {
	return nil
}

func (m *MockOperaWithStrings) BatchUpdateStaff(sourceData, processData []map[string]interface{}) error {
	return nil
}

func (m *MockOperaWithStrings) WriteLog(msg string) {
	m.Logs = append(m.Logs, msg)
}

func (m *MockOperaWithStrings) GetUUID(id string) (string, string) {
	return m.UUID, "mock-uuid"
}

func (m *MockOperaWithStrings) BatchUpdateSourceAsset(sourceData []map[string]interface{}) error {
	m.SyncSData = append(m.SyncSData, sourceData...)
	return nil
}

func (m *MockOperaWithStrings) GetTaskId(int) (uint64, uint64) {
	return 1, 2
}

func TestValidateTimeUsingRegex(t *testing.T) {
	testCases := []struct {
		name     string
		timeStr  string
		expected bool
	}{
		{
			name:     "Valid YYYY-MM-DD",
			timeStr:  "2023-12-31",
			expected: true,
		},
		{
			name:     "Valid HH:MM:SS",
			timeStr:  "23:59:59",
			expected: true,
		},
		{
			name:     "Valid YYYY-MM-DD HH:MM:SS",
			timeStr:  "2023-12-31 23:59:59",
			expected: true,
		},
		{
			name:     "Valid YYYY/MM/DD",
			timeStr:  "2023/12/31",
			expected: true,
		},
		{
			name:     "Valid YYYY/MM/DD HH:MM:SS",
			timeStr:  "2023/12/31 23:59:59",
			expected: true,
		},
		{
			name:     "Valid ISO8601",
			timeStr:  "2023-12-31T23:59:59Z",
			expected: true,
		},
		{
			name:     "Valid ISO8601 with timezone",
			timeStr:  "2023-12-31T23:59:59+08:00",
			expected: true,
		},
		{
			name:     "Valid ISO8601 with milliseconds",
			timeStr:  "2023-12-31T23:59:59.999+08:00",
			expected: true,
		},
		{
			name:     "Valid DD Mon YY HH:MM MST",
			timeStr:  "31 Dec 23 23:59 CST",
			expected: true,
		},
		{
			name:     "Invalid time format",
			timeStr:  "invalid",
			expected: false,
		},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := ValidateTimeUsingRegex(tc.timeStr)
			assert.Equal(t, tc.expected, result)
		})
	}
}

func TestBkCmdbCustomBusiness_GetPerson(t *testing.T) {
	cli := NewBkCmdbCustomBusiness()
	opera := node.NewMockOpera() // 使用 MockOpera
	handler := func(original map[string]interface{}) map[string]interface{} {
		return original
	}
	err := cli.GetPerson(context.TODO(), opera, handler)
	assert.NoError(t, err)
}
