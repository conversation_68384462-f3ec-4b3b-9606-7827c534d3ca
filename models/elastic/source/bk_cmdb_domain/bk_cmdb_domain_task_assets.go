package bk_cmdb_domain

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/olivere/elastic/v7"
)

type BKCmdbDomainTaskAssets struct {
	*es.BaseModel `json:"-"`
	Id            string          `json:"id"`         // NODE+区域+IP 作为唯一值去更新，用作数据对比
	CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
	UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
}

func (b *BKCmdbDomainTaskAssets) IndexName() string {
	return "bk_cmdb_domain_task_assets"
}

func (b *BKCmdbDomainTaskAssets) String() string {
	return ""
}

func NewBKCmdbDomainTaskAssetsModel() *BKCmdbDomainTaskAssets {
	return &BKCmdbDomainTaskAssets{}
}

func (b *BKCmdbDomainTaskAssets) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("bk_inst_id.keyword").
		Field("address.keyword").
		Field("bk_inst_name.keyword").
		Field("dns.keyword").
		Field("type.keyword").
		Field("location.keyword").
		Field("biz.keyword")
	return field
}

func (b *BKCmdbDomainTaskAssets) GetAssetListFields() []map[string]string {
	return []map[string]string{
		{"name": "ID", "key": "bk_inst_id", "all_key": "bk_inst_id"},
		{"name": "域名", "key": "dns", "all_key": "dns"},
		{"name": "实例名", "key": "bk_inst_name", "all_key": "bk_inst_name"},
		{"name": "所属业务", "key": "biz", "all_key": "biz"},
		{"name": "IP", "key": "address", "all_key": "address"},
	}
}

func (b *BKCmdbDomainTaskAssets) GetAssetListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "ID", "key": "bk_inst_id", "all_key": "bk_inst_id"},
		{"name": "域名", "key": "dns", "all_key": "dns"},
		{"name": "实例名", "key": "bk_inst_name", "all_key": "bk_inst_name"},
		{"name": "所属业务", "key": "biz", "all_key": "biz"},
		{"name": "IP", "key": "address", "all_key": "address"},
	}
}
