package cfg

import testcommon "fobrain/fobrain/tests/common_test"

type Common struct {
	Local             bool   `json:"local"`
	Env               string `json:"env"`
	LogLevel          string `json:"log_level"`
	Listen            string `json:"listen"`
	MergeListen       string `json:"merge_listen"`
	Network           string `json:"network"`
	CloseCaptcha      string `json:"close_captcha"`
	SysName           string `json:"app_name"`     // 产品名称
	Copyright         string `json:"copyright"`    // 产品名称
	Version           string `json:"version"`      // 版本
	LicensePath       string `json:"license_path"` // license文件路径
	StoragePath       string `json:"root_storage"` // 存储路径, storage目录的路径即可，比如：./storage
	CloseAuthToken    string `json:"close_auth_token"`
	HiddenMenu        string `json:"hidden_menu"`       // 隐藏菜单,逗号分隔
	DownloadUrl       string `json:"download_url"`      // 升级包下载地址
	EncryptionKey     string `json:"encryption_key"`    // 加密key
	MysqlBinlogPath   string `json:"mysql_binlog_path"` //mysql binlogpath
	MysqlDumpBinPath  string `json:"mysql_dump_path"`
	MysqlBinPath      string `json:"mysql_bin_path"`
	MysqlAdminBinPath string `json:"mysql_admin_bin_path"`
	RepoBasePath      string `json:"repo_base_path"`
	BinlogBasePath    string `json:"binlog_base_path"`
}

// LoadCommon 加载Common配置
func LoadCommon() Common {
	// _ = config.Get("common").Scan(&GetInstance().Common)
	t := GetInstance().Common
	if testcommon.IsTest() {
		t.LicensePath = "/data/fobrain/storage/.test-license"
	}
	return t
}
