package bk_cmdb_custom_module_property

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestBkCmdbCustomEcsAssets_IndexName(t *testing.T) {
	assert.Equal(t, "bk_cmdb_cloud_ecs_task_assets", NewBkCmdbCustomTaskEcsAssetsModel().IndexName())
	assert.Equal(t, "", NewBkCmdbCustomTaskEcsAssetsModel().String())
}

func TestBkCmdbCustomEcsAssets_GetAssetListFields(t *testing.T) {
	fields := NewBkCmdbCustomTaskEcsAssetsModel().GetAssetListFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "IP地址", "key": "PrivateIpAddress", "all_key": "PrivateIpAddress"},
		{"name": "CPU", "key": "ecs_cpu", "all_key": "ecs_cpu"},
		{"name": "组织架构名称", "key": "OrgName", "all_key": "OrgName"},
		{"name": "名称", "key": "biz", "all_key": "biz"},
		{"name": "内存", "key": "ecs_memory", "all_key": "ecs_memory"},
	}
	assert.Equal(t, expectedFields, fields, "字段不匹配")
}

func TestBkCmdbCustomEcsAssets_GetAssetListDetailFields(t *testing.T) {
	fields := NewBkCmdbCustomTaskEcsAssetsModel().GetAssetListDetailFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "IP地址", "key": "PrivateIpAddress", "all_key": "PrivateIpAddress"},
		{"name": "CPU", "key": "ecs_cpu", "all_key": "ecs_cpu"},
		{"name": "组织架构名称", "key": "OrgName", "all_key": "OrgName"},
		{"name": "名称", "key": "biz", "all_key": "biz"},
		{"name": "内存", "key": "ecs_memory", "all_key": "ecs_memory"},
	}
	assert.Equal(t, expectedFields, fields, "字段不匹配")
}
