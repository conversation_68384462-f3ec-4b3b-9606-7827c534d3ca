package mach_lake

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"fobrain/pkg/utils"

	"github.com/olivere/elastic/v7"
)

type (
	MachLakeTaskAssets struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`      // NODE+区域+IP 作为唯一值去更新，用作数据对比
		TaskId        string          `json:"task_id"` //
		ChildTaskId   string          `json:"child_task_id"`
		CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
		UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
	}
)

// IndexName 索引名
func (f *MachLakeTaskAssets) IndexName() string {
	return "mach_lake_task_assets"
}

func (f *MachLakeTaskAssets) String() string {
	if f == nil {
		return ""
	}
	return ""
}

func NewMachLakeTaskAssetsModel() *MachLakeTaskAssets {
	return &MachLakeTaskAssets{}
}

func (f *MachLakeTaskAssets) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("ipAddress.keyword").
		Field("macAddress.keyword")

	return field
}

func (f *MachLakeTaskAssets) GetMachLakeAssetListFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ipAddress", "all_key": "ipAddress"},
		{"name": "Ｍac地址", "key": "macAddress", "all_key": "macAddress"},
		{"name": "内外网", "key": "ipLocality", "all_key": "ipLocality"},
		{"name": "总字节数", "key": "totalBytes", "all_key": "totalBytes"},
		{"name": "总数据包", "key": "totalPackets", "all_key": "totalPackets"},
	}
}

func (f *MachLakeTaskAssets) GetMachLakeAssetListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ipAddress", "all_key": "ipAddress"},
		{"name": "Ｍac地址", "key": "macAddress", "all_key": "macAddress"},
		{"name": "内外网", "key": "ipLocality", "all_key": "ipLocality"},
		{"name": "总字节数", "key": "totalBytes", "all_key": "totalBytes"},
		{"name": "总数据包", "key": "totalPackets", "all_key": "totalPackets"},
	}
}
