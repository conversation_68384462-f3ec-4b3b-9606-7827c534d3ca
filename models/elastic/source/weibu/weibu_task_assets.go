package weibu

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"fobrain/pkg/utils"

	"github.com/olivere/elastic/v7"
)

type (
	WeibuTaskAssets struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`      // NODE+区域+IP 作为唯一值去更新，用作数据对比
		TaskId        string          `json:"task_id"` //
		ChildTaskId   string          `json:"child_task_id"`
		CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
		UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
	}
)

// IndexName 索引名
func (f *WeibuTaskAssets) IndexName() string {
	return "weibu_task_assets"
}

func (f *WeibuTaskAssets) String() string {
	if f == nil {
		return ""
	}
	return ""
}

func NewWeibuTaskAssetsModel() *WeibuTaskAssets {
	return &WeibuTaskAssets{}
}

func (f *WeibuTaskAssets) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("machine").
		Field("machine_name.keyword").
		Field("service_port.app_proto.keyword").
		Field("service_port.service_name.keyword").
		Field("service_port.proto.keyword").
		Field("service_port.port.keyword")

	return field
}

func (f *WeibuTaskAssets) GetWeibuAssetListFields() []map[string]string {
	return []map[string]string{
		{"name": "告警主机", "key": "machine", "all_key": "machine"},
		{"name": "域名", "key": "machine_name", "all_key": "machine_name.*"},
		{"name": "服务", "key": "service", "show_value": "service_port", "all_key": "service_port.*.service"},
		{"name": "传输层协议", "key": "proto", "show_value": "service_port", "all_key": "service_port.*.proto"},
		{"name": "应用层协议", "key": "app_proto", "show_value": "service_port", "all_key": "service_port.*.app_proto"},
		{"name": "关联端口", "key": "port", "show_value": "service_port", "all_key": "service_port.*.port"},
	}
}

func (f *WeibuTaskAssets) GetWeibuAssetListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "告警主机", "key": "machine", "all_key": "machine"},
		{"name": "域名", "key": "machine_name", "all_key": "machine_name.*"},
		{"name": "服务", "key": "service", "show_value": "service_port", "all_key": "service_port.*.service"},
		{"name": "传输层协议", "key": "proto", "show_value": "service_port", "all_key": "service_port.*.proto"},
		{"name": "应用层协议", "key": "app_proto", "show_value": "service_port", "all_key": "service_port.*.app_proto"},
		{"name": "关联端口", "key": "port", "show_value": "service_port", "all_key": "service_port.*.port"},
		{"name": "服务名", "key": "service_name", "show_value": "service_port", "all_key": "service_port.*.service_name"},
		{"name": "关联域名", "key": "machine_name", "show_value": "service_port", "all_key": "service_port.*.machine_name.*"},
	}
}
