package x_ray

import (
	"errors"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/models/mysql/data_source"
)

func TestSync(t *testing.T) {
	t.Run("err", func(t *testing.T) {
		node := &data_source.Node{
			SourceId: data_source.XRaySourceId,
		}
		node.Id = 1

		err := Sync(node, `{"Task": {"id": 1}}`)
		assert.NotEmpty(t, err)
	})

	t.Run("success", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").
			WillReturnRows(sqlmock.NewRows([]string{"id", "key", "value"}).AddRow(int64(8), "key", "value"))

		node := &data_source.Node{
			SourceId: data_source.XRaySourceId,
		}
		node.Id = 1

		taskInfo := `{"Task":{"id":26,"created_at":"2024-11-19 10:52:01","updated_at":"2024-11-19 10:52:01","source_id":14,"node_id":2,"status":0,"source":2,"start_at":"2024-11-19 10:52:01","end_at":"","message":""},"ChildTasks":{"1":{"id":49,"created_at":"2024-11-19 10:52:01","updated_at":"2024-11-19 10:52:01","source_id":14,"node_id":2,"task_id":26,"status":0,"type":1,"sync_data_total":0,"sync_data_success_total":0,"message":"","start_at":"","end_at":""},"2":{"id":50,"created_at":"2024-11-19 10:52:01","updated_at":"2024-11-19 10:52:01","source_id":14,"node_id":2,"task_id":26,"status":0,"type":2,"sync_data_total":0,"sync_data_success_total":0,"message":"","start_at":"","end_at":""},"3":{"id":0,"created_at":"","updated_at":"","source_id":0,"node_id":0,"task_id":0,"status":0,"type":0,"sync_data_total":0,"sync_data_success_total":0,"message":"","start_at":"","end_at":""}}}`
		err := Sync(node, taskInfo)
		assert.Empty(t, err)
	})
}

func Test_setTaskFailure(t *testing.T) {
	t.Run("err", func(t *testing.T) {
		node := &data_source.Node{
			SourceId: data_source.XRaySourceId,
		}
		node.Id = 1

		errMock := errors.New("mock")
		setTaskFailure("syncThreats", "GetThreats", 1, errMock, 1)
		// assert.NotEmpty(t, err)
		// 函数就打印了日志，所以不用断言了
	})
}

func Test_handleThreats(t *testing.T) {
	t.Run("err", func(t *testing.T) {
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()

		node := &data_source.Node{
			SourceId: data_source.XRaySourceId,
		}
		node.Id = 1

		xrayRes := map[string]interface{}{"category": "info_leak", "created_time": "2024-11-04T16:14:24.670756+08:00", "cvss": "3.1", "cwe": nil, "definiteness": "CERTAINLY", "detail": "经过对以下标进行扫描测试：\n```\n************:22\n```\n发现存在该漏洞。\n\n\n\n\n\n\n\n", "discoveries_count": 3, "exp": "暂无", "exposures": []string{"CT-103295", "CVE-2008-5161", "CNNVD-200811-321", "32319"}, "fixed_time": nil, "host_id": 235, "id": float64(131), "impact": "造成用户信息，站点配置信息等敏感信息泄露。", "last_scan_at": "2024-11-06T15:48:50.981463+08:00", "poc": "暂无相关漏洞原理信息", "project": map[string]any{"full_name": "默认工作区", "id": 1, "name": "默认工作区"}, "published_datetime": "2008-11-20T00:00:00+08:00", "related_asset": map[string]any{"id": 271, "type": "SERVICE"}, "related_tasks": []int{112, 107}, "severity": "MEDIUM", "solution": "目前厂商已发布了升级补丁以修复这个安全问题，补丁下载链接：\nOpenSSH\n-------\nhttps://downloads.ssh.com/\n临时解决方法：\n* 在SSH会话中仅使用CTR模式加密算，如AES-CTR。", "status": "UNHANDLED", "summary": "OpenSSH是一种开放源码的SSH协议的实现，初始版本用于OpenBSD平台，现在已经被移植到多种Unix/Linux类操系统下。 如果配置为CBC模式的话，OpenSSH没有正确地处理分组密码算法加密的SSH会话中所出现的错误，导致可能泄露密文中任意块最多32位纯文本。在以标准配使用OpenSSH时，攻击者恢复32位纯文本的成功概率为2^{-18}，此外另一种攻击变种恢复14位纯文本的成功概率为2^{-14}。", "target": map[string]any{"host": "************", "port": 22, "protocol": "tcp"}, "title": "CVE-2008-5161: OpenSSH CBC模式信息泄露漏洞", "updated_time": "2024-11-06T15:48:51.441068+08:00", "weak_passwords": []string{}, "xprocess_ids": []int{112, 107}}
		_, _, err := handleThreats([]map[string]interface{}{xrayRes}, node, uint64(1), uint64(1))
		assert.NotEmpty(t, err)
	})

	t.Run("success", func(t *testing.T) {
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()

		mockServer.Register("_bulk", &elastic.BulkIndexByScrollResponse{
			Updated: 1,
		})

		node := &data_source.Node{
			SourceId: data_source.XRaySourceId,
		}
		node.Id = 1

		xrayRes := map[string]interface{}{"category": "info_leak", "created_time": "2024-11-04T16:14:24.670756+08:00", "cvss": "3.1", "cwe": nil, "definiteness": "CERTAINLY", "detail": "经过对以下标进行扫描测试：\n```\n************:22\n```\n发现存在该漏洞。\n\n\n\n\n\n\n\n", "discoveries_count": 3, "exp": "暂无", "exposures": []string{"CT-103295", "CVE-2008-5161", "CNNVD-200811-321", "32319"}, "fixed_time": nil, "host_id": 235, "id": float64(131), "impact": "造成用户信息，站点配置信息等敏感信息泄露。", "last_scan_at": "2024-11-06T15:48:50.981463+08:00", "poc": "暂无相关漏洞原理信息", "project": map[string]any{"full_name": "默认工作区", "id": 1, "name": "默认工作区"}, "published_datetime": "2008-11-20T00:00:00+08:00", "related_asset": map[string]any{"id": 271, "type": "SERVICE"}, "related_tasks": []int{112, 107}, "severity": "MEDIUM", "solution": "目前厂商已发布了升级补丁以修复这个安全问题，补丁下载链接：\nOpenSSH\n-------\nhttps://downloads.ssh.com/\n临时解决方法：\n* 在SSH会话中仅使用CTR模式加密算，如AES-CTR。", "status": "UNHANDLED", "summary": "OpenSSH是一种开放源码的SSH协议的实现，初始版本用于OpenBSD平台，现在已经被移植到多种Unix/Linux类操系统下。 如果配置为CBC模式的话，OpenSSH没有正确地处理分组密码算法加密的SSH会话中所出现的错误，导致可能泄露密文中任意块最多32位纯文本。在以标准配使用OpenSSH时，攻击者恢复32位纯文本的成功概率为2^{-18}，此外另一种攻击变种恢复14位纯文本的成功概率为2^{-14}。", "target": map[string]any{"host": "************", "port": 22, "protocol": "tcp"}, "title": "CVE-2008-5161: OpenSSH CBC模式信息泄露漏洞", "updated_time": "2024-11-06T15:48:51.441068+08:00", "weak_passwords": []string{}, "xprocess_ids": []int{112, 107}}
		threats, _, _ := handleThreats([]map[string]interface{}{xrayRes}, node, uint64(1), uint64(1))
		assert.NotEmpty(t, threats)
	})
}

func Test_handleAssets(t *testing.T) {
	t.Run("err", func(t *testing.T) {
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		// 模拟网络类型查询失败
		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE node_id = ? AND `key` = ? ORDER BY `data_node_configs`.`id` LIMIT 1").
			WithArgs(int64(1), "network_type").
			WillReturnError(errors.New("mock database error"))

		node := &data_source.Node{
			SourceId: data_source.XRaySourceId,
		}
		node.Id = 1

		xrayRes := map[string]interface{}{"id": float64(131), "address": "127.0.0.1"}
		_, err := handleAssets([]map[string]interface{}{xrayRes}, node, uint64(1), uint64(1))
		assert.NotEmpty(t, err)
	})

	t.Run("success", func(t *testing.T) {
		mockServer := testcommon.NewMockServer()
		defer mockServer.Close()
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()

		// 模拟网络类型查询成功
		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE node_id = ? AND `key` = ? ORDER BY `data_node_configs`.`id` LIMIT 1").
			WithArgs(int64(1), "network_type").
			WillReturnRows(mockDb.NewRows([]string{"id", "node_id", "key", "value"}).AddRow(1, 1, "network_type", "1"))

		// 模拟获取所有配置
		mockDb.ExpectQuery("SELECT * FROM `data_node_configs` WHERE `node_id` = ?").
			WithArgs(int64(1)).
			WillReturnRows(mockDb.NewRows([]string{"id", "node_id", "key", "value"}).
				AddRow(1, 1, "network_type", "1").
				AddRow(2, 1, "person_field", "test"))

		// 模拟 bulk requests 成功
		mockServer.Register("_bulk", &elastic.BulkIndexByScrollResponse{
			Updated: 1,
		})

		node := &data_source.Node{
			SourceId: data_source.XRaySourceId,
		}
		node.Id = 1

		xrayRes := map[string]interface{}{
			"id":          float64(131),
			"address":     "127.0.0.1",
			"information": "test info",
			"mac_address": "00:11:22:33:44:55",
		}

		// 由于当前实现中 assetsBulkRequest 没有添加任何动作但仍会执行 Do()，
		// 所以实际上会返回错误 "No bulk actions to commit"
		count, err := handleAssets([]map[string]interface{}{xrayRes}, node, uint64(1), uint64(1))
		assert.Nil(t, err)
		assert.Equal(t, 1, count)
	})
}
