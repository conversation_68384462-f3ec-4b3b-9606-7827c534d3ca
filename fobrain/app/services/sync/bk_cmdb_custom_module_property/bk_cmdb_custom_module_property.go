package bk_cmdb_custom_module_property

import (
	"context"
	"fmt"
	"fobrain/pkg/utils/handle_es_bulk_error"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"

	"github.com/olivere/elastic/v7"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
	"go-micro.dev/v4/logger"

	"fobrain/fobrain/app/services/node/bk_cmdb"
	"fobrain/fobrain/common/constant"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	assetses "fobrain/models/elastic/assets"
	"fobrain/models/elastic/source/bk_cmdb_custom_module_property"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
	"fobrain/pkg/utils"
)

// SyncCmdbCustomVmMachine 函数用于将自定义虚拟机同步到配置管理数据库（CMDB）
//
// 参数:
//
//	node: *data_source.Node 类型的指针，指向要同步的虚拟机节点数据
//	taskInfo: string 类型，任务信息字符串
//
// 返回值:
//
//	error 类型，如果同步成功则返回 nil，否则返回错误信息
func SyncCmdbCustomVmMachine(node *data_source.Node, taskInfo string) error {
	logs.GetSyncLogger().Infof("SyncCmdbCustomVmMachine start taskInfo:%s", taskInfo)
	assetTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())

	//获取SyncBKCmdbClient
	cli := bk_cmdb.NewBkCmdb()
	//设置SyncBKCmdbClient请求的节点
	if err := cli.SetNode(node.Id); err != nil {
		return err
	}
	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncAsset)).Int() == data_sync_task.SyncAsset {
		//更新资产任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(assetTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("SyncCmdbCustomVmMachine UpdateStatusById StatusDoing err:%s", err.Error())
		}
		go SyncVmMachine(constant.DefaultPageZero, taskInfo, cli, node)
	}
	return nil
}

// SyncCmdbCustomEcs 函数用于同步自定义云主机到配置管理数据库（CMDB）
//
// 参数:
//
//	node: *data_source.Node 类型的指针，指向要同步的云主机节点数据
//	taskInfo: string 类型，任务信息字符串
//
// 返回值:
//
//	error 类型，如果同步成功则返回 nil，否则返回错误信息
func SyncCmdbCustomEcs(node *data_source.Node, taskInfo string) error {
	logs.GetSyncLogger().Infof("SyncCmdbCustomEcs start taskInfo:%s", taskInfo)
	assetTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())

	//获取SyncBKCmdbClient
	cli := bk_cmdb.NewBkCmdb()
	//设置SyncBKCmdbClient请求的节点
	if err := cli.SetNode(node.Id); err != nil {
		return err
	}
	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncAsset)).Int() == data_sync_task.SyncAsset {
		//更新资产任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(assetTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("SyncCmdbCustomEcs UpdateStatusById StatusDoing err:%s", err.Error())
		}
		go SynCloudEcs(constant.DefaultPageZero, taskInfo, cli, node)
	}
	return nil
}

// SyncVmMachine 函数用于同步虚拟机信息。
//
// 参数：
// page int：指定分页的页码。
// id uint64：与同步任务相关的唯一标识符。
//
// 功能：
// 1. 调用 bk_cmdb.NewBkCmdb().GetAssetsModule 方法获取虚拟机列表，使用指定的页码和放大的页面大小（默认大小的4倍）。
// 2. 如果在获取虚拟机列表时发生错误，记录错误信息，调用 updateFailed 函数更新失败状态，并返回。
// 3. 使用 gjson 库解析返回的虚拟机列表数据，获取数据总量。
// 4. 记录获取到的虚拟机总数信息。
// 5. 打印虚拟机列表的详细信息。
func SyncVmMachine(page int, taskInfo string, cli *bk_cmdb.BkCmdb, node *data_source.Node) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())
	logs.GetSyncLogger().Infof("SyncCmdbCustomVmMachine SyncVmMachine start taskId:%d,taskChildId:%d", taskId, taskChildId)
	pageSize := constant.DefaultSize * 4
	// 获取数据
	result, total, err := getAssetsModule(page, pageSize, cli, bk_cmdb.ModelVmMachine)
	if err != nil {
		logs.GetSyncLogger().Errorf("SyncCmdbCustomVmMachine SyncVmMachine getAssetsModule err:%s", err)
		updateFailed(taskId, taskChildId, "SyncCmdbCustomVmMachine SyncVmMachine", err)
		return
	}
	logger.Infof("SyncCmdbCustomVmMachine SyncVmMachine total:%d", total)
	//子任务更新需要同步的数据总量
	if page == constant.DefaultPageZero {
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataTotal(taskChildId, int(total))
		if err != nil {
			// 操作失败
			logs.GetSyncLogger().Errorf("SyncCmdbCustomVmMachine SyncVmMachine UpdateSyncDataTotal err:%s", err.Error())
			return
		}
	}
	// 默认设置为3，智能判断
	networkType, err := node.GetNetworkType()
	if err != nil {
		logs.GetSyncLogger().Errorf("SyncCmdbCustomVmMachine SyncVmMachine GetNetworkType err:%s", err.Error())
		return
	}
	personField := "name"
	if cli.GetConfig("person_field") != "" && cli.GetConfig("person_field") != nil {
		personField = cli.GetConfig("person_field").(string)
	}
	flag := execVmMachineData(taskId, taskChildId, networkType, personField, total, "SyncVmMachine", result, node)
	if !flag {
		return
	}
	pages := calculateTotalPages(int(total), pageSize)
	for i := 2; i <= pages; i++ {
		result, total, err = getAssetsModule(i-1, pageSize, cli, bk_cmdb.ModelVmMachine)
		if err != nil {
			logs.GetSyncLogger().Errorf("SyncCmdbCustomVmMachine SyncVmMachine getAssetsModule err:%s", err)
			updateFailed(taskId, taskChildId, "SyncCmdbCustomVmMachine SyncVmMachine", err)
			return
		}

		flag = execVmMachineData(taskId, taskChildId, networkType, personField, total, "SyncVmMachine", result, node)
		if !flag {
			return
		}
		time.Sleep(1 * time.Second)
	}
	logs.GetSyncLogger().Infof("SyncCmdbCustomVmMachine SyncVmMachine end taskId:%d,taskChildId:%d", taskId, taskChildId)
	//更新子任务完成状态
	err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(taskChildId, data_sync_child_task.StatusSuccess)
	if err != nil {
		logs.GetSyncLogger().Errorf("SyncCmdbCustomVmMachine SyncVmMachine UpdateStatusById StatusSuccess err:%s", err.Error())
	}
	return
}

// SynCloudEcs 函数用于将云主机数据同步到 CMDB 中。
//
// 参数:
//
//	page: 表示当前处理的是哪一页的数据。
//	taskInfo: 包含任务相关信息的字符串。
//	cli: 与 CMDB 交互的客户端实例。
//	node: 节点信息结构体指针，包含云主机的相关信息。
func SynCloudEcs(page int, taskInfo string, cli *bk_cmdb.BkCmdb, node *data_source.Node) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())
	logs.GetSyncLogger().Infof("SyncCmdbCustomVmMachine SynCloudEcs start taskId:%d,taskChildId:%d", taskId, taskChildId)
	pageSize := constant.DefaultSize * 4
	// 获取数据
	result, total, err := getAssetsModule(page, pageSize, cli, bk_cmdb.ModelCloudEcs)
	if err != nil {
		logs.GetSyncLogger().Errorf("SyncCmdbCustomVmMachine SynCloudEcs getAssetsModule err:%s", err)
		updateFailed(taskId, taskChildId, "SyncCmdbCustomVmMachine SynCloudEcs", err)
		return
	}
	logger.Infof("SyncCmdbCustomVmMachine SynCloudEcs total:%d", total)
	//子任务更新需要同步的数据总量
	if page == constant.DefaultPageZero {
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataTotal(taskChildId, int(total))
		if err != nil {
			// 操作失败
			logs.GetSyncLogger().Errorf("SyncCmdbCustomVmMachine SynCloudEcs UpdateSyncDataTotal err:%s", err.Error())
			return
		}
	}
	// 默认设置为3，智能判断
	networkType, err := node.GetNetworkType()
	if err != nil {
		logs.GetSyncLogger().Errorf("SyncCmdbCustomVmMachine SynCloudEcs GetNetworkType err:%s", err.Error())
		return
	}
	flag := execEcsData(taskId, taskChildId, networkType, total, "SynCloudEcs", result, node)
	if !flag {
		return
	}
	pages := calculateTotalPages(int(total), pageSize)
	for i := 2; i <= pages; i++ {
		result, total, err = getAssetsModule(i-1, pageSize, cli, bk_cmdb.ModelCloudEcs)
		if err != nil {
			logs.GetSyncLogger().Errorf("SyncCmdbCustomVmMachine SynCloudEcs getAssetsModule err:%s", err)
			updateFailed(taskId, taskChildId, "SyncCmdbCustomVmMachine SynCloudEcs", err)
			return
		}

		flag = execEcsData(taskId, taskChildId, networkType, total, "SynCloudEcs", result, node)
		if !flag {
			return
		}
		time.Sleep(1 * time.Second)
	}
	logs.GetSyncLogger().Infof("SyncCmdbCustomVmMachine SynCloudEcs end taskId:%d,taskChildId:%d", taskId, taskChildId)
	//更新子任务完成状态
	err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(taskChildId, data_sync_child_task.StatusSuccess)
	if err != nil {
		logs.GetSyncLogger().Errorf("SyncCmdbCustomVmMachine SynCloudEcs UpdateStatusById StatusSuccess err:%s", err.Error())
	}
	return
}

// execVmMachineData 函数用于执行虚拟机机器数据的同步操作。
//
// 参数:
//
//	taskId: 父任务ID。
//	taskChildId: 子任务ID。
//	networkType: 网络类型。
//	total: 数据总数。
//	methodName: 方法名称，用于日志记录。
//	result: 包含虚拟机机器数据的gjson.Result对象。
//	node: 节点信息结构体指针，包含节点相关的配置信息。
//
// 返回值:
//
//	布尔值，表示操作是否成功。如果成功，返回true；否则返回false。
func execVmMachineData(taskId, taskChildId uint64, networkType int, personField string, total int64, methodName string, result gjson.Result, node *data_source.Node) bool {
	if result.Exists() && total > 0 {
		assetTaskBulkRequest := es.GetEsClient().Bulk()
		assetProcessBulkRequest := es.GetEsClient().Bulk()
		// 遍历 _id 字段，并根据 id 添加新的字段数据
		var num = 0
		result.ForEach(func(key, value gjson.Result) bool {
			logs.GetSyncLogger().Debugf("%s execData value info:%s", methodName, value)

			if !utils.IsValidIP(gjson.Get(value.String(), "ip_addr").String()) {
				data_sync_child_task.NewDataSyncChildTaskModel().RecordFailedData(taskChildId, "ip", gjson.Get(value.String(), "ip_addr").String(), "保存资产时ip校验未通过")
				logs.GetSyncLogger().Errorf("SyncCmdbCustomVmMachine SyncVmMachine ip校验未通过,ip:%s", gjson.Get(value.String(), "ip_addr").String())
				return true
			}
			areaId, _ := node.GetAreaByIp(gjson.Get(value.String(), "ip_addr").String())
			data, _ := sjson.Set(value.String(), "node_id", node.Id)
			data, _ = sjson.Set(data, "area_id", areaId)
			data, _ = sjson.Delete(data, "create_time")

			//写入任务库
			assetTask := bk_cmdb_custom_module_property.NewBkCmdbCustomTaskVmMachineAssetsModel()
			assetTaskId := strings.ReplaceAll(uuid.New().String(), "-", "")
			data, _ = sjson.Set(data, "task_id", taskId)
			data, _ = sjson.Set(data, "child_task_id", taskChildId)
			assetTaskReq := elastic.NewBulkIndexRequest().Index(assetTask.IndexName()).Id(assetTaskId).Doc(data)
			assetTaskBulkRequest = assetTaskBulkRequest.Add(assetTaskReq)

			//写入过程库
			assetProcess := assetses.NewProcessAssetsModel()

			assetProcessId := strings.ReplaceAll(uuid.New().String(), "-", "")
			assetProcess.CreatedAt = localtime.NewLocalTime(time.Now())
			assetProcess.UpdatedAt = localtime.NewLocalTime(time.Now())
			assetProcess.Id = assetProcessId
			assetProcess.TaskId = taskId
			assetProcess.ChildTaskId = taskChildId
			assetProcess.Node = node.Id
			assetProcess.Source = node.SourceId
			assetProcess.TaskId = taskId
			assetProcess.ChildTaskId = taskChildId
			assetProcess.Ip = gjson.Get(value.String(), "ip_addr").String()
			assetProcess.Area = int(areaId)
			assetProcess.NetworkType = networkType
			assetProcess.Os = gjson.Get(value.String(), "vm_name").String()
			assetProcess.Sn = gjson.Get(value.String(), "sn").String()
			assetProcess.Mac = gjson.Get(value.String(), "mac").String()

			assetProcess.MemorySize = gjson.Get(value.String(), "memory").String()
			assetProcess.CpuCount = int(gjson.Get(value.String(), "cpu_num").Int())
			assetProcess.DiskSize = getDiskNum(gjson.Get(value.String(), "disk").String())
			assetProcess.TaskDataId = assetTaskId

			netWorkType, _ := node.GetNetworkType()
			assetProcess.NetworkType = netWorkType
			assetProcess.PersonField = personField

			assetProcessReq := elastic.NewBulkIndexRequest().Index(assetProcess.IndexName()).Id(assetProcessId).Doc(assetProcess)
			assetProcessBulkRequest = assetProcessBulkRequest.Add(assetProcessReq)
			num = num + 1
			return true
		})
		if num > 0 {
			assetProcessBulkReq, err := assetProcessBulkRequest.Refresh("true").Do(context.Background())
			dispose := errDispose(err, assetProcessBulkReq, fmt.Sprintf("%s execData assetProcessBulkRequest", methodName), taskId, taskChildId)
			if !dispose {
				return false
			}

			assetTaskBulkReq, err := assetTaskBulkRequest.Do(context.Background())
			dispose = errDispose(err, assetTaskBulkReq, fmt.Sprintf("%s execData assetTaskBulkRequest", methodName), taskId, taskChildId)
			if !dispose {
				return false
			}
			// 更新子任务同步的数据总量
			if err == nil {
				//更新成功数量
				err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(taskChildId, len(assetTaskBulkReq.Items))
				if err != nil {
					logs.GetSyncLogger().Errorf("%s execData UpdateSyncDataSuccessTotal err:%s", methodName, err.Error())
					return false
				}
			}
		}
	}
	return true
}

// execEcsData 函数用于执行ECS数据的同步操作。
//
// 参数:
//
//	taskId: 父任务ID。
//	taskChildId: 子任务ID。
//	networkType: 网络类型。
//	total: 数据总数。
//	methodName: 方法名称，用于日志记录。
//	result: 包含ECS数据的gjson.Result对象。
//	node: 节点信息结构体指针，包含节点相关的配置信息。
//
// 返回值:
//
//	布尔值，表示操作是否成功。如果成功，返回true；否则返回false。
func execEcsData(taskId, taskChildId uint64, networkType int, total int64, methodName string, result gjson.Result, node *data_source.Node) bool {
	if result.Exists() && total > 0 {
		assetTaskBulkRequest := es.GetEsClient().Bulk()
		assetProcessBulkRequest := es.GetEsClient().Bulk()
		// 遍历 _id 字段，并根据 id 添加新的字段数据
		var num = 0
		result.ForEach(func(key, value gjson.Result) bool {
			logs.GetSyncLogger().Debugf("%s execData value info:%s", methodName, value)

			ip := gjson.Get(value.String(), "PrivateIpAddress").String()

			if !utils.IsValidIP(ip) {
				return true
			}
			areaId, _ := node.GetAreaByIp(ip)
			data, _ := sjson.Set(value.String(), "node_id", node.Id)
			data, _ = sjson.Set(data, "area_id", areaId)
			data, _ = sjson.Delete(data, "create_time")

			//写入任务库
			assetTask := bk_cmdb_custom_module_property.NewBkCmdbCustomTaskEcsAssetsModel()
			assetTaskId := strings.ReplaceAll(uuid.New().String(), "-", "")
			data, _ = sjson.Set(data, "task_id", taskId)
			data, _ = sjson.Set(data, "child_task_id", taskChildId)
			assetTaskReq := elastic.NewBulkIndexRequest().Index(assetTask.IndexName()).Id(assetTaskId).Doc(data)
			assetTaskBulkRequest = assetTaskBulkRequest.Add(assetTaskReq)

			//写入过程库
			assetProcess := assetses.NewProcessAssetsModel()

			assetProcessId := strings.ReplaceAll(uuid.New().String(), "-", "")
			assetProcess.CreatedAt = localtime.NewLocalTime(time.Now())
			assetProcess.UpdatedAt = localtime.NewLocalTime(time.Now())
			assetProcess.Id = assetProcessId

			assetProcess.Area = int(areaId)
			assetProcess.Node = node.Id
			assetProcess.Source = node.SourceId
			assetProcess.TaskId = taskId
			assetProcess.ChildTaskId = taskChildId
			assetProcess.Ip = ip
			assetProcess.NetworkType = networkType
			assetProcess.BusinessSystem = gjson.Get(value.String(), "biz").String()

			assetProcess.MemorySize = gjson.Get(value.String(), "ecs_memory").String()
			assetProcess.CpuCount = int(gjson.Get(value.String(), "ecs_cpu").Int())
			assetProcess.TaskDataId = assetTaskId

			netWorkType, _ := node.GetNetworkType()
			assetProcess.NetworkType = netWorkType

			assetProcessReq := elastic.NewBulkIndexRequest().Index(assetProcess.IndexName()).Id(assetProcessId).Doc(assetProcess)
			assetProcessBulkRequest = assetProcessBulkRequest.Add(assetProcessReq)
			num = num + 1
			return true
		})
		if num > 0 {
			assetProcessBulkReq, err := assetProcessBulkRequest.Refresh("true").Do(context.Background())
			dispose := errDispose(err, assetProcessBulkReq, fmt.Sprintf("%s execData assetProcessBulkRequest", methodName), taskId, taskChildId)
			if !dispose {
				return false
			}

			assetTaskBulkReq, err := assetTaskBulkRequest.Do(context.Background())
			dispose = errDispose(err, assetTaskBulkReq, fmt.Sprintf("%s execData assetTaskBulkRequest", methodName), taskId, taskChildId)
			if !dispose {
				return false
			}
			// 更新子任务同步的数据总量
			if err == nil {
				//更新成功数量
				err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(taskChildId, len(assetTaskBulkReq.Items))
				if err != nil {
					logs.GetSyncLogger().Errorf("%s execData UpdateSyncDataSuccessTotal err:%s", methodName, err.Error())
					return false
				}
			}
		}
	}
	return true
}

// getDiskNum 函数从传入的字符串中提取磁盘大小，并返回整数表示的大小。
//
// 参数:
//
//	diskStr: 包含磁盘大小的字符串，格式为 "label:sizeGB"，其中 label 是标签，size 是磁盘大小（可能包含单位 GB）。
//
// 返回值:
//
//	返回磁盘大小的整数值。如果无法正确解析字符串，则返回 0。
func getDiskNum(diskStr string) int {
	var diskSize int
	// 匹配包含数字和 "GB" 的字符串
	re := regexp.MustCompile(`[-+]?[0-9]*\.?[0-9]+GB`)
	matches := re.FindAllString(diskStr, -1)

	var total float64
	for _, match := range matches {
		// 去掉 "GB" 后提取数字部分
		numberStr := strings.TrimSuffix(match, "GB")
		number, err := strconv.ParseFloat(strings.TrimSpace(numberStr), 64)
		if err != nil {
			logger.Errorf("invalid number in disk: %s", match)
			return diskSize
		}
		// 累加容量
		total += number
	}
	// 取整
	return int(total)
}

// errDispose 错误处理
func errDispose(err error, req *elastic.BulkResponse, errStr string, taskId, taskChildId uint64) bool {
	if err != nil || req.Errors {
		errString := handle_es_bulk_error.HandleBulkResp("", err, req)
		logs.GetSyncLogger().Errorf("%s.Do err:%s", errStr, errString)

		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
		if taskErr != nil {
			logs.GetSyncLogger().Errorf(" %s.Do task UpdateFailById err:%s", errStr, taskErr)
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("%s.Do childTask UpdateFailById err:%s", errStr, childTaskErr.Error())
		}
		return false
	}
	return true
}

// getAssetsModule 函数用于获取资产模块的数据
//
// 参数:
//
//	page: int 类型，表示当前页码
//	size: int 类型，表示每页大小
//	bkObjId: string 类型，表示蓝鲸对象ID
//
// 返回值:
//
//	gjson.Result 类型，表示查询结果
//	int64 类型，表示总数
//	error 类型，表示错误信息，如果查询成功则返回 nil
//
// 说明:
//
//	该函数使用 bk_cmdb.NewBkCmdb().GetAssetsModule 方法获取资产模块的数据，
//	并处理可能出现的错误。如果查询成功，则解析返回结果，并返回结果和总数；
//	如果查询失败，则记录错误信息并返回错误。
func getAssetsModule(page, size int, cli *bk_cmdb.BkCmdb, bkObjId string) (gjson.Result, int64, error) {
	vmMachineList, err := cli.GetAssetsModule(page, size, bkObjId)
	if err != nil {
		logs.GetSyncLogger().Errorf("SyncCmdbCustomVmMachine  GetAssetsModule bkObjId:%s,err:%s", bkObjId, err.Error())
		return gjson.Result{}, 0, err
	}
	total := gjson.Get(vmMachineList, "data.count").Int()
	result := gjson.Get(vmMachineList, "data.info")
	return result, total, nil
}

// updateFailed 函数用于更新任务失败状态
func updateFailed(taskId, taskChildId uint64, methodName string, err error) {
	//更新主任务状态为失败
	taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, err.Error())
	if taskErr != nil {
		logs.GetSyncLogger().Errorf("%s task UpdateFailById err:%s", methodName, taskErr.Error())
	}

	//更新子任务状态为失败
	childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, err.Error())
	if childTaskErr != nil {
		logs.GetSyncLogger().Errorf("%s childTask UpdateFailById err:%s", methodName, childTaskErr.Error())
	}
	return
}

// calculateTotalPages 函数用于计算给定总数和每页大小情况下所需的总页数。
//
// 参数:
//
//	totalCount: 数据总数。
//	pageSize: 每页显示的数据量。
//
// 返回值:
//
//	返回所需的总页数。如果总数为0，则返回0。
func calculateTotalPages(totalCount int, pageSize int) int {
	if totalCount == 0 {
		return 0
	}
	pages := totalCount / pageSize
	if totalCount%pageSize != 0 {
		pages++
	}
	return pages
}
