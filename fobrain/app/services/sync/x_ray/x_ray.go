package x_ray

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/olivere/elastic/v7"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"

	"fobrain/fobrain/app/services/node/x_ray"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/poc"
	xray "fobrain/models/elastic/source/x-ray"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
)

// Sync
// @Summary 同步 x-ray 漏洞台账数据
func Sync(node *data_source.Node, taskInfo string) error {
	logs.GetSyncLogger().Infof("SyncX-Ray start taskInfo:%s", taskInfo)
	threatTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncThreat)).Int())
	assetTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())

	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())

	callbackThreats := func(data []map[string]interface{}) error {
		count, _, err := handleThreats(data, node, threatTaskId, taskId)
		if err == nil {
			fmt.Println("handleThreats count: ", count)
			err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(threatTaskId, count)
			if err != nil {
				logs.GetSyncLogger().Errorf("x-ray syncThreats UpdateSyncDataSuccessTotal err:%s", err.Error())
			}
		}
		return err
	}

	callbackAssets := func(data []map[string]interface{}) error {
		count, err := handleAssets(data, node, assetTaskId, taskId)
		if err == nil {
			fmt.Println("handleAssets count: ", count)
			err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(assetTaskId, count)
			if err != nil {
				logs.GetSyncLogger().Errorf("x-ray syncAssets UpdateSyncDataSuccessTotal err:%s", err.Error())
			}
		}
		return err
	}

	//获取x-rayClient
	cli := x_ray.NewCallbackXRay(callbackThreats, callbackAssets)

	//设置x-rayClient请求的节点
	if err := cli.SetNode(node.Id); err != nil {
		return err
	}

	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncThreat)).Int() == data_sync_task.SyncThreat {
		//更新漏洞任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(threatTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("x-ray syncThreats UpdateStatusById StatusDoing err:%s", err.Error())
		}
		go syncThreats(cli, taskInfo)
	}

	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncAsset)).Int() == data_sync_task.SyncAsset {
		//更新漏洞任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(assetTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("x-ray syncAssets UpdateStatusById StatusDoing err:%s", err.Error())
		}

		go syncAssets(cli, taskInfo)
	}

	return nil
}

func syncAssets(cli *x_ray.XRay, taskInfo string) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())
	logs.GetSyncLogger().Infof("SyncX-Ray syncAssets start taskInfo:%d,taskChildId:%d", taskId, taskChildId)
	//获取资产
	total, err := cli.GetAssetAll()
	if err != nil {
		logs.GetSyncLogger().Errorf("SyncX-Ray syncAssets GetAssetAll err:%s", err)

		setTaskFailure("syncAssets", "GetAssetAll", taskId, err, taskChildId)
		return
	}

	//子任务更新需要同步的数据总量
	err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateById(taskChildId, data_sync_child_task.DataSyncChildTask{
		SyncDataTotal: int(total),
	})

	if err != nil {
		logs.GetSyncLogger().Errorf("x-ray syncAssets UpdateById SyncDataTotal err:%s", err.Error())
	}

	logs.GetSyncLogger().Infof("SyncX-Ray syncAssets end taskInfo:%d,taskChildId:%d", taskId, taskChildId)
	// 更新子任务完成状态
	err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(taskChildId, data_sync_child_task.StatusSuccess)
	if err != nil {
		logs.GetSyncLogger().Errorf("x-ray syncAssets UpdateStatusById StatusSuccess err:%s", err.Error())
	}
}

// syncThreats 同步漏洞
func syncThreats(cli *x_ray.XRay, taskInfo string) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncThreat)).Int())
	logs.GetSyncLogger().Infof("SyncX-Ray syncThreats start taskInfo:%d,taskChildId:%d", taskId, taskChildId)

	//获取漏洞
	total, err := cli.GetVulnerabilityAll()
	if err != nil {
		logs.GetSyncLogger().Errorf("SyncX-Ray syncThreats GetThreats err:%s", err)

		setTaskFailure("syncThreats", "GetThreats", taskId, err, taskChildId)
		return
	}

	//子任务更新需要同步的数据总量
	err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateById(taskChildId, data_sync_child_task.DataSyncChildTask{
		SyncDataTotal: int(total),
	})

	if err != nil {
		logs.GetSyncLogger().Errorf("x-ray syncThreats UpdateById SyncDataTotal err:%s", err.Error())
	}

	logs.GetSyncLogger().Infof("SyncX-Ray syncThreats end taskInfo:%d,taskChildId:%d", taskId, taskChildId)

	// 更新子任务完成状态
	err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(taskChildId, data_sync_child_task.StatusSuccess)
	if err != nil {
		logs.GetSyncLogger().Errorf("x-ray syncThreats UpdateStatusById StatusSuccess err:%s", err.Error())
	}
}

func setTaskFailure(category, operation string, taskId uint64, err error, taskChildId uint64) {
	//更新主任务状态为失败
	taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, operation+" "+err.Error())
	if taskErr != nil {
		logs.GetSyncLogger().Errorf("x-ray "+category+" "+operation+" Task UpdateFailById err:%s", taskErr.Error())
	}

	//更新子任务状态为失败
	childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, operation+" "+err.Error())
	if childTaskErr != nil {
		logs.GetSyncLogger().Errorf("x-ray "+category+" "+operation+" childTask UpdateFailById err:%s", childTaskErr.Error())
	}
}

func handleAssets(items []map[string]interface{}, node *data_source.Node, taskChildId, taskId uint64) (int, error) {
	if len(items) == 0 {
		return 0, nil
	}
	count := 0
	count = len(items)

	assetsTaskBulkRequest := es.GetEsClient().Bulk()
	assetsProcessBulkRequest := es.GetEsClient().Bulk()
	networkType, err := node.GetNetworkType()
	cli := x_ray.New()
	cli.SetNode(node.Id)
	personField := "name"
	if cli.GetConfig("person_field") != "" && cli.GetConfig("person_field") != nil {
		personField = cli.GetConfig("person_field").(string)
	}

	for _, item := range items {
		id := item["id"]
		value, _ := json.Marshal(item)

		doc, _ := sjson.Set(string(value), "node_id", node.Id)
		doc, _ = sjson.Set(doc, "original_id", id)
		ip := item["address"].(string)
		doc, _ = sjson.Set(doc, "ip", ip)
		areaId := node.AreaId
		if ip != "" {
			// 获取区域
			areaId, err = node.GetAreaByIp(ip)
			if err != nil {
				logs.GetSyncLogger().Errorf("x-ray syncAssets GetAreaByIp failed,ip:%s,nodeId:%d,err:%s", ip, node.Id, err)
				r := data_sync_child_task.NewDataSyncChildTaskFailRecord()
				r.Create(&data_sync_child_task.DataSyncChildTaskFailRecord{
					ChildTaskId:  taskChildId,
					DataType:     "ip",
					DataContent:  ip,
					FailedReason: err.Error(),
				})
			}
		}
		doc, _ = sjson.Set(doc, "area_id", areaId)

		// 组件信息
		productList := cli.GetServiceAll(ip)
		doc, _ = sjson.Set(doc, "product", productList)

		// 写入资产库
		assetId := fmt.Sprintf("%d_%d_%v", node.Id, node.AreaId, id)
		assetsTaskId := fmt.Sprintf("%d_%s", taskId, assetId)

		//写入过程库
		assetsProcess := SetProcessAssetFields(assetId, fmt.Sprintf("%v", id.(float64)), assetsTaskId, node, string(value), networkType, int(areaId), personField, taskId, taskChildId, productList)

		//写入任务库
		assetTask := xray.NewTaskAssetsModel()

		doc, _ = sjson.Set(doc, "task_id", taskId)
		doc, _ = sjson.Set(doc, "child_task_id", taskChildId)
		assetsProcess.TaskId = taskId
		assetsProcess.ChildTaskId = taskChildId

		assetTaskReq := elastic.NewBulkIndexRequest().Index(assetTask.IndexName()).Id(assetsTaskId).Doc(doc)
		assetsTaskBulkRequest = assetsTaskBulkRequest.Add(assetTaskReq)

		assetsProcessReq := elastic.NewBulkIndexRequest().Index(assetsProcess.IndexName()).Id(assetId).Doc(assetsProcess)
		assetsProcessBulkRequest = assetsProcessBulkRequest.Add(assetsProcessReq)
	}

	_, err = assetsProcessBulkRequest.Refresh("true").Do(context.Background())
	if err != nil {
		logs.GetSyncLogger().Errorf("x-ra syncAssets assetsProcessBulkRequest.Do err:%s", err)
		return 0, err
	}

	_, err = assetsTaskBulkRequest.Do(context.Background())
	if err != nil {
		logs.GetSyncLogger().Errorf("x-ra syncAssets assetsTaskBulkRequest.Do err:%s", err)
		return 0, err
	}

	return count, nil
}

func handleThreats(items []map[string]interface{}, node *data_source.Node, taskChildId, taskId uint64) (int, []map[string]interface{}, error) {
	if len(items) == 0 {
		return 0, nil, nil
	}
	count := 0
	var threatIds []map[string]interface{}
	count = len(items)

	// threatBulkRequest := es.GetEsClient().Bulk()
	threatTaskBulkRequest := es.GetEsClient().Bulk()
	threatProcessBulkRequest := es.GetEsClient().Bulk()

	for _, item := range items {
		id := item["id"]
		value, _ := json.Marshal(item)

		doc, _ := sjson.Set(string(value), "node_id", node.Id)
		doc, _ = sjson.Set(doc, "area_id", node.AreaId)
		doc, _ = sjson.Set(doc, "original_id", id)

		//写入漏洞库
		// threat := xray.NewThreatsModel()
		threatId := fmt.Sprintf("%d_%d_%v", node.Id, node.AreaId, id)
		// threatReq := elastic.NewBulkIndexRequest().Index(threat.IndexName()).Id(threatId).Doc(doc)
		// threatBulkRequest = threatBulkRequest.Add(threatReq)

		threatTaskId := fmt.Sprintf("%d_%s", taskId, threatId)

		//写入过程库
		threatProcess := SetProcessThreatFields(threatTaskId, fmt.Sprintf("%v", id.(float64)), threatTaskId, node, string(value), taskId, taskChildId)

		//写入任务库
		threatTask := xray.NewTaskThreatsModel()

		doc, _ = sjson.Set(doc, "task_id", taskId)
		doc, _ = sjson.Set(doc, "child_task_id", taskChildId)
		doc, _ = sjson.Set(doc, "ip", threatProcess.Ip)
		doc, _ = sjson.Set(doc, "port", threatProcess.Port)
		doc, _ = sjson.Set(doc, "cveId", threatProcess.Cve)
		doc, _ = sjson.Set(doc, "vulType", threatProcess.VulType)
		doc, _ = sjson.Set(doc, "level_desc", mapLevelDesc(gjson.Get(string(value), "severity").String()))
		doc, _ = sjson.Set(doc, "created_at", threatProcess.CreatedAt)
		doc, _ = sjson.Set(doc, "updated_at", threatProcess.UpdatedAt)

		if gjson.Get(string(value), "target.url").String() != "" {
			doc, _ = sjson.Set(doc, "url", threatProcess.Url)
		}

		threatTaskReq := elastic.NewBulkIndexRequest().Index(threatTask.IndexName()).Id(threatTaskId).Doc(doc)
		threatTaskBulkRequest = threatTaskBulkRequest.Add(threatTaskReq)

		threatProcessReq := elastic.NewBulkIndexRequest().Index(threatProcess.IndexName()).Id(threatTaskId).Doc(threatProcess)
		threatProcessBulkRequest = threatProcessBulkRequest.Add(threatProcessReq)
		threatIds = append(threatIds, map[string]interface{}{"id": threatId, "is_poc": threatProcess.IsPoc, "task_id": taskChildId})
	}

	_, err := threatProcessBulkRequest.Refresh("true").Do(context.Background())
	if err != nil {
		logs.GetSyncLogger().Errorf("x-ra syncThreats threatProcessBulkRequest.Do err:%s", err)
		return 0, threatIds, err
	}

	// _, err = threatBulkRequest.Do(context.Background())
	// if err != nil {
	// 	logs.GetSyncLogger().Errorf("x-ra syncThreats threatBulkRequest.Do err:%s", err)
	// 	return 0, threatIds, err
	// }

	_, err = threatTaskBulkRequest.Do(context.Background())
	if err != nil {
		logs.GetSyncLogger().Errorf("x-ra syncThreats threatTaskBulkRequest.Do err:%s", err)
		return 0, threatIds, err
	}

	return count, threatIds, nil
}

func SetProcessAssetFields(assetId, id, assetTaskId string, node *data_source.Node, value string, networkType int, areaId int, personField string, taskId, taskChildId uint64, productList []map[string]interface{}) *assets.ProcessAssets {
	assetProcess := assets.NewProcessAssetsModel()
	ip := gjson.Get(value, "address").String()

	assetProcess.Id = assetId
	assetProcess.TaskDataId = assetTaskId
	assetProcess.TaskId = taskId
	assetProcess.ChildTaskId = taskChildId
	assetProcess.Area = areaId
	assetProcess.Node = node.Id
	assetProcess.Source = node.SourceId
	assetProcess.Ip = ip
	assetProcess.NetworkType = networkType
	assetProcess.PersonField = personField
	assetProcess.Status = 1
	var product []string
	var ports []*assets.PortInfo
	var ruleInfos []*assets.RuleInfo

	for _, port := range productList {
		var portInfo assets.PortInfo
		var ruleInfo assets.RuleInfo
		var xrayPort int
		var serviceName, application string
		if p, ok := port["port"].(float64); ok {
			xrayPort = int(p)
		}
		if sName, ok := port["service_name"].(string); ok {
			serviceName = sName
		}
		portInfo.Port = xrayPort
		portInfo.Protocol = serviceName
		ports = append(ports, &portInfo)
		if productName, ok := port["application"].(string); ok {
			application = productName
		}
		if application != "" {
			product = append(product, application)
			ruleInfo.Product = application
			ruleInfos = append(ruleInfos, &ruleInfo)
		}
	}
	assetProcess.RuleInfos = ruleInfos
	assetProcess.Product = product
	assetProcess.Ports = ports
	assetProcess.Mac = gjson.Get(value, "mac_address").String()
	assetProcess.Os = gjson.Get(value, "information").String()

	assetProcess.CreatedAt = localtime.NewLocalTime(time.Now())
	assetProcess.UpdatedAt = localtime.NewLocalTime(time.Now())

	netWorkType, _ := node.GetNetworkType()
	assetProcess.NetworkType = netWorkType

	return assetProcess
}

func SetProcessThreatFields(threatId string, id string, threatTaskId string, node *data_source.Node, value string, taskId, taskChildId uint64) *poc.ProcessPoc {
	threatProcess := poc.NewProcessPocModel()

	ip, port := ExtractIPAndPort(gjson.Get(value, "target.url").String())
	url := gjson.Get(value, "target.url").String()
	// 如果 target.url 为空，则通过 host 去取
	if ip == "" {
		ip = gjson.Get(value, "target.host").String()
		port = int(gjson.Get(value, "target.port").Int())
		url = gjson.Get(value, "id").String()
	}
	areaId := node.AreaId
	if ip != "" {
		// 获取区域
		area, err := node.GetAreaByIp(ip)
		if err != nil {
			logs.GetSyncLogger().Errorf("Foeye syncThreats GetAreaByIp failed,ip:%s,nodeId:%d,err:%s", ip, node.Id, err)
			r := data_sync_child_task.NewDataSyncChildTaskFailRecord()
			r.Create(&data_sync_child_task.DataSyncChildTaskFailRecord{
				ChildTaskId:  taskChildId,
				DataType:     "ip",
				DataContent:  ip,
				FailedReason: err.Error(),
			})
		}
		areaId = area
	}

	threatProcess.Id = threatId
	threatProcess.TaskId = taskId
	threatProcess.ChildTaskId = taskChildId
	threatProcess.TaskDataId = threatTaskId
	threatProcess.Area = areaId
	threatProcess.Node = node.Id
	threatProcess.Source = node.SourceId
	threatProcess.IsPoc = IsPoc(gjson.Get(value, "definiteness").String())
	threatProcess.Ip = ip
	threatProcess.Port = port
	threatProcess.Url = url
	threatProcess.Name = gjson.Get(value, "title").String()

	domains := gjson.Get(value, "exposures").Array()
	domainArr := []string{}
	for _, domain := range domains {
		domainArr = append(domainArr, domain.String())
	}

	threatProcess.Cve = GetCVE(domainArr)
	threatProcess.Cnnvd = GetCNNVD(domainArr)
	threatProcess.OriginalId = GetCTId(domainArr)
	// threatProcess.HasExp = int(gjson.Get(value.String(), "hasExp").Int())
	hasExp := 0
	if gjson.Get(value, "exp").Exists() {
		if gjson.Get(value, "exp").String() == "暂无" {
			hasExp = 2
		} else {
			hasExp = 1
		}
	}
	hasPoc := 0
	if gjson.Get(value, "poc").Exists() {
		pocInfo := gjson.Get(value, "poc").String()
		if pocInfo == "暂无相关漏洞原理信息" || pocInfo == "/" || pocInfo == "暂无" {
			hasPoc = 2
		} else {
			hasExp = 1
		}
	}
	threatProcess.HasExp = hasExp
	threatProcess.HasPoc = hasPoc
	threatProcess.Status = int(1)
	threatProcess.Describe = gjson.Get(value, "summary").String()
	threatProcess.Hazard = gjson.Get(value, "impact").String()
	threatProcess.Details = gjson.Get(value, "detail").String()
	threatProcess.Suggestions = gjson.Get(value, "solution").String()

	threatProcess.Level = mapLevel(gjson.Get(value, "severity").String())
	threatProcess.VulType = ConvertVulType(gjson.Get(value, "category").String())

	ct := gjson.Get(value, "created_time").String()
	threatProcess.CreatedAt = parseTimeOrNow(ct)

	updateTime := gjson.Get(value, "created_time").String()
	threatProcess.UpdatedAt = parseTimeOrNow(updateTime)

	return threatProcess
}

func GetTaskDataById(id string) (*xray.TaskThreats, error) {
	threatTask := xray.NewTaskThreatsModel()
	searchResult, err := es.GetEsClient().Search().Index(threatTask.IndexName()).Query(elastic.NewBoolQuery().Must(elastic.NewTermQuery("_id", id))).Do(context.Background())
	if err != nil {
		return nil, err
	}
	if searchResult == nil || len(searchResult.Hits.Hits) == 0 {
		return nil, errors.New("未找到任务数据")
	}
	var task xray.TaskThreats
	err = json.Unmarshal(searchResult.Hits.Hits[0].Source, &task)
	if err != nil {
		return nil, err
	}
	return &task, nil
}
