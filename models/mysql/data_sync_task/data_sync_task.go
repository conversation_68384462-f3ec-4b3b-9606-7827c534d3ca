package data_sync_task

import (
	"fmt"
	"time"

	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/workbench"
)

type DataSyncTask struct {
	mysql.BaseModel
	SourceId uint64         `json:"source_id"`
	NodeId   uint64         `json:"node_id"`
	Status   int            `json:"status"` //  状态：1 未开始 2 执行中 3 成功 4 失败
	Source   int            `json:"source"` //  任务来源：1 定时 2 手动 3 场景
	File     string         `json:"file"`
	StartAt  localtime.Time `gorm:"comment:'任务开始时间';index;" json:"start_at"`
	EndAt    localtime.Time `gorm:"comment:'任务结束时间';index;" json:"end_at"`
	Message  string         `json:"message"` // 失败信息
}

const (
	TableName = "data_sync_tasks"
	//StatusNotStart 任务状态
	StatusNotStart = 1
	StatusDoing    = 2
	StatusSuccess  = 3
	StatusFail     = 4

	// SyncALL 同步数据类型
	SyncALL       = 0 //同步所有节点数据
	SyncAsset     = 1 //同步资产数据
	SyncThreat    = 2 //同步漏洞数据
	SyncPeople    = 3 //同步人员信息
	SyncRelations = 4 //同步内外网映射关系

	// SourceCron 任务来源
	SourceCron   = 1 //定时
	SourceHandle = 2 //手动
	SourceScene  = 3 //场景
)

// TableName 表名
func (d *DataSyncTask) TableName() string {
	return TableName
}

func NewDataSyncTaskModel() *DataSyncTask {
	return &DataSyncTask{BaseModel: mysql.BaseModel{DB: mysql.GetDbClient().Table((&DataSyncTask{}).TableName())}}
}

func (d *DataSyncTask) First(opts ...mysql.HandleFunc) (*DataSyncTask, error) {
	query := d.DB.Table(d.TableName())
	for _, opt := range opts {
		opt(query)
	}
	var item DataSyncTask
	err := query.First(&item).Error
	if err != nil {
		return &DataSyncTask{}, err
	}
	return &item, nil
}
func (d *DataSyncTask) UpdateById(id uint64, data DataSyncTask) error {
	query := d.DB.Model(&DataSyncTask{})
	return query.Where("`id` = ?", id).Updates(data).Error
}

func (d *DataSyncTask) Create(item *DataSyncTask) error {
	return d.DB.Model(&DataSyncTask{}).Create(item).Error
}

func (d *DataSyncTask) UpdateFailById(id uint64, errorStr string) error {
	workbench.NewNotifyAlarmCenter().Create(&workbench.NotifyAlarmCenter{
		MsgType:         workbench.MsgTypeAbnormal,
		MsgContent:      fmt.Sprintf("同步任务失败，任务ID为: %d", id),
		MsgSource:       workbench.MsgSouceDataExtractFail,
		RelationType:    "",
		RelationContent: "",
		Remark:          errorStr,
	})

	query := d.DB.Model(&DataSyncTask{})

	return query.Where("`id` = ?", id).Updates(DataSyncTask{
		Message: errorStr,
		Status:  StatusFail,
		EndAt:   localtime.Time(time.Now()),
	}).Error
}

func (d *DataSyncTask) UpdateFailByIds(ids []uint64, errorStr string) error {

	workbench.NewNotifyAlarmCenter().Create(&workbench.NotifyAlarmCenter{
		MsgType:         workbench.MsgTypeAbnormal,
		MsgContent:      fmt.Sprintf("同步任务失败，任务ID为: %v", ids),
		MsgSource:       workbench.MsgSouceDataExtractFail,
		RelationType:    "",
		RelationContent: "",
		Remark:          errorStr,
	})

	query := d.DB.Model(&DataSyncTask{})

	return query.Where("`id` IN ?", ids).Updates(DataSyncTask{
		Message: errorStr,
		Status:  StatusFail,
		EndAt:   localtime.Time(time.Now()),
	}).Error
}

func (d *DataSyncTask) UpdateStatusById(id uint64, status int) error {
	query := d.DB.Model(&DataSyncTask{})

	return query.Where("`id` = ?", id).Updates(DataSyncTask{
		Status: status,
	}).Error
}

func (d *DataSyncTask) List(page, size int, opts ...mysql.HandleFunc) ([]DataSyncTask, int64, error) {
	query := d.Table(d.TableName())
	for _, opt := range opts {
		opt(query)
	}

	var total int64
	if !mysql.IsPageAll(page, size) {
		if err := query.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		query.Scopes(mysql.PageLimit(page, size))
	}

	var list = make([]DataSyncTask, 0)
	if err := query.Order("created_at desc").Find(&list).Error; err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

func (d *DataSyncTask) Count(opts ...mysql.HandleFunc) (int64, error) {
	var num int64

	query := d.DB.Model(&DataSyncTask{})
	for _, f := range opts {
		f(query)
	}

	err := query.Count(&num).Error
	return num, err
}
