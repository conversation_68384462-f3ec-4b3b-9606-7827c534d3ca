package utils

import (
	"archive/zip"
	"io"
	"os"
	"path/filepath"
)

// CreateZip 创建压缩文件
func CreateZip(zipFileName string, files []string) (string, error) {
	zipFile, err := os.Create(zipFileName)
	if err != nil {
		return "", err
	}
	defer zipFile.Close()

	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()

	for _, file := range files {
		err := addFileToZip(zipWriter, file)
		if err != nil {
			return "", err
		}
	}

	return zipFileName, nil
}

func addFileToZip(zipWriter *zip.Writer, file string) error {
	fileToZip, err := os.Open(file)
	if err != nil {
		return err
	}
	defer fileToZip.Close()

	// 获取文件信息
	fileInfo, err := fileToZip.Stat()
	if err != nil {
		return err
	}

	// 创建ZIP文件头，保留文件的修改时间
	header, err := zip.FileInfoHeader(fileInfo)
	if err != nil {
		return err
	}

	// 使用文件名作为ZIP条目名称（去掉路径前缀）
	header.Name = filepath.Base(file)
	header.Method = zip.Deflate

	zipEntryWriter, err := zipWriter.CreateHeader(header)
	if err != nil {
		return err
	}

	_, err = io.Copy(zipEntryWriter, fileToZip)
	return err
}
