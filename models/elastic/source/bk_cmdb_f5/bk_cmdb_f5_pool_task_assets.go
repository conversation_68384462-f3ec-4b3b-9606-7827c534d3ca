package bk_cmdb_f5

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/olivere/elastic/v7"
)

type (
	BkCmdbF5PoolTaskAssets struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`         // NODE+区域+IP 作为唯一值去更新，用作数据对比
		CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
		UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
	}
)

func (b *BkCmdbF5PoolTaskAssets) IndexName() string {
	return "bk_cmdb_f5_pool_task_assets"
}

func (b *BkCmdbF5PoolTaskAssets) String() string {
	return ""
}

func NewBkCmdbF5PoolTaskAssetsModel() *BkCmdbF5PoolTaskAssets {
	return &BkCmdbF5PoolTaskAssets{}
}

func (b *BkCmdbF5PoolTaskAssets) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("bk_inst_id.keyword").
		Field("bk_inst_name.keyword").
		Field("health_monitors.keyword").
		Field("lb_method.keyword").
		Field("priority_group_activation.keyword").
		Field("member_quantity.keyword").
		Field("f5_ip.keyword")
	return field
}

func (b *BkCmdbF5PoolTaskAssets) GetAssetListFields() []map[string]string {
	return []map[string]string{
		{"name": "ID", "key": "bk_inst_id", "all_key": "bk_inst_id"},
		{"name": "实例名", "key": "bk_inst_name", "all_key": "bk_inst_name"},
		{"name": "健康检查", "key": "health_monitors", "all_key": "health_monitors"},
		{"name": "负载均衡算法", "key": "lb_method", "all_key": "lb_method"},
		{"name": "成员数量", "key": "member_quantity", "all_key": "member_quantity"},
	}
}

func (b *BkCmdbF5PoolTaskAssets) GetAssetListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "ID", "key": "bk_inst_id", "all_key": "bk_inst_id"},
		{"name": "实例名", "key": "bk_inst_name", "all_key": "bk_inst_name"},
		{"name": "健康检查", "key": "health_monitors", "all_key": "health_monitors"},
		{"name": "负载均衡算法", "key": "lb_method", "all_key": "lb_method"},
		{"name": "成员数量", "key": "member_quantity", "all_key": "member_quantity"},
	}
}
