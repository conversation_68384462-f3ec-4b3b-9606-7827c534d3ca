package x_ray

import (
	"context"
	"fmt"
	"fobrain/initialize/mysql"
	pb "fobrain/mergeService/proto"
	"fobrain/models/mysql/data_sync_task"
	"fobrain/pkg/utils/handle_es_bulk_error"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"

	"github.com/olivere/elastic/v7"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"

	"fobrain/fobrain/app/services/node/x_ray"
	"fobrain/fobrain/common/constant"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/models/elastic/poc"
	xray "fobrain/models/elastic/source/x-ray"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/proactive_task_node_relations"
	"fobrain/models/mysql/task"
)

var VulTypeMapping = map[string]string{
	"SQL_INJECTION":          "SQL注入",
	"XSS":                    "XSS",
	"CSRF":                   "CSRF",
	"SSRF":                   "SSRF",
	"BACKDOOR":               "后门",
	"UNSERIALIZATION":        "反序列化",
	"CODE_INJECTION":         "代码注入",
	"FILE_UPLOAD":            "文件上传",
	"FILE_INCLUSION":         "文件包含",
	"FILE_WRITE":             "文件修改",
	"FILE_DOWNLOAD":          "文件读取",
	"FILE_DELETION":          "文件删除",
	"OPEN_REDIRECT":          "URL跳转",
	"PERMISSION_BYPASS":      "水平权限绕过",
	"INFO_LEAK":              "信息泄露",
	"UNAUTHORIZED_ACCESS":    "未授权访问",
	"UNSAFE_CONFIG":          "不安全的配置",
	"XXE":                    "XML实体注入",
	"XPATH_INJECTION":        "XPath注入",
	"LDAP_INJECTION":         "LDAP注入",
	"LOGICAL_VULN":           "逻辑错误",
	"DOS":                    "拒绝服务",
	"CRLF_INJECTION":         "CRLF注入",
	"SSTI":                   "模板注入",
	"CLICK_HIJACKING":        "点击劫持",
	"BUFFER_OVERFLOW":        "缓冲区溢出",
	"INTEGER_OVERFLOW":       "整数溢出",
	"FORMAT_STRING":          "格式化字符串",
	"UNINITIALIZED_VARIABLE": "未初始化变量",
	"RACE_CONDITION":         "竞争条件",
	"CRYPTOGRAPHIC":          "密码学问题",
	"TYPE_CONFUSION":         "类型混淆",
	"OTHER":                  "其他",
	"DIRECTORY_TRAVERSAL":    "路径遍历",
	"WEAK_PASSWORD":          "弱口令",
	"MEMORY_SAFE":            "内存安全",
}

func NewSyncTask(node *data_source.Node, taskInfo map[string]any) error {
	cli := x_ray.New()
	err := cli.SetNode(node.Id)
	if err != nil {
		return err
	}

	proactiveTask, err := task.NewProactiveTasks().FindById(int64(taskInfo["task_id"].(uint64)))
	if err != nil {
		return err
	}

	proactiveTaskNodeRelation, err := proactive_task_node_relations.FindById(int64(taskInfo["proactive_task_node_relation_id"].(int)))
	if err != nil {
		return err
	}

	sync := SyncTask{
		cli:                       cli,
		proactiveTask:             proactiveTask,
		proactiveTaskNodeRelation: proactiveTaskNodeRelation,
		defaultPage:               constant.DefaultPageZero,
		node:                      node,
	}

	// x-ray 仅支持漏洞同步
	go sync.syncTaskThreat(sync.defaultPage, proactiveTaskNodeRelation.NodeTaskId)

	proactiveTask.SyncStatus = task.SyncStatusSyncing
	task.NewProactiveTasks().Save(proactiveTask)

	return nil
}

type SyncTask struct {
	cli                       *x_ray.XRay
	node                      *data_source.Node
	proactiveTaskNodeRelation *proactive_task_node_relations.ProactiveTaskNodeRelations
	defaultPage               int
	proactiveTask             *task.ProactiveTasks
}

func (sync *SyncTask) syncTaskThreat(page int, nodeTaskId int) {
	// 获取漏洞数据
	threats, err := sync.cli.GetTaskThreats(int64(nodeTaskId), constant.DefaultSize, int64(page))
	if err != nil {
		logs.GetLogger().Errorf("x-ray syncThreats GetThreats err:%s", err)
		return
	}

	total := gjson.Get(threats, "count").Int() // 数据总量
	result := gjson.Get(threats, "result")

	// 如果是第一页，更新漏洞总数
	if page == constant.DefaultPageZero {
		if err := proactive_task_node_relations.UpdateThreatSum(sync.proactiveTaskNodeRelation.Id, int(total)); err != nil {
			logs.GetLogger().Errorf("sync x-ray task threat - save proactiveTaskNodeRelation Asset sum error: %v", err.Error())
		}
	}

	if total <= 0 {
		proactive_task_node_relations.UpdateNodeDataSyncThreatStatus(sync.proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncSuccessStatus)
		return
	}
	if result.Exists() {
		threatIds := make([]map[string]interface{}, 0)
		// threatBulkRequest := es.GetEsClient().Bulk()
		threatProcessBulkRequest := es.GetEsClient().Bulk()
		threatTaskBulkRequest := es.GetEsClient().Bulk()
		sourceThreatTaskBulkRequest := es.GetEsClient().Bulk()

		// 遍历 _id 字段，并根据 id 添加新的字段数据
		result.ForEach(func(key, value gjson.Result) bool {
			data, _ := sjson.Set(value.String(), "node_id", sync.node.Id)
			data, _ = sjson.Set(data, "area_id", sync.node.AreaId)

			threatId := strings.ReplaceAll(uuid.New().String(), "-", "")
			// threatReq := elastic.NewBulkIndexRequest().Index(xray.NewThreatsModel().IndexName()).Id(threatId).Doc(data)
			// threatBulkRequest = threatBulkRequest.Add(threatReq)

			// 生成漏洞处理模型
			threatProcess := generateThreatProcess(sync, value, threatId, gjson.Get(value.String(), "id").String())
			threatProcessReq := elastic.NewBulkIndexRequest().Index(threatProcess.IndexName()).Id(threatId).Doc(threatProcess)
			threatProcessBulkRequest = threatProcessBulkRequest.Add(threatProcessReq)

			sourceTask := sync.handleSourceTask(data_sync_task.SyncThreat, value.String(), threatId)
			sourceTask, _ = sjson.Set(sourceTask, "id", threatId)
			sourceThreatTaskReq := elastic.NewBulkIndexRequest().Index(xray.NewTaskThreatsModel().IndexName()).Id(threatId).Doc(sourceTask)
			sourceThreatTaskBulkRequest = sourceThreatTaskBulkRequest.Add(sourceThreatTaskReq)

			threatTask := xray.NewFinishedThreatsModel()
			threatTaskId := strings.ReplaceAll(uuid.New().String(), "-", "")
			data, _ = sjson.Set(data, "proactive_task_id", sync.proactiveTask.Id)
			data, _ = sjson.Set(data, "is_poc", threatProcess.IsPoc)
			data, _ = sjson.Set(data, "ip", threatProcess.Ip)
			data, _ = sjson.Set(data, "port", threatProcess.Port)
			if gjson.Get(value.String(), "target.url").String() != "" {
				data, _ = sjson.Set(data, "url", threatProcess.Url)
			}

			data, _ = sjson.Set(data, "name", threatProcess.Name)
			data, _ = sjson.Set(data, "status", threatProcess.Status)
			data, _ = sjson.Set(data, "describe", threatProcess.Describe)
			data, _ = sjson.Set(data, "details", threatProcess.Details)
			data, _ = sjson.Set(data, "suggestions", threatProcess.Suggestions)
			data, _ = sjson.Set(data, "level", threatProcess.Level)
			data, _ = sjson.Set(data, "level_desc", mapLevelDesc(gjson.Get(value.String(), "severity").String()))
			data, _ = sjson.Set(data, "vulType", threatProcess.VulType)
			data, _ = sjson.Set(data, "created_at", threatProcess.CreatedAt)
			data, _ = sjson.Set(data, "updated_at", threatProcess.UpdatedAt)
			data, _ = sjson.Set(data, "cveId", threatProcess.Cve)

			threatTaskReq := elastic.NewBulkIndexRequest().Index(threatTask.IndexName()).Id(threatTaskId).Doc(data)
			threatTaskBulkRequest = threatTaskBulkRequest.Add(threatTaskReq)

			threatIds = append(threatIds, map[string]interface{}{"id": threatId, "is_poc": poc.YesPoc, "task_id": fmt.Sprintf("%s_%s", "task_threat_sync", strconv.Itoa(int(sync.proactiveTask.Id)))})
			return true
		})

		if err := sync.handleBulkRequest(threatProcessBulkRequest, "threatProcessBulkRequest"); err != nil {
			return
		}

		if err := sync.handleBulkRequest(threatTaskBulkRequest, "threatTaskBulkRequest"); err != nil {
			return
		}

		// if err := sync.handleBulkRequest(threatBulkRequest, "threatBulkRequest"); err != nil {
		// 	return
		// }
		if err := sync.handleBulkRequest(sourceThreatTaskBulkRequest, "sourceThreatTaskBulkRequest"); err != nil {
			return
		}
		//if successCount := rq.Push(cfg.LoadQueue().VulnMergeQueue, threatIds); successCount > 0 {
		//	logs.GetSyncLogger().Infof("x-ray syncThreats successCount:%d, expectedCount:%d", successCount, len(threatIds))
		//}
	}

	if page*constant.DefaultSize <= int(total) {
		sync.syncTaskThreat(page+1, nodeTaskId)
	} else {
		proactive_task_node_relations.UpdateNodeDataSyncThreatStatus(sync.proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncSuccessStatus)
		// 发送任务结束标识
		mergeResult, err := pb.GetProtoClient().TriggerMergeForVuln(context.Background(), &pb.TriggerMergeForVulnRequest{
			TriggerMergeBaseRequest: &pb.TriggerMergeBaseRequest{
				SourceId:     sync.node.SourceId,
				NodeId:       sync.node.Id,
				TriggerEvent: "主动扫描任务结束",
				TaskId:       fmt.Sprint(sync.proactiveTask.Id + mysql.ProactiveTaskIdAdd),
				ChildTaskId:  fmt.Sprint(sync.proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd),
			},
			DataRangeByTask: &pb.DataRangeByTask{
				TaskId:      fmt.Sprint(sync.proactiveTask.Id + mysql.ProactiveTaskIdAdd),
				ChildTaskId: fmt.Sprint(sync.proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd),
				NodeId:      sync.node.Id,
			},
		}, pb.ClientWithAddress)
		if err != nil {
			logs.GetLogger().Errorf("proactive task end Send to TriggerMergeRequest error:%v", err)
		}
		if !mergeResult.Success {
			logs.GetLogger().Errorf("proactive task end Send to TriggerMergeRequest Message error:%v", mergeResult.Message)
		}
	}
}

func (sync SyncTask) handleSourceTask(syncType int64, sourceTask, assetId string) string {
	if syncType == data_sync_task.SyncAsset {
		sourceTask, _ = sjson.Set(sourceTask, "asset_task_id", assetId)
	} else if syncType == data_sync_task.SyncThreat {
		sourceTask, _ = sjson.Set(sourceTask, "poc_task_id", assetId)
		sourceTask, _ = sjson.Set(sourceTask, "original_id", gjson.Get(sourceTask, "id").String())
	}

	sourceTask, _ = sjson.Set(sourceTask, "source", sync.node.SourceId)
	sourceTask, _ = sjson.Set(sourceTask, "source_id", sync.node.SourceId)
	sourceTask, _ = sjson.Set(sourceTask, "node_id", sync.node.Id)
	sourceTask, _ = sjson.Set(sourceTask, "node", sync.node.Id)
	sourceTask, _ = sjson.Set(sourceTask, "area_id", sync.node.AreaId)
	sourceTask, _ = sjson.Set(sourceTask, "area", sync.node.AreaId)
	sourceTask, _ = sjson.Set(sourceTask, "task_id", sync.proactiveTask.Id+mysql.ProactiveTaskIdAdd)
	sourceTask, _ = sjson.Set(sourceTask, "child_task_id", sync.proactiveTaskNodeRelation.Id+mysql.ProactiveTaskIdAdd)
	sourceTask, _ = sjson.Set(sourceTask, "sync_created_at", localtime.NewLocalTime(time.Now()))
	sourceTask, _ = sjson.Set(sourceTask, "sync_updated_at", localtime.NewLocalTime(time.Now()))
	return sourceTask
}
func generateThreatProcess(sync *SyncTask, value gjson.Result, threatId, OriginalId string) poc.ProcessPoc {
	ip, port := ExtractIPAndPort(gjson.Get(value.String(), "target.url").String())
	url := gjson.Get(value.String(), "target.url").String()
	// 如果 target.url 为空，则通过 host 去取
	if ip == "" {
		ip = gjson.Get(value.String(), "target.host").String()
		port = int(gjson.Get(value.String(), "target.port").Int())
		url = gjson.Get(value.String(), "id").String()
	}

	threatProcess := poc.NewProcessPocModel()
	threatProcess.Id = threatId
	threatProcess.TaskId = sync.proactiveTask.Id + mysql.ProactiveTaskIdAdd
	threatProcess.ChildTaskId = sync.proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd
	threatProcess.OriginalId = OriginalId
	threatProcess.Area = sync.node.AreaId
	threatProcess.Node = sync.node.Id
	threatProcess.Source = sync.node.SourceId
	threatProcess.IsPoc = IsPoc(gjson.Get(value.String(), "definiteness").String())
	threatProcess.Ip = ip
	threatProcess.Port = port
	threatProcess.Url = url
	threatProcess.Name = gjson.Get(value.String(), "title").String()

	domains := gjson.Get(value.String(), "exposures").Array()
	domainArr := []string{}
	for _, domain := range domains {
		domainArr = append(domainArr, domain.String())
	}

	threatProcess.Cve = GetCVE(domainArr)
	// threatProcess.HasExp = int(gjson.Get(value.String(), "has_exp").Int())
	threatProcess.Status = int(1)
	threatProcess.Describe = gjson.Get(value.String(), "summary").String()
	threatProcess.Hazard = gjson.Get(value.String(), "impact").String()
	threatProcess.Details = gjson.Get(value.String(), "detail").String()
	threatProcess.Suggestions = gjson.Get(value.String(), "solution").String()
	//threatProcess.LastResponse = gjson.Get(value.String(), "last_response").String()

	threatProcess.Level = mapLevel(gjson.Get(value.String(), "severity").String())
	threatProcess.VulType = ConvertVulType(gjson.Get(value.String(), "category").String())

	ct := gjson.Get(value.String(), "created_time").String()
	threatProcess.CreatedAt = parseTimeOrNow(ct)

	updateTime := gjson.Get(value.String(), "created_time").String()
	threatProcess.UpdatedAt = parseTimeOrNow(updateTime)

	return *threatProcess
}

// ExtractIPAndPort
// @Summary 提取IP和端口
func ExtractIPAndPort(raw string) (string, int) {
	// 正则表达式匹配IP和端口，协议可以在前或后
	re := regexp.MustCompile(`(?:(?:[a-zA-Z]+://)?(\d{1,3}(?:\.\d{1,3}){3}):(\d{1,5})|(\d{1,3}(?:\.\d{1,3}){3})[:](\d{1,5})|(\d{1,3}(?:\.\d{1,3}){3}))/?`)
	matches := re.FindStringSubmatch(raw)

	var ip string
	var portStr string

	// 检查不同的匹配情况
	if len(matches) > 0 {
		if matches[1] != "" && matches[2] != "" {
			// 匹配带协议的IP:Port格式
			ip = matches[1]
			portStr = matches[2]
		} else if matches[3] != "" && matches[4] != "" {
			// 匹配 IP:Port 格式
			ip = matches[3]
			portStr = matches[4]
		} else {
			// 只匹配IP，不指定端口，默认设置
			ip = matches[5]
			if raw[:5] == "https" {
				portStr = "443" // HTTPS 默认端口
			} else {
				portStr = "80" // HTTP 默认端口
			}
		}
	} else {
		// 如果没有匹配，返回空字符串和默认端口
		return "", 80
	}

	port, _ := strconv.Atoi(portStr) // 忽略转换错误，默认处理

	return ip, port
}

func ConvertVulType(vulType string) string {
	typeStr := VulTypeMapping[strings.ToUpper(vulType)]
	if typeStr == "" {
		return "未知"
	}

	return typeStr
}

func IsPoc(definiteness string) int {
	if strings.ToUpper(definiteness) == "CERTAINLY" {
		return poc.YesPoc
	} else {
		return poc.NoPoc
	}
}

// mapLevel 将漏洞等级映射为特定的等级值
func mapLevel(level string) int {
	switch level {
	case "LOW":
		return 1 // 低危
	case "MEDIUM":
		return 2 // 中危
	case "HIGH":
		return 3 // 高危
	case "CRITICAL":
		return 4 // 严重
	default:
		return 5 // 未知
	}
}

func mapLevelDesc(level string) string {
	switch level {
	case "LOW":
		return "低危" // 低危
	case "MEDIUM":
		return "中危" // 中危
	case "HIGH":
		return "高危" // 高危
	case "CRITICAL":
		return "严重" // 严重
	default:
		return "未知" // 未知
	}
}

func GetCVE(exposures []string) string {
	for _, exp := range exposures {
		if strings.Contains(exp, "CVE") {
			return exp
		}
	}
	return ""
}

func GetCNNVD(exposures []string) string {
	for _, exp := range exposures {
		if strings.Contains(exp, "CNNVD") {
			return exp
		}
	}
	return ""
}

func GetCTId(exposures []string) string {
	for _, exp := range exposures {
		if strings.Contains(exp, "CT-") {
			return exp
		}
	}
	return ""
}

// parseTimeOrNow 尝试解析时间字符串，如果失败则返回当前时间
func parseTimeOrNow(timeStr string) *localtime.Time {
	if timeStr != "" {
		if t, err := time.Parse(localtime.TimeFormat, timeStr); err == nil {
			return localtime.NewLocalTime(t)
		}
	}
	return localtime.NewLocalTime(time.Now())
}

// handleBulkRequest 处理批量请求并检查错误
func (sync *SyncTask) handleBulkRequest(bulkRequest *es.SafeBulkService, requestName string) error {
	bulkResp, err := bulkRequest.Refresh("true").Do(context.Background())
	if err != nil || bulkResp.Errors {
		errString := handle_es_bulk_error.HandleBulkResp("x_ray", err, bulkResp)
		proactive_task_node_relations.UpdateNodeDataSyncThreatStatus(sync.proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncFailStatus)
		return fmt.Errorf("%s Do err: %v", requestName, errString)
	}
	return nil
}
