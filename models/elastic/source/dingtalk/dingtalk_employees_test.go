package dingtalk

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPeopleIndexName(t *testing.T) {
	indexName := NewDingtalkTaskEmployeesModel().IndexName()
	assert.Equal(t, "dingtalk_task_employees", indexName)
}

func TestPeopleString(t *testing.T) {
	str := NewDingtalkTaskEmployeesModel().String()
	assert.Equal(t, "", str)
}

func TestGetDingTalkPeopleListFields(t *testing.T) {
	fields := NewDingtalkTaskEmployeesModel().GetDingTalkPeopleListFields()

	expectedFields := []map[string]string{
		{"name": "工号", "key": "job_number"},
		{"name": "姓名", "key": "name"},
		{"name": "电话号码", "key": "mobile"},
		{"name": "企业邮箱", "key": "email"},
		{"name": "职位", "key": "title"},
		{"name": "部门", "key": "name", "show_value": "departments"},
	}

	assert.Equal(t, expectedFields, fields)
}

func TestGetDingTalkPeopleListDetailFields(t *testing.T) {
	fields := NewDingtalkTaskEmployeesModel().GetDingTalkPeopleListDetailFields()

	expectedFields := []map[string]string{
		{"name": "工号", "key": "job_number"},
		{"name": "姓名", "key": "name"},
		{"name": "电话号码", "key": "mobile"},
		{"name": "企业邮箱", "key": "email"},
		{"name": "职位", "key": "title"},
		{"name": "部门", "key": "name", "show_value": "departments"},
		{"name": "头像", "key": "avatar"},
		{"name": "办公地点", "key": "work_place"},
		{"name": "备注", "key": "remark"},
		{"name": "爱好", "key": "extension"},
		{"name": "入职时间", "key": "hired_date"},
	}

	assert.Equal(t, expectedFields, fields)
}
