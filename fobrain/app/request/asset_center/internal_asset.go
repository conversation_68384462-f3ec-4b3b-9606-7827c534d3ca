package asset_center

import (
	"fobrain/fobrain/common/request"
)

type AssetRequest struct {
	Keyword              string   `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
	Name                 string   `json:"name" form:"name" uri:"name" validate:"omitempty,max=100" zh:"姓名"`
	Id                   string   `json:"id" form:"id" uri:"id" validate:"omitempty,min=1" zh:"列表 ID"`
	Ids                  []string `json:"ids" form:"ids" uri:"ids" zh:"IDS"`
	SourceIds            []uint64 `json:"source_ids" form:"source_ids" uri:"source_ids" zh:"数据源ids"`
	Ips                  []string `json:"ips" form:"ips" uri:"ips"  zh:"IP地址"`
	Ports                []uint64 `json:"ports" form:"ports" uri:"ports"  zh:"端口"`
	Protocols            []string `json:"protocols" form:"protocols" uri:"protocols" zh:"协议"`
	Component            []string `json:"component" form:"component" uri:"component" zh:"组件"`
	AssetsStatus         []uint64 `json:"assets_status" form:"assets_status" uri:"assets_status" zh:"资产状态"`
	IpTypes              []uint64 `json:"ip_types" form:"ip_types" uri:"ip_types" zh:"IP类型"`
	NetworkType          int      `json:"network_type" form:"network_type" uri:"network_type" zh:"IP网络类型"`
	RdmIps               []string `json:"rdm_ips" form:"rdm_ips" uri:"rdm_ips"  zh:"映射IP"` // 字段信息不存在
	MacAddress           []string `json:"mac_address" form:"mac_address" uri:"mac_address"  zh:"MAC地址"`
	Domain               []string `json:"domain" form:"domain" uri:"domain"  zh:"内网域名"`
	Area                 []uint64 `json:"area" form:"area" uri:"area"  zh:"所属区域"`
	PhysicalFacilities   []string `json:"physical_facilities" form:"physical_facilities" uri:"physical_facilities"  zh:"关联实体设备"` // 字段信息不存在
	BusinessApp          []string `json:"business_app" form:"business_app" uri:"business_app"  zh:"业务系统"`
	BusinessDepartment   []string `json:"business_department" form:"business_department" uri:"business_department"  zh:"业务系统部门"`
	BusinessAppPrincipal []string `json:"business_app_principal" form:"business_app_principal" uri:"business_app_principal"  zh:"业务系统负责人"`
	OperatorPrincipal    []string `json:"operator_principal" form:"operator_principal" uri:"operator_principal"  zh:"运维负责人"`
	FirstTime            []string `json:"first_time" form:"first_time" uri:"first_time"  zh:"首次上报时间"`
	FinallyTime          []string `json:"finally_time" form:"finally_time" uri:"finally_time"  zh:"最后上报时间"`
	IsDeviceExtracted    []uint64 `json:"is_device_extracted" form:"is_device_extracted" uri:"is_device_extracted"  zh:" // 是否参与了实体提取,1是未参与，2是参与了"`
	Recycle              int      `json:"recycle" form:"recycle" uri:"recycle"  zh:" // 是否回收站数据 ,0 不区分 1否，2是"`
	Field                string   `json:"field" form:"field" uri:"field"  zh:"排序字段"`
	MustAttr             []string `json:"must_attr" form:"must_attr" uri:"must_attr"  zh:"必存在数据属性"`
	Order                string   `json:"order" form:"order" uri:"order"  zh:"排序方式"`
	OperationTypeString  string   `json:"operation_type_string" form:"operation_type_string" uri:"operation_type_string" validate:"omitempty,oneof=in not_in == !== null not_null = !="  zh:"操作类型"`
	SearchCondition      []string `json:"search_condition" form:"search_condition" uri:"search_condition"  zh:"搜索条件"`
	SelectFields         []string `json:"select_fields" form:"select_fields" uri:"select_fields"  zh:"需要返回的字段列表"`
}

type InternalAssetRequest struct {
	request.PageRequest
	AssetRequest
}

type AssetRuleInfosRequest struct {
	Keyword         string   `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
	Field           string   `json:"field" form:"field" uri:"field" validate:"required,oneof='first_tag' 'second_tag' 'product'" zh:"组件分类 资产类型"`
	NetworkType     int      `json:"network_type" form:"network_type" uri:"network_type" zh:"IP网络类型"`
	Recycle         int      `json:"recycle" form:"recycle" uri:"recycle" zh:"是否回收站 不传为所有。1不是 2是  不传为所有"`
	SearchCondition []string `json:"search_condition" form:"search_condition" uri:"search_condition"  zh:"搜索条件"`
}

type InternalAssetIdRequest struct {
	Id string `json:"id" form:"id" uri:"id" validate:"omitempty,min=1" zh:"列表 ID"`
}

type InternalAssetIdsRequest struct {
	Ids             []string `json:"ids" form:"ids" uri:"ids" zh:"IDS"`
	Keyword         string   `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
	SearchCondition []string `json:"search_condition" form:"search_condition" uri:"search_condition"  zh:"搜索条件"`
}

type IpKeywordRequest struct {
	Keyword     string `json:"keyword" form:"keyword" uri:"keyword" zh:"模糊查询"`
	Recycle     int    `json:"recycle" form:"recycle" uri:"recycle" zh:"是否回收站 不传为所有。1不是 2是  不传为所有"`
	KeyWordType int    `json:"type" form:"type" uri:"type" validate:"omitempty,oneof=1 2 3 4 5 6" zh:"模糊查询类型 1资产 2漏洞 3设备 4合规监测 5业务系统 6资产图谱业务系统"`
	Field       string `json:"field" form:"field" uri:"field" zh:"查询字段"`
}
