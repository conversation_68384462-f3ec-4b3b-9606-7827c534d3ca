package cfg

type Logger struct {
	Level               string  `json:"level"`
	OutPutConsole       bool    `json:"output_console"`
	OutPutFile          bool    `json:"output_file"`
	FileName            string  `json:"-"`
	FileNameFobrain     string  `json:"file_name_fobrain"`
	FileNameService     string  `json:"file_name_service"`
	FileNameAsset       string  `json:"file_name_asset"`
	FileNameDevice      string  `json:"file_name_device"`
	FileNameVuln        string  `json:"file_name_vuln"`
	FileNamePerson      string  `json:"file_name_person"`
	FileNameScheduelr   string  `json:"file_name_scheduler"`
	FileNameSync        string  `json:"file_name_sync"`
	FileNameCascade     string  `json:"file_name_cascade"`
	FileNameBackup      string  `json:"file_name_backup"`
	MaxSize             int     `json:"max_size"`
	MaxAge              int     `json:"max_age"` //util:day
	MaxBackups          int     `json:"max_backups"`
	LocalTime           bool    `json:"local_time"`
	Compress            bool    `json:"compress"`
	AuditLogMaxSizeGB   float64 `json:"audit_log_max_size_gb"`
	AuditLogExpiredDays int     `json:"audit_log_expired_days"`
}

// LoadLogger 加载日志配置
func LoadLogger() Logger {
	GetInstance()
	l := GetInstance().Logger
	l.FileName = l.FileNameFobrain
	l.OutPutConsole = true
	return l
}

func LoadLoggerByType(t string) Logger {
	GetInstance()
	l := GetInstance().Logger
	if t == "scheduler" {
		l.FileName = l.FileNameScheduelr
		l.OutPutConsole = false
	} else if t == "sync" {
		l.FileName = l.FileNameSync
		l.OutPutConsole = true
	} else if t == "cascade" {
		l.FileName = l.FileNameCascade
		l.OutPutConsole = false
	} else if t == "backup" {
		l.FileName = l.FileNameBackup
		l.OutPutConsole = true
	} else {
		l.FileName = l.FileNameFobrain
		l.OutPutConsole = true
	}
	return l
}

func LoadMergeLoggerByType(t string) Logger {
	GetInstance()
	l := GetInstance().MergeLogger
	if t == "asset" {
		l.FileName = l.FileNameAsset
	}
	if t == "device" {
		l.FileName = l.FileNameDevice
	}
	if t == "vuln" {
		l.FileName = l.FileNameVuln
	}
	if t == "vuln_update" {
		l.FileName = l.FileNameVuln
	}
	if t == "person" {
		l.FileName = l.FileNamePerson
	}
	if t == "service" {
		l.FileName = l.FileNameService
	}
	return l
}
