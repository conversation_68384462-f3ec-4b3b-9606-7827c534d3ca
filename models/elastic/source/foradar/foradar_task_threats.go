package foradar

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/olivere/elastic/v7"
)

type (
	ForadarTaskThreats struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`      // NODE+区域+IP+Port 作为唯一值去更新，用作记录每次任务拉取数据
		TaskId        string          `json:"task_id"` //
		ChildTaskId   string          `json:"child_task_id"`
		CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
		UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
	}
)

// IndexName 索引名
func (f *ForadarTaskThreats) IndexName() string {
	return "foradar_task_threats"
}

func (f *ForadarTaskThreats) String() string {
	if f == nil {
		return ""
	}
	return ""
}

func NewForadarTaskThreatsModel() *ForadarTaskThreats {
	return &ForadarTaskThreats{}
}

func (f *ForadarTaskThreats) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("ip").
		Field("ip.keyword").
		Field("url.keyword").
		Field("common_title.keyword").
		Field("vul_type.keyword").
		Field("host_info.keyword")

	return field
}

func (f *ForadarTaskThreats) GetThreatListFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "漏洞名称", "key": "common_title"},
		//{"name": "漏洞分类", "key": "is_poc_scan"},
		{"name": "CVE编号", "key": "cve_id"},
		{"name": "漏洞类型", "key": "vul_type"},
		{"name": "漏洞等级", "key": "level"},
		{"name": "漏洞地址", "key": "url"},
		{"name": "发现时间", "key": "created_at"},
	}
}

func (f *ForadarTaskThreats) GetThreatListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "漏洞名称", "key": "common_title"},
		//{"name": "漏洞分类", "key": "is_poc_scan"},
		{"name": "CVE编号", "key": "cve_id"},
		{"name": "漏洞类型", "key": "vul_type"},
		{"name": "漏洞等级", "key": "level"},
		{"name": "漏洞地址", "key": "url"},
		{"name": "发现时间", "key": "created_at"},
	}
}
