package dingtalk

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/olivere/elastic/v7"
)

type (
	DingtalkTaskEmployees struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`      //NODE+userId+区域+Task 作为唯一值去更新，用作记录每次任务拉取数据
		TaskId        string          `json:"task_id"` //
		ChildTaskId   string          `json:"child_task_id"`
		CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
		UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
	}
)

// IndexName 索引名
func (d *DingtalkTaskEmployees) IndexName() string {
	return "dingtalk_task_employees"
}

func (d *DingtalkTaskEmployees) String() string {
	if d == nil {
		return ""
	}
	return ""
}

func NewDingtalkTaskEmployeesModel() *DingtalkTaskEmployees {
	return &DingtalkTaskEmployees{}
}

func (d *DingtalkTaskEmployees) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("title.keyword").
		Field("job_number.keyword").
		Field("email.keyword").
		Field("name.keyword").
		Field("mobile.keyword")

	return field
}

func (d *DingtalkTaskEmployees) GetDingTalkPeopleListFields() []map[string]string {
	return []map[string]string{
		{"name": "工号", "key": "job_number"},
		{"name": "姓名", "key": "name"},
		{"name": "电话号码", "key": "mobile"},
		{"name": "企业邮箱", "key": "email"},
		{"name": "职位", "key": "title"},
		{"name": "部门", "key": "name", "show_value": "departments"},
	}
}

func (d *DingtalkTaskEmployees) GetDingTalkPeopleListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "工号", "key": "job_number"},
		{"name": "姓名", "key": "name"},
		{"name": "电话号码", "key": "mobile"},
		{"name": "企业邮箱", "key": "email"},
		{"name": "职位", "key": "title"},
		{"name": "部门", "key": "name", "show_value": "departments"},
		{"name": "头像", "key": "avatar"},
		{"name": "办公地点", "key": "work_place"},
		{"name": "备注", "key": "remark"},
		{"name": "爱好", "key": "extension"},
		{"name": "入职时间", "key": "hired_date"},
	}
}
