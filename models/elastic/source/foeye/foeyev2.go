package foeye

import (
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/proactive_task_node_relations"
)

const (
	FoeyeV2SourceTaskAssetsIndex  = "foeyev2_task_assets"
	FoeyeV2SourceTaskThreatsIndex = "foeyev2_task_threats"
	FoeyeV2FinishedAssets         = "foeyev2_finished_assets"
	FoeyeV2FinishedThreats        = "foeyev2_finished_threats"
)

func QueryProactiveTaskVersion(proactiveTaskId int) string {
	var version = "v3"
	nodeIds := proactive_task_node_relations.GetNodeIds(proactiveTaskId)
	if len(nodeIds) > 0 {
		config, _ := data_source.NewNodeConfigModel().GetNodeConfig(uint64(nodeIds[0]))
		if config != nil && len(config) > 1 {
			t, ok := config["version"]
			if ok && t == "v2" {
				return "v2"
			}
		}
	}
	return version
}
