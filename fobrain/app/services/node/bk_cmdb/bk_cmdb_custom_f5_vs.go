package bk_cmdb

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	httputil "fobrain/pkg/http"
	"io"
	"strconv"
	"time"

	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
	"go-micro.dev/v4/logger"

	"fobrain/fobrain/app/services/node"
	"fobrain/fobrain/app/services/operate"
	"fobrain/fobrain/common/constant"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/models/elastic/assets"
	"fobrain/pkg/utils"
)

type BkCmdbCustomF5Vs struct {
	node.Client
	AppCode    string
	AppSecret  string
	Account    string
	Ip         string
	Protocol   string
	opera      operate.Operate
	StaffField string
}

const (
	CustomF5VsUriGetModuleApi = "%s://%s/api/c/compapi/v2/cc/search_inst_by_object"
	CustomModelF5Vs           = "f5_vs"
)

func NewBkCmdbCustomF5Vs() *BkCmdbCustomF5Vs {
	return &BkCmdbCustomF5Vs{Client: node.NewDefaultClient()}
}

func (b *BkCmdbCustomF5Vs) initPrams(opera operate.Operate) {
	if b.AppCode == "" {
		b.AppCode = opera.GetParamByKey("app_code").(string)
		b.AppSecret = opera.GetParamByKey("app_secret").(string)
		b.Account = opera.GetParamByKey("account").(string)
		b.Ip = opera.GetParamByKey("ip").(string)
		b.Protocol = opera.GetParamByKey("protocol").(string)
		b.StaffField = opera.GetParamByKey("person_field").(string)
	}
}

func (b *BkCmdbCustomF5Vs) GetVul(ctx context.Context, opera operate.Operate, fu node.ProcessHandler) error {
	return nil
}
func (b *BkCmdbCustomF5Vs) GetPerson(ctx context.Context, opera operate.Operate, fu node.ProcessHandler) error {
	return nil
}

func (b *BkCmdbCustomF5Vs) GetAssets(ctx context.Context, opera operate.Operate, fu node.ProcessHandler) error {
	b.initPrams(opera)
	pageSize := constant.DefaultSize * 4
	result, total, err := b.CustomGetAssetsModule(constant.DefaultPageZero, pageSize, CustomModelF5Vs, 0)
	if err != nil {
		return err
	}
	logger.Infof("BkCmdbCustomF5Vs 获取数据源的总数 %d", total)

	num := 0
	originalDatas, processDatas, successNum, err := b.execData(result, total, opera)
	if err != nil {
		return err
	}
	num += successNum
	logger.Infof("BkCmdbCustomF5Vs 首次执行成功总数 %d", num)
	err = opera.BatchUpdateAsset(originalDatas, processDatas, []map[string]interface{}{})
	if err != nil {
		logger.Errorf("BkCmdbCustomF5Vs BatchUpdateAsset 批量更新数据失败: %v", err.Error())
		return err
	}
	pages := calculateTotalPages(int(total), pageSize)
	logger.Infof("获取资产数据完成, 总页数: %d，总数:%d", pages, total)
	for i := 2; i <= pages; i++ {
		result, total, err = b.CustomGetAssetsModule(i-1, pageSize, CustomModelF5Vs, 0)
		if err != nil {
			logger.Errorf("BkCmdbCustomF5Vs CustomGetAssetsModule : %v", err.Error())
			return err
		}
		originalDatas, processDatas, successNum, err = b.execData(result, total, opera)
		if err != nil {
			logger.Errorf("BkCmdbCustomF5Vs execData 组装数据失败: %v", err.Error())
			return err
		}
		err = opera.BatchUpdateAsset(originalDatas, processDatas, []map[string]interface{}{})
		if err != nil {
			logger.Errorf("BkCmdbCustomF5Vs BatchUpdateAsset 批量更新数据失败: %v", err.Error())
			return err
		}
		num += successNum
		logger.Infof("BkCmdbCustomF5Vs 第 %d 页执行成功总数:%d,SuccessNum: %d", i, num, successNum)

		time.Sleep(1 * time.Second)
	}
	err = opera.UpdateSyncDataTotal(1, num)
	if err != nil {
		logger.Errorf("BkCmdbCustomF5Vs UpdateSyncDataTotal 更新总数失败: %v", err.Error())
		return err
	}
	err = opera.UpdateSyncDataSuccessTotal(1, num)
	if err != nil {
		logger.Errorf("BkCmdbCustomF5Vs UpdateSyncDataSuccessTotal 更新总数失败: %v", err.Error())
		return err
	}
	logger.Infof("BkCmdbCustomF5Vs GetAssets 执行完成")
	return nil
}

func (b *BkCmdbCustomF5Vs) execData(result gjson.Result, total int64, opera operate.Operate) ([]map[string]interface{}, []map[string]interface{}, int, error) {
	defer func() {
		if reErr := recover(); reErr != nil {
			logs.GetCrontabLogger().Infof("BkCmdbCustomF5Vs execData result:%s > FAIL, 返回结果: %v", result.Raw, reErr)
		}
	}()
	var successNum int
	var originalDatas, processDatas []map[string]interface{}
	if result.Exists() && total > 0 {
		result.ForEach(func(key, value gjson.Result) bool {
			ip := gjson.Get(value.String(), "f5_ip").String()
			port := gjson.Get(value.String(), "port").String()
			logger.Infof("BkCmdbCustomF5Vs 获取资产数据IP: %v", ip)
			if !utils.IsValidIP(ip) {
				logger.Errorf("BkCmdbCustomF5Vs execData 无效的IP地址: %v", ip)
				return true
			}
			// 根据id 获取详情数据
			id := gjson.Get(value.String(), "bk_inst_id").Int()
			details, _, err := b.CustomGetAssetsModule(0, 0, CustomModelF5Vs, id)
			logger.Infof("BkCmdbCustomF5Vs execData 获取资产详情数据Id: %v", id)
			if err != nil {
				logger.Errorf("BkCmdbCustomF5Vs execData 获取详情数据失败: %v", err.Error())
				return false
			}
			sourceId, processId := opera.GetUUID(fmt.Sprintf("%s_%s_%d", ip, port, id))

			data, _ := sjson.Set(value.String(), "details", details.Raw)
			var original map[string]interface{}
			if err := json.Unmarshal([]byte(data), &original); err != nil {
				opera.WriteLog("数据反序列化失败: " + err.Error())
				logger.Errorf("BkCmdbCustomF5Vs execData 数据反序列化失败: %v", err.Error())
				return false
			}
			original["id"] = sourceId
			originalDatas = append(originalDatas, original)
			processData := b.AssetsTransform(value.String())
			processData["id"] = processId
			processDatas = append(processDatas, processData)
			successNum++
			return true
		})
	}
	return originalDatas, processDatas, successNum, nil
}

// CustomGetAssetsModule 获取资产模块数据
func (b *BkCmdbCustomF5Vs) CustomGetAssetsModule(page, size int, bkObjId string, id int64) (gjson.Result, int64, error) {
	pageMap := make(map[string]int)
	pageMap["start"] = page * size
	pageMap["limit"] = size
	if id != 0 {
		pageMap["start"] = 0
		pageMap["limit"] = 1
	}

	params := map[string]interface{}{
		"page":          pageMap,
		"bk_app_code":   b.AppCode,
		"bk_app_secret": b.AppSecret,
		"bk_username":   b.Account,
		"bk_obj_id":     bkObjId,
	}
	if id != 0 {
		params["condition"] = map[string]interface{}{
			"bk_inst_id": id,
		}
	}

	// 发送请求
	httpClient := httputil.NewHttpClient(b.Client.Client)
	apiUri := fmt.Sprintf(CustomF5VsUriGetModuleApi, b.Protocol, b.Ip)
	resp, err := httpClient.Post(apiUri, params, nil)

	if err != nil {
		return gjson.Result{}, 0, err
	}

	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)

	if err != nil {
		return gjson.Result{}, 0, err
	}

	if resp.StatusCode != 200 || gjson.Get(string(body), "code").Int() != 0 {
		return gjson.Result{}, 0, errors.New(gjson.Get(string(body), "message").String())
	}
	list := string(body)
	total := gjson.Get(list, "data.count").Int()
	result := gjson.Get(list, "data.info")
	return result, total, nil
}

func (b *BkCmdbCustomF5Vs) AssetsTransform(item string) map[string]interface{} {
	ip := gjson.Get(item, "ip_addr").String()
	now := time.Now()
	port, _ := strconv.Atoi(gjson.Get(item, "port").String())
	processData := assets.ProcessAssets{
		Ip:       ip,
		HostName: gjson.Get(item, "bk_inst_name").String(),
		MapIp:    ip,
		Ports: []*assets.PortInfo{
			{
				Port: port,
			},
		},
		CreatedAt:   localtime.NewLocalTime(now),
		UpdatedAt:   localtime.NewLocalTime(now),
		PersonField: b.StaffField,
	}
	processByte, _ := json.Marshal(processData)
	var process map[string]interface{}
	json.Unmarshal(processByte, &process)
	return process
}

func (b *BkCmdbCustomF5Vs) calculateTotalPages(totalCount int, pageSize int) int {
	if totalCount == 0 {
		return 0
	}
	pages := totalCount / pageSize
	if totalCount%pageSize != 0 {
		pages++
	}
	return pages
}
