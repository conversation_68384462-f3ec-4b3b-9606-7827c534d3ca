package system_configs

import (
	"context"
	"fmt"
	"os/exec"
	"strings"
	"time"

	"fobrain/fobrain/app/crontab"
	"fobrain/fobrain/app/services/backup"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	"fobrain/fobrain/logs"
	mcron "fobrain/models/mysql/crontab"
	"fobrain/models/mysql/system_configs"
	"fobrain/pkg/cfg"

	"git.gobies.org/fobrain/unibackup/pkg/types"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type backupConfig struct {
	Type              *string `json:"type" validate:"required,oneof=local cloud"`            // 备份类型 local/cloud
	FullIntervalDays  *int    `json:"full_interval_days" validate:"omitempty,min=1,max=30"`  // 全量备份间隔天数
	IncrIntervalHours *int    `json:"incr_interval_hours" validate:"omitempty,min=1,max=24"` // 增量备份间隔小时数
	RetentionDays     *int    `json:"retention_days" validate:"omitempty,min=7,max=365"`     // 备份保留天数

	// 云存储配置（展开为同级字段）
	CloudStorageType      *string `json:"cloud_storage_type" validate:"omitempty,oneof=s3 oss cos"` // 云存储类型
	CloudStorageEndpoint  *string `json:"cloud_storage_endpoint" validate:"omitempty,url"`          // 云存储端点
	CloudStorageBucket    *string `json:"cloud_storage_bucket" validate:"omitempty,min=1"`          // 存储桶名称
	CloudStorageAccessKey *string `json:"cloud_storage_access_key" validate:"omitempty,min=1"`      // 访问密钥
	CloudStorageSecretKey *string `json:"cloud_storage_secret_key" validate:"omitempty,min=1"`      // 密钥
	CloudStorageRegion    *string `json:"cloud_storage_region"`                                     // 区域
}

// ManualFullBackup 手动分组全量备份
func ManualFullBackup(ctx *gin.Context) error {
	if !backup.IsEnabled() {
		return response.FailWithMessage(ctx, "备份服务未启用")
	}

	// 检查是否有全量备份正在运行
	if backup.IsBackupTaskRunning(types.BackupTypeArchival) {
		return response.FailWithMessage(ctx, "分组全量备份正在运行中")
	}

	// 检查融合任务
	if backup.IsMergeTaskRunning() {
		return response.FailWithMessage(ctx, "融合任务正在运行，无法执行备份")
	}

	manager := backup.GetManager()
	if manager == nil {
		return response.FailWithMessage(ctx, "备份管理器不可用")
	}

	config, err := backup.GetBackupConfig()
	if err != nil {
		return response.FailWithMessage(ctx, fmt.Sprintf("获取备份配置失败: %v", err))
	}

	// 构建分组全量备份请求
	req := buildGroupBackupRequest(types.BackupTypeArchival, "手动分组全量备份", config)
	if len(req.Sources) == 0 {
		return response.FailWithMessage(ctx, "没有启用的数据源")
	}

	// 执行分组全量备份
	taskID, err := manager.BackupAllAsync(context.Background(), req)
	if err != nil {
		return response.FailWithMessage(ctx, fmt.Sprintf("分组全量备份失败: %v", err))
	}

	// 更新上次全量备份时间
	backup.UpdateLastBackupTime(backup.BackupLastFullTimeKey, time.Now())

	return response.OkWithData(ctx, map[string]string{"task_id": taskID})
}

// ManualIncrBackup 手动分组增量备份
func ManualIncrBackup(ctx *gin.Context) error {
	if !backup.IsEnabled() {
		return response.FailWithMessage(ctx, "备份服务未启用")
	}

	// 检查是否有增量备份正在运行
	if backup.IsBackupTaskRunning(types.BackupTypeChainIncremental) {
		return response.FailWithMessage(ctx, "分组增量备份正在运行中")
	}

	// 检查融合任务
	if backup.IsMergeTaskRunning() {
		return response.FailWithMessage(ctx, "融合任务正在运行，无法执行备份")
	}

	manager := backup.GetManager()
	if manager == nil {
		return response.FailWithMessage(ctx, "备份管理器不可用")
	}

	config, err := backup.GetBackupConfig()
	if err != nil {
		return response.FailWithMessage(ctx, fmt.Sprintf("获取备份配置失败: %v", err))
	}

	// 构建分组增量备份请求
	req := buildGroupBackupRequest(types.BackupTypeChainIncremental, "手动分组增量备份", config)
	if len(req.Sources) == 0 {
		return response.FailWithMessage(ctx, "没有启用的数据源")
	}

	// 执行分组增量备份
	taskID, err := manager.BackupAllAsync(context.Background(), req)
	if err != nil {
		return response.FailWithMessage(ctx, fmt.Sprintf("分组增量备份失败: %v", err))
	}

	// 更新上次增量备份时间
	backup.UpdateLastBackupTime(backup.BackupLastIncrTimeKey, time.Now())

	return response.OkWithData(ctx, map[string]string{"task_id": taskID})
}

// ListBackups 获取备份列表（使用最新SDK统一查询接口）
func ListBackups(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		SearchText string `json:"search_text" form:"search_text"` // 搜索文本
		Limit      int    `json:"limit" form:"limit"`             // 分页大小
		Offset     int    `json:"offset" form:"offset"`           // 分页偏移
	}{})
	if err != nil {
		return err
	}

	if !backup.IsEnabled() {
		return response.FailWithMessage(ctx, "备份服务未启用")
	}

	manager := backup.GetManager()
	if manager == nil {
		return response.FailWithMessage(ctx, "备份管理器不可用")
	}

	// 构建过滤条件
	filter := types.BackupFilter{
		SearchText: params.SearchText,
		Limit:      params.Limit,
		Offset:     params.Offset,
		TaskTypes:  []types.TaskType{types.RestoreAllTask, types.BackupAllTask},
	}

	// 查询备份列表
	result, err := manager.ListAllBackups(context.Background(), filter)
	if err != nil {
		return response.FailWithMessage(ctx, fmt.Sprintf("查询备份列表失败: %v", err))
	}

	return response.OkWithData(ctx, result)
}

// GetBackupDeletionInfo 获取备份删除影响信息
func GetBackupDeletionInfo(ctx *gin.Context) error {
	taskID := ctx.Param("id")
	if taskID == "" {
		return response.FailWithMessage(ctx, "任务ID不能为空")
	}

	manager := backup.GetManager()
	if manager == nil {
		return response.FailWithMessage(ctx, "备份管理器不可用")
	}

	// 获取删除影响信息
	info, err := manager.GetBackupDeletionInfo(context.Background(), taskID)
	if err != nil {
		return response.FailWithMessage(ctx, fmt.Sprintf("获取删除信息失败: %v", err))
	}

	return response.OkWithData(ctx, info)
}

// DeleteBackup 删除备份（使用最新SDK删除接口）
func DeleteBackup(ctx *gin.Context) error {
	taskID := ctx.Param("id")
	if taskID == "" {
		return response.FailWithMessage(ctx, "任务ID不能为空")
	}

	manager := backup.GetManager()
	if manager == nil {
		return response.FailWithMessage(ctx, "备份管理器不可用")
	}

	// 删除备份
	err := manager.DeleteBackupByTaskID(context.Background(), taskID)
	if err != nil {
		return response.FailWithMessage(ctx, fmt.Sprintf("删除备份失败: %v", err))
	}

	return response.Ok(ctx)
}

// RestoreBackup 恢复备份
func RestoreBackup(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		TaskID string `json:"task_id" form:"task_id" binding:"required"` // 备份任务ID
		Force  *bool  `json:"force" form:"force"`                        // 是否强制恢复（可选，默认false）
	}{})
	if err != nil {
		return err
	}

	if !backup.IsEnabled() {
		return response.FailWithMessage(ctx, "备份服务未启用")
	}

	// 检查融合任务
	if backup.IsMergeTaskRunning() {
		return response.FailWithMessage(ctx, "融合任务正在运行，无法执行恢复")
	}

	manager := backup.GetManager()
	if manager == nil {
		return response.FailWithMessage(ctx, "备份管理器不可用")
	}

	// 确定是否强制恢复
	force := false
	if params.Force != nil {
		force = *params.Force
	}

	// 使用SDK的RestoreByTaskIDWithCallback接口，恢复完成后自动执行迁移
	restoreTaskID, err := manager.RestoreByTaskIDWithCallback(
		context.Background(),
		params.TaskID,
		force,
		executePostRestoreMigration, // 回调函数
	)
	if err != nil {
		return response.FailWithMessage(ctx, fmt.Sprintf("恢复备份失败: %v", err))
	}

	return response.OkWithData(ctx, map[string]string{
		"restore_task_id": restoreTaskID,
		"message":         "恢复任务已启动",
	})
}

// executePostRestoreMigration 恢复成功后的迁移回调函数
func executePostRestoreMigration(ctx context.Context) error {
	logs.GetLogger().Info("恢复成功，开始执行数据迁移和定时任务重置")

	// 1. 执行数据迁移
	var cmd *exec.Cmd
	if cfg.LoadCommon().Local {
		// 本地开发环境：使用 make 命令
		rootDir := strings.ReplaceAll(cfg.GetInstance().Common.StoragePath, "storage", "")
		cmd = exec.Command("sh", "-c", fmt.Sprintf("cd %s && make migrate-run Component=all", rootDir))
	} else {
		// 正式环境：使用编译后的 cmd 二进制文件
		cmd = exec.Command("cmd", "migrate", "run")
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		logs.GetLogger().Error("恢复后数据迁移失败", "error", err, "output", string(output))
		return fmt.Errorf("数据迁移失败: %v, 输出: %s", err, string(output))
	}

	logs.GetLogger().Info("恢复后数据迁移完成", "output", string(output))

	// 2. 重置定时任务状态
	if err := resetCrontabRunningStatus(); err != nil {
		logs.GetLogger().Warn("重置定时任务状态失败", "error", err)
	}

	// 3. 重新加载定时任务
	if err := reloadAllCrontabJobs(); err != nil {
		logs.GetLogger().Error("重新加载定时任务失败", "error", err)
		return fmt.Errorf("重新加载定时任务失败: %v", err)
	}

	logs.GetLogger().Info("恢复后数据迁移和定时任务重置完成")
	return nil
}

// resetCrontabRunningStatus 重置定时任务运行状态
func resetCrontabRunningStatus() error {
	return mcron.NewCrontabModel().DB.
		Where("running = ?", mcron.RunningYes).
		Update("running", mcron.RunningNo).Error
}

// reloadAllCrontabJobs 重新加载所有定时任务
func reloadAllCrontabJobs() error {
	cronIns := crontab.GetCronIns()
	if cronIns == nil {
		return fmt.Errorf("定时任务调度器不可用")
	}

	logs.GetLogger().Info("开始重新加载定时任务")

	// 1. 获取数据库中的所有任务ID
	dbTasks, _, err := mcron.NewCrontabModel().List(0, 0)
	if err != nil {
		return fmt.Errorf("获取数据库任务列表失败: %v", err)
	}

	// 2. 停止可能存在的旧任务（基于数据库ID范围）
	for _, task := range dbTasks {
		cronIns.StopJob(cast.ToString(task.Id))
	}

	// 3. 重新加载所有任务
	err = cronIns.LoadMysqlJob()
	if err != nil {
		return fmt.Errorf("重新加载定时任务失败: %v", err)
	}

	logs.GetLogger().Info("定时任务重新加载完成")
	return nil
}

// GetTaskStatus 获取任务状态
func GetTaskStatus(ctx *gin.Context) error {
	taskID := ctx.Param("id")
	if taskID == "" {
		return response.FailWithMessage(ctx, "任务ID不能为空")
	}

	manager := backup.GetManager()
	if manager == nil {
		return response.FailWithMessage(ctx, "备份管理器不可用")
	}

	// 获取任务详情
	taskInfo, err := manager.GetTask(taskID)
	if err != nil {
		return response.FailWithMessage(ctx, fmt.Sprintf("获取任务状态失败: %v", err))
	}

	return response.OkWithData(ctx, taskInfo)
}

// GetBackupConfig 获取备份配置
func GetBackupConfig(ctx *gin.Context) error {
	config, err := backup.GetBackupConfig()
	if err != nil {
		return response.FailWithMessage(ctx, fmt.Sprintf("获取备份配置失败: %v", err))
	}

	return response.OkWithData(ctx, config)
}

// UpdateBackupConfig 更新备份配置
func UpdateBackupConfig(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &backupConfig{})
	if err != nil {
		return err
	}

	// 构建配置更新map
	updates := make(map[string]interface{})
	if params.Type != nil {
		updates[backup.BackupTypeKey] = *params.Type
	}
	updates[backup.BackupEnabledKey] = "true"

	if params.FullIntervalDays != nil {
		updates[backup.BackupFullIntervalDaysKey] = fmt.Sprintf("%d", *params.FullIntervalDays)
	}

	if params.IncrIntervalHours != nil {
		updates[backup.BackupIncrIntervalHoursKey] = fmt.Sprintf("%d", *params.IncrIntervalHours)
	}

	if params.RetentionDays != nil {
		updates[backup.BackupRetentionDaysKey] = fmt.Sprintf("%d", *params.RetentionDays)
	}
	if params.CloudStorageType != nil {
		updates[backup.CloudStorageTypeKey] = *params.CloudStorageType
	}

	if params.CloudStorageEndpoint != nil {
		updates[backup.CloudStorageEndpointKey] = *params.CloudStorageEndpoint
	}

	if params.CloudStorageBucket != nil {
		updates[backup.CloudStorageBucketKey] = *params.CloudStorageBucket
	}

	if params.CloudStorageAccessKey != nil {
		updates[backup.CloudStorageAccessKeyKey] = *params.CloudStorageAccessKey
	}

	if params.CloudStorageSecretKey != nil {
		updates[backup.CloudStorageSecretKeyKey] = *params.CloudStorageSecretKey
	}

	if params.CloudStorageRegion != nil {
		updates[backup.CloudStorageRegionKey] = *params.CloudStorageRegion
	}

	// 如果是云存储配置，先验证配置有效性
	if params.Type != nil && *params.Type == "cloud" {
		if err := validateCloudStorageConfig(params); err != nil {
			return response.FailWithMessage(ctx, fmt.Sprintf("云存储配置验证失败: %v", err))
		}
	}

	// 批量更新配置
	if len(updates) > 0 {
		configService := system_configs.NewSystemConfigs()
		if err := configService.SetMultiConfig(updates); err != nil {
			return response.FailWithMessage(ctx, fmt.Sprintf("更新配置失败: %v", err))
		}

		// 如果备份服务已启用，动态更新配置
		if backup.IsEnabled() {
			if err := backup.UpdateConfig(); err != nil {
				return response.FailWithMessage(ctx, fmt.Sprintf("动态更新配置失败: %v", err))
			}
		}
	}

	return response.Ok(ctx)
}

// ForceUnlock 强制解锁备份系统（紧急情况使用）
func ForceUnlock(ctx *gin.Context) error {
	// 这是一个危险操作，需要管理员权限
	// 在实际使用中可能需要额外的权限验证

	if err := backup.ForceUnlock(); err != nil {
		return response.FailWithMessage(ctx, fmt.Sprintf("强制解锁失败: %v", err))
	}

	return response.OkWithMessage(ctx, "备份系统已强制解锁")
}

// GetLockStatus 获取备份系统锁状态
func GetLockStatus(ctx *gin.Context) error {
	status := map[string]interface{}{
		"is_locked":     false,
		"lock_reason":   "",
		"running_tasks": []string{},
	}

	// if !backup.IsEnabled() {
	// 	status["lock_reason"] = "备份服务未启用"
	// 	return response.OkWithData(ctx, status)
	// }

	manager := backup.GetManager()
	if manager == nil {
		status["lock_reason"] = "备份管理器不可用"
		return response.OkWithData(ctx, status)
	}

	// 检查是否有运行中的任务
	filter := types.BackupFilter{
		TaskTypes: []types.TaskType{types.RestoreAllTask, types.RestoreTask},
		Statuses:  []types.TaskStatus{types.TaskStatusRunning, types.TaskStatusPending},
		Limit:     20,
	}

	result, err := manager.ListAllBackups(context.Background(), filter)
	if err != nil {
		status["lock_reason"] = fmt.Sprintf("查询任务状态失败: %v", err)
		return response.OkWithData(ctx, status)
	}

	runningTasks := []map[string]interface{}{}
	for _, task := range result.Tasks {
		runningTasks = append(runningTasks, map[string]interface{}{
			"task_id":    task.ID,
			"type":       string(task.Type),
			"source":     string(task.Source),
			"status":     string(task.Status),
			"progress":   task.Progress,
			"start_time": task.StartTime,
		})
	}

	if len(runningTasks) > 0 {
		status["is_locked"] = true
		status["lock_reason"] = "有恢复任务正在运行"
		status["running_tasks"] = runningTasks
	}

	return response.OkWithData(ctx, status)
}

// TestCloudStorageConfig 测试云存储配置连通性
func TestCloudStorageConfig(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &struct {
		CloudStorageType      string `json:"cloud_storage_type" validate:"required,oneof=s3 oss cos"` // 云存储类型
		CloudStorageEndpoint  string `json:"cloud_storage_endpoint" validate:"required,url"`          // 云存储端点
		CloudStorageBucket    string `json:"cloud_storage_bucket" validate:"required,min=1"`          // 存储桶名称
		CloudStorageAccessKey string `json:"cloud_storage_access_key" validate:"required,min=1"`      // 访问密钥
		CloudStorageSecretKey string `json:"cloud_storage_secret_key" validate:"required,min=1"`      // 密钥
		CloudStorageRegion    string `json:"cloud_storage_region"`                                    // 区域
	}{})
	if err != nil {
		return err
	}

	manager := backup.GetManager()
	if manager == nil {
		return response.FailWithMessage(ctx, "备份管理器不可用")
	}

	// 构建云存储配置
	cloudConfig := &types.CloudStorageConfig{
		Enabled:   true,
		Type:      params.CloudStorageType,
		Endpoint:  params.CloudStorageEndpoint,
		Bucket:    params.CloudStorageBucket,
		AccessKey: params.CloudStorageAccessKey,
		SecretKey: params.CloudStorageSecretKey,
		Region:    params.CloudStorageRegion,
	}

	// 测试云存储连通性
	testResult, err := manager.TestCloudStorageConnectivity(context.Background(), cloudConfig)
	if err != nil {
		return response.FailWithMessage(ctx, fmt.Sprintf("测试云存储配置失败: %v", err))
	}

	return response.OkWithData(ctx, testResult)
}

// validateCloudStorageConfig 验证云存储配置
func validateCloudStorageConfig(params *backupConfig) error {
	manager := backup.GetManager()
	if manager == nil {
		return fmt.Errorf("备份管理器不可用")
	}

	// 获取当前配置
	currentConfig, err := backup.GetBackupConfig()
	if err != nil {
		return fmt.Errorf("获取当前配置失败: %v", err)
	}

	// 构建要测试的云存储配置（合并当前配置和新配置）
	cloudConfig := &types.CloudStorageConfig{
		Enabled:   true,
		Type:      currentConfig.CloudStorageType,
		Endpoint:  currentConfig.CloudStorageEndpoint,
		Bucket:    currentConfig.CloudStorageBucket,
		AccessKey: currentConfig.CloudStorageAccessKey,
		SecretKey: currentConfig.CloudStorageSecretKey,
		Region:    currentConfig.CloudStorageRegion,
	}

	// 用新参数覆盖
	if params.CloudStorageType != nil {
		cloudConfig.Type = *params.CloudStorageType
	}
	if params.CloudStorageEndpoint != nil {
		cloudConfig.Endpoint = *params.CloudStorageEndpoint
	}
	if params.CloudStorageBucket != nil {
		cloudConfig.Bucket = *params.CloudStorageBucket
	}
	if params.CloudStorageAccessKey != nil {
		cloudConfig.AccessKey = *params.CloudStorageAccessKey
	}
	if params.CloudStorageSecretKey != nil {
		cloudConfig.SecretKey = *params.CloudStorageSecretKey
	}
	if params.CloudStorageRegion != nil {
		cloudConfig.Region = *params.CloudStorageRegion
	}

	// 检查必要字段是否完整
	if cloudConfig.Type == "" || cloudConfig.Endpoint == "" || cloudConfig.Bucket == "" ||
		cloudConfig.AccessKey == "" || cloudConfig.SecretKey == "" {
		return fmt.Errorf("云存储配置不完整，请确保所有必要字段都已填写")
	}

	// 测试云存储连通性
	testResult, err := manager.TestCloudStorageConnectivity(context.Background(), cloudConfig)
	if err != nil {
		return fmt.Errorf("云存储连通性测试失败: %v", err)
	}

	if !testResult.Success {
		return fmt.Errorf("云存储配置无效: %s", testResult.Error)
	}

	return nil
}

// buildGroupBackupRequest 构建分组备份请求
func buildGroupBackupRequest(backupType types.BackupType, description string, config *backup.BackupConfig) types.BackupAllRequest {
	var sources []types.BackupRequest

	// 默认添加MySQL备份
	sources = append(sources, types.BackupRequest{
		SourceType:  types.MySQL,
		SourceName:  cfg.LoadMysql().Database,
		BackupType:  backupType,
		Description: fmt.Sprintf("%s-MySQL", description),
	})

	// 默认添加ES备份
	sources = append(sources, types.BackupRequest{
		SourceType:  types.Elasticsearch,
		SourceName:  "main_cluster",
		BackupType:  backupType,
		Description: fmt.Sprintf("%s-ES", description),
	})

	return types.BackupAllRequest{
		Sources:          sources,
		Description:      description,
		Atomic:           true,
		CleanupOnFailure: true,
	}
}
