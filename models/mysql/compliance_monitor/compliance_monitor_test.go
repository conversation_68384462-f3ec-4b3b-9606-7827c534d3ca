package compliance_monitor

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
)

func TestCreate(t *testing.T) {

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 调用函数
	err := NewComplianceMonitorModel().Create(&ComplianceMonitor{
		UserId:    1,
		Name:      "test",
		Rule:      "test",
		AssetType: 1,
		Event:     1,
		Status:    1,
	})

	assert.NotNil(t, err)
	assert.Equal(t, err.Error(), "all expectations were already fulfilled, call to database transaction Begin was not expected")
}

func TestFirst(t *testing.T) {

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 调用函数
	c, err := NewComplianceMonitorModel().First()
	fmt.Println(c)
	assert.NotNil(t, err)
}

func TestGetIds(t *testing.T) {

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 调用函数
	c, err := NewComplianceMonitorModel().GetIds()
	fmt.Println(c)
	assert.NotNil(t, err)
}

func TestList(t *testing.T) {

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 调用函数
	c, d, err := NewComplianceMonitorModel().List(0, 0, mysql.WithWhere("id = ?", 1))
	fmt.Println(c)
	fmt.Println(d)
	assert.NotNil(t, err)
}

func TestUpdate(t *testing.T) {

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 调用函数
	err := NewComplianceMonitorModel().Update(ComplianceMonitor{
		UserId:    1,
		Name:      "test",
		Rule:      "test",
		AssetType: 1,
		Event:     1,
		Status:    1,
	}, mysql.WithWhere("id = ?", 1))
	assert.NotNil(t, err)
}

func TestCount(t *testing.T) {

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 调用函数
	count, err := NewComplianceMonitorModel().Count(mysql.WithWhere("id = ?", 1))
	fmt.Println(count)
	assert.NotNil(t, err)
}
