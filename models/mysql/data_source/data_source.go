package data_source

import (
	"database/sql/driver"
	"encoding/json"
	"strings"

	"fobrain/initialize/mysql"
)

type Source struct {
	mysql.BaseModel
	Name              string `json:"name"`
	EnName            string `json:"en_name"`
	Type              string `json:"type"`
	Desc              string `json:"desc"`
	Icon              string `json:"icon"`
	Version           string `json:"version"`
	Status            int    `json:"status"`
	Show              int    `json:"show"`
	Sort              int    `json:"sort"`
	IsTaskSync        bool   `json:"is_task_sync"`
	IsCronSync        bool   `json:"is_cron_sync"`
	HasAssetData      bool   `json:"has_asset_data"`
	HasVulData        bool   `json:"has_vul_data"`
	HasPersonnelData  bool   `json:"has_personnel_data"`
	HasRelations      bool   `json:"has_relations"`
	HasSync           int    `json:"has_sync"`
	CustomFieldConfig string `json:"custom_field_config"` //自定义字段配置
}

const (
	// FoeyeSourceId 数据源ID
	FoeyeSourceId           = 1
	ForadarSourceId         = 2
	QTCloudSourceId         = 3  //青藤云
	DingTalkSourceId        = 4  //钉钉
	BKCmdbSourceId          = 5  //蓝鲸CMDB
	FileImportSourceId      = 6  //文件
	HandworkSourceId        = 7  //手动添加
	AliYunCloudSourceId     = 12 //阿里云-云盾
	QiZhiUAuditHostSourceId = 11 //齐治-堡垒机

	WeiBuSourceId        = 8  //微步
	ChangTingWAFSourceId = 9  // 长亭WAF
	D01SourceId          = 10 // D01

	MachLakeSourceId              = 13 // 马赫谷
	XRaySourceId                  = 14 // x-ray
	BKCmdbCustomVmMachineSourceId = 15 // 蓝鲸CMDB自定义虚拟机
	BKCmdbCustomCloudEcsSourceId  = 16 // 蓝鲸CMDB自定义ECS
	HuaweiHkCloudSourceId         = 17 //华为云中移香港

	BKCmdbCustomDomainSourceId   = 18 // 蓝鲸CMDB_自定义域名
	BKCmdbCustomBusinessSourceId = 19 // 蓝鲸CMDB_自定义业务
	BKCmdbCustomF5VsSourceId     = 20 // 蓝鲸CMDB_自定义F5VS
	BKCmdbCustomF5PoolSourceId   = 21 // 蓝鲸CMDB_自定义F5POOL
	ZhongyiFeishuSourceId        = 22 // 中移香港飞书人员数据源
	YuntuSourceId                = 23 // 长亭云图
	UCloudSourceId               = 24 // ucloud
	RizhiyiSourceId              = 25 // 日志易
	AliyunSLBSourceId            = 26 // 阿里云负载均衡SLB

	PublicAliyunSlbSourceId  = 27 // 公有云-阿里云负载均衡
	PublicliyunEcsSourceId   = 28 // 公有云-阿里云ECS
	PublicTencentCVMSourceId = 29 // 公有云-腾讯云CVM
	PublicTencentCLBSourceId = 30 // 公有云-腾讯云负载均衡
	PublicHuaweiEcsSourceId  = 31 // 公有云-华为云ECS
	SangforSIPSourceId       = 32 // 深信服态势感知SIP
	QianxinServerSourceId    = 33 // 奇安信-统一服务器安全管理系统
	JumpServerSourceId       = 34 // JumpServer堡垒机
	WeibuOnesecSourceId      = 35 // 微步OneSec
	NSFocusRsasSourceId      = 36 // 绿盟-RSAS

	// HasSyncYes 是否支持同步数据：1 是 2 否(1为页面可同步数据，2为接口或者导入同步数据)
	HasSyncYes = 1
	HasSyncNo  = 2

	// StatusEnable 状态：1 启用 2 禁用
	StatusEnable  = 1
	StatusDisable = 2

	// ShowEnable 是否显示：1 显示 2 隐藏
	ShowEnable  = 1
	ShowDisable = 2

	TypeAliYunCloud = "aliyun_cloud"
	QiZhiUAuditHost = "qizhi_uaudithost"
)

const (
	// SourceTypeAsset 资产
	SourceTypeAsset = 1
	// SourceTypePoc 漏洞
	SourceTypePoc = 2
	// SourceTypeDevice 设备
	SourceTypeDevice = 3
	// SourceTypeInternetAsset 互联网资产
	SourceTypeInternetAsset = 4
	// SourceTypeIntranetAsset 内网资产
	SourceTypeIntranetAsset = 5
)

// TableName 表名
func (s *Source) TableName() string {
	return "data_sources"
}

func NewSourceModel() *Source {
	return &Source{BaseModel: mysql.BaseModel{DB: mysql.GetDbClient().Table((&Source{}).TableName())}}
}

func AllSources() ([]*Source, int64, error) {
	var list = make([]*Source, 0)
	err := mysql.GetDbClient().Table((&Source{}).TableName()).Find(&list).Error
	if err != nil {
		return nil, 0, err
	}
	return list, int64(len(list)), nil
}

type CustomFieldConfig struct {
	Field     string `json:"field"`
	Label     string `json:"label"`
	Component string `json:"component"`
}

// CustomFieldConfigs 自定义字段配置切片类型，实现 Scanner 和 Valuer 接口
type CustomFieldConfigs []CustomFieldConfig

// Scan 实现 Scanner 接口，用于从数据库读取数据
func (c *CustomFieldConfigs) Scan(value interface{}) error {
	if value == nil {
		*c = CustomFieldConfigs{}
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return nil
	}

	if len(bytes) == 0 {
		*c = CustomFieldConfigs{}
		return nil
	}

	return json.Unmarshal(bytes, c)
}

// Value 实现 Valuer 接口，用于向数据库写入数据
func (c CustomFieldConfigs) Value() (driver.Value, error) {
	if len(c) == 0 {
		return "[]", nil
	}
	return json.Marshal(c)
}

type SourceListResult struct {
	Source
	NodeNum            int                `json:"node_num"`
	TypeNames          string             `json:"type_names"`
	NodeFailNum        int                `json:"node_fail_num"`
	CustomFieldConfigs CustomFieldConfigs `json:"custom_field_config" gorm:"column:custom_field_config"`
}

type SourceListItem struct {
	Id   uint64 `gorm:"primaryKey;autoIncrement;comment:id" json:"id"`
	Name string `json:"name"`
	Icon string `json:"icon"`
	Type string `json:"type"`
	Md5  string `json:"md5"`
	Num  int    `json:"num"`
}

func (s *Source) First(opts ...mysql.HandleFunc) (Source, error) {
	query := s.DB.Table(s.TableName())
	for _, opt := range opts {
		opt(query)
	}
	var role Source
	err := query.First(&role).Error
	if err != nil {
		return Source{}, err
	}
	return role, nil
}

// SourceNames
// 根据 id 获取 source name
func (s *Source) SourceNames(ids []uint64) []*Source {
	var source []*Source
	mysql.GetDbClient().Table((&Source{}).TableName()).Where("id in (?)", ids).Find(&source)
	return source
}

func (s *Source) GetByIds(ids []string) ([]*Source, error) {
	var source []*Source
	err := s.DB.Table(s.TableName()).Where("id in (?)", ids).Find(&source).Error
	if err != nil {
		return nil, err
	}
	return source, nil
}
func (s *Source) ListOpt(page, size int, opts ...mysql.HandleFunc) ([]*Source, int64, error) {
	var list []*Source
	var total int64

	query := s.DB.Model(s.TableName())
	for _, opt := range opts {
		opt(query)
	}

	query.Count(&total)

	if total > 0 {
		if !mysql.IsPageAll(page, size) {
			query.Scopes(mysql.PageLimit(page, size))
		}
		query.Order("created_at desc").Scan(&list)
	}

	return list, total, nil
}

func (s *Source) List(page, size int, search string, typeId []int, hasNod, hasSync, hasData, isNode int) ([]SourceListResult, int64, error) {
	var list []SourceListResult
	var total int64
	query := s.DB.Model(&Source{}).Select("data_sources.*,GROUP_CONCAT(DISTINCT type.name) as type_names, COUNT(DISTINCT IF(node.status = 3, node.id, NULL)) AS node_num, COUNT(DISTINCT IF(node.status != 3, node.id, NULL)) AS node_fail_num").
		Joins("LEFT JOIN data_nodes as node ON data_sources.id = node.source_id and node.deleted_at is null").
		Joins("LEFT JOIN data_source_type_map as map ON map.source_id = data_sources.id").
		Joins("LEFT JOIN data_source_types as type ON type.id = map.source_type_id").
		Where("data_sources.show = 1")
	if len(typeId) > 0 {
		query = query.Where("type.id IN ?", typeId)
	}
	if hasSync == 1 {
		query = query.Where("data_sources.is_task_sync = ?", 1)
	}
	if hasSync == 2 {
		query = query.Where("data_sources.is_cron_sync = ?", 1)
	}
	if hasData == 1 {
		query = query.Where("data_sources.has_asset_data = ?", 1)
	}
	if hasData == 2 {
		query = query.Where("data_sources.has_vul_data = ?", 1)
	}
	if hasData == 3 {
		query = query.Where("data_sources.has_personnel_data = ?", 1)
	}
	if search != "" {
		search = strings.TrimSpace(search)
		query = query.Where("data_sources.name LIKE ?", "%"+search+"%")
	}
	query.Group("data_sources.id")
	if hasNod == 1 {
		query = query.Having("COUNT(DISTINCT case when node.status != 3 then node.id else null end) >0")
	}
	if hasNod == 2 {
		query = query.Having("COUNT(DISTINCT case when node.status != 3 then node.id else null end) =0")
	}
	if isNode == 1 {
		query = query.Where("node.source_id  >0 ")
	}
	query.Order("data_sources.sort asc")

	query.Count(&total)
	if total > 0 {
		query.Limit(size).Offset((page - 1) * size).Scan(&list)
	}
	return list, total, nil
}
func (s *Source) Update(item Source, opts ...mysql.HandleFunc) error {
	query := s.DB.Model(&Source{})
	for _, f := range opts {
		f(query)
	}

	return query.Where("`id` = ?", item.Id).
		Select("name", "en_name", "type", "desc", "icon", "version", "status", "show", "sort", "is_task_sync", "is_cron_sync", "has_asset_data", "has_vul_data", "has_personnel_data", "has_sync", "custom_field_config").
		Updates(item).Error
}

func (s *Source) ItemByTask() ([]SourceListItem, error) {
	var list []SourceListItem
	query := s.DB.Model(&Source{}).Select("data_sources.id,data_sources.icon,data_sources.name,'source' as type,COUNT(DISTINCT task.id) AS num").
		Joins("LEFT JOIN data_nodes as node ON data_sources.id = node.source_id and node.deleted_at is null").
		Joins("LEFT JOIN data_sync_tasks as task ON data_sources.id = task.source_id")
	query.Group("data_sources.id")
	query.Having("COUNT(DISTINCT task.id) >0")
	query.Scan(&list)
	return list, nil
}
func (s *Source) Item(opts ...mysql.HandleFunc) ([]Source, error) {
	query := s.DB.Table(s.TableName())
	for _, opt := range opts {
		opt(query)
	}
	var list []Source
	err := query.Scan(&list).Error
	if err != nil {
		return list, err
	}
	return list, nil
}

func (s *Source) Items(page, size int, opts ...mysql.HandleFunc) ([]*Source, int64, error) {
	query := s.Table(s.TableName())
	for _, opt := range opts {
		opt(query)
	}

	var total int64
	if !mysql.IsPageAll(page, size) {
		if err := query.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		query.Scopes(mysql.PageLimit(page, size))
	}

	var list = make([]*Source, 0)
	if err := query.Find(&list).Error; err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

func (s *Source) Count(opts ...mysql.HandleFunc) (int64, error) {
	query := s.DB.Table(s.TableName())
	for _, opt := range opts {
		opt(query)
	}
	var total int64
	query.Count(&total)

	return total, nil
}

func (s *Source) Create(item *Source) error {
	return s.DB.Model(&Source{}).Create(item).Error
}

// TaskSyncSource
// @Summary 需要主动扫描的数据源
func (s *Source) TaskSyncSource() (any, error) {
	var source []*struct {
		Name string `json:"name"`
		Id   uint64 `json:"id"`
	}

	sourceIds, err := NewNodeModel().GetExistingSourceIds()
	if err != nil {
		return map[string]any{}, err
	}

	validSourceIds := []uint64{
		FoeyeSourceId,
		D01SourceId,
		XRaySourceId,
		NSFocusRsasSourceId,
	}

	validIds := make([]uint64, 0)
	for _, id := range sourceIds {
		for _, validId := range validSourceIds {
			if id == validId {
				validIds = append(validIds, validId)
				break
			}
		}
	}

	if err = s.DB.Table(s.TableName()).Where("id in ?", validIds).Find(&source).Error; err != nil {
		return map[string]any{}, err
	}
	return source, nil
}
