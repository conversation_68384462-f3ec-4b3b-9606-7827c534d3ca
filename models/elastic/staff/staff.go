package staff

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"

	"go-micro.dev/v4/logger"

	"fobrain/models/mysql/data_source"

	"github.com/olivere/elastic/v7"

	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/redis"
	redis_helper "fobrain/models/redis"
	"fobrain/pkg/utils"

	goRedis "github.com/go-redis/redis/v8"
)

type (
	Staff struct {
		*es.BaseModel        `json:"-"`
		Id                   string            `json:"id"`                      // 人名
		SsoId                string            `json:"sso_id"`                  // 单点登录ID
		SsoName              string            `json:"sso_name"`                // 单点登录Name
		Fid                  string            `json:"fid"`                     // 人员唯一标识，来自process_staff.unique_key
		FidHash              string            `json:"fid_hash"`                // 人员唯一标识的hash
		OriginalIds          []string          `json:"original_ids"`            // 原始ids-【补充】
		OriginalIdsSource    map[string]string `json:"original_ids_source"`     // 原始ids-数据源映射-【补充】
		Name                 string            `json:"name"`                    // 姓名
		EnglishName          []string          `json:"english_name"`            // 英文名
		EnglishNameSource    map[string]string `json:"english_name_source"`     // 英文名来源
		Area                 int               `json:"area"`                    // 区域
		AreaSource           map[string]int    `json:"area_source"`             // 区域来源
		Title                []string          `json:"title"`                   // 职位
		TitleSource          map[string]string `json:"title_source"`            // 职位来源
		Mobile               string            `json:"mobile"`                  // 手机号
		Email                []string          `json:"email"`                   // 邮箱
		EmailSource          map[string]string `json:"email_source"`            // 邮箱来源
		Department           []string          `json:"department"`              // 部门
		DepartmentSource     map[string]string `json:"department_source"`       // 部门来源
		Status               int               `json:"status"`                  // 状态 1：在职；2：离职
		StatusSource         map[string]int    `json:"status_source"`           // 状态来源
		WorkNumber           string            `json:"work_number"`             // 工号
		WorkNumberSource     map[string]string `json:"work_number_source"`      // 工号来源
		ProcessIds           []string          `json:"process_ids"`             // 过程表ID
		DepartmentsIds       []string          `json:"departments_ids"`         // 部门ids
		AllDepartmentsIds    []string          `json:"all_departments_ids"`     // 全部门ids
		SourceIds            []uint64          `json:"source_ids"`              // 数据源ids-【补充】
		NodeIds              []uint64          `json:"node_ids"`                // 节点ids-【补充】
		TaskDataIds          []string          `json:"staff_task_ids"`          // 人员任务ids-【补充】
		AllSourceIds         []uint64          `json:"all_source_ids"`          // 所有数据源ids-【补充】
		AllNodeIds           []uint64          `json:"all_node_ids"`            // 所有节点ids-【补充】
		AllTaskDataIds       []string          `json:"all_staff_task_ids"`      // 所有任务ids-【补充】
		AllProcessIds        []string          `json:"all_process_ids"`         // 所有过程ids-【补充】
		CreatedAt            *localtime.Time   `json:"created_at"`              // 创建时间
		UpdatedAt            *localtime.Time   `json:"updated_at"`              // 更新时间
		MergeCount           int               `json:"merge_count"`             // 融合次数
		CustomFields         map[string]string `json:"custom_fields"`           // 自定义字段
		DataSourceResponseAt *localtime.Time   `json:"data_source_response_at"` // 数据源最后一次响应时间，来自数据源更新动作，属于审计属性
	}

	deleteStaff struct {
		Id            string   `json:"id"`
		Department    []string `json:"department"`
		DepartmentIds []string `json:"department_ids"`
	}
)

func (p *Staff) AppendSourceIds(ids ...uint64) {
	if len(p.SourceIds) == 0 {
		p.SourceIds = make([]uint64, 0)
	}
	p.SourceIds = append(p.SourceIds, ids...)
	p.SourceIds = utils.ListDistinct(p.SourceIds)
}

func (p *Staff) AppendNodeIds(ids ...uint64) {
	if len(p.NodeIds) == 0 {
		p.NodeIds = make([]uint64, 0)
	}
	p.NodeIds = append(p.NodeIds, ids...)
	p.NodeIds = utils.ListDistinct(p.NodeIds)
}

func (p *Staff) AppendTaskDataIds(ids ...string) {
	if len(p.TaskDataIds) == 0 {
		p.TaskDataIds = make([]string, 0)
	}
	p.TaskDataIds = append(p.TaskDataIds, ids...)
	p.TaskDataIds = utils.ListDistinct(p.TaskDataIds)
}

func (p *Staff) AppendProcessIds(ids ...string) {
	if len(p.ProcessIds) == 0 {
		p.ProcessIds = make([]string, 0)
	}
	p.ProcessIds = append(p.ProcessIds, ids...)
	p.ProcessIds = utils.ListDistinct(p.ProcessIds)
}

func (p *Staff) SampleHash() map[string]interface{} {
	return map[string]interface{}{
		"id":                      p.Id,
		"fid":                     p.Fid,
		"fid_hash":                p.FidHash,
		"name":                    p.Name,
		"english_name":            p.EnglishName,
		"title":                   p.Title,
		"mobile":                  p.Mobile,
		"email":                   p.Email,
		"created_at":              p.CreatedAt,
		"updated_at":              p.UpdatedAt,
		"data_source_response_at": p.DataSourceResponseAt,
		"status":                  p.StatusDesc(),
		"department":              p.Department,
		"work_number":             p.WorkNumber,
		"source_names":            data_source.NewSourceModel().SourceNames(p.AllSourceIds),
		"custom_fields":           p.CustomFields,
	}
}

func (p *Staff) StatusDesc() string {
	switch p.Status {
	case 2:
		return "离职"
	default:
		return "在职"
	}
}

// IndexName 索引名
func (p *Staff) IndexName() string {
	return "staff"
}

func (p *Staff) String() string {
	if p == nil {
		return ""
	}
	return ""
}

func NewStaff() *Staff {
	return &Staff{}
}

func (p *Staff) NewKeywordQuery(keyword string, boolQuery *elastic.BoolQuery) *elastic.BoolQuery {

	escapedKeyword := fmt.Sprintf("*%s*", utils.Escape(keyword))

	// Add Wildcard queries for each field using the BoolQuery's Should clause
	boolQuery.Should(
		elastic.NewWildcardQuery("title", escapedKeyword).CaseInsensitive(true),
		elastic.NewWildcardQuery("department", escapedKeyword).CaseInsensitive(true),
		elastic.NewWildcardQuery("email", escapedKeyword).CaseInsensitive(true),
		elastic.NewWildcardQuery("name", escapedKeyword).CaseInsensitive(true),
		elastic.NewWildcardQuery("english_name", escapedKeyword).CaseInsensitive(true),
		elastic.NewWildcardQuery("mobile", escapedKeyword).CaseInsensitive(true),
		elastic.NewWildcardQuery("work_number", escapedKeyword).CaseInsensitive(true),
	).MinimumShouldMatch("1")
	return boolQuery
}

func (p *Staff) FindByWorkNumber(workNumber string, _source ...interface{}) ([]Staff, error) {
	q := elastic.NewBoolQuery()
	q = q.Must(elastic.NewTermQuery("work_number", workNumber))
	req := p.GetClient().Search().Index(p.IndexName()).Query(q)
	if len(_source) > 0 {
		req.Source(_source)
	}
	result, err := req.Do(context.Background())
	if err != nil {
		logs.GetLogger().Warnf("FindByWorkNumber staff error error: %v,", err)
		return []Staff{}, err
	}

	if result.Hits.TotalHits.Value == 0 {
		logs.GetLogger().Warnf("FindByWorkNumber not found.")
		return []Staff{}, nil
	}

	staffs := make([]Staff, 0)
	for _, hit := range result.Hits.Hits {
		item, err := convertToStaffModel(hit)
		if err != nil {
			return []Staff{}, err
		}

		staffs = append(staffs, *item)
	}

	return staffs, nil
}

// convertToStaffModel ES查询结果转换为Staff结构体
func convertToStaffModel(hit *elastic.SearchHit) (*Staff, error) {
	item := NewStaff()
	err := json.Unmarshal(hit.Source, item)
	if err != nil {
		return nil, err
	}
	return item, nil
}

func (p *Staff) First(ctx context.Context, name, mobile string) (*Staff, error) {
	q := elastic.NewBoolQuery()
	q = q.Must(elastic.NewTermQuery("name", name), elastic.NewTermQuery("mobile", mobile))
	searchResult, err := p.GetClient().Search().Index(p.IndexName()).Size(1).Query(q).Do(ctx)
	if err != nil {
		logs.GetLogger().Warnf("search staff error. Name:%s, Mobile:%s, error: %v,", name, mobile, err)
		return nil, errors.New("search error")
	}
	if searchResult.Hits.TotalHits.Value == 0 {
		logs.GetLogger().Warnf("person not found. Name:%s, Mobile:%s", name, mobile)
		return nil, nil
	}
	item, err := convertToStaffModel(searchResult.Hits.Hits[0])
	return item, err
}

func (p *Staff) GetByName(ctx context.Context, name string) (*Staff, error) {
	name = strings.TrimSpace(name)
	q := elastic.NewBoolQuery()
	q = q.Must(elastic.NewTermQuery("name", name))
	searchResult, err := p.GetClient().Search().Index(p.IndexName()).Size(1).Query(q).Do(ctx)
	if err != nil {
		return nil, err
	}
	if searchResult.Hits.TotalHits.Value == 0 {
		logs.GetLogger().Warnf("person not found. Name:%s,", name)
		return nil, nil
	}
	item, err := convertToStaffModel(searchResult.Hits.Hits[0])
	return item, err
}

func (p *Staff) GetByNames(ctx context.Context, names []string) ([]*Staff, error) {
	var result []*Staff
	mapIds := make(map[string]struct{})
	redisKey := redis_helper.StaffKey("name")
	redisClient := redis.GetRedisClient()
	for _, name := range names {
		ids, err := redisClient.HGet(context.Background(), redisKey, name).Result()
		if err != nil && !errors.Is(err, goRedis.Nil) {
			return nil, errors.Wrap(err, "get staff error")
		}
		if ids != "" {
			idArr := strings.Split(ids, ",")
			for _, id := range idArr {
				staffModel, err := p.GetStaffFromRedisById(ctx, id)
				if err != nil {
					return nil, errors.Wrap(err, "get staff error")
				}
				if staffModel != nil {
					if _, e := mapIds[id]; !e {
						mapIds[id] = struct{}{}
						result = append(result, staffModel)
					}
				}
			}
		}
	}

	return result, nil
}

func (p *Staff) GetByEnglishNames(ctx context.Context, names []string) ([]*Staff, error) {
	var result []*Staff
	mapIds := make(map[string]struct{})
	redisKey := redis_helper.StaffKey("english_name")
	redisClient := redis.GetRedisClient()

	for _, name := range names {
		ids, err := redisClient.HGet(context.Background(), redisKey, name).Result()
		if err != nil && !errors.Is(err, goRedis.Nil) {
			return nil, errors.Wrap(err, "get staff error")
		}
		if ids != "" {
			idArr := strings.Split(ids, ",")
			for _, id := range idArr {
				staffModel, err := p.GetStaffFromRedisById(ctx, id)
				if err != nil && !errors.Is(err, goRedis.Nil) {
					return nil, errors.Wrap(err, "get staff error")
				}
				if staffModel != nil {
					if _, e := mapIds[id]; !e {
						mapIds[id] = struct{}{}
						result = append(result, staffModel)
					}
				}
			}
		}
	}

	return result, nil
}

func (p *Staff) GetByEmails(ctx context.Context, emails []string) ([]*Staff, error) {
	redisKey := redis_helper.StaffKey("email")
	redisClient := redis.GetRedisClient()

	var result []*Staff
	mapIds := make(map[string]struct{})
	for _, email := range emails {
		ids, err := redisClient.HGet(context.Background(), redisKey, email).Result()
		if err != nil && !errors.Is(err, goRedis.Nil) {
			return nil, errors.Wrap(err, "get staff error")
		}

		if ids != "" {
			idArr := strings.Split(ids, ",")
			for _, id := range idArr {
				staffModel, err := p.GetStaffFromRedisById(ctx, id)
				if err != nil && !errors.Is(err, goRedis.Nil) {
					return nil, errors.Wrap(err, "get staff error")
				}
				if staffModel != nil {
					if _, e := mapIds[id]; !e {
						mapIds[id] = struct{}{}
						result = append(result, staffModel)
					}
				}
			}
		}
	}

	return result, nil
}

func (p *Staff) GetByPhones(ctx context.Context, phones []string) ([]*Staff, error) {
	redisKey := redis_helper.StaffKey("mobile")
	redisClient := redis.GetRedisClient()
	var result []*Staff
	mapIds := make(map[string]struct{})
	for _, phone := range phones {
		id, err := redisClient.HGet(context.Background(), redisKey, phone).Result()
		if err != nil && !errors.Is(err, goRedis.Nil) {
			return nil, errors.Wrap(err, "get staff error")
		}
		if id != "" {
			staffModel, err := p.GetStaffFromRedisById(ctx, id)
			if err != nil && !errors.Is(err, goRedis.Nil) {
				return nil, errors.Wrap(err, "get staff error")
			}
			if staffModel != nil {
				if _, e := mapIds[id]; !e {
					mapIds[id] = struct{}{}
					result = append(result, staffModel)
				}
			}
		}
	}

	return result, nil
}

func (p *Staff) GetByWorkNumbers(ctx context.Context, workNumbers []string) ([]*Staff, error) {
	var result []*Staff
	mapIds := make(map[string]struct{})
	redisKey := redis_helper.StaffKey("work_number")
	redisClient := redis.GetRedisClient()
	for _, workNumber := range workNumbers {
		ids, err := redisClient.HGet(context.Background(), redisKey, workNumber).Result()
		if err != nil && !errors.Is(err, goRedis.Nil) {
			return nil, errors.Wrap(err, "get staff error")
		}
		if ids != "" {
			idArr := strings.Split(ids, ",")
			for _, id := range idArr {
				staffModel, err := p.GetStaffFromRedisById(ctx, id)
				if err != nil && !errors.Is(err, goRedis.Nil) {
					return nil, errors.Wrap(err, "get staff error")
				}
				if staffModel != nil {
					if _, e := mapIds[id]; !e {
						mapIds[id] = struct{}{}
						result = append(result, staffModel)
					}
				}
			}
		}
	}

	return result, nil
}

func (p *Staff) GetById(ctx context.Context, id string) (*Staff, error) {
	q := elastic.NewBoolQuery()
	q = q.Must(elastic.NewTermQuery("id", id))
	searchResult, err := p.GetClient().Search().Index(p.IndexName()).Size(1).Query(q).Do(ctx)
	if err != nil {
		logs.GetLogger().Warnf("search staff error. Ids:%s, error: %v,", id, err)
		return nil, errors.New("search error")
	}
	if searchResult.Hits.TotalHits.Value == 0 {
		logs.GetLogger().Debugf("person not found. Ids:%s", id)
		return nil, nil
	}
	item, err := convertToStaffModel(searchResult.Hits.Hits[0])
	return item, err
}

func (p *Staff) GetByFId(ctx context.Context, id string) (*Staff, error) {
	q := elastic.NewBoolQuery()
	q = q.Must(elastic.NewTermQuery("fid", id))
	searchResult, err := p.GetClient().Search().Index(p.IndexName()).Size(1).Query(q).Do(ctx)
	if err != nil {
		logs.GetLogger().Warnf("search staff error. Ids:%s, error: %v,", id, err)
		return nil, errors.New("search error")
	}
	if searchResult.Hits.TotalHits.Value == 0 {
		logs.GetLogger().Debugf("person not found. Ids:%s", id)
		return nil, nil
	}
	item, err := convertToStaffModel(searchResult.Hits.Hits[0])
	return item, err
}

func (p *Staff) GetByFIds(ctx context.Context, ids []string) ([]*Staff, error) {
	q := elastic.NewBoolQuery()
	ids = utils.CompactStrings(ids)
	q = q.Should(
		elastic.NewTermsQueryFromStrings("fid_hash", ids...),
		elastic.NewTermsQueryFromStrings("fid", ids...),
	)
	searchResult, err := p.GetClient().Search().Size(1000).Index(p.IndexName()).Query(q).Do(ctx)
	if err != nil {
		logs.GetLogger().Warnf("search staff error. Ids:%s, error: %v,", ids, err)
		return nil, errors.New("search error")
	}
	if searchResult.Hits.TotalHits.Value == 0 {
		logs.GetLogger().Debugf("person not found. Ids:%s", ids)
		return nil, nil
	}
	items, err := ConvertToStaffsModel(searchResult.Hits.Hits)
	return items, err
}

// ConvertToStaffsModel ES查询结果转换为Staff结构体
func ConvertToStaffsModel(hits []*elastic.SearchHit) ([]*Staff, error) {
	var items []*Staff
	for _, hit := range hits {
		item := NewStaff()
		err := json.Unmarshal(hit.Source, item)
		if err != nil {
			return nil, err
		}
		items = append(items, item)
	}
	return items, nil
}

// AggPersonnelDepartmentsList 聚合统计人员台账列表中的数据
func (p *Staff) AggPersonnelDepartmentsList(ctx context.Context) (map[string]int64, error) {
	department := make(map[string]int64)
	departmentAggr := elastic.NewTermsAggregation().Field("all_departments_ids").Size(100000).ShardSize(100000)
	searchResult, err := p.GetClient().Search().Index(p.IndexName()).
		Aggregation("all_departments_ids", departmentAggr).
		Do(ctx)
	if err != nil {
		logger.Errorf("search staff error. Department:%s, error: %v,", p.Department, err)
		return nil, err
	}

	if agg, found := searchResult.Aggregations.Terms("all_departments_ids"); found {
		for _, bucket := range agg.Buckets {
			keyStr, ok := bucket.Key.(string)
			if !ok {
				logger.Errorf("bucket key is not string, %v", bucket.Key)
				continue
			}

			department[keyStr] = bucket.DocCount
		}
	}
	return department, nil
}

// CountStaffDepartments 统计部门中的人员数量
func (p *Staff) CountStaffDepartments(ctx context.Context, ids []interface{}) (int64, string, error) {
	var total int64
	content := make([]string, 0)
	q := elastic.NewBoolQuery()
	if len(ids) > 0 {
		q = q.Must(elastic.NewTermsQuery("departments_ids", ids...))
	}

	departmentAggr := elastic.NewTermsAggregation().Field("department").Size(100000).ShardSize(100000)
	searchResult, err := p.GetClient().Search().Index(p.IndexName()).Query(q).
		Aggregation("department", departmentAggr).
		Do(ctx)
	if err != nil {
		logger.Errorf("search staff error. Department:%s, error: %v,", p.Department, err)
		return 0, "", err
	}

	if agg, found := searchResult.Aggregations.Terms("department"); found {
		for _, bucket := range agg.Buckets {
			keyStr, ok := bucket.Key.(string)
			if !ok {
				logger.Errorf("bucket key is not string, %v", bucket.Key)
				continue
			}
			content = append(content, keyStr)
			total += bucket.DocCount
		}
	}
	if len(content) > 10 {
		content = content[:10]
	}
	return total, strings.Join(content, ","), nil

}

func (p *Staff) DeleteByDepartments(departments []string, ids []uint64) {
	// 删除所有部门
	if len(departments) == 0 || len(ids) == 0 {
		script := elastic.NewScript(`ctx._source.department = params.emptyArray; ctx._source.departments_ids = params.emptyArray; ctx._source.all_departments_ids = params.emptyArray`).
			Param("emptyArray", []string{})
		_, err := p.GetClient().UpdateByQuery().
			Index(p.IndexName()).
			Script(script).
			Do(context.Background())
		if err != nil {
			return
		}
	}

	// 根据条件更新
	departmentsInterface := make([]interface{}, 0)
	idsInterface := make([]interface{}, 0)
	idsStr := make([]string, 0)
	for _, department := range departments {
		departmentsInterface = append(departmentsInterface, department)
	}
	for _, id := range ids {
		idsInterface = append(idsInterface, id)
		idsStr = append(idsStr, strconv.FormatUint(id, 10))
	}
	q := elastic.NewBoolQuery()
	q = q.Must(elastic.NewTermsQuery("department", departmentsInterface...), elastic.NewTermsQuery("departments_ids", idsInterface...))
	searchResult, err := p.GetClient().Search().Index(p.IndexName()).Query(q).Do(context.Background())
	if err != nil {
		return
	}

	if len(searchResult.Hits.Hits) == 0 {
		return
	}
	deleteStaffData := make([]deleteStaff, 0)
	for _, hit := range searchResult.Hits.Hits {
		record := Staff{}
		if err = json.Unmarshal(hit.Source, &record); err != nil {
			return
		}
		// 部门名称差集
		d := difference(record.Department, departments)
		dIds := difference(record.DepartmentsIds, idsStr)
		deleteStaffData = append(deleteStaffData, deleteStaff{
			Id:            record.Id,
			Department:    d,
			DepartmentIds: dIds,
		})
	}

	batchSize := 1000
	for i := 0; i < len(deleteStaffData); i += batchSize {
		end := i + batchSize
		if end > len(deleteStaffData) {
			end = len(deleteStaffData)
		}
		bulkRequest := p.GetClient().Bulk().Refresh("true")
		for _, info := range deleteStaffData[i:end] {
			doc := map[string]interface{}{"department": info.Department, "departments_ids": info.DepartmentIds, "all_departments_ids": info.DepartmentIds}
			req := elastic.NewBulkUpdateRequest().Index(p.IndexName()).Id(info.Id).Doc(doc)
			bulkRequest.Add(req)
		}
		if _, err = bulkRequest.Do(context.Background()); err != nil {
			return
		}
	}
}
func (p *Staff) CacheAllStaff(force bool) error {
	q := &elastic.MatchAllQuery{}
	rows, err := es.All[Staff](1000, q, []elastic.Sorter{})
	if err != nil {
		return errors.Wrap(err, "cache all staff error")
	}
	// 先清空之前的数据
	allStaff := make(map[string]string)
	mapWorkNumber2ID := make(map[string]string)
	mapMobile2ID := make(map[string]string)
	mapFID2ID := make(map[string]string)
	mapEmail2ID := make(map[string][]string)
	mapName2ID := make(map[string][]string)
	mapEnglishName2ID := make(map[string][]string)

	for _, staff := range rows {
		encodedStaff, err := json.Marshal(staff)
		if err != nil {
			return errors.Wrap(err, "cache all staff error")
		}
		allStaff[staff.Id] = string(encodedStaff)
		for _, email := range staff.Email {
			if _, ok := mapEmail2ID[email]; !ok {
				mapEmail2ID[email] = make([]string, 0)
			}
			mapEmail2ID[email] = append(mapEmail2ID[email], staff.Id)
		}
		mapMobile2ID[staff.Mobile] = staff.Id
		mapWorkNumber2ID[staff.WorkNumber] = staff.Id
		mapFID2ID[staff.Fid] = staff.Id
		mapFID2ID[staff.FidHash] = staff.Id
		if _, ok := mapName2ID[staff.Name]; !ok {
			mapName2ID[staff.Name] = make([]string, 0)
		}
		mapName2ID[staff.Name] = append(mapName2ID[staff.Name], staff.Id)
		for _, en := range staff.EnglishName {
			if _, ok := mapEnglishName2ID[en]; !ok {
				mapEnglishName2ID[en] = make([]string, 0)
			}
			mapEnglishName2ID[en] = append(mapEnglishName2ID[en], staff.Id)
		}
	}
	redisClient := redis.GetRedisClient()
	redisKey := redis_helper.StaffKey("all")
	redisClient.Del(context.Background(), redisKey)
	redisClient.HSet(context.Background(), redisKey, allStaff)
	redisKeyWorkNumber := redis_helper.StaffKey("work_number")
	redisClient.Del(context.Background(), redisKeyWorkNumber)
	redisClient.HSet(context.Background(), redisKeyWorkNumber, mapWorkNumber2ID)
	redisKeyMobile := redis_helper.StaffKey("mobile")
	redisClient.Del(context.Background(), redisKeyMobile)
	redisClient.HSet(context.Background(), redisKeyMobile, mapMobile2ID)
	redisKeyFID := redis_helper.StaffKey("fid")
	redisClient.Del(context.Background(), redisKeyFID)
	redisClient.HSet(context.Background(), redisKeyFID, mapFID2ID)
	emailMap := make(map[string]string)
	for k, v := range mapEmail2ID {
		emailMap[k] = strings.Join(v, ",")
	}
	redisKeyEmail := redis_helper.StaffKey("email")
	redisClient.Del(context.Background(), redisKeyEmail)
	redisClient.HSet(context.Background(), redisKeyEmail, emailMap)
	redisKeyName := redis_helper.StaffKey("name")
	nameMap := make(map[string]string)
	for k, v := range mapName2ID {
		nameMap[k] = strings.Join(v, ",")
	}
	redisClient.Del(context.Background(), redisKeyName)
	redisClient.HSet(context.Background(), redisKeyName, nameMap)
	redisKeyEnglishName := redis_helper.StaffKey("english_name")
	englishNameMap := make(map[string]string)
	for k, v := range mapEnglishName2ID {
		englishNameMap[k] = strings.Join(v, ",")
	}
	redisClient.Del(context.Background(), redisKeyEnglishName)
	redisClient.HSet(context.Background(), redisKeyEnglishName, englishNameMap)
	return nil
}
func (p *Staff) GetByFIDsFromCache(ctx context.Context, ids []string) ([]*Staff, error) {
	var result []*Staff
	mapIds := make(map[string]struct{})
	redisClient := redis.GetRedisClient()
	redisKeyFID := redis_helper.StaffKey("fid")

	for _, id := range ids {
		staffModel, err := p.GetStaffFromRedisById(ctx, id)
		if err != nil && !errors.Is(err, goRedis.Nil) {
			return nil, errors.Wrap(err, "get staff error")
		}
		if staffModel != nil {
			if _, e := mapIds[id]; !e {
				mapIds[id] = struct{}{}
				result = append(result, staffModel)
			}
			continue
		}
		mapId, err := redisClient.HGet(context.Background(), redisKeyFID, id).Result()
		if err != nil && !errors.Is(err, goRedis.Nil) {
			return nil, errors.Wrap(err, "get staff error")
		}
		if mapId != "" {
			staffModel, err := p.GetStaffFromRedisById(ctx, mapId)
			if err != nil && !errors.Is(err, goRedis.Nil) {
				return nil, errors.Wrap(err, "get staff error")
			}
			if staffModel != nil {
				if _, e := mapIds[id]; !e {
					mapIds[id] = struct{}{}
					result = append(result, staffModel)
				}
			}
		}
	}

	return result, nil
}
func (p *Staff) GetStaffFromRedisById(ctx context.Context, id string) (*Staff, error) {
	redisClient := redis.GetRedisClient()
	redisKey := redis_helper.StaffKey("all")
	staff, err := redisClient.HGet(context.Background(), redisKey, id).Result()
	if err != nil && !errors.Is(err, goRedis.Nil) {
		return nil, errors.Wrap(err, "get staff error")
	}
	if staff != "" {
		staffModel := &Staff{}
		err = json.Unmarshal([]byte(staff), staffModel)
		if err != nil {
			return nil, errors.Wrap(err, "decode staff error")
		}
		return staffModel, nil
	}
	return nil, nil
}
func difference(source, data []string) []string {
	diffMap := make(map[string]bool)
	for _, item := range data {
		diffMap[item] = true
	}
	result := make([]string, 0)
	for _, item := range source {
		if !diffMap[item] {
			result = append(result, item)
		}
	}
	return result
}

// UpdateStatusByDataSourceResponseAt 更新资产状态为离线
// 如果数据源最后一次响应时间小于给定阈值，则更新资产状态为离线
// 同时更新updated_at字段为当前时间
func (p *Staff) UpdateStatusByDataSourceResponseAt(ctx context.Context, threshold time.Duration) (int64, error) {
	now := time.Now()
	// 截止时间
	cutoffTime := now.Add(-threshold)
	// 范围查询
	rangeQuery := elastic.NewRangeQuery("data_source_response_at").Lt(cutoffTime.Format("2006-01-02 15:04:05"))

	scriptSource := "ctx._source.status = params.offline_status; ctx._source.updated_at = params.current_time_str;"

	scriptParams := map[string]interface{}{
		"offline_status":   2, // 2 离职
		"current_time_str": now.Format("2006-01-02 15:04:05"),
	}
	script := elastic.NewScript(scriptSource).Params(scriptParams)

	updateByQueryService := p.GetClient().UpdateByQuery(p.IndexName()).
		Query(rangeQuery).
		Script(script).
		Slices("auto").      // 批次大小
		Conflicts("proceed") // 冲突处理方式

	resp, err := updateByQueryService.Do(ctx)
	if err != nil {
		logs.GetLogger().Errorf("更新人员状态失败: %v", err)
		return 0, err
	}

	if resp == nil {
		logs.GetLogger().Warnf("更新人员状态失败: UpdateByQuery返回空响应")
		return 0, errors.New("更新人员状态失败: UpdateByQuery返回空响应")
	}

	logs.GetLogger().Infof("更新人员状态成功: 更新数据量：%d次，截止时间：%s", resp.Updated, cutoffTime.Format(time.RFC3339))
	return resp.Updated, nil
}
