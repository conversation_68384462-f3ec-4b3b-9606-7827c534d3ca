package crontab

import (
	"fobrain/fobrain/app/repository/proactive_task/source_task"
	"fobrain/fobrain/app/repository/task_center/proactive_scan"
	"fobrain/fobrain/app/services/node/foeye"
	"fobrain/fobrain/logs"
	"fobrain/initialize/mysql"
	mcron "fobrain/models/mysql/crontab"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/system_configs"
	"fobrain/models/mysql/task"
	"strconv"

	"github.com/tidwall/gjson"
)

const (
	FoeyeTaskStateWait    = 0 // 待执行
	FoeyeTaskStateRunning = 1 // 执行中
	FoeyeTaskStateStop    = 2 // 暂停
	FoeyeTaskStateFin     = 3 // 完成
	FoeyeTaskStateAutStop = 4 // 自动暂停
)

func (j *Job) MonitorProactiveTasks(_ *Cron, _ *mcron.CrontabModel) string {
	j.GetStatus()
	return j.Immediately()
}

// Immediately
// @Summary 需要立即执行的任务且并未开始执行的
func (j *Job) Immediately() string {
	conf, err := system_configs.NewSystemConfigs().GetScanBanConfig()
	if err != nil {
		logs.GetLogger().Errorf("MonitorProactiveTasks GetScanBanConfig错误 err:%v", err.Error())
		return err.Error()
	}

	if system_configs.NewSystemConfigs().IsBanTime(conf) {
		logs.GetLogger().Info("禁扫中，跳过下发")
		return "禁扫中，跳过下发"
	}

	tasks := make([]*task.ProactiveTasks, 0)
	query := task.NewProactiveTasks().Where("status = ?", -1)
	query = query.Where("scan_plan = ?", 1)
	query.Find(&tasks)

	for _, t := range tasks {
		info := source_task.New(t).ImmediateTask()
		logs.GetLogger().Info(info)
	}

	return "下发成功"
}

func (j *Job) GetStatus() {
	tasks := make([]task.ProactiveTasks, 0)
	query := task.NewProactiveTasks().Where("status in ?", []int{1, 2}) // 仅查询扫描中的任务信息
	query = query.Where("scan_plan = ?", 1)
	query.Find(&tasks) // 查找 正在扫描中的任务
	// 更新所有节点任务的进度
	for _, t := range tasks {
		errStr := source_task.New(&t).SyncStatus()
		if errStr != "" && errStr != "状态同步成功" {
			logs.GetLogger().Errorf("MonitorProactiveTasks SyncStatus错误 err:%v", errStr)
		}
	}
}

func (j *Job) GetCli(nodeId uint64) (*foeye.Foeye, error) {
	cli := foeye.NewFoeye()
	err := cli.SetNode(nodeId)
	if err != nil {
		return nil, err
	}

	config, err := data_source.NewNodeConfigModel().GetNodeConfig(nodeId)
	if err != nil {
		return nil, err
	}

	err = cli.SetConfig(config)
	if err != nil {
		return nil, err
	}

	return cli, nil
}

// MonitorTaskToStart 监测周期任务 到点执行
func (j *Job) MonitorTaskToStart(_ *Cron, mc *mcron.CrontabModel) {

	taskId := gjson.Get(mc.Params, "taskId").Int()

	//获取任务的详情
	taskInfo, err := task.NewProactiveTasks().GetTaskDetail(taskId)
	if err != nil {
		logs.GetLogger().Errorf("周期任务到点执行错误 err:%s", err.Error())
	}

	// 判断任务 当前是否为 打开状态
	if taskInfo.IsStart == 1 {
		// 获取该任务对应的节点
		nodeIds, err := task.NewProactiveTasks().GetNodesByTask(taskId)
		if err != nil {
			logs.GetLogger().Errorf("周期任务到点执行错误 err:%s", err.Error())
		}

		_, err, _ = proactive_scan.SaveTask(nil, taskInfo.Name, taskInfo.Desc, taskInfo.TaskType, taskInfo.SourceId, 1, "",
			taskInfo.ScanTime, (taskInfo.RepeatEndTime).String(), taskInfo.ScanMode, taskInfo.ScanIPRangeType, taskInfo.ScanIPRanges, taskInfo.PocScanType,
			taskInfo.SelectedPocs, taskInfo.ScanPort, taskInfo.ScanType, taskInfo.Bandwidth, taskInfo.Concurrency, taskInfo.OtherCfgs, nodeIds, uint64(taskInfo.UserId))
		if err != nil {
			logs.GetLogger().Errorf("周期任务到点执行错误 err:%s", err.Error())
		}
	}

}

// DelMonitorTask 周期任务 - 重复时间到了 - 进行删除
func (j *Job) DelMonitorTask(_ *Cron, mc *mcron.CrontabModel) {
	taskId := gjson.Get(mc.Params, "taskId").Int()
	var opts []mysql.HandleFunc
	opts = append(opts, mysql.WithWhere("name = ?", "周期任务"+strconv.Itoa(int(taskId))+"到点执行"))
	err := mcron.NewCrontabModel().Del(opts...)
	if err != nil {
		logs.GetLogger().Errorf("周期任务重复执行结束失败 err:%s", err.Error())
	}
}

func GetClis(nodeIds []uint64) (*foeye.Foeye, error) {
	cli := foeye.NewFoeye()
	for _, nodeId := range nodeIds {
		err := cli.SetNode(nodeId)
		if err != nil {
			return nil, err
		}

		config, err := data_source.NewNodeConfigModel().GetNodeConfig(nodeId)
		if err != nil {
			return nil, err
		}

		err = cli.SetConfig(config)
		if err != nil {
			return nil, err
		}
	}
	return cli, nil
}

// RegisterMonitorProactiveTasks 注册监测主动扫描任务定时任务
func RegisterMonitorProactiveTasks() *mcron.CrontabModel {
	return NewSchedulerJob("MonitorProactiveTasks", "*/5 * * * * *", "MonitorProactiveTasks", "")
}
