package compliance_monitor

import (
	"encoding/json"
	"testing"

	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"

	assetses "fobrain/models/elastic/assets"
	"fobrain/models/mysql/compliance_monitor"
)

// 测试 BuildESQuery 函数
func TestBuildESQuery(t *testing.T) {
	tests := []struct {
		name     string
		rules    []QueryRule
		ruleType int
		expected string
	}{
		{
			name: "测试在线状态查询",
			rules: []QueryRule{
				{
					RuleName: "ip",
					Whether:  "in",
					Content:  "***********",
				},
			},
			ruleType: 1,
			expected: "***********",
		},
		{
			name: "测试离线状态查询",
			rules: []QueryRule{
				{
					RuleName: "protocol",
					Whether:  "in",
					Content:  "***********",
				},
			},
			ruleType: 1,
			expected: "***********",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := NewComplianceMonitorTaskRecords()
			query := p.BuildESQuery(tt.rules, tt.ruleType)

			// 将查询转换为 JSON 字符串
			source, err := query.Source()
			assert.NoError(t, err)

			queryJSON, err := json.Marshal(source)
			assert.NoError(t, err)
			assert.Contains(t, string(queryJSON), tt.expected)
		})
	}
}

// 测试 convertToAssetsModel 函数
func TestConvertToAssetsModel(t *testing.T) {
	tests := []struct {
		name     string
		jsonData string
		wantErr  bool
	}{

		{
			name:     "测试空数据",
			jsonData: `{}`,
			wantErr:  false,
		},
		{
			name:     "测试无效 JSON",
			jsonData: `invalid json`,
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			hit := &elastic.SearchHit{
				Source: json.RawMessage(tt.jsonData),
			}

			model, err := convertToAssetsModel(hit)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.NotNil(t, model)
		})
	}
}

// 测试 FilterAssetDataForListMode 函数
func TestFilterAssetDataForListMode(t *testing.T) {
	// 准备测试数据
	ports := []*assetses.PortInfo{
		{Port: 80, Protocol: "http"},
		{Port: 443, Protocol: "https"},
		{Port: 22, Protocol: "ssh"},
		{Port: 3306, Protocol: "mysql"},
	}

	ruleInfos := []*assetses.RuleInfo{
		{Product: "nginx"},
		{Product: "mysql"},
		{Product: "apache"},
	}

	asset := &assetses.Assets{
		Id:        "test-asset",
		Ip:        "***********",
		Ports:     ports,
		RuleInfos: ruleInfos,
	}

	tests := []struct {
		name             string
		rules            []QueryRule
		ruleType         int
		expectedPorts    int
		expectedProducts int
		description      string
	}{
		{
			name: "只有端口规则-保留所有组件",
			rules: []QueryRule{
				{RuleName: "port", Whether: "in", Content: "80"},
			},
			ruleType:         compliance_monitor.RuleTypePort,
			expectedPorts:    1, // 只保留80端口
			expectedProducts: 3, // 保留所有组件（没有组件规则）
			description:      "端口过滤，组件不过滤",
		},
		{
			name: "只有协议规则-保留所有组件",
			rules: []QueryRule{
				{RuleName: "protocol", Whether: "in", Content: "http"},
			},
			ruleType:         compliance_monitor.RuleTypePort,
			expectedPorts:    1, // 只保留http协议端口
			expectedProducts: 3, // 保留所有组件（没有组件规则）
			description:      "协议过滤，组件不过滤",
		},
		{
			name: "只有组件规则-保留所有端口",
			rules: []QueryRule{
				{RuleName: "product", Whether: "in", Content: "nginx"},
			},
			ruleType:         compliance_monitor.RuleTypePort,
			expectedPorts:    4, // 保留所有端口（没有端口规则）
			expectedProducts: 1, // 只保留nginx组件
			description:      "组件过滤，端口不过滤",
		},
		{
			name: "混合规则-都过滤",
			rules: []QueryRule{
				{RuleName: "port", Whether: "in", Content: "80"},
				{RuleName: "product", Whether: "in", Content: "nginx"},
			},
			ruleType:         compliance_monitor.RuleTypePort,
			expectedPorts:    1, // 只保留80端口
			expectedProducts: 1, // 只保留nginx组件
			description:      "端口和组件都过滤",
		},
		{
			name: "多端口规则",
			rules: []QueryRule{
				{RuleName: "port", Whether: "in", Content: "80,443"},
			},
			ruleType:         compliance_monitor.RuleTypePort,
			expectedPorts:    2, // 保留80和443端口
			expectedProducts: 3, // 保留所有组件
			description:      "多端口过滤",
		},
		{
			name: "多组件规则",
			rules: []QueryRule{
				{RuleName: "product", Whether: "in", Content: "nginx,mysql"},
			},
			ruleType:         compliance_monitor.RuleTypePort,
			expectedPorts:    4, // 保留所有端口
			expectedProducts: 2, // 保留nginx和mysql组件
			description:      "多组件过滤",
		},
		{
			name: "无匹配规则",
			rules: []QueryRule{
				{RuleName: "port", Whether: "in", Content: "9999"},
			},
			ruleType:         compliance_monitor.RuleTypePort,
			expectedPorts:    0, // 没有匹配的端口
			expectedProducts: 3, // 保留所有组件（没有组件规则）
			description:      "无匹配端口",
		},
		{
			name: "非RuleTypePort类型-保留所有",
			rules: []QueryRule{
				{RuleName: "port", Whether: "in", Content: "80"},
			},
			ruleType:         compliance_monitor.RuleTypeIp,
			expectedPorts:    4, // 保留所有端口
			expectedProducts: 3, // 保留所有组件
			description:      "非端口规则类型",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := NewComplianceMonitorTaskRecords()
			result := p.FilterAssetDataForListMode(asset, tt.rules, tt.ruleType)

			assert.Equal(t, tt.expectedPorts, len(result.Ports),
				"端口数量不匹配: %s", tt.description)
			assert.Equal(t, tt.expectedProducts, len(result.RuleInfos),
				"组件数量不匹配: %s", tt.description)

			// 验证基础字段被正确复制
			assert.Equal(t, asset.Id, result.Id)
			assert.Equal(t, asset.Ip, result.Ip)
		})
	}
}

// 测试 processPortRule 函数
func TestProcessPortRule(t *testing.T) {
	ports := []*assetses.PortInfo{
		{Port: 80, Protocol: "http"},
		{Port: 443, Protocol: "https"},
		{Port: 22, Protocol: "ssh"},
	}

	tests := []struct {
		name          string
		targetPorts   []string
		expectedCount int
		expectedPorts []int
	}{
		{
			name:          "单个端口匹配",
			targetPorts:   []string{"80"},
			expectedCount: 1,
			expectedPorts: []int{80},
		},
		{
			name:          "多个端口匹配",
			targetPorts:   []string{"80", "443"},
			expectedCount: 2,
			expectedPorts: []int{80, 443},
		},
		{
			name:          "无匹配端口",
			targetPorts:   []string{"9999"},
			expectedCount: 0,
			expectedPorts: []int{},
		},
		{
			name:          "包含空格的端口",
			targetPorts:   []string{" 80 ", "443"},
			expectedCount: 2,
			expectedPorts: []int{80, 443},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := NewComplianceMonitorTaskRecords()
			matchedPorts := make(map[string]*assetses.PortInfo)

			p.processPortRule(ports, tt.targetPorts, matchedPorts)

			assert.Equal(t, tt.expectedCount, len(matchedPorts))

			// 验证匹配的端口号
			actualPorts := make([]int, 0)
			for _, port := range matchedPorts {
				actualPorts = append(actualPorts, port.Port)
			}

			for _, expectedPort := range tt.expectedPorts {
				found := false
				for _, actualPort := range actualPorts {
					if actualPort == expectedPort {
						found = true
						break
					}
				}
				assert.True(t, found, "期望端口 %d 未找到", expectedPort)
			}
		})
	}
}

// 测试 processProtocolRule 函数
func TestProcessProtocolRule(t *testing.T) {
	ports := []*assetses.PortInfo{
		{Port: 80, Protocol: "http"},
		{Port: 443, Protocol: "https"},
		{Port: 8080, Protocol: "HTTP"}, // 测试大小写不敏感
	}

	tests := []struct {
		name              string
		targetProtocols   []string
		expectedCount     int
		expectedProtocols []string
	}{
		{
			name:              "单个协议匹配",
			targetProtocols:   []string{"http"},
			expectedCount:     2, // http和HTTP都应该匹配
			expectedProtocols: []string{"http", "HTTP"},
		},
		{
			name:              "多个协议匹配",
			targetProtocols:   []string{"http", "https"},
			expectedCount:     3,
			expectedProtocols: []string{"http", "https", "HTTP"},
		},
		{
			name:              "无匹配协议",
			targetProtocols:   []string{"ftp"},
			expectedCount:     0,
			expectedProtocols: []string{},
		},
		{
			name:              "大小写混合",
			targetProtocols:   []string{"HTTP"},
			expectedCount:     2,
			expectedProtocols: []string{"http", "HTTP"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := NewComplianceMonitorTaskRecords()
			matchedPorts := make(map[string]*assetses.PortInfo)

			p.processProtocolRule(ports, tt.targetProtocols, matchedPorts)

			assert.Equal(t, tt.expectedCount, len(matchedPorts))
		})
	}
}

// 测试 processProductRule 函数
func TestProcessProductRule(t *testing.T) {
	ruleInfos := []*assetses.RuleInfo{
		{Product: "nginx"},
		{Product: "Apache HTTP Server"},
		{Product: "MySQL Server"},
		{Product: "nginx-proxy"},
	}

	tests := []struct {
		name             string
		targetProducts   []string
		expectedCount    int
		expectedProducts []string
	}{
		{
			name:             "单个组件精确匹配",
			targetProducts:   []string{"nginx"},
			expectedCount:    2, // nginx和nginx-proxy都包含nginx
			expectedProducts: []string{"nginx", "nginx-proxy"},
		},
		{
			name:             "多个组件匹配",
			targetProducts:   []string{"nginx", "MySQL"},
			expectedCount:    3, // nginx, nginx-proxy, MySQL Server
			expectedProducts: []string{"nginx", "nginx-proxy", "MySQL Server"},
		},
		{
			name:             "无匹配组件",
			targetProducts:   []string{"redis"},
			expectedCount:    0,
			expectedProducts: []string{},
		},
		{
			name:             "大小写不敏感",
			targetProducts:   []string{"APACHE"},
			expectedCount:    1,
			expectedProducts: []string{"Apache HTTP Server"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := NewComplianceMonitorTaskRecords()
			matchedRuleInfos := make(map[string]*assetses.RuleInfo)

			p.processProductRule(ruleInfos, tt.targetProducts, matchedRuleInfos)

			assert.Equal(t, tt.expectedCount, len(matchedRuleInfos))
		})
	}
}

// 测试 extractProductFromRuleInfos 函数
func TestExtractProductFromRuleInfos(t *testing.T) {
	tests := []struct {
		name             string
		ruleInfos        []*assetses.RuleInfo
		expectedCount    int
		expectedProducts []string
	}{
		{
			name: "正常组件列表",
			ruleInfos: []*assetses.RuleInfo{
				{Product: "nginx"},
				{Product: "mysql"},
				{Product: "apache"},
			},
			expectedCount:    3,
			expectedProducts: []string{"nginx", "mysql", "apache"},
		},
		{
			name: "包含重复组件",
			ruleInfos: []*assetses.RuleInfo{
				{Product: "nginx"},
				{Product: "nginx"},
				{Product: "mysql"},
			},
			expectedCount:    2,
			expectedProducts: []string{"nginx", "mysql"},
		},
		{
			name: "包含nil和空产品",
			ruleInfos: []*assetses.RuleInfo{
				{Product: "nginx"},
				nil,
				{Product: ""},
				{Product: "mysql"},
			},
			expectedCount:    2,
			expectedProducts: []string{"nginx", "mysql"},
		},
		{
			name:             "空列表",
			ruleInfos:        []*assetses.RuleInfo{},
			expectedCount:    0,
			expectedProducts: []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := NewComplianceMonitorTaskRecords()
			result := p.extractProductFromRuleInfos(tt.ruleInfos)

			assert.Equal(t, tt.expectedCount, len(result))

			// 验证包含期望的产品
			for _, expectedProduct := range tt.expectedProducts {
				found := false
				for _, actualProduct := range result {
					if actualProduct == expectedProduct {
						found = true
						break
					}
				}
				assert.True(t, found, "期望产品 %s 未找到", expectedProduct)
			}
		})
	}
}

func TestComplianceMonitorTaskRecordsFuncs(t *testing.T) {
	r := NewComplianceMonitorTaskRecords()
	assert.Equal(t, "compliance_monitor_task_records", r.IndexName())
	assert.Equal(t, "", r.String())

	r.Status = 1
	assert.Equal(t, "在线", r.StatusDesc())
	r.Status = 0
	assert.Equal(t, "离线", r.StatusDesc())

	assert.Equal(t, "未知", r.NetworkTypeDesc())
	r.NetworkType = 1
	assert.Equal(t, "内网", r.NetworkTypeDesc())
	r.NetworkType = 2
	assert.Equal(t, "外网", r.NetworkTypeDesc())
	r.PersonInfo = []*assetses.PersonBase{
		{
			Fid: "2343243",
		},
	}
	r.CcPerson = map[string]string{
		"name": "name1",
		"fid":  "fid1",
	}
	assert.Equal(t, "2343243", r.PersonFid())
	assert.Equal(t, "name1", r.CcPersonName())
	assert.Equal(t, "fid1", r.CcPersonFid())

	r.Ip = "***********"
	sampleMap := r.SampleHash()

	assert.Equal(t, "***********", sampleMap["ip"])
}
