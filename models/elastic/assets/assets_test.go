package assets

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/olivere/elastic/v7"
	. "github.com/smartystreets/goconvey/convey"
)

func TestEqual(t *testing.T) {
	Convey("TestEqual", t, func() {
		<PERSON><PERSON>("BNil", func() {
			a := &Business{}
			So(a.Equal(nil), ShouldBeFalse)
		})
		<PERSON>vey("SystemAndOwnerEqual", func() {
			a := &Business{System: "system", Owner: "owner"}
			b := &Business{System: "system", Owner: "owner"}
			So(a.Equal(b), ShouldBeTrue)
		})
	})
}

func TestNewKeywordQuery(t *testing.T) {
	boolQuery := elastic.NewBoolQuery()
	Convey("TestNewKeywordQuery", t, func() {
		<PERSON>vey("Success", func() {
			p := &Assets{}
			So(p.<PERSON>eywordQuery("", boolQuery), ShouldNotBeNil)
		})
	})
}

func TestSampleHash(t *testing.T) {
	<PERSON><PERSON>("TestSampleHash", t, func() {
		<PERSON><PERSON>("Success", func() {
			p := &Assets{}
			So(p.SampleHash(), ShouldNotBeNil)
		})
	})
}

// 测试 AppendSourceIds
func TestAssets_AppendSourceIds(t *testing.T) {
	assets := &Assets{}
	assets.AppendSourceIds(1, 2, 3, 3, 2, 4)

	expected := []uint64{1, 2, 3, 4}
	assert.ElementsMatch(t, expected, assets.SourceIds)
}

// 测试 AppendNodeIds
func TestAssets_AppendNodeIds(t *testing.T) {
	assets := &Assets{}
	assets.AppendNodeIds(10, 20, 30, 30, 20, 40)

	expected := []uint64{10, 20, 30, 40}
	assert.ElementsMatch(t, expected, assets.NodeIds)
}

// 测试 AppendTaskDataIds
func TestAssets_AppendTaskDataIds(t *testing.T) {
	assets := &Assets{}
	assets.AppendTaskDataIds("task1", "task2", "task3", "task3", "task2", "task4")

	expected := []string{"task1", "task2", "task3", "task4"}
	assert.ElementsMatch(t, expected, assets.TaskDataIds)
}

// 测试 AppendProcessIds
func TestAssets_AppendProcessIds(t *testing.T) {
	assets := &Assets{}
	assets.AppendProcessIds("proc1", "proc2", "proc3", "proc3", "proc2", "proc4")

	expected := []string{"proc1", "proc2", "proc3", "proc4"}
	assert.ElementsMatch(t, expected, assets.ProcessIds)
}

// 测试 RuleProduct
func TestAssets_RuleProduct(t *testing.T) {
	assets := &Assets{
		RuleInfos: []*RuleInfo{
			{Product: "ProductA"},
			{Product: "ProductB"},
			{Product: "ProductC"},
		},
	}
	expected := []string{"ProductA", "ProductB", "ProductC"}
	assert.ElementsMatch(t, expected, assets.RuleProduct())
}

// 测试 PortInfos
func TestAssets_PortInfos(t *testing.T) {
	assets := &Assets{
		NetworkType: 2,
		Ports: []*PortInfo{
			{80, "TCP", 200, "http://example.com", "example.com", "Example"},
			{443, "TCP", 0, "https://example.com", "example.com", "Secure Example"},
		},
	}
	expected := [][]string{
		{"80", "TCP", "http://example.com", "Example", "example.com", "200"},
		{"443", "TCP", "https://example.com", "Secure Example", "example.com", "-"},
	}
	assert.Equal(t, expected, assets.PortInfos())
}

// 测试 BusinessSystems
func TestAssets_BusinessSystems(t *testing.T) {
	assets := &Assets{
		Business: []*Business{
			{System: "Finance"},
			{System: "HR"},
			{System: "Finance"},
		},
	}
	expected := "Finance,HR"
	assert.Equal(t, expected, assets.BusinessSystems())
}

// 测试 BusinessOwners
func TestAssets_BusinessOwners(t *testing.T) {
	assets := &Assets{
		Business: []*Business{
			{PersonBase: []*PersonBase{{Name: "Alice"}}},
			{PersonBase: []*PersonBase{{Name: "Bob"}}},
			{PersonBase: []*PersonBase{{Name: "Alice"}}},
		},
	}
	expected := "Alice,Bob"
	assert.Equal(t, expected, assets.BusinessOwners())
}

// 测试 OperStringSlice
func TestAssets_OperStringSlice(t *testing.T) {
	assets := &Assets{
		OperInfo: []*PersonBase{
			{Id: "1", Name: "Alice"},
			{Id: "2", Name: "Bob"},
			{FindInfo: []*PersonFindInfo{{SourceValue: "Unknown"}}},
		},
	}
	expected := []string{"Alice", "Bob", "Unknown"}
	assert.ElementsMatch(t, expected, assets.OperStringSlice())
}

// 测试 OperString
func TestAssets_OperString(t *testing.T) {
	assets := &Assets{
		OperInfo: []*PersonBase{
			{Id: "1", Name: "Alice"},
			{Id: "2", Name: "Bob"},
			{FindInfo: []*PersonFindInfo{{SourceValue: "Unknown"}}},
		},
	}
	expected := "Alice,Bob,Unknown"
	assert.Equal(t, expected, assets.OperString())
}

// TestGetProducts 测试 GetProducts 方法
func TestGetProducts(t *testing.T) {
	tests := []struct {
		name      string
		ruleInfos []*RuleInfo
		expected  string
	}{
		{
			name:      "空规则",
			ruleInfos: []*RuleInfo{},
			expected:  "",
		},
		{
			name: "非空规则",
			ruleInfos: []*RuleInfo{
				{Product: "ProductA"},
				{Product: "ProductB"},
				{Product: "ProductC"},
			},
			expected: "ProductA,ProductB,ProductC",
		},
		{
			name: "包含 nil 值的规则",
			ruleInfos: []*RuleInfo{
				{Product: "ProductA"},
				nil,
				{Product: "ProductB"},
				nil,
				{Product: "ProductC"},
			},
			expected: "ProductA,ProductB,ProductC",
		},
		{
			name: "所有规则为 nil",
			ruleInfos: []*RuleInfo{
				nil,
				nil,
				nil,
			},
			expected: "",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			assets := &Assets{
				RuleInfos: test.ruleInfos,
			}
			result := assets.GetProducts()
			if result != test.expected {
				t.Errorf("Test case '%s' failed: got '%s', expected '%s'", test.name, result, test.expected)
			}
		})
	}
}

// TestDomains 测试 Domains 方法
func TestDomains(t *testing.T) {
	tests := []struct {
		name     string
		ports    []*PortInfo
		expected []string
	}{
		{
			name: "Ports 中包含有效 Domain",
			ports: []*PortInfo{
				{Domain: "example.com"},
				{Domain: "test.com"},
				{Domain: "demo.com"},
			},
			expected: []string{"example.com", "test.com", "demo.com"},
		},
		{
			name: "Ports 中包含空和有效 Domain",
			ports: []*PortInfo{
				{Domain: "example.com"},
				{Domain: ""},
				{Domain: "test.com"},
				{Domain: ""},
				{Domain: "demo.com"},
			},
			expected: []string{"example.com", "test.com", "demo.com"},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			assets := &Assets{
				Ports: test.ports,
			}
			result := assets.Domains()
			if !reflect.DeepEqual(result, test.expected) {
				t.Errorf("Test case '%s' failed: got %v, expected %v", test.name, result, test.expected)
			}
		})
	}
}

func TestAssets_BusinessDepartments(t *testing.T) {
	tests := []struct {
		name     string
		assets   Assets
		expected string
	}{
		{
			name: "空部门列表",
			assets: Assets{
				BusinessDepartment: []*DepartmentBase{},
			},
			expected: "",
		},
		{
			name: "单个部门",
			assets: Assets{
				BusinessDepartment: []*DepartmentBase{
					{Name: "研发部"},
				},
			},
			expected: "研发部",
		},
		{
			name: "多个部门",
			assets: Assets{
				BusinessDepartment: []*DepartmentBase{
					{Name: "研发部"},
					{Name: "测试部"},
					{Name: "运维部"},
				},
			},
			expected: "研发部,测试部,运维部",
		},
		{
			name: "包含空名称的部门",
			assets: Assets{
				BusinessDepartment: []*DepartmentBase{
					{Name: "研发部"},
					{Name: ""},
					{Name: "测试部"},
				},
			},
			expected: "研发部,测试部",
		},
		{
			name: "包含重复部门",
			assets: Assets{
				BusinessDepartment: []*DepartmentBase{
					{Name: "研发部"},
					{Name: "研发部"},
					{Name: "测试部"},
				},
			},
			expected: "研发部,测试部",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.assets.BusinessDepartments()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAssets_OperDepartments(t *testing.T) {
	tests := []struct {
		name     string
		assets   Assets
		expected string
	}{
		{
			name: "空部门列表",
			assets: Assets{
				OperDepartment: []*DepartmentBase{},
			},
			expected: "",
		},
		{
			name: "单个部门",
			assets: Assets{
				OperDepartment: []*DepartmentBase{
					{Name: "运维部"},
				},
			},
			expected: "运维部",
		},
		{
			name: "多个部门",
			assets: Assets{
				OperDepartment: []*DepartmentBase{
					{Name: "运维一部"},
					{Name: "运维二部"},
					{Name: "运维三部"},
				},
			},
			expected: "运维一部,运维二部,运维三部",
		},
		{
			name: "包含空名称的部门",
			assets: Assets{
				OperDepartment: []*DepartmentBase{
					{Name: "运维一部"},
					{Name: ""},
					{Name: "运维二部"},
				},
			},
			expected: "运维一部,运维二部",
		},
		{
			name: "包含重复部门",
			assets: Assets{
				OperDepartment: []*DepartmentBase{
					{Name: "运维一部"},
					{Name: "运维一部"},
					{Name: "运维二部"},
				},
			},
			expected: "运维一部,运维二部",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.assets.OperDepartments()
			assert.Equal(t, tt.expected, result)
		})
	}
}
