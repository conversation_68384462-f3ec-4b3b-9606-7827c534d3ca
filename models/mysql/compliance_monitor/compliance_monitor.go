package compliance_monitor

import (
	"errors"
	"fmt"
	"time"

	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/mysql"
)

type ComplianceMonitor struct {
	mysql.BaseModel
	UserId            uint64         `json:"user_id"`
	Name              string         `json:"name"`
	Desc              string         `json:"desc"`
	Rule              string         `json:"rule"`
	RuleType          int            `json:"rule_type"`
	AssetType         int            `json:"asset_type"`
	Event             int            `json:"event"`
	Date              string         `json:"date"`
	Time              string         `json:"time"`
	Status            int            `json:"status"`
	LastTime          localtime.Time `json:"last_time"`
	LastAbnormalAsset uint64         `json:"last_abnormal_asset"`
}

const RuleTypePort = 1              // 规则类型:  1:端口协议组件监测 2:IP监测
const RuleTypeIp = 2                // 规则类型:  1:端口协议组件监测 2:IP监测
const RuleTypeValueExposureWeek = 3 // 规则类型，两高一弱

const StatusEnable = 1  // 开启
const StatusDisable = 2 // 禁用

const EventExec = 1       // 立即执行
const EventOnce = 2       // 仅执行一次
const EventEveryDay = 3   // 每天
const EventEveryWeek = 4  // 每周
const EventEveryMonth = 5 // 每月

// TableName 表名
func (m *ComplianceMonitor) TableName() string {
	return "compliance_monitors"
}

func NewComplianceMonitorModel() *ComplianceMonitor {
	return &ComplianceMonitor{BaseModel: mysql.BaseModel{DB: mysql.GetDbClient().Table((&ComplianceMonitor{}).TableName())}}
}

func (m *ComplianceMonitor) Create(item *ComplianceMonitor) error {
	return m.DB.Table(m.TableName()).Create(item).Error
}

func (m *ComplianceMonitor) First(opts ...mysql.HandleFunc) (ComplianceMonitor, error) {
	query := m.DB.Table(m.TableName())
	for _, opt := range opts {
		opt(query)
	}
	var s ComplianceMonitor
	err := query.First(&s).Error
	if err != nil {
		return ComplianceMonitor{}, err
	}
	return s, nil
}

func (m *ComplianceMonitor) GetIds(opts ...mysql.HandleFunc) ([]uint64, error) {
	var ids []uint64

	query := m.DB.Table(m.TableName()).Select("id")
	for _, opt := range opts {
		opt(query)
	}

	if err := query.Scan(&ids).Error; err != nil {
		return nil, err
	}

	return ids, nil
}

func (m *ComplianceMonitor) List(page, size int, opts ...mysql.HandleFunc) ([]ComplianceMonitor, int64, error) {
	query := m.DB.Table(m.TableName())
	for _, opt := range opts {
		opt(query)
	}

	var total int64
	if !mysql.IsPageAll(page, size) {
		if err := query.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		query.Scopes(mysql.PageLimit(page, size))
	}

	var list = make([]ComplianceMonitor, 0)
	if err := query.Find(&list).Error; err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

func (m *ComplianceMonitor) Update(item ComplianceMonitor, opts ...mysql.HandleFunc) error {
	query := m.DB.Model(&ComplianceMonitor{})
	for _, f := range opts {
		f(query)
	}
	return query.Where("`id` = ?", item.Id).Updates(item).Error
}

func (m *ComplianceMonitor) Count(opts ...mysql.HandleFunc) (int64, error) {
	var num int64

	query := m.DB.Model(&ComplianceMonitor{})
	for _, f := range opts {
		f(query)
	}

	err := query.Count(&num).Error
	return num, err
}

func (m *ComplianceMonitor) GetLocalTimeByEvent(event int, dateStr string, timeStr string) (localtime.Time, error) {
	// 加载时区
	loc, _ := time.LoadLocation("Asia/Shanghai") // 使用 UTC+8 的时区
	startAt := localtime.Time{}
	switch event {
	case EventExec: //立即执行
		startAt = localtime.Time(time.Now())
	case EventOnce: //仅执行一次
		parseTime, err := time.ParseInLocation(localtime.TimeNotSecondFormat, fmt.Sprintf("%s %s", dateStr, timeStr), loc)
		if err != nil {
			return localtime.Time{}, err
		}
		startAt = localtime.Time(parseTime)
	case EventEveryDay: //每天
		dateStr = localtime.NewLocalTime(time.Now()).DateString()
		// 使用与上述字符串相同的布局解析目标时间
		t, _ := time.ParseInLocation(localtime.TimeNotSecondFormat, fmt.Sprintf("%s %s", dateStr, timeStr), loc)
		startAt = localtime.Time(t)
	case EventEveryWeek: //每周
		dateStr = localtime.NewLocalTime(time.Now()).DateString()
		// 使用与上述字符串相同的布局解析目标时间
		t, _ := time.ParseInLocation("2006-01-02 15:04", fmt.Sprintf("%s %s", dateStr, timeStr), loc)
		startAt = localtime.Time(t)
	case EventEveryMonth: //每月
		dateStr = localtime.NewLocalTime(time.Now()).DateString()
		// 使用与上述字符串相同的布局解析目标时间
		t, _ := time.ParseInLocation("2006-01-02 15:04", fmt.Sprintf("%s %s", dateStr, timeStr), loc)
		startAt = localtime.Time(t)
	default:
		return startAt, errors.New("event not exists")
	}

	return startAt, nil
}

func (m *ComplianceMonitor) Delete(opts ...mysql.HandleFunc) error {

	query := m.DB.Model(&ComplianceMonitor{})
	for _, f := range opts {
		f(query)
	}

	return query.Delete(&ComplianceMonitor{}, "id > 0").Error
}
