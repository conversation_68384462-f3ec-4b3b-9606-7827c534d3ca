package net_mapping

type NetMappingGplotResponse struct {
	Source         string `json:"source"`
	SourceName     string `json:"source_name"`
	SourcePort     string `json:"source_port"`
	SourceDomain   string `json:"source_domain"`
	SourceProtocol string `json:"source_protocol"`
	SourceUrl      string `json:"source_url"`
	Target         string `json:"target"`
	TargetName     string `json:"target_name"`
	TargetPort     string `json:"target_port"`
	TargetDomain   string `json:"target_domain"`
	TargetProtocol string `json:"target_protocol"`
	TargetUrl      string `json:"target_url"`
}

// BusinessMappingGplotResponse 业务系统拓扑图响应结构
type BusinessMappingGplotResponse struct {
	Links []NetMappingGplotResponse `json:"links"` // 连接关系数据，用于绘制连线
	Data  []BusinessNodeData        `json:"data"`  // 节点数据，用于绘制节点
}

// BusinessNodeData 业务系统拓扑图节点数据
type BusinessNodeData struct {
	Name      string `json:"name"`       // IP
	IpKind    string `json:"ip_kind"`    // 内外网
	VulnCount int64  `json:"vuln_count"` // 漏洞数量
}
