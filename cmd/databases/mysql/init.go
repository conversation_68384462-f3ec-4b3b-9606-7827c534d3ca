package migrations

import (
	"fobrain/webhook/dao/migrate"

	"git.gobies.org/caasm/fobrain-components/go-migrate/config"
)

func InitMysqlLoad() {
	config.Migrations = append(config.Migrations, CreateUsers20240111100845Table())
	config.Migrations = append(config.Migrations, CreateRole20240111161300Table())
	config.Migrations = append(config.Migrations, CreateUsersRoles20240111163001Table())
	config.Migrations = append(config.Migrations, CreateMenus20240111163344Table())
	config.Migrations = append(config.Migrations, CreateRolesMenus20240111170926Table())
	config.Migrations = append(config.Migrations, CreateTokens20240111185011Table())
	config.Migrations = append(config.Migrations, CreateAuditLogs20240112140942Table())
	config.Migrations = append(config.Migrations, CreateCrontab20240112185344Table())
	config.Migrations = append(config.Migrations, CreateCrontabHistory20240112190159Table())
	config.Migrations = append(config.Migrations, CreateFtpUsers20240116165446Table())
	config.Migrations = append(config.Migrations, CreateDataNodes20240124105634Table())
	config.Migrations = append(config.Migrations, CreateDataNodesConfig20240124105648Table())
	config.Migrations = append(config.Migrations, CreateDataSources20240124112514Table())
	config.Migrations = append(config.Migrations, CreateDataSourceTypes20240124112519Table())
	config.Migrations = append(config.Migrations, CreateDataSourceTypeMap20240124112527Table())
	config.Migrations = append(config.Migrations, CreateFusionTemplate20240131145508Table())
	config.Migrations = append(config.Migrations, CreateFusionTemplateRules20240131150701Table())
	config.Migrations = append(config.Migrations, CreateDataAccessTasks20240202164049Table())
	config.Migrations = append(config.Migrations, CreateDataSyncTasks20240202164105Table())
	config.Migrations = append(config.Migrations, CreateDataSyncChildTasks0240314142245Table())
	config.Migrations = append(config.Migrations, CreateSystemConfigs202402281657Table())
	config.Migrations = append(config.Migrations, CreateDataNodeLogs20240311101756Table())
	config.Migrations = append(config.Migrations, CreateScenes20240329155420Table())
	config.Migrations = append(config.Migrations, CreateDataMergeTasks20240703093723Table())
	config.Migrations = append(config.Migrations, CreateAliyunAvd20240706101224Table())
	config.Migrations = append(config.Migrations, CreateStrategy20240716114527Table())
	config.Migrations = append(config.Migrations, CreateNetworkAreas20240717105703Table())
	config.Migrations = append(config.Migrations, CreateISPAreas20240717112124Table())
	config.Migrations = append(config.Migrations, CreateGEOAreas20240717113712Table())
	config.Migrations = append(config.Migrations, CreateVulnerability20240815145341())
	config.Migrations = append(config.Migrations, CreateVulnerabilityUploadRecord20240815194051())
	config.Migrations = append(config.Migrations, CreateVulnerabilityCveSource20240816152724())
	config.Migrations = append(config.Migrations, CreateVulnerabilityCnvdSource20240816154513())
	config.Migrations = append(config.Migrations, CreateVulnerabilityCnnvdSource20240816154837())
	config.Migrations = append(config.Migrations, CreateVulnerabilityConformResult20240820162030())
	config.Migrations = append(config.Migrations, CreateIpMapping20240909120617Table())
	config.Migrations = append(config.Migrations, CreateIpMappingAuditData20240909121059Table())
	config.Migrations = append(config.Migrations, CreateIpMappingAuditLog20240909121133Table())
	config.Migrations = append(config.Migrations, CreateIpMappingStatisticsAudit20240909121155Table())
	config.Migrations = append(config.Migrations, CreateIpMappingStatistics20240909121201Table())
	config.Migrations = append(config.Migrations, CreateProactiveTasks20240909144839())
	config.Migrations = append(config.Migrations, CreateAddModeToDataNodesTable20240910153602())
	config.Migrations = append(config.Migrations, CreateProactiveTaskNodeRelations20240910163222Table())
	config.Migrations = append(config.Migrations, CreateSystemUpgradeLog20240911153640Table())

	config.Migrations = append(config.Migrations, CreateAddIsStartToProactiveTasksTable20240913103824())

	config.Migrations = append(config.Migrations, CreateAddUserIdToProactiveTasks20240913161203())
	config.Migrations = append(config.Migrations, CreateAddNodeTaskIdToProactiveTaskNodeRelations20240913162411())
	config.Migrations = append(config.Migrations, CreateMergeProgress20240914170750Table())
	config.Migrations = append(config.Migrations, CreateAddSourceIdToProactiveTaskNodeRelations20240919152112())
	config.Migrations = append(config.Migrations, CreateBlackWhiteConfig20240920160727Table())
	config.Migrations = append(config.Migrations, CreateSystemLogs20240921143342Table())
	config.Migrations = append(config.Migrations, CreateComplianceMonitors20240111100845Table())
	config.Migrations = append(config.Migrations, CreateComplianceMonitorTasks20240903145422Table())
	config.Migrations = append(config.Migrations, CreateNotifyAlarmCenter20240925152729Table())
	config.Migrations = append(config.Migrations, CreateThreatHistories20240921141251Table())
	config.Migrations = append(config.Migrations, CreateLogsQcSetting20240923111218Table())
	config.Migrations = append(config.Migrations, CreateIpRanges20240925111837Table())
	config.Migrations = append(config.Migrations, CreateIpRangesSources20240925112134Table())
	config.Migrations = append(config.Migrations, CreateThreatTasks20240925193557Table())
	config.Migrations = append(config.Migrations, CreateAddStartAtToProactiveTaskNodeRelations20240927160509())
	config.Migrations = append(config.Migrations, CreateAddEndAtToProactiveTaskNodeRelations20240927160942())
	config.Migrations = append(config.Migrations, CreateReportTemplates20240925161910Table())
	config.Migrations = append(config.Migrations, CreateWorkbenchDataProcessOverview20240930115757Table())
	config.Migrations = append(config.Migrations, CreatePocSettings20240930120455Table())
	config.Migrations = append(config.Migrations, CreateEmailConfig20241010144411Table())
	config.Migrations = append(config.Migrations, CreateWorkbenchAssetSecurityCoverage20241015152321Table())
	config.Migrations = append(config.Migrations, CreateWorkbenchAssetExemptRecord20241015152329Table())
	config.Migrations = append(config.Migrations, CreateUsersStaffs20241014155142())
	config.Migrations = append(config.Migrations, CreateMergeAssetBlacklist20241018184905Table())
	config.Migrations = append(config.Migrations, CreateMergeDeviceBlacklist20241018185022Table())
	config.Migrations = append(config.Migrations, CreatePersonnelDepartments20241105164723Table())
	config.Migrations = append(config.Migrations, migrate.CreateWebhookEventUrlConfig20241108173534Table())
	config.Migrations = append(config.Migrations, CreateDataCaptureNodes20241029142306Table())
	config.Migrations = append(config.Migrations, migrate.CreateWebhookEventSendResult20241109154345Table())
	config.Migrations = append(config.Migrations, CreateFieldTagRules20241113123210Table())
	config.Migrations = append(config.Migrations, CreateAddSourceTypeCreateFieldTagRulesTable20241114152508())
	config.Migrations = append(config.Migrations, CreateCustomTags20241114153859Table())
	config.Migrations = append(config.Migrations, CreateAddSourceIsClashPersonnelDepartmentsTable20241113213331())
	config.Migrations = append(config.Migrations, CreateAssetMappingTable20241115112603TableTable())
	config.Migrations = append(config.Migrations, CreateCustomFields20241115161222Table())
	config.Migrations = append(config.Migrations, CreateCascadeUpgradeRecords20241112103540Table())
	config.Migrations = append(config.Migrations, CreateCascadeUpgradePackage20241112105852Table())
	config.Migrations = append(config.Migrations, CreateCascadeUpgradeDistributeRecord20241112141300Table())
	config.Migrations = append(config.Migrations, CreateCascadeUpgradeDownload20241112163212Table())
	config.Migrations = append(config.Migrations, CreateAddCascadeUpgradeRecordsTable20241116085217())
	config.Migrations = append(config.Migrations, CreateFieldReadUserIdNotifyAlarmCenter20241121200210Table())
	config.Migrations = append(config.Migrations, CreateFieldStaffIdNotifyAlarmCenter20241122200210Table())
	config.Migrations = append(config.Migrations, CreateAlterCascadeUpgradeRecordsTable20241119202103())
	config.Migrations = append(config.Migrations, CreateCascadeUpgradeRecordDetails20241120214423Table())
	config.Migrations = append(config.Migrations, CreateAlterCascadeUpgradeRecordsTable20241122201112())
	config.Migrations = append(config.Migrations, migrate.CreateAlterWebhookEventSendResultTable20241120171813())
	config.Migrations = append(config.Migrations, CreateAdapterMessages20241123194041Table())
	config.Migrations = append(config.Migrations, CreateAlterIpMappingAuditLogTable20241202105749())
	config.Migrations = append(config.Migrations, CreateAlertRemarkNotifyAlarmCenterTable20241202154230())
	config.Migrations = append(config.Migrations, CreateBusinessStrategy20241204114058())
	config.Migrations = append(config.Migrations, CreatePocAutoDistributeConfig20241210153506Table())
	config.Migrations = append(config.Migrations, CreateAddDataNodesTable20241210061014())
	config.Migrations = append(config.Migrations, CreateAddCascadeUpgradePackagesTable20241206181011())
	config.Migrations = append(config.Migrations, CreateAlertGenreAndPocIdsThreatTasksTables20241212201753())
	config.Migrations = append(config.Migrations, CreateCustomFieldGroup20241210111011Table())
	config.Migrations = append(config.Migrations, CreateAddIPMapping20241210173011())
	config.Migrations = append(config.Migrations, CreateAddIPMappingStatistic20241212115411())
	config.Migrations = append(config.Migrations, CreateAlertShowRepairPocAutoDistributeConfigTable20241215133704())
	config.Migrations = append(config.Migrations, CreateAddCustomFieldGroupTable20241215122110())
	config.Migrations = append(config.Migrations, CreateCascadeSyncRecords20241216155228Table())
	config.Migrations = append(config.Migrations, CreateAlterWebhookEventUrlConfigTable20241219155922())
	config.Migrations = append(config.Migrations, CreatePocAccessorys20241225095844Table())
	config.Migrations = append(config.Migrations, CreateAddFileToDataSyncTasksTable20241220174042())
	config.Migrations = append(config.Migrations, CreateAddRuleInfosToUsersTable20241220105846())
	config.Migrations = append(config.Migrations, CreateMergeRecords20241219215340Table())
	config.Migrations = append(config.Migrations, CreateNetMapping20241226110521Table())
	config.Migrations = append(config.Migrations, CreateNetMappingArea20241226111325Table())
	config.Migrations = append(config.Migrations, CreateNetMappingAuditDatas20241226144729Table())
	config.Migrations = append(config.Migrations, CreateNetMappingAuditLogs20241226150110Table())
	config.Migrations = append(config.Migrations, CreateAlertNetMappingTable20250103173533())
	config.Migrations = append(config.Migrations, CreateAlertNetMappingAuditDatasTable20250103173801())
	config.Migrations = append(config.Migrations, RunNetMappingDataMigrate())
	config.Migrations = append(config.Migrations, CreateAlterCascadeSyncRecordsAddStatus20250106234617())
	config.Migrations = append(config.Migrations, CreateAlterMergeRecordsAddParamsTable20250115163511())
	config.Migrations = append(config.Migrations, CreateAddThreatTasks20250120154634())
	config.Migrations = append(config.Migrations, CreateAddDataPermissionToUsersTable20250213152711())
	config.Migrations = append(config.Migrations, CreateAddHiddenAndModuleAndComponentToMenus20250213122901())
	config.Migrations = append(config.Migrations, CreateAddSysToRolesTable20250213122001())
	config.Migrations = append(config.Migrations, CreateCreateMenusApiTable20250213141901())
	config.Migrations = append(config.Migrations, CreateAddTimeoutFrequencyPocAutoDistributeConfigTable20250218185420())
	config.Migrations = append(config.Migrations, CreateAlertFieldTagRulesTable20250311160501())
	config.Migrations = append(config.Migrations, CreateAlterCascadeUpgradeRecordsAddDownloadErr20250312153817())
	config.Migrations = append(config.Migrations, CreateFieldTagRules20250318144511Index())
	config.Migrations = append(config.Migrations, CreateAddSubTriggerToMergeRecords20250325105312())
	config.Migrations = append(config.Migrations, CreateAlterMergeRecordsChangeParamsLen20250327100610())
	config.Migrations = append(config.Migrations, CreateDataSyncChildTaskFailRecord20250331104655Table())
	config.Migrations = append(config.Migrations, CreateAlterDataSyncChildTasksTable20250331105653())
	config.Migrations = append(config.Migrations, CreateAlterWorkbenchAssetExemptRecordAddExemptConditionHuman20250403142837())
	config.Migrations = append(config.Migrations, CreateAlterIpRangesRemoveType20250407105703())
	config.Migrations = append(config.Migrations, CreateFieldTagRules20250410161111Index())
	config.Migrations = append(config.Migrations, CreateAlterDataNodeAddAreaRule20250403111921())
	config.Migrations = append(config.Migrations, CreateCustomFieldMeta20250415162200Table())
	config.Migrations = append(config.Migrations, CreateWorkbenchStatisticsByHour20250423180736Table())
	config.Migrations = append(config.Migrations, CreateAddHasRelationsToDataSourcesTable20250427172011())
	config.Migrations = append(config.Migrations, CreateAddFailInfoToUsersTable20250506104416())
	config.Migrations = append(config.Migrations, CreateAddRequestDataToAuditLogTable20250509110922())
	config.Migrations = append(config.Migrations, CreateAlterMethodToMenusTable20250512191705())
	config.Migrations = append(config.Migrations, CreateAlterResultToAuditLogsTable20250521113635())
	config.Migrations = append(config.Migrations, CreateMergeExceptions20250521104835Table())
	config.Migrations = append(config.Migrations, CreateAddCustomFieldConfigToDataSourceTable20250609122008())
	config.Migrations = append(config.Migrations, AddTaskModelToComplianceMonitorsTable20241227090000Table())
	config.Migrations = append(config.Migrations, CreateAlterScanIpRangesLongtextToProactiveTasks20240614120000())
	config.Migrations = append(config.Migrations, CreateAlterNodeAreaRules20250718170103())

	config.Migrations = append(config.Migrations, CreateComplianceRiskHistory202507251834())
	config.Migrations = append(config.Migrations, CreateComplianceAccessorys20250801121144Table())
}
