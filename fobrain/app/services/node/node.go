package node

import (
	"crypto/tls"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/spf13/cast"
	"github.com/tidwall/gjson"

	"fobrain/initialize/mysql"
	"fobrain/models/mysql/data_source"
)

type Client struct {
	configs map[string]any
	nodeId  uint64
	*http.Client
}

type SendRequest interface {
	Retest(ids []string) (string, error)
	GetCheckProgress(ids []string) (int, error)
	GetCheckResult(ids []string, pocObj map[string]interface{}) (gjson.Result, int, error)
}

// NewDefaultClient 默认客户端
func NewDefaultClient() Client {
	return Client{
		Client: &http.Client{
			Timeout: 30 * time.Second,
			Transport: &http.Transport{
				TLSClientConfig:   &tls.Config{InsecureSkipVerify: true},
				DisableKeepAlives: true,
			},
		},
	}
}

// GetConfig 获取配置信息
func (f *Client) GetConfig(key string) any {
	if v, ok := f.configs[key]; ok {
		return v
	}
	return nil
}

// GetApiKey
// @Summary 获取api key
func (f *Client) GetApiKey() string {
	apiKey, ok := f.GetConfig("api_key").(string)
	if !ok {
		return ""
	}
	return apiKey
}

// SetNode 设置节点配置
func (f *Client) SetNode(nodeId uint64) error {
	config, err := data_source.NewNodeConfigModel().GetNodeConfig(nodeId)
	if err != nil {
		return err
	}
	f.nodeId = nodeId
	f.configs = config
	if pErr := f.setProxyByConfig(); pErr != nil {
		return pErr
	}
	return nil
}

// GetNodeId 获取节点id
func (f *Client) GetNodeId() uint64 {
	return f.nodeId
}

// GetSource 获取来源
func (f *Client) GetSourceId() uint64 {
	dataSource, err := data_source.NewNodeModel().First(mysql.WithColumnValue("id", f.nodeId))
	if err != nil {
		return 0
	}
	return dataSource.SourceId
}

// SetConfig 设置配置信息
func (f *Client) SetConfig(configs map[string]any) error {
	f.configs = configs
	if pErr := f.setProxyByConfig(); pErr != nil {
		return pErr
	}
	return nil
}

// 根据配置设置代理
func (f *Client) setProxyByConfig() error {
	if proxyVal, ok := f.configs["proxy"]; ok && proxyVal != nil && proxyVal != "" {
		if proxy, pErr := url.Parse(cast.ToString(proxyVal)); pErr != nil {
			return pErr
		} else {
			f.Client.Transport = &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
				Proxy:           http.ProxyURL(proxy),
			}
		}
	}
	return nil
}

func (f *Client) GetConfigToUint64(key string) uint64 {
	if v, ok := f.configs[key]; ok && v != nil && v != "" {
		return cast.ToUint64(v)
	}
	return 0
}

func (f *Client) GetConfigToSlice(key string) []string {
	if v, ok := f.configs[key]; ok && v != nil && v != "" {
		vSlice := strings.Split(cast.ToString(v), ",")
		if len(vSlice) == 0 {
			return []string{}
		}
		return vSlice
	}
	return []string{}
}
