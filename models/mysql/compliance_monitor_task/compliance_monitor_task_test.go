package compliance_monitor_task

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"fobrain/fobrain/common/localtime"
	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
)

func TestCreate(t *testing.T) {

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 调用函数
	err := NewComplianceMonitorTaskModel().Create(&ComplianceMonitorTask{
		ComplianceMonitorId: 1,
		StartAt:             localtime.Time(time.Now()),
		Status:              1,
	})

	assert.NotNil(t, err)
	assert.Equal(t, err.Error(), "all expectations were already fulfilled, call to database transaction Begin was not expected")
}

func TestFirst(t *testing.T) {

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 调用函数
	c, err := NewComplianceMonitorTaskModel().First()
	fmt.Println(c)
	assert.NotNil(t, err)
}

func TestList(t *testing.T) {

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 调用函数
	c, d, err := NewComplianceMonitorTaskModel().List(0, 0, mysql.WithWhere("id = ?", 1))
	fmt.Println(c)
	fmt.Println(d)
	assert.NotNil(t, err)
}

func TestUpdate(t *testing.T) {

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 调用函数
	err := NewComplianceMonitorTaskModel().Update(ComplianceMonitorTask{
		ComplianceMonitorId: 1,
		StartAt:             localtime.Time(time.Now()),
		Status:              1,
	}, mysql.WithWhere("id = ?", 1))
	assert.NotNil(t, err)
}

func TestCount(t *testing.T) {

	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()

	// 调用函数
	count, err := NewComplianceMonitorTaskModel().Count(mysql.WithWhere("id = ?", 1))
	fmt.Println(count)
	assert.NotNil(t, err)
}
