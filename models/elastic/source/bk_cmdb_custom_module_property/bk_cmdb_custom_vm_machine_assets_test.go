package bk_cmdb_custom_module_property

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestBkCmdbCustomVmMachineAssets_IndexName(t *testing.T) {
	assert.Equal(t, "bk_cmdb_vm_machine_task_assets", NewBkCmdbCustomTaskVmMachineAssetsModel().IndexName())
	assert.Equal(t, "", NewBkCmdbCustomTaskVmMachineAssetsModel().String())
}

func TestBkCmdbCustomVmMachineAssets_GetAssetListFields(t *testing.T) {
	fields := NewBkCmdbCustomTaskVmMachineAssetsModel().GetAssetListFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "IP地址", "key": "ip_addr", "all_key": "ip_addr"},
		{"name": "内存", "key": "memory", "all_key": "memory"},
		{"name": "sn", "key": "sn", "all_key": "sn"},
		{"name": "mac 地址", "key": "mac", "all_key": "mac"},
	}
	assert.Equal(t, expectedFields, fields, "字段不匹配")
}

func TestBkCmdbCustomVmMachineAssets_GetAssetListDetailFields(t *testing.T) {
	fields := NewBkCmdbCustomTaskVmMachineAssetsModel().GetAssetListDetailFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "IP地址", "key": "ip_addr", "all_key": "ip_addr"},
		{"name": "内存", "key": "memory", "all_key": "memory"},
		{"name": "sn", "key": "sn", "all_key": "sn"},
		{"name": "mac 地址", "key": "mac", "all_key": "mac"},
	}
	assert.Equal(t, expectedFields, fields, "字段不匹配")
}
