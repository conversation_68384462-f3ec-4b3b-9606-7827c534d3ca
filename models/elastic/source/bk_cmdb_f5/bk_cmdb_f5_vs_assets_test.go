package bk_cmdb_f5

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestBkCmdbF5VsAssets_GetAssetListDetailFields(t *testing.T) {
	fields := NewBkCmdbF5VsTaskAssetsModel().GetAssetListDetailFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "ID", "key": "bk_inst_id", "all_key": "bk_inst_id"},
		{"name": "实例名", "key": "bk_inst_name", "all_key": "bk_inst_name"},
		{"name": "Ip地址", "key": "f5_ip", "all_key": "f5_ip"},
		{"name": "VS的负载IP地址", "key": "ip_addr", "all_key": "ip_addr"},
		{"name": "VS的负载端口", "key": "port", "all_key": "port"},
		{"name": "iApps", "key": "iapp", "all_key": "iapp"},
		{"name": "iRule名称", "key": "irule", "all_key": "irule"},
	}
	assert.Equal(t, expectedFields, fields, "字段不匹配")
}

func TestBkCmdbF5VsAssets_GetAssetListFields(t *testing.T) {
	fields := NewBkCmdbF5VsTaskAssetsModel().GetAssetListFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "ID", "key": "bk_inst_id", "all_key": "bk_inst_id"},
		{"name": "实例名", "key": "bk_inst_name", "all_key": "bk_inst_name"},
		{"name": "Ip地址", "key": "f5_ip", "all_key": "f5_ip"},
		{"name": "VS的负载IP地址", "key": "ip_addr", "all_key": "ip_addr"},
		{"name": "VS的负载端口", "key": "port", "all_key": "port"},
		{"name": "iApps", "key": "iapp", "all_key": "iapp"},
		{"name": "iRule名称", "key": "irule", "all_key": "irule"},
	}
	assert.Equal(t, expectedFields, fields, "字段不匹配")
}

func TestBkCmdbF5VsAssets_IndexName(t *testing.T) {
	assert.Equal(t, "bk_cmdb_f5_vs_task_assets", NewBkCmdbF5VsTaskAssetsModel().IndexName())
	assert.Equal(t, "", NewBkCmdbF5VsTaskAssetsModel().String())
}
