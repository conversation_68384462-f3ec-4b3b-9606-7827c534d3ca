package crontab

import (
	"encoding/json"
	"fmt"
	cascade_service "fobrain/fobrain/app/services/cascade"
	"fobrain/fobrain/logs"
	"fobrain/initialize/mysql"
	mcron "fobrain/models/mysql/crontab"
	models "fobrain/module/cascade/models/data_capture_nodes"
	"fobrain/module/cascade/sdk"
)

func AddCascadeNodeTask(node *models.DataCaptureNodes) {
	// 解析定时任务配置
	var configMap map[string]interface{}

	err := json.Unmarshal([]byte(node.ConfigInfo), &configMap)
	if err != nil {
		return
	}

	spec := getSpec(fmt.Sprintf("%v", configMap["repeat_type"]),
		fmt.Sprintf("%v", configMap["date"]),
		fmt.Sprintf("%v", configMap["start_time"]))

	if spec == "" {
		logs.GetLogger().Info("当前节点没有定时任务")
		return
	}

	name := fmt.Sprintf("级联节点%d同步数据任务", node.Id)
	jsonString, err := json.<PERSON>(map[string]uint64{"nodeId": node.Id})
	if err != nil {
		logs.GetLogger().Errorf("解析Json失败:%s", err.Error())
	}

	data := &mcron.CrontabModel{
		UserId: 1,
		Name:   name,
		Status: mcron.StatusEnable,
		Type:   mcron.TypeSystem,
		Spec:   spec,
		Method: "CascadeNodeTasks",
		Params: string(jsonString),
	}

	if err = mcron.NewCrontabModel().Create(data); err != nil {
		logs.GetLogger().Errorf("定时任务创建失败:%s", err.Error())
	}

	if err = GetCronIns().LoadMysqlJob(data.Id); err != nil {
		logs.GetLogger().Errorf("Job注册失败:%s", err.Error())
	}
}

func UpdateCascadeNodeTask(node *models.DataCaptureNodes) {
	// 查询是否存在
	name := fmt.Sprintf("级联节点%d同步数据任务", node.Id)
	cronInfo, err := mcron.NewCrontabModel().First(mysql.WithWhere("name", name))
	if err != nil {
		logs.GetLogger().Errorf("定时任务获取失败:%s", err.Error())
		AddCascadeNodeTask(node)
		return
	}

	// 解析定时任务配置
	var configMap map[string]interface{}

	err = json.Unmarshal([]byte(node.ConfigInfo), &configMap)
	if err != nil {
		logs.GetLogger().Errorf("解析定时任务配置失败:%s", err.Error())
		return
	}

	spec := getSpec(fmt.Sprintf("%v", configMap["repeat_type"]),
		fmt.Sprintf("%v", configMap["date"]),
		fmt.Sprintf("%v", configMap["start_time"]))

	if spec == "" {
		logs.GetLogger().Info("当前节点没有定时任务")
		return
	}

	if cronInfo != nil {
		if cronInfo.Spec != spec {
			cronInfo.Spec = spec
			err = mcron.NewCrontabModel().Updates(cronInfo, mysql.WithSelect("name", "type", "spec", "status", "method", "params"))
			if err != nil {
				logs.GetLogger().Errorf("定时任务更新失败:%s", err.Error())
			}
			err = GetCronIns().LoadMysqlJob(cronInfo.Id)
			if err != nil {
				logs.GetLogger().Error("Job更新失败:%s", err.Error())
			}
		}
	}
}

func DeleteCascadeNodeTask(nodeId uint64) {
	// 获取定时任务
	name := fmt.Sprintf("级联节点%d同步数据任务", nodeId)
	cronInfo, err := mcron.NewCrontabModel().First(mysql.WithWhere("name", name))
	if err != nil {
		logs.GetLogger().Errorf("定时任务获取失败:%s", err.Error())
		return
	}
	// 删除定时任务
	err = cronInfo.Del(mysql.WithWhere("id", cronInfo.Id))
	if err != nil {
		logs.GetLogger().Errorf("定时任务删除失败:%s", err.Error())
		return
	}
	// 停止Job
	GetCronIns().StopJob(cronInfo.Name)
}

func (j *Job) CascadeNodeTasks(_ *Cron, model *mcron.CrontabModel) string {
	var params map[string]uint64
	err := json.Unmarshal([]byte(model.Params), &params)
	if err != nil {
		logs.GetLogger().Errorf("解析Json失败:%s", err.Error())
		return "解析Json失败"
	}
	nodeId := params["nodeId"]
	node, err := sdk.GetNodeInfo(int(nodeId))
	if err != nil {
		logs.GetLogger().Errorf("获取节点信息失败:%s", err.Error())
		return "获取节点信息失败"
	}
	tagMap := cascade_service.ParseTag(node.Tag)
	if tagMap["has_asset_data"] {
		cascade_service.TriggerGetAssetList(int(nodeId))
	}
	if tagMap["has_vul_data"] {
		cascade_service.TriggerGetVulnList(int(nodeId))
	}
	return "触发获取节点数据任务成功"
}

// 级联定时拉数据，不在定时任务里维护，通过main里面的 注册节点更新事件 实现功能
func RegisterCascadNodeTasks() *mcron.CrontabModel {
	return NewSchedulerJob("CascadNodeTasks", "01 * * * * *", "CascadNodeTasks", "")
}
