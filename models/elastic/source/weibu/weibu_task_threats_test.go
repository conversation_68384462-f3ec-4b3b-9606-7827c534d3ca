package weibu

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestWeibuTaskThreats_IndexName(t *testing.T) {
	assert.Equal(t, "weibu_task_threats", NewWeibuTaskThreatsModel().IndexName())
	assert.Equal(t, "", NewWeibuTaskThreatsModel().String())
}

func TestWeibuTaskThreats_GetWeibuThreatListFields(t *testing.T) {
	m := NewWeibuTaskThreatsModel()
	fields := m.GetWeibuThreatListFields()

	assert.Equal(t, fields, []map[string]string{
		{"name": "主机IP地址", "key": "machine", "all_key": "machine.*"},
		{"name": "处置状态", "key": "status", "all_key": "status"},
		{"name": "漏洞级别", "key": "severity_desc", "all_key": "severity_desc.*"},
		{"name": "漏洞类型", "key": "vulnerability_type_desc", "all_key": "vulnerability_type_desc.*"},
		{"name": "漏洞名称", "key": "vulnerability_name", "all_key": "vulnerability_name.*"},
	})
}

func TestWeibuTaskThreats_GetWeibuThreatListDetailFields(t *testing.T) {
	m := NewWeibuTaskThreatsModel()
	fields := m.GetWeibuThreatListDetailFields()

	assert.Equal(t, fields, []map[string]string{
		{"name": "主机IP地址", "key": "machine", "all_key": "machine.*"},
		{"name": "资产机器信息", "key": "assets_machine", "all_key": "assets_machine"},
		{"name": "处置状态", "key": "status", "all_key": "status"},
		{"name": "威胁类型", "key": "threat_type", "all_key": "threat_type"},
		{"name": "主机数", "key": "machine_count", "all_key": "machine_count"},
		{"name": "漏洞级别", "key": "severity_desc", "all_key": "severity_desc.*"},
		{"name": "漏洞类型", "key": "vulnerability_type_desc", "all_key": "vulnerability_type_desc.*"},
		{"name": "漏洞名称", "key": "vulnerability_name.*", "all_key": "vulnerability_name.*"},
		{"name": "最近发现时间", "key": "last_occ_time", "all_key": "last_occ_time"},
	})
}
