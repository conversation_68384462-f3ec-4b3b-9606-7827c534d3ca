package xray

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTaskAssets_IndexName(t *testing.T) {
	assert.Equal(t, "xray_task_assets", NewTaskAssetsModel().IndexName())
	assert.Equal(t, "", NewTaskAssetsModel().String())
}

func TestTaskAssets_GetAssetListFields(t *testing.T) {
	m := NewTaskAssetsModel()
	fields := m.GetAssetListFields()
	assert.Equal(t, fields, []map[string]string{
		{"name": "IP地址", "key": "ip", "all_key": "ip"},
		{"name": "操作系统", "key": "information", "all_key": "information"},
		{"name": "设备类型", "key": "device_type", "all_key": "device_type.*.name"},
		{"name": "mac 地址", "key": "mac_address", "all_key": "mac_address"},
		{"name": "端口", "key": "port", "all_key": "product.*.port"},
		{"name": "协议", "key": "protocol", "all_key": "product.*.service_name"},
		{"name": "组件信息", "key": "application", "all_key": "product.*.application"},
	})
}

func TestTaskAssets_GetAssetListDetailFields(t *testing.T) {
	m := NewTaskAssetsModel()
	fields := m.GetAssetListDetailFields()
	assert.Equal(t, fields, []map[string]string{
		{"name": "IP地址", "key": "ip", "all_key": "ip"},
		{"name": "操作系统", "key": "information", "all_key": "information"},
		{"name": "设备类型", "key": "device_type", "all_key": "device_type.*.name"},
		{"name": "mac 地址", "key": "mac_address", "all_key": "mac_address"},
		{"name": "端口", "key": "port", "all_key": "product.*.port"},
		{"name": "协议", "key": "protocol", "all_key": "product.*.service_name"},
		{"name": "组件信息", "key": "application", "all_key": "product.*.application"},
	})
}
