package file_import

import (
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/initialize/es"
	"github.com/stretchr/testify/require"
	"testing"
	"time"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/assets"
	"fobrain/models/mysql/asset_mapping"
	"fobrain/models/mysql/data_source"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"
)

func TestCheckAssetMapping(t *testing.T) {
	// 模拟有效的数据
	validRows := [][]string{
		// 跳过前两行
		{"host_ip", "host_port", "float_ip", "float_port", "load_ip", "load_port", "ssl_ip", "ssl_port", "dmz_ip", "dmz_port", "finance_ip", "finance_port", "domain"},
		{"***********", "8080", "***********", "8081", "***********", "8082", "***********", "8083", "***********", "8084", "***********", "8085", "example.com"},
		{"***********", "9090", "***********", "9091", "***********", "9092", "***********", "9093", "***********", "9094", "***********", "9095", "example2.com"},
	}

	// 模拟无效的数据
	invalidRows := [][]string{
		{"host_ip", "host_port", "float_ip", "float_port", "load_ip", "load_port", "ssl_ip", "ssl_port", "dmz_ip", "dmz_port", "finance_ip", "finance_port", "domain"},
		{"", "8080", "***********", "8081", "***********", "8082", "***********", "8083", "***********", "8084", "***********", "8085", "example.com"},             // 空 IP
		{"***********", "99999", "***********", "8081", "***********", "8082", "***********", "8083", "***********", "8084", "***********", "8085", "example.com"}, // 无效端口
		{"***********", "8080", "invalid_ip", "8081", "***********", "8082", "***********", "8083", "***********", "8084", "***********", "8085", "example.com"},   // 无效 IP
	}

	// 模拟重复数据
	duplicateRows := [][]string{
		{"***********", "8081", "***********", "8081", "***********", "8082", "***********", "8083", "***********", "8084", "***********", "8085", "example.com"},
		{"***********", "8082", "***********", "8081", "***********", "8082", "***********", "8083", "***********", "8084", "***********", "8085", "example.com"},
		{"***********", "8080", "***********", "8081", "***********", "8082", "***********", "8083", "***********", "8084", "***********", "8085", "example.com"},
	}

	// 测试有效的资产映射数据
	t.Run("valid data", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `asset_mappings`").
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "created_at", "updated_at", "host_ip", "host_port", "float_ip", "float_port",
				"load_ip", "load_port", "ssl_ip", "ssl_port", "dmz_ip", "dmz_port", "finance_ip",
				"finance_port", "domain",
			}).AddRow(
				1, time.Now(), time.Now(), "127.0.0.1", 80, "127.0.0.1", 80,
				"127.0.0.1", 80, "127.0.0.1", 80, "127.0.0.1", 80, "127.0.0.1",
				80, "www.com",
			))
		assetMapping, _, err := CheckAssetMapping(validRows)
		assert.Nil(t, err)
		assert.Equal(t, len(assetMapping), 1)
	})

	// 测试端口号无效
	t.Run("invalid data", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `asset_mappings`").
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "created_at", "updated_at", "host_ip", "host_port", "float_ip", "float_port",
				"load_ip", "load_port", "ssl_ip", "ssl_port", "dmz_ip", "dmz_port", "finance_ip",
				"finance_port", "domain",
			}).AddRow(
				1, time.Now(), time.Now(), "127.0.0.1", 80, "127.0.0.1", 80,
				"127.0.0.1", 80, "127.0.0.1", 80, "127.0.0.1", 80, "127.0.0.1",
				80, "www.com",
			))
		assetMapping, _, err := CheckAssetMapping(invalidRows)
		assert.NotNil(t, err) // 应该返回错误
		assert.Nil(t, assetMapping)
		assert.Equal(t, err.Error(), "第 2 行端口号无效") // 应该指出第 3 行的 IP 无效
	})

	// 成功数据
	t.Run("Success", func(t *testing.T) {
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `asset_mappings`").
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "created_at", "updated_at", "host_ip", "host_port", "float_ip", "float_port",
				"load_ip", "load_port", "ssl_ip", "ssl_port", "dmz_ip", "dmz_port", "finance_ip",
				"finance_port", "domain",
			}).AddRow(
				1, time.Now(), time.Now(), "127.0.0.1", 80, "127.0.0.1", 80,
				"127.0.0.1", 80, "127.0.0.1", 80, "127.0.0.1", 80, "127.0.0.1",
				80, "www.com",
			))
		_, _, err := CheckAssetMapping(duplicateRows)
		assert.Nil(t, err)
	})
}

func TestAddPortsAndDelPort(t *testing.T) {
	// 准备测试数据
	initialData := `{
		"port": 8080,
		"protocol": "http",
		"status": 1,
		"url": "http://example.com",
		"domain": "example.com",
		"title": "Example"
	}`

	ports := []assets.PortInfo{
		{Port: 8080, Protocol: "http", Status: 1, Url: "http://example.com", Domain: "example.com", Title: "Example"},
	}

	// 调用被测试的函数
	result := addPortsAndDelPort(initialData, ports)

	// 验证结果
	if !gjson.Get(result, "ports").Exists() {
		t.Errorf("Expected 'ports' field to exist in result")
	}

	if gjson.Get(result, "port").Exists() {
		t.Errorf("Expected 'port' field to be deleted from result")
	}

	if gjson.Get(result, "protocol").Exists() {
		t.Errorf("Expected 'protocol' field to be deleted from result")
	}

	if gjson.Get(result, "status").Exists() {
		t.Errorf("Expected 'status' field to be deleted from result")
	}

	if gjson.Get(result, "url").Exists() {
		t.Errorf("Expected 'url' field to be deleted from result")
	}

	if gjson.Get(result, "domain").Exists() {
		t.Errorf("Expected 'domain' field to be deleted from result")
	}

	if gjson.Get(result, "title").Exists() {
		t.Errorf("Expected 'title' field to be deleted from result")
	}
}

func TestConvertIsPublicToNetwork(t *testing.T) {
	tests := []struct {
		isPublic    int
		expected    int
		description string
	}{
		{1, 1, "isPublic 为 1 时，应该返回 1"},
		{2, 2, "isPublic 为 2 时，应该返回 2"},
		{0, 3, "isPublic 为 0 时，应该返回 3"},
		{3, 3, "isPublic 为 3 时，应该返回 3"},
		{-1, 3, "isPublic 为负数时，应该返回 3"},
	}

	for _, test := range tests {
		t.Run(test.description, func(t *testing.T) {
			result := convertIsPublicToNetwork(test.isPublic)
			if result != test.expected {
				t.Errorf("convertIsPublicToNetwork(%d) = %d; want %d", test.isPublic, result, test.expected)
			}
		})
	}
}

func TestAddBulkRequest(t *testing.T) {
	// 根据 addBulkRequest 函数签名创建对应的 bulk services
	mockAssetTaskBulkService := es.GetEsClient().Bulk()
	mockAssetProcessBulkService := es.GetEsClient().Bulk()
	mockAssetProcessDeviceBulkService := es.GetEsClient().Bulk()

	node := &data_source.Node{
		SoftDeleteModel: mysql.SoftDeleteModel{
			Id: 1,
		},
		AreaId:   1,
		SourceId: 1,
	}

	data := `{
		"ip": "***********",
		"product": "product1,product2",
		"on_line_status": 1,
		"ports": "[{\"port\": 80, \"protocol\": \"http\"}]",
		"sn": "",
		"custom_fields": "{}"
	}`

	taskId := uint64(1)
	taskChildId := uint64(1)

	addBulkRequest(mockAssetTaskBulkService, mockAssetProcessBulkService, mockAssetProcessDeviceBulkService, node, data, taskId, taskChildId, nil)

	// 验证正确的 bulk service 操作数量
	// addBulkRequest 总是向 assetTaskBulkService 和 assetProcessBulkService 添加请求
	assert.Equal(t, 1, mockAssetTaskBulkService.NumberOfActions())
	assert.Equal(t, 1, mockAssetProcessBulkService.NumberOfActions())
	// 由于没有 sn，不会向 assetProcessDeviceBulkService 添加请求
	assert.Equal(t, 0, mockAssetProcessDeviceBulkService.NumberOfActions())
}

func TestCheckBusinessSystem(t *testing.T) {
	rows := [][]string{
		{"", "", "", "", "", "", "", "", "", "", "", ""}, // Skip first 4 rows
		{"", "", "", "", "", "", "", "", "", "", "", ""},
		{"", "", "", "", "", "", "", "", "", "", "", ""},
		{"", "", "", "", "", "", "", "", "", "", "", ""},
		//{"BusinessName", "Address", "***********", "UserName", "1", "v1.0", "1", "1", "1", "1", "1", "1"},
		{"BusinessName", "Address", "", "", "", "dept", "1", "v1.0", "1", "3", "1", "1", "1", "1", "1", "mark"},
	}

	mockServer := testcommon.NewMockServer()
	defer mockServer.Close()
	mockServer.Register("staff/_search", []*elastic.SearchHit{
		{
			Id:     "1",
			Source: []byte(`{"fid_hash":"123"}`),
		},
	})

	assets, err := CheckBusinessSystem(rows)
	assert.NoError(t, err)
	assert.NoError(t, err)
	assert.Equal(t, 1, len(assets))
	assert.Equal(t, "BusinessName", assets[0].BusinessName)
}

func TestCheckBusinessSystem_InvalidIP(t *testing.T) {
	rows := [][]string{
		{"", "", "", "", "", "", "", "", "", "", "", ""}, // Skip first 4 rows
		{"", "", "", "", "", "", "", "", "", "", "", ""},
		{"", "", "", "", "", "", "", "", "", "", "", ""},
		{"", "", "", "", "", "", "", "", "", "", "", ""},
		{"BusinessName", "Address", "", "", "", "dept", "1", "v1.0", "1", "3", "1", "1", "1", "1", "1", "mark"},
	}

	_, err := CheckBusinessSystem(rows)

	assert.NoError(t, err)
	//assert.Contains(t, err.Error(), "ip格式错误")
}

func TestCheckAssets(t *testing.T) {
	t.Run("传统格式-有效数据", func(t *testing.T) {
		// 传统格式：前4行为空，第5行开始为数据，第一行只有一列（或为空）
		rows := [][]string{
			{""}, // 第一行只有一列，这样 len(rows[0]) == 1，会走传统格式处理
			{""},
			{""},
			{""},
			{"TestArea", "***********", "1", "80", "1", "http", "http://example.com", "example.com", "Title", "1", "***********/24", "HostName", "EthName", "OS", "Kernel", "DeviceId", "DeviceName", "Model", "Maker", "Sn123", "00:1A:2B:3C:4D:5E", "Product", "BusinessSystem", "BusinessOwner", "Oper", "MachineRoom", "1", "1024", "50%", "Intel", "i7", "4", "2", "500", "60%", "0.5"},
		}
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `network_areas`").
			WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "TestArea"))

		result, hasDevice, err := CheckAssets(rows, "name")
		assert.NoError(t, err)
		assert.True(t, hasDevice) // 因为有SN，所以应该有设备

		var assets []AssetValidate
		err = json.Unmarshal([]byte(result), &assets)
		assert.NoError(t, err)
		assert.Equal(t, 1, len(assets))
		assert.Equal(t, "***********", assets[0].Ip)
		assert.Equal(t, "TestArea", assets[0].Area)
		assert.Equal(t, "Sn123", assets[0].Sn)
	})

	t.Run("传统格式-IP为空", func(t *testing.T) {
		rows := [][]string{
			{""}, // 第一行只有一列，走传统格式
			{""},
			{""},
			{""},
			{"TestArea", "", "1", "80", "1", "http", "http://example.com", "example.com", "Title", "1", "***********/24", "HostName", "EthName", "OS", "Kernel", "DeviceId", "DeviceName", "Model", "Maker", "", "00:1A:2B:3C:4D:5E", "Product", "BusinessSystem", "BusinessOwner", "Oper", "MachineRoom", "1", "1024", "50%", "Intel", "i7", "4", "2", "500", "60%", "0.5"},
		}
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `network_areas`").
			WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "TestArea"))

		_, _, err := CheckAssets(rows, "name")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "第 5 行存在格式错误，错误信息：IP格式错误")
	})

	t.Run("传统格式-区域不存在", func(t *testing.T) {
		rows := [][]string{
			{""}, // 第一行只有一列，走传统格式
			{""},
			{""},
			{""},
			{"NonExistentArea", "***********", "1", "80", "1", "http", "http://example.com", "example.com", "Title", "1", "***********/24", "HostName", "EthName", "OS", "Kernel", "DeviceId", "DeviceName", "Model", "Maker", "", "00:1A:2B:3C:4D:5E", "Product", "BusinessSystem", "BusinessOwner", "Oper", "MachineRoom", "1", "1024", "50%", "Intel", "i7", "4", "2", "500", "60%", "0.5"},
		}
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `network_areas`").
			WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "TestArea"))

		_, _, err := CheckAssets(rows, "name")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "所属区域不存在")
	})

	t.Run("标准导出模板格式", func(t *testing.T) {
		rows := [][]string{
			{"IP地址", "所属区域", "端口", "协议", "URL", "网站标题", "域名", "组件信息", "在线状态", "主机名", "IP类型", "MAC地址", "业务系统", "业务负责人", "运维人员"},
			{"***********", "TestArea", "80", "http", "http://example.com", "Example Title", "example.com", "nginx", "在线", "test-host", "IPv4", "00:1A:2B:3C:4D:5E", "TestBusiness", "TestOwner", "TestOper"},
		}

		result, hasDevice, err := CheckAssets(rows, "name")
		assert.NoError(t, err)
		assert.False(t, hasDevice) // 标准模板没有SN字段，所以没有设备

		// 验证返回的是JSON格式的AssetValidate数组
		var assets []*AssetValidate
		err = json.Unmarshal([]byte(result), &assets)
		assert.NoError(t, err)
		assert.Equal(t, 1, len(assets))
		assert.Equal(t, "***********", assets[0].Ip)
		assert.Equal(t, "TestArea", assets[0].Area)
		assert.Equal(t, "1", assets[0].OnLineStatus) // "在线" 应该转换为 "1"
		assert.Equal(t, "1", assets[0].IpType)       // "IPv4" 应该转换为 "1"
	})

	t.Run("数据行数不足", func(t *testing.T) {
		rows := [][]string{
			{""}, // 第一行只有一列，走传统格式
			{""},
		}

		_, _, err := CheckAssets(rows, "name")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "数据行数不足4行")
	})

	t.Run("传统格式-无SN无设备", func(t *testing.T) {
		rows := [][]string{
			{""}, // 第一行只有一列，走传统格式
			{""},
			{""},
			{""},
			{"TestArea", "***********", "1", "80", "1", "http", "http://example.com", "example.com", "Title", "1", "***********/24", "HostName", "EthName", "OS", "Kernel", "DeviceId", "DeviceName", "Model", "Maker", "", "00:1A:2B:3C:4D:5E", "Product", "BusinessSystem", "BusinessOwner", "Oper", "MachineRoom", "1", "1024", "50%", "Intel", "i7", "4", "2", "500", "60%", ""},
		}
		mockDb := testcommon.GetMysqlMock()
		defer mockDb.Close()
		mockDb.ExpectQuery("SELECT * FROM `network_areas`").
			WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "TestArea"))

		result, hasDevice, err := CheckAssets(rows, "name")
		assert.NoError(t, err)
		assert.False(t, hasDevice) // 没有SN，所以没有设备

		var assets []AssetValidate
		err = json.Unmarshal([]byte(result), &assets)
		assert.NoError(t, err)
		assert.Equal(t, 1, len(assets))
		assert.Equal(t, "***********", assets[0].Ip)
		assert.Equal(t, "TestArea", assets[0].Area)
	})

	t.Run("标准导出模板-JSON序列化错误模拟", func(t *testing.T) {
		// 这个测试用例主要是为了覆盖JSON序列化错误的情况
		// 在实际情况下很难触发，但我们可以通过正常数据来验证正常流程
		rows := [][]string{
			{"IP地址", "所属区域"},
			{"***********", "TestArea"},
		}

		result, hasDevice, err := CheckAssets(rows, "name")
		assert.NoError(t, err)
		assert.False(t, hasDevice)

		// 验证返回的是有效的JSON
		var assets []*AssetValidate
		err = json.Unmarshal([]byte(result), &assets)
		assert.NoError(t, err)
		assert.Equal(t, 1, len(assets))
	})
}

func TestHandleExportTemplate(t *testing.T) {
	t.Run("有效的标准导出模板", func(t *testing.T) {
		rows := [][]string{
			{"IP地址", "所属区域", "端口", "协议", "URL", "网站标题", "域名", "组件信息", "在线状态", "主机名", "IP类型", "MAC地址", "业务系统", "业务负责人", "运维人员"},
			{"***********", "TestArea", "80", "http", "http://example.com", "Example Title", "example.com", "nginx", "在线", "test-host", "IPv4", "00:1A:2B:3C:4D:5E", "TestBusiness", "TestOwner", "TestOper"},
			{"***********", "TestArea", "443", "https", "https://example2.com", "Example2 Title", "example2.com", "apache", "离线", "test-host2", "IPv6", "00:1A:2B:3C:4D:5F", "TestBusiness2", "TestOwner2", "TestOper2"},
		}

		assets, err := HandleExportTemplate(rows, "")
		assert.NoError(t, err)
		assert.Equal(t, 2, len(assets))

		// 验证第一条记录
		assert.Equal(t, "***********", assets[0].Ip)
		assert.Equal(t, "TestArea", assets[0].Area)
		assert.Equal(t, "80", assets[0].Port)
		assert.Equal(t, "http", assets[0].Protocol)
		assert.Equal(t, "1", assets[0].OnLineStatus) // "在线" 转换为 "1"
		assert.Equal(t, "1", assets[0].IpType)       // "IPv4" 转换为 "1"

		// 验证第二条记录
		assert.Equal(t, "***********", assets[1].Ip)
		assert.Equal(t, "TestArea", assets[1].Area)
		assert.Equal(t, "443", assets[1].Port)
		assert.Equal(t, "https", assets[1].Protocol)
		assert.Equal(t, "2", assets[1].OnLineStatus) // "离线" 转换为 "2"
		assert.Equal(t, "2", assets[1].IpType)       // "IPv6" 转换为 "2"
	})

	t.Run("IP地址为空", func(t *testing.T) {
		rows := [][]string{
			{"IP地址", "所属区域"},
			{"", "TestArea"},
		}

		_, err := HandleExportTemplate(rows, "")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "IP地址不允许为空")
	})

	t.Run("所属区域为空", func(t *testing.T) {
		rows := [][]string{
			{"IP地址", "所属区域"},
			{"***********", ""},
		}

		_, err := HandleExportTemplate(rows, "")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "所属区域不允许为空")
	})

	t.Run("IP类型错误", func(t *testing.T) {
		rows := [][]string{
			{"IP地址", "所属区域", "IP类型"},
			{"***********", "TestArea", "InvalidType"},
		}

		_, err := HandleExportTemplate(rows, "")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "IP类型错误")
	})

	t.Run("不存在数据", func(t *testing.T) {
		rows := [][]string{
			{"IP地址", "所属区域"},
		}

		_, err := HandleExportTemplate(rows, "")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "不存在数据")
	})

	t.Run("包含未知字段", func(t *testing.T) {
		rows := [][]string{
			{"IP地址", "所属区域", "未知字段"},
			{"***********", "TestArea", "unknown_value"},
		}

		assets, err := HandleExportTemplate(rows, "")
		assert.NoError(t, err)
		assert.Equal(t, 1, len(assets))
		assert.Equal(t, "***********", assets[0].Ip)
		assert.Equal(t, "TestArea", assets[0].Area)
		// 未知字段应该被忽略，不影响处理
	})

	t.Run("在线状态其他值", func(t *testing.T) {
		rows := [][]string{
			{"IP地址", "所属区域", "在线状态"},
			{"***********", "TestArea", "其他状态"},
		}

		assets, err := HandleExportTemplate(rows, "")
		assert.NoError(t, err)
		assert.Equal(t, 1, len(assets))
		assert.Equal(t, "***********", assets[0].Ip)
		// 其他状态值应该保持原样
		assert.Equal(t, "其他状态", assets[0].OnLineStatus)
	})

	t.Run("自定义字段处理", func(t *testing.T) {
		rows := [][]string{
			{"IP地址", "所属区域", "业务系统", "业务负责人", "运维人员"},
			{"***********", "TestArea", "TestBusiness", "TestOwner", "TestOper"},
		}

		assets, err := HandleExportTemplate(rows, "")
		assert.NoError(t, err)
		assert.Equal(t, 1, len(assets))
		assert.Equal(t, "***********", assets[0].Ip)
		assert.Equal(t, "TestArea", assets[0].Area)
		assert.Equal(t, "TestBusiness", assets[0].BusinessSystem)
		assert.Equal(t, "TestOwner", assets[0].BusinessOwner)
		assert.Equal(t, "TestOper", assets[0].Oper)
	})

	t.Run("空行处理", func(t *testing.T) {
		rows := [][]string{
			{"IP地址", "所属区域"},
			{"***********", "TestArea"},
			{"", ""}, // 空行
			{"***********", "TestArea"},
		}

		_, err := HandleExportTemplate(rows, "")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "IP地址不允许为空")
	})

	t.Run("列数不匹配", func(t *testing.T) {
		rows := [][]string{
			{"IP地址", "所属区域", "端口"},
			{"***********", "TestArea"}, // 缺少端口列
		}

		assets, err := HandleExportTemplate(rows, "")
		assert.NoError(t, err)
		assert.Equal(t, 1, len(assets))
		assert.Equal(t, "***********", assets[0].Ip)
		assert.Equal(t, "TestArea", assets[0].Area)
		assert.Equal(t, "", assets[0].Port) // 缺少的列应该为空
	})
}

func TestCheckThreats(t *testing.T) {
	rows := [][]string{
		{"", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""}, // Skip first 4 rows
		{"", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""},
		{"", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""},
		{"", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""},
		{"Area", "***********", "80", "http://example.com", "1", "VulName", "VulType", "1", "CVE-2023-0001", "CNVD-2023-0001", "CNNVD-2023-0001", "1", "1", "1", "Description", "Details", "Hazard", "Suggestions", "LastResponse"},
	}

	result, err := CheckThreats(rows)
	assert.NoError(t, err)

	var threats []ThreatValidate
	err = json.Unmarshal([]byte(result), &threats)
	assert.NoError(t, err)
	assert.Equal(t, 1, len(threats))
	assert.Equal(t, "***********", threats[0].Ip)
}

func TestCheckThreats_InvalidIP(t *testing.T) {
	rows := [][]string{
		{"", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""}, // Skip first 4 rows
		{"", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""},
		{"", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""},
		{"", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""},
		{"Area", "InvalidIP", "80", "http://example.com", "1", "VulName", "VulType", "1", "CVE-2023-0001", "CNVD-2023-0001", "CNNVD-2023-0001", "1", "1", "1", "Description", "Details", "Hazard", "Suggestions", "LastResponse"},
	}

	_, err := CheckThreats(rows)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "IP必须是一个有效的IP地址")
}

func TestCheckPeoples(t *testing.T) {
	rows := [][]string{
		{"", "", "", "", "", "", "", ""}, // Skip first 4 rows
		{"", "", "", "", "", "", "", ""},
		{"", "", "", "", "", "", "", ""},
		{"", "", "", "", "", "", "", ""},
		{"Area", "Name", "EnglishName", "Title", "15534563457", "<EMAIL>", "Department", "1"},
	}

	result, err := CheckPeoples(rows, "name")
	assert.NoError(t, err)

	var peoples []PeopleValidate
	err = json.Unmarshal([]byte(result), &peoples)
	assert.NoError(t, err)
	assert.Equal(t, 1, len(peoples))
	assert.Equal(t, "Name", peoples[0].Name)
}

func TestCheckPeoples_InvalidPhone(t *testing.T) {
	rows := [][]string{
		{"", "", "", "", "", "", "", ""}, // Skip first 4 rows
		{"", "", "", "", "", "", "", ""},
		{"", "", "", "", "", "", "", ""},
		{"", "", "", "", "", "", "", ""},
		{"Area", "Name", "EnglishName", "Title", "InvalidPhone", "<EMAIL>", "Department", "1"},
	}

	_, err := CheckPeoples(rows, "name")
	assert.Nil(t, err)
}

func TestHasDuplicateHostIPPort(t *testing.T) {
	tests := []struct {
		name     string
		assets   []asset_mapping.AssetMapping
		expected bool
	}{
		{
			name: "No duplicates",
			assets: []asset_mapping.AssetMapping{
				{HostIp: "***********", HostPort: intPtr(80)},
				{HostIp: "***********", HostPort: intPtr(8080)},
			},
			expected: false,
		},
		{
			name: "With duplicates",
			assets: []asset_mapping.AssetMapping{
				{HostIp: "***********", HostPort: intPtr(80)},
				{HostIp: "***********", HostPort: intPtr(80)},
			},
			expected: true,
		},
		{
			name: "Different ports",
			assets: []asset_mapping.AssetMapping{
				{HostIp: "***********", HostPort: intPtr(80)},
				{HostIp: "***********", HostPort: intPtr(8080)},
			},
			expected: false,
		},
		{
			name: "Different IPs",
			assets: []asset_mapping.AssetMapping{
				{HostIp: "***********", HostPort: intPtr(80)},
				{HostIp: "***********", HostPort: intPtr(80)},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := hasDuplicateHostIPPort(tt.assets)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func intPtr(i int) *int {
	return &i
}

func TestCheckIP(t *testing.T) {
	tests := []struct {
		name     string
		ipString string
		row      int
		expected error
	}{
		{
			name:     "Valid IP",
			ipString: "***********",
			row:      1,
			expected: nil,
		},
		{
			name:     "Empty IP",
			ipString: "",
			row:      2,
			expected: nil,
		},
		{
			name:     "Invalid IP",
			ipString: "InvalidIP",
			row:      3,
			expected: errors.New("第 3 行IP地址无效"),
		},
		{
			name:     "Another Invalid IP",
			ipString: "256.256.256.256",
			row:      4,
			expected: errors.New("第 4 行IP地址无效"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := checkIP(tt.ipString, tt.row)
			if tt.expected != nil {
				assert.EqualError(t, err, tt.expected.Error())
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestConvertProcessDevice(t *testing.T) {
	assetProcess := &assets.ProcessAssets{
		Sn:              "12345",
		HostName:        "test-host",
		Os:              "Linux",
		Kernel:          "5.4",
		DeviceId:        "67890",
		DeviceName:      "Device 1",
		Maker:           "Maker A",
		Mac:             "00:0a:95:9d:68:16",
		MachineRoom:     "Room 1",
		Status:          1,
		MemorySize:      "16384",
		MemoryUsageRate: "80.5",
		CpuMaker:        "Intel",
		CpuBrand:        "Xeon",
		CpuCount:        4,
		DiskCount:       2,
		DiskSize:        500,
		DiskUsageRate:   "60.2",
		LoadAverage:     "1.2",
	}

	param := ConvertPublicParam{
		TaskId:       1,
		TaskChildId:  1,
		AssetTaskId:  "asset-task-123",
		NetworkCards: "***********,********,**********",
	}
	node := &data_source.Node{
		SourceId:       1,
		AreaId:         1,
		Name:           "",
		Source:         "",
		Types:          "",
		Status:         0,
		Use:            0,
		LastTaskAt:     nil,
		LastTaskStatus: 0,
		LastSyncResult: "",
		Configs:        nil,
		DataTypes:      "",
		Mode:           0,
	}
	// Call the function to test
	result := convertProcessDevice(node, assetProcess, param, map[string]string{})
	fmt.Printf("-=======%+v", result)
	// Assertions to check if the conversion is correct
	assert.Equal(t, "12345", result.UniqueKey)
	assert.Equal(t, 1, result.Area)
	assert.Equal(t, "test-host", result.HostName)
	assert.Equal(t, "Linux", result.Os)
	assert.Equal(t, "5.4", result.Kernel)
	assert.NotNil(t, result.PrivateIp)
	assert.Nil(t, result.PublicIp)
}
func TestCheckPortInfo_ValidCase(t *testing.T) {
	asset := &AssetValidate{
		Port:   "8080,80",
		Status: "1,2",
		Url:    "https://example.com/path",
		Domain: "example.com,www.test.com",
		Ip:     "***********",
	}
	err := CheckPortInfo(asset)
	assert.NoError(t, err)
}

func TestCheckPortInfo_InvalidPort(t *testing.T) {
	asset := &AssetValidate{
		Port: "a,8080", // 第一个端口无效
		Ip:   "127.0.0.1",
	}
	err := CheckPortInfo(asset)
	assert.Error(t, err)
	assert.EqualError(t, err, "端口格式错误")
}

func TestCheckPortInfo_InvalidStatus(t *testing.T) {
	asset := &AssetValidate{
		Status: "active,2", // 第一个状态无效
		Ip:     "127.0.0.1",
	}
	err := CheckPortInfo(asset)
	assert.Error(t, err)
	assert.EqualError(t, err, "状态格式错误")
}

func TestCheckPortInfo_InvalidUrl(t *testing.T) {
	asset := &AssetValidate{
		Url: "xxx", // 无效URL
		Ip:  "127.0.0.1",
	}
	err := CheckPortInfo(asset)
	assert.Error(t, err)
	assert.EqualError(t, err, "URL格式错误")
}

func TestCheckPortInfo_InvalidDomain(t *testing.T) {
	asset := &AssetValidate{
		Domain: "invalid-domain", // 无效域名
		Ip:     "127.0.0.1",
	}
	err := CheckPortInfo(asset)
	assert.Error(t, err)
	assert.EqualError(t, err, "域名格式错误")
}

func TestCheckPortInfo_EmptyIp(t *testing.T) {
	asset := &AssetValidate{
		Ip: "",
	}
	err := CheckPortInfo(asset)
	assert.Error(t, err)
	assert.EqualError(t, err, "IP格式错误或未填写")
}

func TestCheckPortInfo_InvalidIp(t *testing.T) {
	asset := &AssetValidate{
		Ip: "invalid_ip",
	}
	err := CheckPortInfo(asset)
	assert.Error(t, err)
	assert.EqualError(t, err, "IP格式错误或未填写")
}

func TestCheckPortInfo_MultiplePorts_Invalid(t *testing.T) {
	asset := &AssetValidate{
		Port: "8080,invalid,80", // 中间端口无效
		Ip:   "127.0.0.1",
	}
	err := CheckPortInfo(asset)
	assert.Error(t, err)
	assert.EqualError(t, err, "端口格式错误")
}

func TestCheckPortInfo_MixedErrors(t *testing.T) {
	asset := &AssetValidate{
		Port:   "xxx0,111",   // 端口错误
		Url:    "bad-url",    // URL错误
		Ip:     "invalid_ip", // IP错误
		Domain: "bad.domain", // 域名错误
	}
	err := CheckPortInfo(asset)
	require.Error(t, err)
	assert.Contains(t, err.Error(), "端口格式错误") // 返回第一个错误
}
