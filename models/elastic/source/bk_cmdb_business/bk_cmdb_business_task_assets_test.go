package bk_cmdb_business

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestBKCmdbBusinessTaskAssets_IndexName(t *testing.T) {
	assert.Equal(t, "bk_cmdb_business_task_assets", NewBKCmdbBusinessTaskAssetsModel().IndexName())
	assert.Equal(t, "", NewBKCmdbBusinessTaskAssetsModel().String())
}

func TestBkCmdbBusinessAssets_GetAssetListFields(t *testing.T) {
	fields := NewBKCmdbBusinessTaskAssetsModel().GetAssetListFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "ID", "key": "bk_biz_id", "all_key": "bk_biz_id"},
		{"name": "业务系统名称", "key": "bk_biz_name", "all_key": "bk_biz_name"},
		{"name": "运维人员", "key": "biz_sysmanager", "all_key": "biz_sysmanager"},
		{"name": "部门名称", "key": "belong_org", "all_key": "belong_org"},
		{"name": "业务系统负责人", "key": "biz_sysadminer", "all_key": "biz_sysadminer"},
		{"name": "供应商", "key": "supplier", "all_key": "supplier"},
		{"name": "时区", "key": "time_zone", "all_key": "time_zone"},
		{"name": "地址", "key": "main_page_url", "all_key": "main_page_url"},
		{"name": "摘要", "key": "app_brief", "all_key": "app_brief"},
	}
	assert.Equal(t, expectedFields, fields, "字段不匹配")
}

func TestBkCmdbBusinessAssets_GetAssetListDetailFields(t *testing.T) {
	fields := NewBKCmdbBusinessTaskAssetsModel().GetAssetListDetailFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "ID", "key": "bk_biz_id", "all_key": "bk_biz_id"},
		{"name": "业务系统名称", "key": "bk_biz_name", "all_key": "bk_biz_name"},
		{"name": "运维人员", "key": "biz_sysmanager", "all_key": "biz_sysmanager"},
		{"name": "部门名称", "key": "belong_org", "all_key": "belong_org"},
		{"name": "业务系统负责人", "key": "biz_sysadminer", "all_key": "biz_sysadminer"},
		{"name": "供应商", "key": "supplier", "all_key": "supplier"},
		{"name": "时区", "key": "time_zone", "all_key": "time_zone"},
		{"name": "地址", "key": "main_page_url", "all_key": "main_page_url"},
		{"name": "摘要", "key": "app_brief", "all_key": "app_brief"},
	}
	assert.Equal(t, expectedFields, fields, "字段不匹配")
}
