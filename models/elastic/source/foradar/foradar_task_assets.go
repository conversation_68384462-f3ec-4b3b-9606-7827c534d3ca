package foradar

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/olivere/elastic/v7"
)

type (
	ForadarTaskAssets struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`      //Task+NODE+区域+IP 作为唯一值去更新，用作记录每次任务拉取数据
		TaskId        string          `json:"task_id"` //
		ChildTaskId   string          `json:"child_task_id"`
		CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
		UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
	}
)

// IndexName 索引名
func (f *ForadarTaskAssets) IndexName() string {
	return "foradar_task_assets"
}

func (f *ForadarTaskAssets) String() string {
	if f == nil {
		return ""
	}
	return ""
}

func NewForadarTaskAssetsModel() *ForadarTaskAssets {
	return &ForadarTaskAssets{}
}

func (f *ForadarTaskAssets) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("ip").
		Field("host_list.title.keyword").
		Field("host_list.protocol.keyword").
		Field("host_list.domain.keyword")

	return field
}

func (f *ForadarTaskAssets) GetAssetListFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "端口", "key": "port", "show_value": "detail"},
		{"name": "协议", "key": "protocol", "show_value": "detail"},
		{"name": "URL", "key": "url", "show_value": "host_list"},
		{"name": "网站标题", "key": "title", "show_value": "host_list"},
		{"name": "域名", "key": "domain", "show_value": "host_list"},
		{"name": "状态码", "key": "http_status_code", "show_value": "host_list"},
		{"name": "组件", "key": "cn_product", "show_value": "rule_tags"},
		{"name": "企业名称", "key": "clue_company_name"},
	}
}

func (f *ForadarTaskAssets) GetAssetListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "端口", "key": "port", "show_value": "detail"},
		{"name": "协议", "key": "protocol", "show_value": "detail"},
		{"name": "URL", "key": "url", "show_value": "host_list"},
		{"name": "网站标题", "key": "title", "show_value": "host_list"},
		{"name": "域名", "key": "domain", "show_value": "host_list"},
		{"name": "状态码", "key": "http_status_code", "show_value": "host_list"},
		{"name": "组件", "key": "cn_product", "show_value": "rule_tags"},
		{"name": "企业名称", "key": "clue_company_name"},
		{"name": "数据来源", "key": "assets_source", "show_value": "host_list"},
		{"name": "更新时间", "key": "last_update_time"},
		{"name": "创建时间", "key": "create_time"},
	}
}
