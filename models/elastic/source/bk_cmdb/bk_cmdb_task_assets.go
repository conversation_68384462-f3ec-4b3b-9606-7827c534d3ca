package bk_cmdb

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/olivere/elastic/v7"
)

type (
	BKCmdbTaskAssets struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`      //Task+NODE+区域+IP 作为唯一值去更新，用作记录每次任务拉取数据
		TaskId        string          `json:"task_id"` //
		ChildTaskId   string          `json:"child_task_id"`
		CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
		UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
	}
)

// IndexName 索引名
func (b *BKCmdbTaskAssets) IndexName() string {
	return "bk_cmdb_task_assets"
}

func (b *BKCmdbTaskAssets) String() string {
	if b == nil {
		return ""
	}
	return ""
}

func NewBKCmdbTaskAssetsModel() *BKCmdbTaskAssets {
	return &BKCmdbTaskAssets{}
}

func (b *BKCmdbTaskAssets) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("bk_host_outerip.keyword").
		Field("bk_os_bit.keyword").
		Field("bk_comment.keyword").
		Field("bk_sn.keyword").
		Field("bk_host_innerip.keyword").
		Field("area_id.keyword").
		Field("bk_os_version.keyword").
		Field("bk_mem.keyword").
		Field("bk_cpu_module.keyword").
		Field("bk_state_name.keyword").
		Field("bk_isp_name.keyword").
		Field("bk_province_name.keyword").
		Field("bk_disk.keyword").
		Field("bk_os_type.keyword").
		Field("bk_host_name.keyword")

	return field
}

func (b *BKCmdbTaskAssets) GetAssetListFields() []map[string]string {
	return []map[string]string{
		{"name": "主机ID", "key": "bk_host_id"},
		{"name": "内网IP地址", "key": "bk_host_innerip"},
		{"name": "云区域", "key": "bk_cloud_id"},
		{"name": "主要维护人", "key": "operator"},
		{"name": "操作系统", "key": "bk_os_name"},
		{"name": "版本号", "key": "bk_os_version"},
		{"name": "设备SN", "key": "bk_sn"},
		{"name": "CPU厂商", "key": "bk_cpu_module"},
		{"name": "CPU品牌", "key": "bk_cpu_architecture"},
		{"name": "CPU核心数", "key": "bk_cpu"},
	}
}

func (b *BKCmdbTaskAssets) GetAssetListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "主机ID", "key": "bk_host_id"},
		{"name": "内网IP地址", "key": "bk_host_innerip"},
		{"name": "云区域", "key": "bk_cloud_id"},
		{"name": "主要维护人", "key": "operator"},
		{"name": "操作系统", "key": "bk_os_name"},
		{"name": "版本号", "key": "bk_os_version"},
		{"name": "设备SN", "key": "bk_sn"},
		{"name": "CPU厂商", "key": "bk_cpu_module"},
		{"name": "CPU品牌", "key": "bk_cpu_architecture"},
		{"name": "CPU核心数", "key": "bk_cpu"},
		{"name": "操作系统类型", "key": "bk_os_type"}, //1:Linux;2:Windows;3:AIX
		{"name": "内网MAC地址", "key": "bk_mac"},
		{"name": "操作系统位数", "key": "bk_os_bit"},
	}
}
