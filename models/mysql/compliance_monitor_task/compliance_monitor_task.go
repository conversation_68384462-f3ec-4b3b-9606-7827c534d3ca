package compliance_monitor_task

import (
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/mysql"
)

type ComplianceMonitorTask struct {
	mysql.BaseModel
	ComplianceMonitorId uint64         `json:"compliance_monitor_id"`
	StartAt             localtime.Time `json:"start_at"`
	EndAt               localtime.Time `json:"end_at"`
	Status              int            `json:"status"`
	Message             string         `json:"message"`
	AbnormalAsset       uint64         `json:"abnormal_asset"`
}

const StatusNotStart = 1
const StatusDoing = 2
const StatusSuccess = 3
const StatusFail = 4

// TableName 表名
func (m *ComplianceMonitorTask) TableName() string {
	return "compliance_monitor_tasks"
}

func NewComplianceMonitorTaskModel() *ComplianceMonitorTask {
	return &ComplianceMonitorTask{BaseModel: mysql.BaseModel{DB: mysql.GetDbClient().Table((&ComplianceMonitorTask{}).TableName())}}
}

func (m *ComplianceMonitorTask) Create(item *ComplianceMonitorTask) error {
	return m.DB.Table(m.TableName()).Create(item).Error
}

func (m *ComplianceMonitorTask) First(opts ...mysql.HandleFunc) (ComplianceMonitorTask, error) {
	query := m.DB.Table(m.TableName())
	for _, opt := range opts {
		opt(query)
	}
	var s ComplianceMonitorTask
	err := query.First(&s).Error
	if err != nil {
		return ComplianceMonitorTask{}, err
	}
	return s, nil
}

func (m *ComplianceMonitorTask) List(page, size int, opts ...mysql.HandleFunc) ([]ComplianceMonitorTask, int64, error) {
	query := m.DB.Table(m.TableName())

	for _, opt := range opts {
		opt(query)
	}

	var total int64
	if !mysql.IsPageAll(page, size) {
		if err := query.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		query.Scopes(mysql.PageLimit(page, size))
	}

	var list = make([]ComplianceMonitorTask, 0)
	if err := query.Find(&list).Error; err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

func (m *ComplianceMonitorTask) Update(item ComplianceMonitorTask, opts ...mysql.HandleFunc) error {
	query := m.DB.Model(&ComplianceMonitorTask{})
	for _, f := range opts {
		f(query)
	}
	return query.Where("`id` = ?", item.Id).Updates(item).Error
}

func (m *ComplianceMonitorTask) Count(opts ...mysql.HandleFunc) (int64, error) {
	var num int64

	query := m.DB.Model(&ComplianceMonitorTask{})
	for _, f := range opts {
		f(query)
	}

	err := query.Count(&num).Error
	return num, err
}

func (m *ComplianceMonitorTask) Delete(opts ...mysql.HandleFunc) error {

	query := m.DB.Model(&ComplianceMonitorTask{})
	for _, f := range opts {
		f(query)
	}

	return query.Delete(&ComplianceMonitorTask{}).Error
}
