package bk_cmdb_f5

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/olivere/elastic/v7"
)

type (
	BkCmdbF5VsTaskAssets struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`         // NODE+区域+IP 作为唯一值去更新，用作数据对比
		CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
		UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
	}
)

func (b *BkCmdbF5VsTaskAssets) IndexName() string {
	return "bk_cmdb_f5_vs_task_assets"
}

func (b *BkCmdbF5VsTaskAssets) String() string {
	return ""
}

func NewBkCmdbF5VsTaskAssetsModel() *BkCmdbF5VsTaskAssets {
	return &BkCmdbF5VsTaskAssets{}
}

func (b *BkCmdbF5VsTaskAssets) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("bk_inst_id.keyword").
		Field("bk_inst_name.keyword").
		Field("f5_ip.keyword").
		Field("ip_addr.keyword").
		Field("port.keyword").
		Field("iapp.keyword").
		Field("irule.keyword")
	return field
}

func (b *BkCmdbF5VsTaskAssets) GetAssetListFields() []map[string]string {
	return []map[string]string{
		{"name": "ID", "key": "bk_inst_id", "all_key": "bk_inst_id"},
		{"name": "实例名", "key": "bk_inst_name", "all_key": "bk_inst_name"},
		{"name": "Ip地址", "key": "f5_ip", "all_key": "f5_ip"},
		{"name": "VS的负载IP地址", "key": "ip_addr", "all_key": "ip_addr"},
		{"name": "VS的负载端口", "key": "port", "all_key": "port"},
		{"name": "iApps", "key": "iapp", "all_key": "iapp"},
		{"name": "iRule名称", "key": "irule", "all_key": "irule"},
	}
}

func (b *BkCmdbF5VsTaskAssets) GetAssetListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "ID", "key": "bk_inst_id", "all_key": "bk_inst_id"},
		{"name": "实例名", "key": "bk_inst_name", "all_key": "bk_inst_name"},
		{"name": "Ip地址", "key": "f5_ip", "all_key": "f5_ip"},
		{"name": "VS的负载IP地址", "key": "ip_addr", "all_key": "ip_addr"},
		{"name": "VS的负载端口", "key": "port", "all_key": "port"},
		{"name": "iApps", "key": "iapp", "all_key": "iapp"},
		{"name": "iRule名称", "key": "irule", "all_key": "irule"},
	}
}
