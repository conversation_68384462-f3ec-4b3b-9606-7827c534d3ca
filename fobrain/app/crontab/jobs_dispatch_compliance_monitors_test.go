package crontab

import (
	log "fobrain/fobrain/logs"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/compliance_monitor"
	"fobrain/models/mysql/compliance_monitor_task"
	"fobrain/models/mysql/crontab"
	"fobrain/pkg/utils/common_logs"
	"git.gobies.org/caasm/fobrain-components/utils/localtime"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
	"strconv"
	"testing"
	"time"
)

func TestJob_DispatchComplianceMonitors1(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "List", []compliance_monitor.ComplianceMonitor{
			compliance_monitor.ComplianceMonitor{
				BaseModel: mysql.BaseModel{Id: 1},
				Event:     compliance_monitor.EventEveryDay,
				LastTime:  &localtime.Time{},
				Date:      strconv.Itoa(int(time.Now().Weekday())),
			},
		}, int64(1), nil),
		gomonkey.ApplyFuncReturn(log.GetLogger, &common_logs.Logger{
			Logger: &zap.Logger{},
		}),
		gomonkey.ApplyMethodReturn(&common_logs.Logger{}, "Errorf"),
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "GetLocalTimeByEvent", localtime.Time{}, nil),
		gomonkey.ApplyMethodReturn(&compliance_monitor_task.ComplianceMonitorTask{}, "Count", int64(0), nil),
		gomonkey.ApplyMethodReturn(&compliance_monitor_task.ComplianceMonitorTask{}, "Create", nil),
	}
	j := &Job{}
	res := j.DispatchComplianceMonitors(&Cron{}, &crontab.CrontabModel{})
	for _, patch := range patches {
		patch.Reset()
	}
	assert.NotNil(t, res)
}

func TestJob_DispatchComplianceMonitors2(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "List", []compliance_monitor.ComplianceMonitor{
			compliance_monitor.ComplianceMonitor{
				BaseModel: mysql.BaseModel{Id: 1},
				Event:     compliance_monitor.EventEveryWeek,
				LastTime:  &localtime.Time{},
				Date:      strconv.Itoa(int(time.Now().Weekday())),
			},
		}, int64(1), nil),
		gomonkey.ApplyFuncReturn(log.GetLogger, &common_logs.Logger{
			Logger: &zap.Logger{},
		}),
		gomonkey.ApplyMethodReturn(&common_logs.Logger{}, "Errorf"),
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "GetLocalTimeByEvent", localtime.Time{}, nil),
		gomonkey.ApplyMethodReturn(&compliance_monitor_task.ComplianceMonitorTask{}, "Count", int64(0), nil),
		gomonkey.ApplyMethodReturn(&compliance_monitor_task.ComplianceMonitorTask{}, "Create", nil),
	}
	j := &Job{}
	res := j.DispatchComplianceMonitors(&Cron{}, &crontab.CrontabModel{})
	for _, patch := range patches {
		patch.Reset()
	}
	assert.NotNil(t, res)
}

func TestJob_DispatchComplianceMonitors3(t *testing.T) {
	patches := []*gomonkey.Patches{
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "List", []compliance_monitor.ComplianceMonitor{
			compliance_monitor.ComplianceMonitor{
				BaseModel: mysql.BaseModel{Id: 1},
				Event:     compliance_monitor.EventEveryMonth,
				LastTime:  &localtime.Time{},
				Date:      strconv.Itoa(int(time.Now().Day())),
			},
		}, int64(1), nil),
		gomonkey.ApplyFuncReturn(log.GetLogger, &common_logs.Logger{
			Logger: &zap.Logger{},
		}),
		gomonkey.ApplyMethodReturn(&common_logs.Logger{}, "Errorf"),
		gomonkey.ApplyMethodReturn(&compliance_monitor.ComplianceMonitor{}, "GetLocalTimeByEvent", localtime.Time{}, nil),
		gomonkey.ApplyMethodReturn(&compliance_monitor_task.ComplianceMonitorTask{}, "Count", int64(0), nil),
		gomonkey.ApplyMethodReturn(&compliance_monitor_task.ComplianceMonitorTask{}, "Create", nil),
	}
	j := &Job{}
	res := j.DispatchComplianceMonitors(&Cron{}, &crontab.CrontabModel{})
	for _, patch := range patches {
		patch.Reset()
	}
	assert.NotNil(t, res)
}

func TestRegisterDispatchComplianceMonitors(t *testing.T) {
	res := RegisterIpMappingAlarm()
	assert.NotNil(t, res)
}
