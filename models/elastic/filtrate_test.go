package elastic

import (
	"encoding/json"
	"testing"

	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/assert"
)

func TestParseQueryConditions(t *testing.T) {
	str := []string{
		`{"ip.keyword":["*************"],"operation_type_string":"in","condition":"or"}`,
		`{"ip.keyword":["*************"],"operation_type_string":"in","condition":"or"}`,
	}
	conditions, err := ParseQueryConditions(str)
	assert.Nil(t, err)
	assert.Equal(t, 2, len(conditions))
}

func TestBuildBoolQuery(t *testing.T) {
	tests := []struct {
		name                  string
		field                 string
		operationTypeString   string
		logicalConnective     string
		params                interface{}
		expectedShouldLength  int
		expectedMustLength    int
		expectedMustNotLength int
	}{
		{
			name:                  "Test Terms Query with OR",
			field:                 "ip.keyword",
			operationTypeString:   "in",
			logicalConnective:     "or",
			params:                []string{"*************", "************"},
			expectedShouldLength:  2, // Expected length based on the condition
			expectedMustLength:    0,
			expectedMustNotLength: 0,
		},
		{
			name:                  "Test Term Query with AND",
			field:                 "ip.keyword",
			operationTypeString:   "==",
			logicalConnective:     "and",
			params:                []string{"*************"},
			expectedShouldLength:  0,
			expectedMustLength:    1,
			expectedMustNotLength: 0,
		},
		{
			name:                  "Test Must Not Query with != operator",
			field:                 "ip.keyword",
			operationTypeString:   "!==",
			logicalConnective:     "and",
			params:                []string{"************"},
			expectedShouldLength:  0,
			expectedMustLength:    0,
			expectedMustNotLength: 1,
		},
		{
			name:                  "Test Must Not Query with != operator",
			field:                 "rule_infos.product",
			operationTypeString:   "!==",
			logicalConnective:     "and",
			params:                []string{"支撑系统"},
			expectedShouldLength:  0,
			expectedMustLength:    0,
			expectedMustNotLength: 1,
		},
		{
			name:                  "Test Must Not Query with in operator",
			field:                 "rule_infos.product",
			operationTypeString:   "in",
			logicalConnective:     "and",
			params:                []string{"支撑系统"},
			expectedShouldLength:  0,
			expectedMustLength:    0,
			expectedMustNotLength: 1,
		},
		{
			name:                  "Test Must Not Query with == operator",
			field:                 "rule_infos.product",
			operationTypeString:   "==",
			logicalConnective:     "and",
			params:                []string{"支撑系统"},
			expectedShouldLength:  0,
			expectedMustLength:    0,
			expectedMustNotLength: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			boolQuery := elastic.NewBoolQuery()
			boolQuery = BuildBoolQuery(tt.field, tt.operationTypeString, tt.logicalConnective, tt.params, boolQuery)
		})
	}
}

// TestBusinessDepartmentTermsQuery 测试 BusinessDepartmentTermsQuery 函数
func TestBusinessDepartmentTermsQuery(t *testing.T) {
	// 测试用例
	testCases := []struct {
		name     string
		names    []interface{}
		field    string
		expected map[string]interface{}
	}{
		{
			name:  "单个部门名称",
			names: []interface{}{"研发部"},
			field: "business_department.name.keyword",
			expected: map[string]interface{}{
				"bool": map[string]interface{}{
					"should": []interface{}{
						map[string]interface{}{
							"nested": map[string]interface{}{
								"path": "business_department.parents",
								"query": map[string]interface{}{
									"bool": map[string]interface{}{
										"must": map[string]interface{}{
											"terms": map[string]interface{}{
												"business_department.parents.name.keyword": []interface{}{"研发部"},
											},
										},
									},
								},
							},
						},
						map[string]interface{}{
							"nested": map[string]interface{}{
								"path": "business_department",
								"query": map[string]interface{}{
									"bool": map[string]interface{}{
										"must": map[string]interface{}{
											"terms": map[string]interface{}{
												"business_department.name.keyword": []interface{}{"研发部"},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
		{
			name:  "多个部门名称",
			names: []interface{}{"研发部", "测试部", "运维部"},
			field: "department_base.name.keyword",
			expected: map[string]interface{}{
				"bool": map[string]interface{}{
					"should": []interface{}{
						map[string]interface{}{
							"nested": map[string]interface{}{
								"path": "department_base.parents",
								"query": map[string]interface{}{
									"bool": map[string]interface{}{
										"must": map[string]interface{}{
											"terms": map[string]interface{}{
												"department_base.parents.name.keyword": []interface{}{"研发部", "测试部", "运维部"},
											},
										},
									},
								},
							},
						},
						map[string]interface{}{
							"nested": map[string]interface{}{
								"path": "department_base",
								"query": map[string]interface{}{
									"bool": map[string]interface{}{
										"must": map[string]interface{}{
											"terms": map[string]interface{}{
												"department_base.name.keyword": []interface{}{"研发部", "测试部", "运维部"},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
		{
			name:  "空部门名称列表",
			names: []interface{}{},
			field: "oper_department.name.keyword",
			expected: map[string]interface{}{
				"bool": map[string]interface{}{
					"should": []interface{}{
						map[string]interface{}{
							"nested": map[string]interface{}{
								"path": "oper_department.parents",
								"query": map[string]interface{}{
									"bool": map[string]interface{}{
										"must": map[string]interface{}{
											"terms": map[string]interface{}{
												"oper_department.parents.name.keyword": []interface{}{},
											},
										},
									},
								},
							},
						},
						map[string]interface{}{
							"nested": map[string]interface{}{
								"path": "oper_department",
								"query": map[string]interface{}{
									"bool": map[string]interface{}{
										"must": map[string]interface{}{
											"terms": map[string]interface{}{
												"oper_department.name.keyword": []interface{}{},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
		{
			name:  "非字符串类型的部门名称",
			names: []interface{}{123, true, 45.67},
			field: "business_department.name.keyword",
			expected: map[string]interface{}{
				"bool": map[string]interface{}{
					"should": []interface{}{
						map[string]interface{}{
							"nested": map[string]interface{}{
								"path": "business_department.parents",
								"query": map[string]interface{}{
									"bool": map[string]interface{}{
										"must": map[string]interface{}{
											"terms": map[string]interface{}{
												"business_department.parents.name.keyword": []interface{}{"123", "true", "45.67"},
											},
										},
									},
								},
							},
						},
						map[string]interface{}{
							"nested": map[string]interface{}{
								"path": "business_department",
								"query": map[string]interface{}{
									"bool": map[string]interface{}{
										"must": map[string]interface{}{
											"terms": map[string]interface{}{
												"business_department.name.keyword": []interface{}{"123", "true", "45.67"},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}

	// 执行测试用例
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 调用被测试函数
			query := BusinessDepartmentTermsQuery(tc.names, tc.field)
			
			// 将查询转换为 Source
			source, err := query.Source()
			assert.NoError(t, err, "转换查询为Source时不应有错误")
			
			// 将 Source 转换为 JSON 字符串
			actualJSON, err := json.Marshal(source)
			assert.NoError(t, err, "将Source转换为JSON时不应有错误")
			
			// 将预期结果转换为 JSON 字符串
			expectedJSON, err := json.Marshal(tc.expected)
			assert.NoError(t, err, "将预期结果转换为JSON时不应有错误")
			
			// 比较实际结果和预期结果
			assert.JSONEq(t, string(expectedJSON), string(actualJSON), "生成的查询与预期不符")
		})
	}
}

// TestBusinessDepartmentQuery 测试 BusinessDepartmentQuery 函数
func TestBusinessDepartmentQuery(t *testing.T) {
	// 测试用例
	testCases := []struct {
		name     string
		deptName string
		field    string
		expected map[string]interface{}
	}{
		{
			name:     "基本部门查询",
			deptName: "研发部",
			field:    "business_department.name.keyword",
			expected: map[string]interface{}{
				"bool": map[string]interface{}{
					"should": []interface{}{
						map[string]interface{}{
							"nested": map[string]interface{}{
								"path": "business_department.parents",
								"query": map[string]interface{}{
									"bool": map[string]interface{}{
										"must": map[string]interface{}{
											"term": map[string]interface{}{
												"business_department.parents.name.keyword": "研发部",
											},
										},
									},
								},
							},
						},
						map[string]interface{}{
							"nested": map[string]interface{}{
								"path": "business_department",
								"query": map[string]interface{}{
									"bool": map[string]interface{}{
										"must": map[string]interface{}{
											"term": map[string]interface{}{
												"business_department.name.keyword": "研发部",
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
		{
			name:     "多级部门路径",
			deptName: "技术部/研发组/后端团队",
			field:    "department_base.name.keyword",
			expected: map[string]interface{}{
				"bool": map[string]interface{}{
					"should": []interface{}{
						map[string]interface{}{
							"nested": map[string]interface{}{
								"path": "department_base.parents",
								"query": map[string]interface{}{
									"bool": map[string]interface{}{
										"must": map[string]interface{}{
											"term": map[string]interface{}{
												"department_base.parents.name.keyword": "技术部/研发组/后端团队",
											},
										},
									},
								},
							},
						},
						map[string]interface{}{
							"nested": map[string]interface{}{
								"path": "department_base",
								"query": map[string]interface{}{
									"bool": map[string]interface{}{
										"must": map[string]interface{}{
											"term": map[string]interface{}{
												"department_base.name.keyword": "技术部/研发组/后端团队",
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}

	// 执行测试用例
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 调用被测试函数
			query := BusinessDepartmentQuery(tc.deptName, tc.field)
			
			// 将查询转换为 Source
			source, err := query.Source()
			assert.NoError(t, err, "转换查询为Source时不应有错误")
			
			// 将 Source 转换为 JSON 字符串
			actualJSON, err := json.Marshal(source)
			assert.NoError(t, err, "将Source转换为JSON时不应有错误")
			
			// 将预期结果转换为 JSON 字符串
			expectedJSON, err := json.Marshal(tc.expected)
			assert.NoError(t, err, "将预期结果转换为JSON时不应有错误")
			
			// 比较实际结果和预期结果
			assert.JSONEq(t, string(expectedJSON), string(actualJSON), "生成的查询与预期不符")
		})
	}
}
