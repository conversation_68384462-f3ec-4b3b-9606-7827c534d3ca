package helper

import (
	"context"
	"encoding/json"
	"fmt"
	"fobrain/initialize/mysql"
	"fobrain/initialize/redis"
	logs "fobrain/mergeService/utils/log"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/personnel_departments"
	redis_helper "fobrain/models/redis"
	"slices"
	"strconv"
)

// GetPersonInfoByList 根据映射字段获取人员信息
// 根据映射字段分类获取人员信息
func GetPersonInfoByList(mapOper map[string][]*assets.PersonWithMapping) ([]*assets.PersonBase, []*assets.DepartmentBase, error) {
	logger := logs.GetLogger("service")
	personResult := make([]*assets.PersonBase, 0)
	departmentResult := make([]*assets.DepartmentBase, 0)
	departmentIds := make(map[uint64]bool)

	sta := staff.NewStaff()
	// 定义字段映射配置
	fieldConfig := map[string]struct {
		getter    func(context.Context, []string) ([]*staff.Staff, error)
		matcher   func(*staff.Staff, *assets.PersonWithMapping) bool
		fieldName string
	}{
		"id": {
			getter: sta.GetByFIDsFromCache,
			matcher: func(p *staff.Staff, val *assets.PersonWithMapping) bool {
				return p.Id == val.SourceValue || p.FidHash == val.SourceValue
			},
			fieldName: "人员ID",
		},
		"name": {
			getter: sta.GetByNames,
			matcher: func(p *staff.Staff, val *assets.PersonWithMapping) bool {
				return p.Name == val.SourceValue
			},
			fieldName: "姓名",
		},
		"english_name": {
			getter: sta.GetByEnglishNames,
			matcher: func(p *staff.Staff, val *assets.PersonWithMapping) bool {
				return slices.Contains(p.EnglishName, val.SourceValue)
			},
			fieldName: "英文名",
		},
		"email": {
			getter: sta.GetByEmails,
			matcher: func(p *staff.Staff, val *assets.PersonWithMapping) bool {
				return slices.Contains(p.Email, val.SourceValue)
			},
			fieldName: "邮箱",
		},
		"mobile": {
			getter: sta.GetByPhones,
			matcher: func(p *staff.Staff, val *assets.PersonWithMapping) bool {
				return p.Mobile == val.SourceValue
			},
			fieldName: "手机号",
		},
		"work_number": {
			getter: sta.GetByWorkNumbers,
			matcher: func(p *staff.Staff, val *assets.PersonWithMapping) bool {
				return p.WorkNumber == val.SourceValue
			},
			fieldName: "工号",
		},
	}

	// 存储查询到的所有人员数据
	allPersonData := make(map[string][]*staff.Staff)
	for mappingField, valueList := range mapOper {
		if len(valueList) == 0 {
			continue
		}

		// 获取字段配置
		config, ok := fieldConfig[mappingField]
		if !ok {
			continue
		}

		// 获取sourceValue列表
		sourceValueList := make([]string, 0)
		for _, val := range valueList {
			sourceValueList = append(sourceValueList, val.SourceValue)
		}

		// 获取人员数据
		personData, err := config.getter(context.Background(), sourceValueList)
		if err != nil {
			logger.Warnf("根据%s获取人员台账信息失败. err: %v", config.fieldName, err)
			continue
		}
		allPersonData[mappingField] = personData
	}

	// 处理匹配结果
	// 使用 map 存储每个原始值匹配到的人员数量，考虑源ID
	findCountMap := make(map[string]int)
	// 使用 map 存储每个人员 ID 对应的 PersonBase
	personBaseMap := make(map[string]*assets.PersonBase)
	// 使用 map 存储没有找到匹配人员的原始值
	notFoundValues := make(map[string][]*assets.PersonWithMapping)

	// 第一轮：计算每个原始值匹配到的人员数量
	for mappingField, personData := range allPersonData {
		config := fieldConfig[mappingField]
		for _, val := range mapOper[mappingField] {
			// 使用映射字段+原始值+源ID作为key，确保不同源上报相同信息时分开计算
			key := fmt.Sprintf("%s:%s:%d", mappingField, val.SourceValue, val.SourceId)
			matchCount := 0
			for _, person := range personData {
				if config.matcher(person, val) {
					matchCount++
				}
			}
			// 记录匹配到的人员数量
			findCountMap[key] = matchCount
			// 如果没有找到匹配的人员
			if matchCount == 0 {
				notFoundValues[key] = append(notFoundValues[key], val)
			}
		}
	}

	// 第二轮：处理匹配到的人员
	for mappingField, personData := range allPersonData {
		config := fieldConfig[mappingField]
		for _, val := range mapOper[mappingField] {
			// 使用映射字段+原始值+源ID作为key，确保不同源上报相同信息时分开计算
			key := fmt.Sprintf("%s:%s:%d", mappingField, val.SourceValue, val.SourceId)
			findCount := findCountMap[key]
			if findCount == 0 {
				continue
			}

			for _, person := range personData {
				if config.matcher(person, val) {
					// 获取或创建 PersonBase
					pb, exists := personBaseMap[person.Id]
					if !exists {
						pb = assets.NewPersonBase()
						pb.Id = person.Id
						pb.Fid = person.Fid
						pb.Name = person.Name

						// 获取人员部门信息（只需要获取一次）
						departmentInfo := getPersonDepartmentInfo(person)
						pb = AppendDepartmentToDepartmentBase(pb, departmentInfo)

						// 添加部门到结果集
						for _, department := range departmentInfo {
							if !departmentIds[department.Id] {
								departmentIds[department.Id] = true
								departmentResult = append(departmentResult, department)
							}
						}

						personBaseMap[person.Id] = pb
					}

					// 添加查找信息
					findInfo := &assets.PersonFindInfo{
						SourceId:     val.SourceId,
						NodeId:       val.NodeId,
						SourceValue:  val.SourceValue,
						MappingField: val.MappingField,
						FindCount:    findCount,
					}

					// 检查是否已存在相同的查找信息
					isDuplicate := false
					for _, existingInfo := range pb.FindInfo {
						if existingInfo.MappingField == findInfo.MappingField &&
							existingInfo.SourceValue == findInfo.SourceValue &&
							existingInfo.SourceId == findInfo.SourceId {
							isDuplicate = true
							break
						}
					}

					if !isDuplicate {
						pb.FindInfo = append(pb.FindInfo, findInfo)
					}
				}
			}
		}
	}

	// 处理没有找到匹配人员的原始值
	for _, valueList := range notFoundValues {
		for _, val := range valueList {
			pb := assets.NewPersonBase()
			pb = AppendPersonToPersonBase(pb, nil, val, 0)
			personResult = append(personResult, pb)
		}
	}

	// 将 map 转换为 slice
	for _, pb := range personBaseMap {
		personResult = append(personResult, pb)
	}

	return personResult, departmentResult, nil
}

// 获取人员部门信息
func getPersonDepartmentInfo(person *staff.Staff) []*assets.DepartmentBase {
	var departmentResult []*assets.DepartmentBase
	for _, departmentId := range person.DepartmentsIds {
		// string 类型的departmentId转uint64类型
		departmentId, err := strconv.ParseUint(departmentId, 10, 64)
		if err != nil {
			continue
		}
		// 根据部门id获取部门name，包括父级部门的Id+Name
		depa := GetDepartment(departmentId, person.Name, person.Id, "", "")
		if depa != nil {
			departmentResult = append(departmentResult, depa)
		}
	}
	return departmentResult
}

// AppendPersonToPersonBase 添加人员信息
func AppendPersonToPersonBase(pb *assets.PersonBase, person *staff.Staff, findBy *assets.PersonWithMapping, findCount int) *assets.PersonBase {
	if person != nil {
		pb.Id = person.Id
		pb.Fid = person.Fid
		pb.Name = person.Name
	} else {
		pb.Id = ""
		pb.Fid = ""
		pb.Name = ""
	}
	if pb.FindInfo == nil {
		pb.FindInfo = make([]*assets.PersonFindInfo, 0)
	}
	if findBy != nil {
		pb.FindInfo = append(pb.FindInfo, &assets.PersonFindInfo{
			SourceId:     findBy.SourceId,
			NodeId:       findBy.NodeId,
			SourceValue:  findBy.SourceValue,
			MappingField: findBy.MappingField,
			FindCount:    findCount,
		})
	}
	return pb
}

// AppendDepartmentToDepartmentBase 添加部门信息
func AppendDepartmentToDepartmentBase(pb *assets.PersonBase, department []*assets.DepartmentBase) *assets.PersonBase {
	if department == nil {
		return pb
	}
	if pb == nil {
		pb = assets.NewPersonBase()
	}
	if pb.Department == nil {
		pb.Department = make([]*assets.DepartmentBase, 0)
	}
	// 使用 map 来检查重复，避免嵌套循环
	existingDepts := make(map[uint64]bool)
	for _, dept := range pb.Department {
		existingDepts[dept.Id] = true
	}

	for _, depar := range department {
		if !existingDepts[depar.Id] {
			pb.Department = append(pb.Department, depar)
			existingDepts[depar.Id] = true
		}
	}
	return pb
}

// GetAllDepartments 获取所有部门信息,优先从缓存获取
// @return map[uint64]*personnel_departments.PersonnelDepartments, 所有部门信息,key为部门id
func GetAllDepartments() map[uint64]*personnel_departments.PersonnelDepartments {
	logger := logs.GetLogger("service")
	redisClient := redis.GetRedisClient()
	redisKey := redis_helper.DepartmentKey("all")
	// 尝试从缓存获取
	data, err := redisClient.Get(context.Background(), redisKey).Result()
	if err == nil && len(data) > 0 {
		var allDepartmentsMap map[uint64]*personnel_departments.PersonnelDepartments
		err = json.Unmarshal([]byte(data), &allDepartmentsMap)
		if err != nil {
			logger.Warnf("从缓存获取部门数据失败. err: %v", err)
			return nil
		}
		return allDepartmentsMap
	}
	// 从数据库获取
	logger.Debug("从数据库获取所有部门信息")
	allDepartments, _, err := personnel_departments.NewPersonnelDepartmentsModel().List(0, 0)
	if err != nil {
		logger.Warnf("获取所有部门信息失败. err: %v", err)
		return nil
	}
	// 将部门信息转换为map, key为部门id
	allDepartmentsMap := make(map[uint64]*personnel_departments.PersonnelDepartments)
	for _, department := range allDepartments {
		allDepartmentsMap[department.Id] = department
	}
	return allDepartmentsMap
}

// GetDepartmentById 根据部门id获取部门信息,优先从缓存获取
func GetDepartmentById(departmentId uint64) *personnel_departments.PersonnelDepartments {
	logger := logs.GetLogger("service")
	redisClient := redis.GetRedisClient()
	redisKey := redis_helper.DepartmentKey(strconv.FormatUint(departmentId, 10))
	// 尝试从缓存获取
	data, err := redisClient.Get(context.Background(), redisKey).Result()
	if err == nil && len(data) > 0 {
		var department *personnel_departments.PersonnelDepartments
		err = json.Unmarshal([]byte(data), &department)
		if err != nil {
			logger.Warnf("从缓存获取部门数据失败. err: %v", err)
			return nil
		}
		return department
	}
	// 从数据库获取
	logger.Debugf("从数据库获取部门信息, id: %d", departmentId)
	department, err := personnel_departments.NewPersonnelDepartmentsModel().First(mysql.WithId(departmentId))
	if err != nil {
		logger.Warnf("获取部门信息失败. err: %v", err)
		return nil
	}
	return department
}

// GetDepartment 根据部门id获取完整部门信息
func GetDepartment(departmentId uint64, personName string, personId string, businessName string, businessId string) *assets.DepartmentBase {
	// 查找目标部门
	targetDepartment := GetDepartmentById(departmentId)
	if targetDepartment == nil {
		return nil
	}

	result := &assets.DepartmentBase{
		BusinessSystemId:   businessId,
		BusinessSystemName: businessName,
		UserId:             personId,
		UserName:           personName,
		Id:                 targetDepartment.Id,
		Name:               targetDepartment.FullName,
	}
	// 寻找父级部门
	currentParentId := targetDepartment.ParentId
	for currentParentId != 0 {
		parent := GetDepartmentById(currentParentId)
		if parent == nil {
			break // 父级部门不存在，退出循环
		}
		// 将父级部门添加到结果列表
		result.Parents = append(result.Parents, &assets.DepartmentBase{
			Id:   parent.Id,
			Name: parent.FullName,
		})
		currentParentId = parent.ParentId // Move to the next parent
	}
	return result
}
