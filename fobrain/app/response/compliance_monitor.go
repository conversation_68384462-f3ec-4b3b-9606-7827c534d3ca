package response

import (
	"fobrain/initialize/mysql"
	pb "fobrain/mergeService/proto"
	assetses "fobrain/models/elastic/assets"
	"fobrain/models/mysql/compliance_monitor"
	"git.gobies.org/caasm/fobrain-components/utils/localtime"
)

type ComplianceMonitorTaskIndex struct {
	TaskId            uint64          `json:"task_id"`
	MonitorId         uint64          `json:"monitor_id"`
	MonitorName       string          `json:"monitor_name"`
	MonitorDesc       string          `json:"monitor_desc"`
	MonitorEvent      int             `json:"monitor_event"`
	MonitorTaskModel  int             `json:"monitor_task_model"`
	TaskStatus        int             `json:"task_status"`
	TaskStartAt       *localtime.Time `json:"task_start_at"`
	TaskEndAt         *localtime.Time `json:"task_end_at"`
	TaskAbnormalAsset uint64          `json:"task_abnormal_asset"`
	UserId            uint64          `json:"user_id"`
	UserName          string          `json:"user_name"`
}

type ComplianceMonitorIndex struct {
	mysql.BaseModel
	UserId            uint64         `json:"user_id"`
	UserName          string         `json:"user_name"`
	Name              string         `json:"name"`
	Desc              string         `json:"desc"`
	Rule              string         `json:"rule"`
	RuleType          int            `json:"rule_type"`
	TaskModel         int            `json:"task_model"`
	AssetType         int            `json:"asset_type"`
	Event             int            `json:"event"`
	Date              string         `json:"date"`
	Time              string         `json:"time"`
	Status            int            `json:"status"`
	LastTime          localtime.Time `json:"last_time"`
	LastAbnormalAsset uint64         `json:"last_abnormal_asset"`
}

type ComplianceMonitorShow struct {
	Id                 string                               `json:"id"`
	AssetId            string                               `json:"asset_id"`
	NetworkType        int                                  `json:"network_type"`
	Ip                 string                               `json:"ip"`
	Ports              []*assetses.PortInfo                 `json:"ports"`
	Product            []string                             `json:"product"`
	DeviceNames        []string                             `json:"device_names"`
	Business           []*assetses.Business                 `json:"business"`
	Oper               []*assetses.PersonBase               `json:"oper"`
	Admin              []*pb.IpAdminInfoResponseItem        `json:"admin"`
	ComplianceRules    *ComplianceRule                      `json:"compliance_rules"`
	ComplianceMonitor  compliance_monitor.ComplianceMonitor `json:"compliance_monitor"`
	CreatedAt          *localtime.Time                      `json:"created_at"`
	RuleInfos          []*assetses.RuleInfo                 `json:"rule_infos"`
	FlowStatus         int                                  `json:"flow_status"`
	PersonInfo         []*assetses.PersonBase               `json:"person_info"`
	PersonDepartment   []*assetses.DepartmentBase           `json:"person_department"`
	CcPerson           map[string]string                    `json:"cc_person"`
	LimitDate          *localtime.Time                      `json:"limit_date"`
	OperDepartment     []*assetses.DepartmentBase           `json:"oper_department"`
	BusinessDepartment []*assetses.DepartmentBase           `json:"business_department"`

	ComplianceMonitorId     uint64 `json:"compliance_monitor_id"`      //合规监测ID
	ComplianceMonitorTaskId uint64 `json:"compliance_monitor_task_id"` //合规监测任务ID
}

type ComplianceRule struct {
	Ports    []int    `json:"ports"`
	Product  []string `json:"product"`
	Protocol []string `json:"protocol"`
	Ips      []string `json:"ips"`
}
