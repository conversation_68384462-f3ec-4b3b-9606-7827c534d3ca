package ucloud

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"fobrain/pkg/utils"

	"github.com/olivere/elastic/v7"
)

type (
	TaskAssets struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`         // NODE+区域+IP+Port 作为唯一值去更新，用作数据对比
		CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
		UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
	}
)

// IndexName 索引名
func (t *TaskAssets) IndexName() string {
	return "ucloud_task_assets"
}

func (t *TaskAssets) String() string {
	if t == nil {
		return ""
	}
	return ""
}

func NewTaskAssetsModel() *TaskAssets {
	return &TaskAssets{}
}

func (asset *TaskAssets) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("ip").
		Field("information.keyword").
		Field("mac_address.app_proto.keyword")

	return field
}

func (asset *TaskAssets) GetAssetListFields() []map[string]string {
	return []map[string]string{
		{"name": "资产ID", "key": "asset_id", "all_key": "asset_id"},
		{"name": "资产名称", "key": "name", "all_key": "name"},
		{"name": "区域", "key": "region", "all_key": "region"},
		{"name": "可用区", "key": "zone", "all_key": "zone"},
		{"name": "项目名称", "key": "project_name", "all_key": "project_name"},
		{"name": "项目ID", "key": "project_id", "all_key": "project_id"},
		{"name": "资产类型", "key": "type", "all_key": "type"},
		{"name": "数据获取时间", "key": "source_time", "all_key": "source_time"},
		{"name": "资产状态", "key": "status", "all_key": "status"},
		{"name": "创建时间", "key": "create_time", "all_key": "create_time"},
		{"name": "标签", "key": "tag", "all_key": "tag"},
		{"name": "备注", "key": "remark", "all_key": "remark"},
		{"name": "IP集合", "key": "ip_set", "all_key": "ip_set.*.IP"},
	}
}

func (asset *TaskAssets) GetAssetListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "资产ID", "key": "asset_id", "all_key": "asset_id"},
		{"name": "资产名称", "key": "name", "all_key": "name"},
		{"name": "区域", "key": "region", "all_key": "region"},
		{"name": "可用区", "key": "zone", "all_key": "zone"},
		{"name": "项目名称", "key": "project_name", "all_key": "project_name"},
		{"name": "项目ID", "key": "project_id", "all_key": "project_id"},
		{"name": "资产类型", "key": "type", "all_key": "type"},
		{"name": "数据获取时间", "key": "source_time", "all_key": "source_time"},
		{"name": "资产状态", "key": "status", "all_key": "status"},
		{"name": "创建时间", "key": "create_time", "all_key": "create_time"},
		{"name": "标签", "key": "tag", "all_key": "tag"},
		{"name": "备注", "key": "remark", "all_key": "remark"},
		{"name": "IP集合", "key": "ip_set", "all_key": "ip_set.*.IP"},
	}
}
