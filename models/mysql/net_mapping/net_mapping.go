package net_mapping

import (
	"context"
	"fmt"
	"fobrain/initialize/mysql"
	"fobrain/pkg/utils"
	"strings"

	mysql2 "github.com/go-sql-driver/mysql"
)

var (
	PublicNet  = uint64(1)
	PrivateNet = uint64(2)
)

type NetMapping struct {
	mysql.BaseModel
	// 数据源
	DataSource string `gorm:"column:data_source;type:varchar(16);not null" json:"data_source"`
	// 批次号
	BatchNo string `gorm:"column:batch_no;type:varchar(255);not null" json:"batch_no"`
	// 映射关系1
	FromArea       uint64 `gorm:"column:from_area;not null" json:"from_area"`
	FromIp         string `gorm:"column:from_ip;not null" json:"from_ip"`
	FromPort       string `gorm:"column:from_port" json:"from_port"`
	FromDomain     string `gorm:"column:from_domain" json:"from_domain"`
	FromUrl        string `gorm:"column:from_url" json:"from_url"`
	FromProtocol   string `gorm:"column:from_protocol" json:"from_protocol"`
	FromExistAlarm bool   `gorm:"column:from_exist_alarm;type:smallint(1);not null;default:0" json:"from_exist_alarm"`
	// 映射关系2
	ToArea       uint64 `gorm:"column:to_area;not null" json:"to_area"`
	ToIp         string `gorm:"column:to_ip;not null" json:"to_ip"`
	ToPort       string `gorm:"column:to_port" json:"to_port"`
	ToDomain     string `gorm:"column:to_domain" json:"to_domain"`
	ToUrl        string `gorm:"column:to_url" json:"to_url"`
	ToProtocol   string `gorm:"column:to_protocol" json:"to_protocol"`
	ToExistAlarm bool   `gorm:"column:to_exist_alarm;type:smallint(1);not null;default:0" json:"to_exist_alarm"`
}

func (n *NetMapping) TableName() string {
	return "net_mappings"
}

func NewNetMappingModel() *NetMapping {
	return &NetMapping{BaseModel: mysql.BaseModel{DB: mysql.GetDbClient().Table((&NetMapping{}).TableName())}}
}

func (n *NetMapping) Create(netMapping *NetMapping) error {
	err := n.DB.Create(netMapping).Error
	if err != nil {
		if err.(*mysql2.MySQLError).Number != 1062 {
			return err
		} else {
			var oldMapping *NetMapping
			err = NewNetMappingModel().DB.Table(n.TableName()).
				Where("from_area = ?", netMapping.FromArea).
				Where("from_ip = ?", netMapping.FromIp).
				Where("from_port = ?", netMapping.FromPort).
				Where("to_area = ?", netMapping.ToArea).
				Where("to_ip = ?", netMapping.ToIp).
				Where("to_port = ?", netMapping.ToPort).
				First(&oldMapping).Error
			if err != nil {
				return err
			}
			if netMapping.FromDomain != "" {
				if oldMapping.FromDomain == "" {
					oldMapping.FromDomain = netMapping.FromDomain
				} else {
					if !utils.InArray(netMapping.FromDomain, oldMapping.GetFromDomainList()) {
						oldMapping.FromDomain = fmt.Sprintf("%s,%s", oldMapping.FromDomain, netMapping.FromDomain)
					}
				}
			}
			if netMapping.ToDomain != "" {
				if oldMapping.ToDomain == "" {
					oldMapping.ToDomain = netMapping.ToDomain
				} else {
					if !utils.InArray(netMapping.ToDomain, oldMapping.GetFromDomainList()) {
						oldMapping.ToDomain = fmt.Sprintf("%s,%s", oldMapping.ToDomain, netMapping.ToDomain)
					}
				}
			}
			err = NewNetMappingModel().DB.Table(n.TableName()).Updates(oldMapping).Error
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func (n *NetMapping) CreateBatch(netMappings []*NetMapping) (int64, error) {
	count := int64(0)

	for _, netMapping := range netMappings {
		err := NewNetMappingModel().DB.Table(n.TableName()).Create(netMapping).Error
		if err != nil {
			// 1062 唯一索引冲突
			if err.(*mysql2.MySQLError).Number != 1062 {
				return int64(0), err
			} else {
				var oldMapping *NetMapping
				err = NewNetMappingModel().DB.Table(n.TableName()).
					Where("from_area = ?", netMapping.FromArea).
					Where("from_ip = ?", netMapping.FromIp).
					Where("from_port = ?", netMapping.FromPort).
					Where("to_area = ?", netMapping.ToArea).
					Where("to_ip = ?", netMapping.ToIp).
					Where("to_port = ?", netMapping.ToPort).
					First(&oldMapping).Error
				if err != nil {
					return int64(0), err
				}
				if netMapping.FromDomain != "" {
					if oldMapping.FromDomain == "" {
						oldMapping.FromDomain = netMapping.FromDomain
					} else {
						if !utils.InArray(netMapping.FromDomain, oldMapping.GetFromDomainList()) {
							oldMapping.FromDomain = fmt.Sprintf("%s,%s", oldMapping.FromDomain, netMapping.FromDomain)
						}
					}
				}
				if netMapping.ToDomain != "" {
					if oldMapping.ToDomain == "" {
						oldMapping.ToDomain = netMapping.ToDomain
					} else {
						if !utils.InArray(netMapping.ToDomain, oldMapping.GetFromDomainList()) {
							oldMapping.ToDomain = fmt.Sprintf("%s,%s", oldMapping.ToDomain, netMapping.ToDomain)
						}
					}
				}
				err = NewNetMappingModel().DB.Table(n.TableName()).Updates(oldMapping).Error
				if err != nil {
					return int64(0), err
				}
			}
		} else {
			count++
		}
	}

	return count, nil
}

func (n *NetMapping) UpdateBatch(netMappings []*NetMapping) error {
	for _, netMapping := range netMappings {
		data := map[string]interface{}{
			"data_source":      netMapping.DataSource,
			"batch_no":         netMapping.BatchNo,
			"from_area":        netMapping.FromArea,
			"from_ip":          netMapping.FromIp,
			"from_port":        netMapping.FromPort,
			"from_domain":      netMapping.FromDomain,
			"from_url":         netMapping.FromUrl,
			"from_protocol":    netMapping.FromProtocol,
			"from_exist_alarm": netMapping.FromExistAlarm,
			"to_area":          netMapping.ToArea,
			"to_ip":            netMapping.ToIp,
			"to_port":          netMapping.ToPort,
			"to_domain":        netMapping.ToDomain,
			"to_url":           netMapping.ToUrl,
			"to_protocol":      netMapping.ToProtocol,
			"to_exist_alarm":   netMapping.ToExistAlarm,
		}
		err := n.DB.Table(n.TableName()).Where("id = ?", netMapping.Id).Updates(data).Error
		if err != nil {
			return err
		}
	}
	return nil
}

func (n *NetMapping) ListByPage(page, size int, keyword string) ([]*NetMapping, int64, error) {
	var list = make([]*NetMapping, 0)
	var handleFuncs = make([]mysql.HandleFunc, 0)
	if keyword != "" {
		area, _ := NewNetMappingAreaModel().First(mysql.WithWhere("`name` = ?", keyword))
		handleFuncs = append(handleFuncs, mysql.WithWhere(fmt.Sprintf("%s LIKE ?", "from_ip"), "%"+keyword+"%"))
		for _, field := range []string{"data_source",
			"from_port", "from_domain", "from_url", "from_protocol",
			"to_ip", "to_port", "to_domain", "to_url", "to_protocol"} {
			handleFuncs = append(handleFuncs, mysql.WithOrWhere(fmt.Sprintf("%s LIKE ?", field), "%"+keyword+"%"))
		}
		if area != nil && area.Id > 0 {
			handleFuncs = append(handleFuncs, mysql.WithOrWhere("from_area = ?", area.Id), mysql.WithOrWhere("to_area = ?", area.Id))
		}
	}
	query := n.DB.Table(n.TableName()).Order("created_at DESC")
	for _, handleFunc := range handleFuncs {
		handleFunc(query)
	}
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	if total == 0 {
		return list, 0, nil
	}
	if !mysql.IsPageAll(page, size) {
		query.Scopes(mysql.PageLimit(page, size))
	}

	if err := query.Find(&list).Error; err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

func (n *NetMapping) ListByOpt(opts ...mysql.HandleFunc) ([]*NetMapping, error) {
	query := n.DB.Table(n.TableName()).Order("created_at DESC")
	for _, opt := range opts {
		opt(query)
	}
	var list = make([]*NetMapping, 0)
	if err := query.Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (n *NetMapping) Delete(ctx context.Context, ids []uint64, keyword string) ([]*NetMapping, error) {
	var existData []*NetMapping
	if len(ids) > 0 {
		err := n.DB.Table(n.TableName()).Where("id IN (?)", ids).Find(&existData).Error
		if err != nil {
			return existData, err
		}
		if len(existData) == 0 {
			return existData, nil
		}
		result := n.DB.WithContext(ctx).Delete(existData)
		return existData, result.Error
	} else if keyword != "" {
		var handleFuncs = make([]mysql.HandleFunc, 0)
		if keyword != "" {
			handleFuncs = append(handleFuncs, mysql.WithWhere(fmt.Sprintf("%s LIKE ?", "from_area"), "%"+keyword+"%"))
			for _, field := range []string{"data_source",
				"from_ip", "from_port", "from_domain", "from_url", "from_protocol",
				"to_area", "to_ip", "to_port", "to_domain", "to_url", "to_protocol"} {
				handleFuncs = append(handleFuncs, mysql.WithOrWhere(fmt.Sprintf("%s LIKE ?", field), "%"+keyword+"%"))
			}
		}
		query := n.DB.Table(n.TableName()).Order("created_at DESC")
		for _, handleFunc := range handleFuncs {
			handleFunc(query)
		}
		err := query.Find(&existData).Error
		if err != nil {
			return existData, err
		}
		if len(existData) == 0 {
			return existData, nil
		}
		result := n.DB.WithContext(ctx).Delete(existData)
		return existData, result.Error
	} else {
		err := n.DB.Table(n.TableName()).Find(&existData).Error
		if err != nil {
			return existData, err
		}
		err = n.DB.Table(n.TableName()).Delete(&NetMapping{}).Error
		return existData, err
	}
}

func (n *NetMapping) GetFromDomainList() []string {
	if n.FromDomain == "" {
		return []string{}
	} else {
		return strings.Split(n.FromDomain, ",")
	}
}

func (n *NetMapping) GetToDomainList() []string {
	if n.ToDomain == "" {
		return []string{}
	} else {
		return strings.Split(n.ToDomain, ",")
	}
}
