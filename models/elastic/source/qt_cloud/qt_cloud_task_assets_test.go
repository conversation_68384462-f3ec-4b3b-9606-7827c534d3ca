package qt_cloud

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestQTCloudTaskAssets_IndexName(t *testing.T) {
	assert.Equal(t, "qt_cloud_task_linux_employees", NewQTCloudTaskLinuxEmployeesModel().IndexName())
	assert.Equal(t, "", NewQTCloudTaskLinuxEmployeesModel().String())
}

func TestQTCloudTaskAssets_GetQTCloudAssetListFields(t *testing.T) {
	m := NewQTCloudTaskAssetsModel()
	fields := m.GetQTCloudAssetListFields()

	assert.Equal(t, fields, []map[string]string{
		{"name": "主机IP地址", "key": "displayIp"},
		{"name": "外网IP地址", "key": "connectionIp"},
		{"name": "内网IP地址", "key": "internalIp"},
		{"name": "主机名", "key": "hostname"},
		{"name": "序列号", "key": "serialNumber"},
		{"name": "设备UUID", "key": "uuid"},
		{"name": "CPU信息", "key": "cpu"}, //处理器个数+厂商+品牌
		{"name": "硬盘大小", "key": "diskSize"},
		{"name": "内存大小", "key": "memorySize"},
		{"name": "Agent状态", "key": "agentStatus"}, //Agent状态，0-在线 1-离线 2-停用 3-删除中
		{"name": "Agent版本", "key": "agentVersion"},
		{"name": "最后在线时间", "key": "lastOnlineTime"},
		{"name": "机房位置", "key": "hostLocation"},
		{"name": "业务系统", "key": "bizGroup"},
		{"name": "业务负责人", "key": "chargeName"},
	})
}

func TestQTCloudTaskAssets_GetQTCloudAssetListDetailFields(t *testing.T) {
	m := NewQTCloudTaskAssetsModel()
	fields := m.GetQTCloudAssetListDetailFields()

	assert.Equal(t, fields, []map[string]string{
		{"name": "主机IP地址", "key": "displayIp"},
		{"name": "外网IP地址", "key": "connectionIp"},
		{"name": "内网IP地址", "key": "internalIp"},
		{"name": "主机名", "key": "hostname"},
		{"name": "序列号", "key": "serialNumber"},
		{"name": "设备UUID", "key": "uuid"},
		{"name": "CPU信息", "key": "cpu"}, //处理器个数+厂商+品牌
		{"name": "硬盘大小", "key": "diskSize"},
		{"name": "内存大小", "key": "memorySize"},
		{"name": "Agent状态", "key": "agentStatus"}, //Agent状态，0-在线 1-离线 2-停用 3-删除中
		{"name": "Agent版本", "key": "agentVersion"},
		{"name": "最后在线时间", "key": "lastOnlineTime"},
		{"name": "机房位置", "key": "hostLocation"},
		{"name": "业务系统", "key": "bizGroup"},
		{"name": "业务负责人", "key": "chargeName"},
		{"name": "备注", "key": "remark"},
		{"name": "标签", "key": "hostTagList"},
		{"name": "操作系统信息", "key": "platform"},
		{"name": "内核版本", "key": "kernelVersion"},
		{"name": "硬盘个数", "key": "diskCount"},
		{"name": "硬盘使用率", "key": "diskUsage"},
		{"name": "内存使用率", "key": "memoryUsage"},
		{"name": "通信状态", "key": "onlineStatus"}, // 1 – 在线 0 - 离线
		{"name": "安装时间", "key": "installTime"},
		{"name": "不在线天数", "key": "offlineDays"},
		{"name": "生产商", "key": "manufacturer"},
		{"name": "硬件配置信息", "key": "memories"},
	})
}
