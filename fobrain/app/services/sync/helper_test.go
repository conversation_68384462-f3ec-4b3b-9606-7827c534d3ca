package sync

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestGetUniqueKey 测试 GetUniqueKey 函数
func TestGetUniqueKey(t *testing.T) {
	tests := []struct {
		name        string
		source      string
		uniqueField string
		data        string
		expected    string
		expectError bool
	}{
		{
			name:        "空的uniqueField",
			source:      "test",
			uniqueField: "",
			data:        `{"name":"test"}`,
			expected:    "",
			expectError: true,
		},
		{
			name:        "单个字段",
			source:      "test",
			uniqueField: "name",
			data:        `{"name":"test_user"}`,
			expected:    "test_user",
			expectError: false,
		},
		{
			name:        "多个字段用+连接",
			source:      "test",
			uniqueField: "name+mobile",
			data:        `{"name":"test_user","mobile":"13800138000"}`,
			expected:    "test_user,13800138000",
			expectError: false,
		},
		{
			name:        "字段不存在",
			source:      "test",
			uniqueField: "nonexistent",
			data:        `{"name":"test_user"}`,
			expected:    "",
			expectError: false,
		},
		{
			name:        "字段值为空",
			source:      "test",
			uniqueField: "name",
			data:        `{"name":""}`,
			expected:    "",
			expectError: false,
		},
		{
			name:        "混合存在和不存在的字段",
			source:      "test",
			uniqueField: "name+nonexistent+mobile",
			data:        `{"name":"test_user","mobile":"13800138000"}`,
			expected:    "test_user,,13800138000",
			expectError: false,
		},

		{
			name:        "复杂JSON数据",
			source:      "test",
			uniqueField: "user.name+user.id",
			data:        `{"user":{"name":"张三","id":"123"},"other":"data"}`,
			expected:    "张三,123",
			expectError: false,
		},
		{
			name:        "嵌套字段不存在",
			source:      "test",
			uniqueField: "user.nonexistent",
			data:        `{"user":{"name":"张三"}}`,
			expected:    "",
			expectError: false,
		},
		{
			name:        "数字字段",
			source:      "test",
			uniqueField: "id+age",
			data:        `{"id":123,"age":25}`,
			expected:    "123,25",
			expectError: false,
		},
		{
			name:        "布尔字段",
			source:      "test",
			uniqueField: "active",
			data:        `{"active":true}`,
			expected:    "true",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GetUniqueKey(tt.source, tt.uniqueField, tt.data)
			
			if tt.expectError {
				assert.Error(t, err, "应该返回错误")
			} else {
				assert.NoError(t, err, "不应该返回错误")
			}
			
			assert.Equal(t, tt.expected, result, "返回值不符合预期")
		})
	}
}
