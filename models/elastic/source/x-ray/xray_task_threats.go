package xray

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"fobrain/pkg/utils"

	"github.com/olivere/elastic/v7"
)

type (
	TaskThreats struct {
		*es.BaseModel `json:"-"`
		Id            int             `json:"id"`      // TaskId+NODE+区域+IP+Port 作为唯一值去更新，用作记录每次任务拉取数据
		TaskId        int             `json:"task_id"` //
		ChildTaskId   int             `json:"child_task_id"`
		CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
		UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
	}
)

// IndexName 索引名
func (t *TaskThreats) IndexName() string {
	return "xray_task_threats"
}

func (t *TaskThreats) String() string {
	if t == nil {
		return ""
	}
	return ""
}

func NewTaskThreatsModel() *TaskThreats {
	return &TaskThreats{}
}

func (t *TaskThreats) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("ip").
		Field("common_title.keyword").
		Field("vulType.keyword").
		Field("province.keyword")

	return field
}

func (t *TaskThreats) GetThreatListFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "漏洞名称", "key": "title"},
		{"name": "CVE编号", "key": "cveId"},
		{"name": "漏洞类型", "key": "vulType"},
		{"name": "漏洞等级", "key": "level_desc"},
		{"name": "是否存在Poc", "key": "poc"},
		{"name": "是否存在EXP", "key": "exp"},
		{"name": "漏洞地址", "key": "url"},
		{"name": "发现时间", "key": "created_at"},
	}
}

func (t *TaskThreats) GetThreatListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "漏洞名称", "key": "title"},
		{"name": "CVE编号", "key": "cveId"},
		{"name": "漏洞类型", "key": "vulType"},
		{"name": "漏洞等级", "key": "level_desc"},
		{"name": "是否存在Poc", "key": "poc"},
		{"name": "是否存在EXP", "key": "exp"},
		{"name": "漏洞地址", "key": "url"},
		{"name": "发现时间", "key": "created_at"},
		// {"name": "通报时间", "key": "notice_time"},
		// {"name": "上次扫描时间", "key": "lastupdatetime"},
		{"name": "备注信息", "key": "summary"},
		// {"name": "MAC地址", "key": "mac"},
		// {"name": "地理位置", "key": "city"},
		// {"name": "资产等级", "key": "asset_level"},
		// {"name": "管理单元", "key": "company"},
	}
}
