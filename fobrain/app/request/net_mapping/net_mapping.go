package net_mapping

import "fobrain/fobrain/common/request"

type CreateNetMappingRequest struct {
	FromArea     uint64 `json:"from_area"`
	FromIp       string `json:"from_ip"`
	FromPort     int    `json:"from_port"`
	FromDomain   string `json:"from_domain"`
	FromUrl      string `json:"from_url"`
	FromProtocol string `json:"from_protocol"`
	// 映射关系2
	ToArea     uint64 `json:"to_area"`
	ToIp       string `json:"to_ip"`
	ToPort     int    `json:"to_port"`
	ToDomain   string `json:"to_domain"`
	ToUrl      string `json:"to_url"`
	ToProtocol string `json:"to_protocol"`
}

type ImportNetMappingRequest struct {
	// 1: 模板文件
	// 2: 华为防火墙配置文件
	// 3: A10数据源
	// 4: 飞塔
	// 5: 华为另外的防火墙
	// 6 启明星辰配置文件
	// 7 Ctrix负载均衡配置文件
	FileType    int    `json:"file_type" form:"file_type" uri:"file_type" validate:"required,oneof=1 2 3 4 5 6 7 8 9" zh:"文件类型"`
	FromMapping string `json:"from_mapping" form:"from_mapping" uri:"from_mapping" validate:"required"`
	ToMapping   string `json:"to_mapping" form:"to_mapping" uri:"to_mapping" validate:"required"`
}

type ImportNetMappingConfirmRequest struct {
	// 1: 模板文件
	// 2: 华为防火墙配置文件
	// 3: A10数据源
	// 4: 飞塔
	// 5: 华为另外的防火墙
	// 6: 启明星辰负载均衡
	FileType int    `json:"file_type" form:"file_type" uri:"file_type" validate:"required,oneof=1 2 3 4 5" zh:"文件类型"`
	Fields   string `json:"fields" form:"fields" uri:"fields" validate:"required"`
}

type DeleteNetMappingRequest struct {
	Ids     []uint64 `json:"ids" form:"ids" uri:"ids" validate:"required,dive,min=1" zh:"ID"`
	Keyword string   `json:"keyword" form:"keyword" uri:"keyword" validate:"" zh:"关键词"`
}

type ListNetMappingRequest struct {
	request.PageRequest
	Keyword string `json:"keyword" form:"keyword" uri:"keyword" validate:"" zh:"关键词"`
}

type DownloadTemplateRequest struct {
	FromArea string `json:"from_mapping" form:"from_mapping" uri:"from_mapping" validate:"required"`
	ToArea   string `json:"to_mapping" form:"to_mapping" uri:"to_mapping" validate:"required"`
}

type ListAuditLogRequest struct {
	request.PageRequest
}

type ListAuditDataRequest struct {
	request.PageRequest
	BatchNo string `json:"batch_no" form:"batch_no" uri:"batch_no" validate:"required" zh:"批次号"`
}
