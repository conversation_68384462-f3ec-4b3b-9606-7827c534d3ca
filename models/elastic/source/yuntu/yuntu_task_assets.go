package yuntu

import (
	"fmt"
	"fobrain/initialize/es"
	"git.gobies.org/caasm/fobrain-components/utils"
	"git.gobies.org/caasm/fobrain-components/utils/localtime"
	"github.com/olivere/elastic/v7"
)

type (
	TaskAssets struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`
		CreatedAt     *localtime.Time `json:"created_at"`
		UpdatedAt     *localtime.Time `json:"updated_at"`
	}
)

// IndexName 索引名
func (t *TaskAssets) IndexName() string {
	return "yuntu_task_assets"
}

func (t *TaskAssets) String() string {
	if t == nil {
		return ""
	}
	return ""
}

func NewTaskAssetsModel() *TaskAssets {
	return &TaskAssets{}
}

func (asset *TaskAssets) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("ip")

	return field
}

func (asset *TaskAssets) GetAssetListFields() []map[string]string {
	return []map[string]string{
		{"name": "ID", "key": "id", "all_key": "id"},
		{"name": "IP", "key": "ip", "all_key": "ip"},
		{"name": "ASN名称", "key": "as_name", "all_key": "as_name"},
		{"name": "ASN编号", "key": "as_num", "all_key": "as_num"},
		{"name": "分组", "key": "bu", "all_key": "bu"},
		{"name": "创建时间", "key": "created_at", "all_key": "created_at"},
		{"name": "更新时间", "key": "updated_at", "all_key": "updated_at"},
		{"name": "最后发现时间", "key": "lastseen_at", "all_key": "lastseen_at"},
		{"name": "位置", "key": "location", "all_key": "location"},
		{"name": "存活端口数", "key": "live_port", "all_key": "live_port"},
		{"name": "状态", "key": "status", "all_key": "status"},
		{"name": "网段", "key": "subnet", "all_key": "subnet"},
		{"name": "IP版本", "key": "version", "all_key": "version"},
	}
}

func (asset *TaskAssets) GetAssetListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "ID", "key": "id", "all_key": "id"},
		{"name": "IP", "key": "ip", "all_key": "ip"},
		{"name": "ASN名称", "key": "as_name", "all_key": "as_name"},
		{"name": "ASN编号", "key": "as_num", "all_key": "as_num"},
		{"name": "分组", "key": "bu", "all_key": "bu"},
		{"name": "创建时间", "key": "created_at", "all_key": "created_at"},
		{"name": "更新时间", "key": "updated_at", "all_key": "updated_at"},
		{"name": "最后发现时间", "key": "lastseen_at", "all_key": "lastseen_at"},
		{"name": "位置", "key": "location", "all_key": "location"},
		{"name": "存活端口数", "key": "live_port", "all_key": "live_port"},
		{"name": "状态", "key": "status", "all_key": "status"},
		{"name": "网段", "key": "subnet", "all_key": "subnet"},
		{"name": "IP版本", "key": "version", "all_key": "version"},
	}
}
