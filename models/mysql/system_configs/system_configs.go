package system_configs

import (
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/mysql"
	"strconv"
)

type SystemConfigs struct {
	mysql.BaseModel
	Key   string `json:"key"`
	Value string `json:"value"`
}

// KeyValue 定义一个结构体来接收查询结果
type KeyValue struct {
	Key   string `gorm:"column:key"`
	Value string `gorm:"column:value"`
}

func NewSystemConfigs() *SystemConfigs {
	return &SystemConfigs{BaseModel: mysql.BaseModel{DB: mysql.GetDbClient().Table((&SystemConfigs{}).TableName())}}
}
func (u *SystemConfigs) TableName() string {
	return "system_configs"
}

func (u *SystemConfigs) GetConfig(key string) (string, error) {
	value := ""
	if err := u.DB.Table((&SystemConfigs{}).TableName()).Select(`value`).Where("`key` = ?", key).Find(&value).Error; err != nil {
		return "", err
	}
	return value, nil
}

func (u *SystemConfigs) CreateConfig(key string, value string) error {
	if err := u.DB.Table((&SystemConfigs{}).TableName()).Create(&SystemConfigs{Key: key, Value: value}).Error; err != nil {
		return err
	}
	return nil
}

func (u *SystemConfigs) UpdateConfig(key string, value string) error {
	config := SystemConfigs{
		Key:   key,
		Value: value,
		BaseModel: mysql.BaseModel{
			CreatedAt: *localtime.Now(),
			UpdatedAt: *localtime.Now(),
		},
	}

	result := u.DB.Table(config.TableName()).Where("`key` = ?", key).FirstOrCreate(&config)
	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected > 0 {
		return nil
	}

	err := result.Update("value", value).Error
	return err
}

// GetMultiConfig 获取多个配置项的值
// bug? 经过测试，下面的两个函数如果调用上面的代码没有Debug()就会报错
func (u *SystemConfigs) GetMultiConfig(keys []string) (map[string]string, error) {
	configs := make(map[string]string, len(keys))
	var results []KeyValue
	// 执行查询并扫描到结果切片
	if err := u.DB.Table(u.TableName()).
		Where("`key` IN (?)", keys).
		Find(&results).Error; err != nil {
		return nil, err
	}
	// 将查询结果映射到 map 中
	for _, result := range results {
		configs[result.Key] = result.Value
	}
	return configs, nil
}

// SetMultiConfig 设置多个配置项的值
func (u *SystemConfigs) SetMultiConfig(configs map[string]any) (err error) {
	for key, value := range configs {
		// if err = u.UpdateConfig(key, value.(string)); err != nil {
		if err = NewSystemConfigs().UpdateConfig(key, value.(string)); err != nil {
			return
		}
	}
	return nil
}

// SsoEnable
// @Summary 检查是否启用SSO
// @Return bool 是否启用SSO true:启用 false:未启用
func SsoEnable() bool {
	enable, err := NewSystemConfigs().GetConfig("sso_enable")
	if err != nil {
		return false
	}

	if enable != "true" {
		return false
	}

	authUrl, err := NewSystemConfigs().GetConfig("auth_url")
	if err != nil || authUrl == "" {
		return false
	}

	tokenUrl, err := NewSystemConfigs().GetConfig("token_url")
	if err != nil || tokenUrl == "" {
		return false
	}

	return true
}

func SSOServer() string {
	ssoServer, err := NewSystemConfigs().GetConfig("sso_server")
	if err != nil {
		panic(err)
	}
	return ssoServer
}

func SSOConfigs() map[string]string {
	authUrl, _ := NewSystemConfigs().GetConfig("auth_url")
	tokenUrl, _ := NewSystemConfigs().GetConfig("token_url")
	clientId, _ := NewSystemConfigs().GetConfig("client_id")
	clientSecret, _ := NewSystemConfigs().GetConfig("client_secret")
	ssoEnable, _ := NewSystemConfigs().GetConfig("sso_enable")
	ssoServer, _ := NewSystemConfigs().GetConfig("sso_server")
	loginInfoUrl, _ := NewSystemConfigs().GetConfig("login_info_url")
	ssoCallback, _ := NewSystemConfigs().GetConfig("sso_callback")
	tenant, _ := NewSystemConfigs().GetConfig("tenant")
	role, _ := NewSystemConfigs().GetConfig("sso_role")

	return map[string]string{
		"auth_url":       authUrl,
		"token_url":      tokenUrl,
		"client_id":      clientId,
		"client_secret":  clientSecret,
		"sso_enable":     ssoEnable,
		"sso_server":     ssoServer,
		"login_info_url": loginInfoUrl,
		"sso_callback":   ssoCallback,
		"tenant":         tenant,
		"sso_role":       role,
	}
}

func LoginInfoUrl() string {
	loginInfoUrl, _ := NewSystemConfigs().GetConfig("login_info_url")
	return loginInfoUrl
}
func SSORole() uint64 {
	role, _ := NewSystemConfigs().GetConfig("sso_role")
	res, _ := strconv.Atoi(role)
	return uint64(res)
}

func FourASSOConfigs() map[string]string {
	clientId, _ := NewSystemConfigs().GetConfig("client_id")
	clientSecret, _ := NewSystemConfigs().GetConfig("client_secret")
	tenant, _ := NewSystemConfigs().GetConfig("tenant")
	loginInfoUrl, _ := NewSystemConfigs().GetConfig("login_info_url")
	ssoCallback, _ := NewSystemConfigs().GetConfig("sso_callback")
	ssoRole, _ := NewSystemConfigs().GetConfig("sso_role")
	return map[string]string{
		"app_code":       clientId,
		"app_key":        clientSecret,
		"tenant":         tenant,
		"login_info_url": loginInfoUrl,
		"sso_callback":   ssoCallback,
		"sso_role":       ssoRole,
	}
}
