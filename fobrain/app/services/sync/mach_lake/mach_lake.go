package mach_lake

import (
	"context"
	"fmt"
	"fobrain/pkg/utils/handle_es_bulk_error"
	"net"
	"strings"
	"time"

	nodeMachLake "fobrain/fobrain/app/services/node/mach_lake"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	assetses "fobrain/models/elastic/assets"
	machLakeModel "fobrain/models/elastic/source/mach_lake"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
	"fobrain/pkg/utils"

	"github.com/olivere/elastic/v7"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
)

// SyncMachLake 同MachLake数据
func SyncMachLake(node *data_source.Node, taskInfo string) error {
	logs.GetSyncLogger().Infof("SyncMachLake start taskInfo:%s", taskInfo)
	assetTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())

	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())

	callbackAssets := func(data, ipRanges string) (int, error) {
		count, err := handleAssets(data, ipRanges, node, taskId, assetTaskId)
		if err == nil { //更新成功数量
			err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(assetTaskId, count)
			if err != nil {
				logs.GetSyncLogger().Errorf("MachLake syncAssets UpdateSyncDataSuccessTotal err:%s", err.Error())
			}
		}
		return count, err
	}

	//获取ChangtingWaf Client
	cli := nodeMachLake.NewMachLake(callbackAssets)

	//设置ChangtingWaf Client请求的节点
	if err := cli.SetNode(node.Id); err != nil {
		return err
	}

	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncAsset)).Int() == data_sync_task.SyncAsset {
		//更新资产任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(assetTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("MachLake syncAssets UpdateStatusById StatusDoing err:%s", err.Error())
		}
		go syncAssets(cli, taskInfo, node)
	}

	return nil
}

// syncAssets 同步资产
// @param [int64] otherTaskId 如果是同步任务资产则，通过此 ID 传递任务ID。
func syncAssets(cli *nodeMachLake.MachLake, taskInfo string, node *data_source.Node) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())
	logs.GetSyncLogger().Infof("SyncMachLake syncAssets start taskInfo:%d,taskChildId:%d", taskId, taskChildId)

	//获取资产
	total, err := cli.GetAssetsAll() // GetWeiBuAssetsAll 会调用handleAssets
	if err != nil {
		logs.GetSyncLogger().Errorf("MachLake syncAssets GetAssets err:%s", err.Error())
		setTaskFailure("syncAssets", "GetAssets", taskId, err, taskChildId)
		return
	}

	//子任务更新需要同步的数据总量
	err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateById(taskChildId, data_sync_child_task.DataSyncChildTask{
		SyncDataTotal: total,
	})
	if err != nil {
		logs.GetSyncLogger().Errorf("MachLake syncAssets UpdateById SyncDataTotal err:%s", err.Error())
	}

	logs.GetSyncLogger().Infof("SyncMachLake syncAssets end taskInfo:%d,taskChildId:%d", taskId, taskChildId)
	//更新子任务完成状态
	err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(taskChildId, data_sync_child_task.StatusSuccess)
	if err != nil {
		logs.GetSyncLogger().Errorf("MachLake syncAssets UpdateStatusById StatusSuccess err:%s", err.Error())
	}

}

func handleAssets(data, ipRanges string, node *data_source.Node, taskId, taskChildId uint64) (int, error) {
	// 默认设置为3，智能判断
	networkType, err := node.GetNetworkType()
	if err != nil {
		logs.GetSyncLogger().Errorf("MachLake syncAssets GetNetworkType err:%s", err.Error())
	}
	personField := "name"
	for _, config := range node.Configs {
		if config.Key == "person_field" {
			personField = config.Value
			break
		}
	}

	items := gjson.Get(data, "result.data")
	if len(items.Array()) == 0 {
		return 0, nil
	}
	count := 0
	if items.Exists() {
		// assetBulkRequest := es.GetEsClient().Bulk()
		assetTaskBulkRequest := es.GetEsClient().Bulk()
		assetProcessBulkRequest := es.GetEsClient().Bulk()
		// 遍历 _id 字段，并根据 id 添加新的字段数据
		items.ForEach(func(key, value gjson.Result) bool {
			//logs.GetLogger().Infof("SyncMachLake syncAssets value info:%s", value)
			ip := gjson.Get(value.String(), "ipAddress").String()
			if IsInIpRanges(ip, ipRanges) {
				count += 1
				doc, _ := sjson.Set(value.String(), "node_id", node.Id)
				doc, _ = sjson.Set(doc, "area_id", node.AreaId)

				//写入资产库
				// asset := machLakeModel.NewMachLakeTaskAssetsModel()
				assetId := fmt.Sprintf("%d_%d_%s", node.Id, node.AreaId, ip)
				// assetReq := elastic.NewBulkIndexRequest().Index(asset.IndexName()).Id(assetId).Doc(doc)
				// assetBulkRequest = assetBulkRequest.Add(assetReq)

				//写入任务库
				assetTask := machLakeModel.NewMachLakeTaskAssetsModel()
				assetTaskId := fmt.Sprintf("%d_%s", taskId, assetId)
				doc, _ = sjson.Set(doc, "task_id", taskId)
				doc, _ = sjson.Set(doc, "child_task_id", taskChildId)
				assetTaskReq := elastic.NewBulkIndexRequest().Index(assetTask.IndexName()).Id(assetTaskId).Doc(doc)
				assetTaskBulkRequest = assetTaskBulkRequest.Add(assetTaskReq)

				//写入过程库
				assetProcessId := fmt.Sprintf("%d_%d_%s", taskId, taskChildId, assetId)
				assetProcess := SetProcessAssetFields(assetProcessId, assetTaskId, ip, node, value, networkType, personField, taskId, taskChildId)

				assetProcessReq := elastic.NewBulkIndexRequest().Index(assetProcess.IndexName()).Id(assetProcessId).Doc(assetProcess)
				assetProcessBulkRequest = assetProcessBulkRequest.Add(assetProcessReq)

			}
			return true
		})

		if count == 0 {
			logs.GetSyncLogger().Infof("SyncMachLake handleAssets count:", count)
			return 0, nil
		}

		assetProcessBulkReq, err := assetProcessBulkRequest.Refresh("true").Do(context.Background())

		if err != nil || assetProcessBulkReq.Errors {
			errString := handle_es_bulk_error.HandleBulkResp(assetses.NewProcessAssetsModel().IndexName(), err, assetProcessBulkReq)
			logs.GetSyncLogger().Errorf("MachLake syncAssets assetProcessBulkRequest.Do err:%s", errString)
			return 0, err
		}

		// assetBulkReq, err := assetBulkRequest.Do(context.Background())
		// if err != nil || assetBulkReq.Errors {
		// 	errString := handle_es_bulk_error.HandleBulkResp(machLakeModel.NewMachLakeTaskAssetsModel().IndexName(), err, assetBulkReq)

		// 	logs.GetSyncLogger().Errorf("MachLake syncAssets assetBulkRequest.Do err:%s", errString)

		// 	return 0, err
		// }

		assetTaskBulkReq, err := assetTaskBulkRequest.Do(context.Background())

		if err != nil || assetTaskBulkReq.Errors {
			errString := handle_es_bulk_error.HandleBulkResp(machLakeModel.NewMachLakeTaskAssetsModel().IndexName(), err, assetTaskBulkReq)
			logs.GetSyncLogger().Errorf("MachLake syncAssets assetTaskBulkRequest.Do err:%s", errString)
			return 0, err
		}
	}

	return count, nil
}

func SetProcessAssetFields(assetProcessId, assetTaskId, ip string, node *data_source.Node, value gjson.Result, networkType int, personField string, taskId, taskChildId uint64) *assetses.ProcessAssets {

	assetProcess := assetses.NewProcessAssetsModel()
	assetProcess.Id = assetProcessId
	assetProcess.TaskDataId = assetTaskId

	assetProcess.Area = int(node.AreaId)
	assetProcess.Node = node.Id
	assetProcess.Source = node.SourceId
	assetProcess.TaskId = taskId
	assetProcess.ChildTaskId = taskChildId
	assetProcess.Ip = ip
	assetProcess.NetworkType = networkType
	assetProcess.PersonField = personField
	assetProcess.Status = 1
	assetProcess.Mac = gjson.Get(value.String(), "macAddress").String()

	createTimeStr := gjson.Get(value.String(), "timestamp").String()
	if createTimeStr == "" {
		assetProcess.CreatedAt = localtime.NewLocalTime(time.Now())
		assetProcess.UpdatedAt = localtime.NewLocalTime(time.Now())
	} else {
		layout := "2006-01-02 15:04:05"
		parsedTime, err := time.Parse(time.RFC3339, createTimeStr)
		if err != nil {
			fmt.Println("Error parsing time:", err.Error())
			return assetProcess
		}
		goTimeStr := parsedTime.Format(layout)
		t, _ := time.Parse(layout, goTimeStr)
		assetProcess.CreatedAt = localtime.NewLocalTime(t)
		assetProcess.UpdatedAt = localtime.NewLocalTime(t)
	}

	return assetProcess
}

func setTaskFailure(category, operation string, taskId uint64, err error, taskChildId uint64) {
	//更新主任务状态为失败
	taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, operation+" "+err.Error())
	if taskErr != nil {
		logs.GetSyncLogger().Errorf("MachLake "+category+" "+operation+" Task UpdateFailById err:%s", taskErr.Error())
	}

	//更新子任务状态为失败
	childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, operation+" "+err.Error())
	if childTaskErr != nil {
		logs.GetSyncLogger().Errorf("MachLake "+category+" "+operation+" childTask UpdateFailById err:%s", childTaskErr.Error())
	}
}

func IsIP(addr string) bool {
	ip := net.ParseIP(addr)
	if ip != nil {
		return true
	}

	return false
}

func IsIPv4(addr string) bool {
	ip := net.ParseIP(addr)
	if ip != nil && ip.To4() != nil {
		return true
	}

	return false
}

func IsInIpRanges(ipStr, ipRanges string) bool {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return false
	}

	if strings.TrimSpace(ipRanges) == "" {
		return true
	}

	ipRangeArr := strings.Split(ipRanges, "\n")
	ipRangeCidrs := utils.ConvertIPsToCIDR(ipRangeArr)
	logs.GetSyncLogger().Infof("SyncMachLake IsInIpRanges ipRangeCidrs: ", ipRangeCidrs)
	for _, ipRangeCidr := range ipRangeCidrs {
		_, ipNet, err := net.ParseCIDR(ipRangeCidr)
		if err != nil {
			fmt.Println("解析 IP 段出错:", err)
			continue
		}
		if ipNet.Contains(ip) {
			logs.GetSyncLogger().Infof("SyncMachLake IsInIpRanges ipRanges", ipStr, true)
			return true
		}
	}
	return false
}
