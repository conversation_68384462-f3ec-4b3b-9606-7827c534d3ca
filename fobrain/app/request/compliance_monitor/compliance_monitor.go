package compliance_monitor

import (
	"fobrain/fobrain/common/request"
)

type IndexRequest struct {
	request.PageRequest
	Keyword string `json:"keyword" form:"keyword" uri:"keyword" validate:"omitempty,max=100" zh:"模糊搜索"`
	Event   []int  `json:"event" form:"event" uri:"event" validate:"omitempty" zh:"触发类型： 1立即执行 2仅执行一次 3每天 4每周 5每月"`
}

type StoreRequest struct {
	Name      string `json:"name" form:"name" uri:"name" validate:"required" zh:"规则名称"`
	Desc      string `json:"desc" form:"desc" uri:"desc" validate:"omitempty" zh:"规则描述"`
	Rule      string `json:"rule" form:"rule" uri:"rule" validate:"required" zh:"规则"`
	RuleType  int    `json:"rule_type" form:"rule_type" uri:"rule_type" validate:"omitempty,number,oneof=1 2 3" zh:"规则类型:  1:端口协议组件监测 2:IP监测 3:两高一弱规则监测"`
	TaskModel int    `json:"task_model" form:"task_model" uri:"task_model" validate:"omitempty,number,oneof=1 2" zh:"任务模式：1标记模式 2列表模式"`
	AssetType int    `json:"asset_type" form:"asset_type" uri:"asset_type" validate:"omitempty,number,oneof=0 1 2" zh:"资产类型：0全部 1内网 2外网"`
	Event     int    `json:"event" form:"event" uri:"event" validate:"required,number,oneof=1 2 3 4 5" zh:"触发类型： 1立即执行 2仅执行一次 3每天 4每周 5每月"`
	Date      string `json:"date" form:"date" uri:"date" validate:"omitempty" zh:"日前/月/周"`
	Time      string `json:"time" form:"time" uri:"time" validate:"omitempty" zh:"开始时间"`
}

type UpdateRequest struct {
	Id        uint64 `json:"id" form:"id" uri:"id" validate:"required" zh:"规则id"`
	Name      string `json:"name" form:"name" uri:"name" validate:"required" zh:"规则名称"`
	Desc      string `json:"desc" form:"desc" uri:"desc" validate:"omitempty" zh:"规则描述"`
	Rule      string `json:"rule" form:"rule" uri:"rule" validate:"required" zh:"规则"`
	RuleType  int    `json:"rule_type" form:"rule_type" uri:"rule_type" validate:"omitempty,number,oneof=1 2 3" zh:"规则类型:  1:端口协议组件监测 2:IP监测 3:两高一弱规则监测"`
	TaskModel int    `json:"task_model" form:"task_model" uri:"task_model" validate:"omitempty,number,oneof=1 2" zh:"任务模式：1标记模式 2列表模式"`
	AssetType int    `json:"asset_type" form:"asset_type" uri:"asset_type" validate:"omitempty,number,oneof=0 1 2" zh:"资产类型：0全部 1内网 2外网"`
	Event     int    `json:"event" form:"event" uri:"event" validate:"required,number,oneof=1 2 3 4 5" zh:"触发类型： 1立即执行 2仅执行一次 3每天 4每周 5每月"`
	Date      string `json:"date" form:"date" uri:"date" validate:"omitempty" zh:"日前/月/周"`
	Time      string `json:"time" form:"time" uri:"time" validate:"omitempty" zh:"开始时间"`
}

type SetStatusRequest struct {
	Id     uint64 `json:"id" form:"id" uri:"id" validate:"required" zh:"规则id"`
	Status int    `json:"status" form:"status" uri:"status" validate:"required,number,oneof=1 2" zh:"合规监测状态：1启用 2禁用"`
}
