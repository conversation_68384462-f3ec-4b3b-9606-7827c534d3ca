package file_import

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/olivere/elastic/v7"
)

type (
	FileImportTaskThreats struct {
		*es.BaseModel `json:"-"`
		Id            string         `json:"id"`            // NODE+id+区域+Task 作为唯一值去更新，用作记录每次任务拉取数据
		TaskId        string         `json:"task_id"`       //
		Area          int            `json:"area"`          // 区域
		Source        int            `json:"source"`        // 来源
		Node          int            `json:"node"`          // 节点
		Ip            string         `json:"ip"`            // ip
		Port          int            `json:"port"`          // 端口
		Url           string         `json:"url"`           //  漏洞地址
		Level         int            `json:"level"`         //  漏洞等级 低危：0 中危：1 高危：2 严重：3 未知：4
		Name          string         `json:"name"`          // 漏洞名称
		VulType       string         `json:"vulType"`       // 漏洞类型
		Cve           string         `json:"cve"`           // cve编号
		Cnvd          string         `json:"cnvd"`          // cnvd编号
		Cnnvd         string         `json:"cnnvd"`         // cnnvd编号
		HasExp        int            `json:"has_exp"`       // 是否存在exp
		HasPoc        int            `json:"has_poc"`       // 是否存在poc
		Status        int            `json:"status"`        // 状态
		Describe      string         `json:"describe"`      // 描述
		Details       string         `json:"details"`       // 详情
		Hazard        string         `json:"hazard"`        // 危险性
		Suggestions   string         `json:"suggestions"`   // 修复建议
		LastResponse  string         `json:"last_response"` // 最后一次响应
		CreatedAt     localtime.Time `json:"created_at"`    // 创建时间
		UpdatedAt     localtime.Time `json:"updated_at"`    // 更新时间
	}
)

// IndexName 索引名
func (f *FileImportTaskThreats) IndexName() string {
	return "file_import_task_threats"
}

func (f *FileImportTaskThreats) String() string {
	if f == nil {
		return ""
	}
	return ""
}

func NewFileImportTaskThreatsModel() *FileImportTaskThreats {
	return &FileImportTaskThreats{}
}

func (f *FileImportTaskThreats) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("ip").
		Field("common_title.keyword").
		Field("vulType.keyword")

	return field
}

func (f *FileImportTaskThreats) GetThreatListFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "漏洞地址", "key": "url"},
		{"name": "漏洞等级", "key": "level"},
		{"name": "漏洞类型", "key": "vulType"},
		{"name": "漏洞名称", "key": "common_title"},
		{"name": "CVE编号", "key": "cve"},
		{"name": "CNVD编号", "key": "cnvd"},
		{"name": "CNNVD编号", "key": "cnnvd"},
		{"name": "描述", "key": "describe"},
	}
}

func (f *FileImportTaskThreats) GetThreatListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "Port", "key": "port"},
		{"name": "漏洞地址", "key": "url"},
		{"name": "漏洞等级", "key": "level"},
		{"name": "漏洞类型", "key": "vulType"},
		{"name": "漏洞名称", "key": "common_title"},
		{"name": "CVE编号", "key": "cve"},
		{"name": "CNVD编号", "key": "cnvd"},
		{"name": "CNNVD编号", "key": "cnnvd"},
		{"name": "是否存在exp", "key": "has_exp"},
		{"name": "是否存在poc", "key": "has_poc"},
		{"name": "描述", "key": "describe"},
		{"name": "详情", "key": "details"},
		{"name": "危险性", "key": "hazard"},
		{"name": "修复建议", "key": "suggestions"},
		{"name": "最后一次响应", "key": "last_response"},
	}
}
