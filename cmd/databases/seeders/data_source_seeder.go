package seeders

import (
	"errors"
	"fmt"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/data_source"
	"gorm.io/gorm"
)

// 自6月12日以后，id>=34的以EnName+“_task_*”作为原始数据的任务索引
var sources = []data_source.Source{
	{BaseModel: mysql.BaseModel{Id: 1}, Name: "FOEYE·网络资产测绘及风险分析系统", EnName: "foeye", Type: "foeye", Icon: "/storage/app/public/icon/foeye.png", Status: 1, Show: 1, Sort: 100, IsTaskSync: true, IsCronSync: true, HasAssetData: true, HasVulData: true, Version: "v3.0", HasSync: 1, Desc: "基于华顺信安自主研发的网络空间测绘技术，对企业内部网络资产数据进行全面的采集，配合漏洞扫描、资产合规性监测等安全防护模块，为企业的网络系统提供强大的安全监控和应急响应能力"},
	{BaseModel: mysql.BaseModel{Id: 2}, Name: "FORadar·互联网资产攻击面管理平台", EnName: "foradar_saas", Type: "foradar_saas", Icon: "/storage/app/public/icon/foradar.png", Status: 1, Show: 1, Sort: 100, Version: "v1.13.5", IsCronSync: true, HasAssetData: true, HasVulData: true, HasSync: 1, Desc: "基于互联网资产引擎技术，通过云端推荐算法深度挖掘客户暴露在互联网侧的影子资产，同时配合漏洞扫描、数据泄露等安全检测模块，对企业互联网资产数据进行全面、高效、精准的采集和分析。以攻击者视角洞悉资产风险，为企业资产管理者提供资产外部攻击面的一站式管理，简化资产管理流程。"},
	{BaseModel: mysql.BaseModel{Id: 3}, Name: "万相·主机自适应安全平台", EnName: "qty", Type: "qty", Icon: "/storage/app/public/icon/qty.png", Version: "v3.4.15.8", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasVulData: true, HasPersonnelData: false, HasSync: 1, Desc: "采⽤自适应安全架构，有效解决传统专注防御手段的被动处境，为系统添加强大的实时监控和响应能⼒，帮助企业有效预测风险，精准感知威胁，提升响应效率，保障企业安全的最后⼀公⾥。"},
	{BaseModel: mysql.BaseModel{Id: 4}, Name: "钉钉", EnName: "dingtalk", Type: "dingtalk", Icon: "/storage/app/public/icon/dingtalk.png", Status: 1, Show: 1, Sort: 100, Version: "v7.6.15", IsCronSync: true, HasPersonnelData: true, HasSync: 1, Desc: "钉钉(DingTalk)是阿里巴巴集团打造的企业级智能移动办公平台，引领未来新一代工作方式,将陪伴每一个企业成长，是数字经济时代的企业组织协同办公和应用开发平台，是新生产力工具。"},
	{BaseModel: mysql.BaseModel{Id: 5}, Name: "蓝鲸CMDB", EnName: "bk_cmdb", Type: "bk_cmdb", Icon: "/storage/app/public/icon/bk_cmdb.png", Version: "v6.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasPersonnelData: true, HasSync: 1, Desc: "嘉为蓝鲸配置管理中心软件（CMDB）是面向企业数字化运维的新一代CMDB产品，我们围绕以应用为中心建立企业元数据管理平台的理念，以深度自动发现、无缝流程联动、灵活数据消费、智能数据可视的功能特色为支撑，帮助企业解决数据管理低效率、低准确、高延迟、难消费的问题现状，高效治理频繁变化的IT资产，为IT运维体系提供可信有效的数据支撑。"},
	{BaseModel: mysql.BaseModel{Id: 6}, Name: "文件导入", EnName: "file_import", Type: "file_import", Icon: "/storage/app/public/icon/import.png", Status: 1, Show: 1, Sort: 100, Version: "", IsTaskSync: true, IsCronSync: true, HasAssetData: true, HasVulData: true, HasPersonnelData: true, HasSync: 2, Desc: ""},
	{BaseModel: mysql.BaseModel{Id: 7}, Name: "人工添加", EnName: "handwork", Type: "handwork", Icon: "/storage/app/public/icon/handwork.png", Show: 0, Sort: 100, HasSync: 2, Desc: ""},
	{BaseModel: mysql.BaseModel{Id: 8}, Name: "微步·威胁感知平台TDP", EnName: "WeiBuTDP", Type: "tdp", Icon: "/storage/app/public/icon/weibu_tdp.png", Status: 1, Show: 1, Sort: 100, IsTaskSync: false, IsCronSync: true, HasAssetData: true, HasVulData: true, Version: "v3.2.18", HasSync: 1, Desc: "资产全面梳理，攻击者视角排查风险;聚焦真实威胁，告别海量报警;自动化分析，明辨蛛丝马迹;闭环自动化响应，告别手动低效"},
	{BaseModel: mysql.BaseModel{Id: 9}, Name: "雷池·下一代Web应用防火墙", EnName: "LongiWAF", Type: "changting_waf", Icon: "/storage/app/public/icon/changting_waf.jpg", Status: 1, Show: 1, Sort: 100, IsTaskSync: false, IsCronSync: true, HasAssetData: true, HasVulData: false, Version: "高级版", HasSync: 1, Desc: "雷池（SafeLine）Web 应用防火墙"},
	{BaseModel: mysql.BaseModel{Id: 10}, Name: "D01·网络风险资产监测分析系统", EnName: "d01", Type: "d01", Icon: "/storage/app/public/icon/d01.png", Status: 1, Show: 1, Sort: 100, IsTaskSync: true, IsCronSync: true, HasAssetData: true, HasVulData: true, Version: "v3.0", HasSync: 1, Desc: "基于公安部一所自主研发的网络空间测绘技术，对企业内部网络资产数据进行全面的采集，配合漏洞扫描、资产合规性监测等安全防护模块，为企业的网络系统提供强大的安全监控和应急响应能力"},
	{BaseModel: mysql.BaseModel{Id: 11}, Name: "齐治堡垒机", EnName: "qizhi_uaudithost", Type: "qizhi_uaudithost", Icon: "/storage/app/public/icon/qizhi_uaudithost.png", Status: 1, Show: 1, Sort: 100, Version: "v3.3.12", IsCronSync: true, HasAssetData: true, HasSync: 1, Desc: "作为堡垒机的开创者——齐治科技，针对云端运维安全需求推出基于云计算技术架构的云堡垒机产品，解决云端面临云资产违规访问、云业务误操作、云数据外泄、云运维无监控等云IT运维管理难题。"},
	{BaseModel: mysql.BaseModel{Id: 12}, Name: "私有云-阿里云云盾", EnName: "aliyun_cloud", Type: "aliyun_cloud", Icon: "/storage/app/public/icon/yundun.png", Status: 1, Show: 1, Sort: 100, Version: "v3.16.2", IsCronSync: true, HasVulData: true, HasSync: 1, Desc: "云盾是阿里巴巴集团多年来安全技术研究积累的成果，结合阿里云云计算平台强大的数据分析能力。 为中小网 站提供如安全漏洞检测、网页木马检测以及面向云服务器用户提供的主机入侵检测、防DDOS等一站式安全服务。"},
	{BaseModel: mysql.BaseModel{Id: 13}, Name: "马赫谷·TFA网络全流量溯源与分析系统", EnName: "MachLake", Type: "tfa", Icon: "/storage/app/public/icon/mach_lake_logo.png", Status: 1, Show: 1, Sort: 100, IsTaskSync: false, IsCronSync: true, HasAssetData: true, HasVulData: false, Version: "1.2.0", HasSync: 1, Desc: "MachLake-TFA"},
	{BaseModel: mysql.BaseModel{Id: 14}, Name: "X-RAY·长亭洞鉴安全评估系统", EnName: "x_ray", Type: "x_ray", Icon: "/storage/app/public/icon/x_ray.png", Status: 1, Show: 1, Sort: 100, IsTaskSync: true, IsCronSync: true, HasAssetData: true, HasVulData: true, Version: "v10-24.03.001_r9", HasSync: 1, Desc: ""},
	{BaseModel: mysql.BaseModel{Id: 15}, Name: "蓝鲸CMDB_自定义虚拟机", EnName: "bk_cmdb_vm_machine", Type: "bk_cmdb_vm_machine", Icon: "/storage/app/public/icon/cmdb_vm.png", Version: "v6.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasSync: 1, Desc: "嘉为蓝鲸配置管理中心软件（CMDB）是面向企业数字化运维的新一代CMDB产品，我们围绕以应用为中心建立企业元数据管理平台的理念，以深度自动发现、无缝流程联动、灵活数据消费、智能数据可视的功能特色为支撑，帮助企业解决数据管理低效率、低准确、高延迟、难消费的问题现状，高效治理频繁变化的IT资产，为IT运维体系提供可信有效的数据支撑。"},
	{BaseModel: mysql.BaseModel{Id: 16}, Name: "蓝鲸CMDB_自定义ECS", EnName: "bk_cmdb_cloud_ecs", Type: "bk_cmdb_cloud_ecs", Icon: "/storage/app/public/icon/cmdb_ecs.png", Version: "v6.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasSync: 1, Desc: "嘉为蓝鲸配置管理中心软件（CMDB）是面向企业数字化运维的新一代CMDB产品，我们围绕以应用为中心建立企业元数据管理平台的理念，以深度自动发现、无缝流程联动、灵活数据消费、智能数据可视的功能特色为支撑，帮助企业解决数据管理低效率、低准确、高延迟、难消费的问题现状，高效治理频繁变化的IT资产，为IT运维体系提供可信有效的数据支撑。"},
	{BaseModel: mysql.BaseModel{Id: 17}, Name: "华为云-中移香港", EnName: "huawei_hk_cloud", Type: "huawei_hk_cloud", Icon: "/storage/app/public/icon/huawei_cloud.png", Status: 1, Show: 1, Sort: 100, IsTaskSync: false, IsCronSync: true, HasAssetData: true, HasVulData: false, Version: "1.2.0", HasSync: 1, Desc: "华为云提供稳定可靠、安全可信、可持续发展的云服务,致力于让云无处不在,让智能无所不及,共建智能世界云底座。助力企业降本增效,全球300万客户的共同选择。"},
	{BaseModel: mysql.BaseModel{Id: 18}, Name: "蓝鲸CMDB_自定义域名", EnName: "bk_cmdb_domain", Type: "bk_cmdb_domain", Icon: "/storage/app/public/icon/domain.png", Version: "v6.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasSync: 1, Desc: "嘉为蓝鲸配置管理中心软件（CMDB）是面向企业数字化运维的新一代CMDB产品，我们围绕以应用为中心建立企业元数据管理平台的理念，以深度自动发现、无缝流程联动、灵活数据消费、智能数据可视的功能特色为支撑，帮助企业解决数据管理低效率、低准确、高延迟、难消费的问题现状，高效治理频繁变化的IT资产，为IT运维体系提供可信有效的数据支撑。"},
	{BaseModel: mysql.BaseModel{Id: 19}, Name: "蓝鲸CMDB_自定义业务", EnName: "bk_cmdb_business", Type: "bk_cmdb_business", Icon: "/storage/app/public/icon/business.png", Version: "v6.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasSync: 1, Desc: "嘉为蓝鲸配置管理中心软件（CMDB）是面向企业数字化运维的新一代CMDB产品，我们围绕以应用为中心建立企业元数据管理平台的理念，以深度自动发现、无缝流程联动、灵活数据消费、智能数据可视的功能特色为支撑，帮助企业解决数据管理低效率、低准确、高延迟、难消费的问题现状，高效治理频繁变化的IT资产，为IT运维体系提供可信有效的数据支撑。"},
	{BaseModel: mysql.BaseModel{Id: 20}, Name: "蓝鲸CMDB_自定义F5_VS", EnName: "bk_cmdb_f5_vs", Type: "bk_cmdb_f5_vs", Icon: "/storage/app/public/icon/f5_vs.png", Version: "v6.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasSync: 1, Desc: "嘉为蓝鲸配置管理中心软件（CMDB）是面向企业数字化运维的新一代CMDB产品，我们围绕以应用为中心建立企业元数据管理平台的理念，以深度自动发现、无缝流程联动、灵活数据消费、智能数据可视的功能特色为支撑，帮助企业解决数据管理低效率、低准确、高延迟、难消费的问题现状，高效治理频繁变化的IT资产，为IT运维体系提供可信有效的数据支撑。"},
	// 蓝鲸CMDB_自定义F5_POOL 数据源不启用不显示，但是要初始化到数据库，保证策略显示正常
	{BaseModel: mysql.BaseModel{Id: 21}, Name: "蓝鲸CMDB_自定义F5_POOL", EnName: "bk_cmdb_f5_pool", Type: "bk_cmdb_f5_pool", Icon: "/storage/app/public/icon/f5_pool.png", Version: "v6.0", Status: 2, Show: 2, Sort: 100, IsCronSync: true, HasAssetData: true, HasSync: 1, Desc: "嘉为蓝鲸配置管理中心软件（CMDB）是面向企业数字化运维的新一代CMDB产品，我们围绕以应用为中心建立企业元数据管理平台的理念，以深度自动发现、无缝流程联动、灵活数据消费、智能数据可视的功能特色为支撑，帮助企业解决数据管理低效率、低准确、高延迟、难消费的问题现状，高效治理频繁变化的IT资产，为IT运维体系提供可信有效的数据支撑。"},
	{BaseModel: mysql.BaseModel{Id: 22}, Name: "飞书-中移香港", EnName: "zhongyi_feishu", Type: "zhongyi_feishu", Icon: "/storage/app/public/icon/feishu.png", Version: "v1.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasPersonnelData: true, HasSync: 1, Desc: "飞书是先进企业协作与管理平台，一站式整合了即时消息、日历、云文档、视频会议等功能，通过顺畅沟通与高效协同，助力企业或团队组织升级。"},
	{BaseModel: mysql.BaseModel{Id: 23}, Name: "云图·长亭攻击面管理运营平台", EnName: "yuntu", Type: "yuntu", Icon: "/storage/app/public/icon/yuntu.svg", Version: "v1.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasVulData: true, HasPersonnelData: false, HasSync: 1, Desc: "长亭云图是一款攻击面管理运营平台，主要解决企业在复杂IT环境中资产可见性不足、攻击面扩大以及安全风险难以持续管理的问题。"},
	{BaseModel: mysql.BaseModel{Id: 24}, Name: "UCLOUD优刻得", EnName: "ucloud", Type: "ucloud", Icon: "/storage/app/public/icon/youkede.png", Version: "v1.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasVulData: false, HasPersonnelData: false, HasSync: 1, Desc: "云主机 UHost 安全稳定、可弹性扩展的高性能云服务器。"},
	{BaseModel: mysql.BaseModel{Id: 25}, Name: "日志易", EnName: "rizhiyi", Type: "rizhiyi", Icon: "/storage/app/public/icon/rizhiyi.png", Version: "v1.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasVulData: false, HasPersonnelData: true, HasSync: 1, Desc: "日志易是日志管理平台，主要解决企业在复杂IT环境中资产可见性不足、攻击面扩大以及安全风险难以持续管理的问题。"},
	{BaseModel: mysql.BaseModel{Id: 26}, Name: "私有云-阿里云负载均衡SLB", EnName: "aliyun_slb", Type: "aliyun_slb", Icon: "/storage/app/public/icon/aliyun_slb.ico", Version: "v1.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasSync: 1, Desc: "阿里云负载均衡（Server Load Balancer，简称SLB）是对云上流量进行按需分发的服务。通过将流量分发到不同的后端服务来扩展应用系统的服务吞吐能力，消除单点故障并提升应用系统的可用性。"},
	//{BaseModel: mysql.BaseModel{Id: 27}, Name: "公有云-阿里云负载均衡", EnName: "public_aliyun_slb", Type: "public_aliyun_slb", Icon: "/storage/app/public/icon/aliyun_slb.ico", Version: "v1.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasSync: 1, Desc: "阿里云负载均衡（Server Load Balancer，简称SLB）是对云上流量进行按需分发的服务。通过将流量分发到不同的后端服务来扩展应用系统的服务吞吐能力，消除单点故障并提升应用系统的可用性。"},
	//{BaseModel: mysql.BaseModel{Id: 28}, Name: "公有云-阿里云ECS", EnName: "public_aliyun_ecs", Type: "public_aliyun_ecs", Icon: "/storage/app/public/icon/aliyun_slb.ico", Version: "v1.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasSync: 1, Desc: "阿里云服务器ECS是一种处理能力可弹性伸缩的云主机虚拟服务器，它使服务器托管更安全稳定，可降低开发运维成本。"},
	{BaseModel: mysql.BaseModel{Id: 29}, Name: "公有云-腾讯云CVM", EnName: "public_tencent_cvm", Type: "public_tencent_cvm", Icon: "/storage/app/public/icon/tencent.ico", Version: "v1.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasSync: 1, Desc: "腾讯云服务器（CVM）为您提供安全可靠的弹性云计算服务。 只需几分钟，您就可以在云端获取和启用云服务器，并实时扩展或缩减云计算资源。"},
	{BaseModel: mysql.BaseModel{Id: 30}, Name: "公有云-腾讯云负载均衡", EnName: "public_tencent_clb", Type: "public_tencent_clb", Icon: "/storage/app/public/icon/tencent.ico", Version: "v1.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasSync: 1, Desc: "负载均衡（Cloud Load Balancer，CLB）提供安全快捷的四七层流量分发服务，访问流量经由 CLB 可以自动分配到多台后端服务器上，扩展系统的服务能力并消除单点故障。轻松应对大流量访问场景。 网关负载均衡(Gateway Load Balancer，GWLB)是运行在网络层的负载均衡。通过 GWLB 可以帮助客户部署、扩展和管理第三方虚拟设备，操作简单，安全性强。"},
	{BaseModel: mysql.BaseModel{Id: 31}, Name: "公有云-华为云ECS", EnName: "public_huawei_ecs", Type: "public_huawei_ecs", Icon: "/storage/app/public/icon/huawei_cloud.png", Version: "v1.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasSync: 1, Desc: "华为云弹性云服务器(简称ECS)是一种可随时自助获取,可弹性伸缩的云服务器,用户打造可靠,安全,灵活,高效的应用环境,确保服务持久稳定运行,提升运维效率。"},
	// 深信服态势感知SIP
	{BaseModel: mysql.BaseModel{Id: 32}, Name: "深信服态势感知SIP", EnName: "sangfor_sip", Type: "sangfor_sip", Icon: "/storage/app/public/icon/sangfor_sip.ico", Version: "v1.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasSync: 1, HasVulData: true, Desc: "深信服的态势感知解决方案专注于为用户提供全方位的网络安全监控和预警服务。"},
	{BaseModel: mysql.BaseModel{Id: 33}, Name: "奇安信-统一服务器安全管理系统", EnName: "qianxin_server", Type: "qianxin_server", Icon: "/storage/app/public/icon/qianxin.ico", Version: "v8.0.3", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasVulData: false, HasPersonnelData: false, HasSync: 1, Desc: "奇安信网神统一服务器安全管理系统是面向政企用户的虚拟化、云、数据中心环境，保护其服务器、虚拟机、云主机、容器等IT基础设施安全的产品。通过网络微隔离、多引擎协同文件防护、主机网络入侵防御等防护方式，消除工作负载中的恶意扫描、漏洞攻击、病毒感染及横向传播等安全威胁，结合安全态势感知及可视化能力，对安全风险进行统一分析和展现。"},
	{BaseModel: mysql.BaseModel{Id: 34}, Name: "JumpServer 堡垒机", EnName: "jump_server", Type: "jump_server", Icon: "/storage/app/public/icon/jumpserver.png", Version: "v3.10.10", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasVulData: false, HasPersonnelData: false, HasSync: 1, Desc: "JumpServer 堡垒机帮助企业以更安全的方式管控和登录各种类型的资产。", CustomFieldConfig: `[{"field": "access_key_id","label": "AccessKeyID","component": "input"},{"field": "access_key_secret","label": "AccessKeySecret","component": "input"}]`},
	{BaseModel: mysql.BaseModel{Id: 35}, Name: "微步·终端安全管理平台OneSEC", EnName: "weibu_onesec", Type: "weibu_onesec", Icon: "/storage/app/public/icon/weibu_onesec.png", Version: "v1.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasSync: 1, Desc: "终端安全管理平台OneSEC，办公终端安全防护轻量化解决方案。", CustomFieldConfig: `[{"field": "appkey","label": "App Key","component": "input"},{"field": "appsecret","label": "App Secret","component": "input"}]`},
	{BaseModel: mysql.BaseModel{Id: 36}, Name: "绿盟-RSAS", EnName: "nsfocus_rsas", Type: "nsfocus_rsas", Icon: "/storage/app/public/icon/nsfocus.png", Version: "v1.0", Status: 1, Show: 1, Sort: 100, IsCronSync: false, IsTaskSync: true, HasAssetData: false, HasSync: 1, HasVulData: true, HasRelations: false, Desc: "绿盟-RSAS,它高效、全方位的检测网络中的各类脆弱性风险，提供专业、有效的安全分析和修补建议，并贴合安全管理流程对修补效果进行审计，最大程度减小受攻击面，是您身边专业的“漏洞管理专家”。"},
	{BaseModel: mysql.BaseModel{Id: 37}, Name: "K8S", EnName: "k8s", Type: "k8s", Icon: "/storage/app/public/icon/k8s.png", Version: "v1.18.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasVulData: false, HasPersonnelData: false, HasSync: 1, Desc: "K8S容器化", CustomFieldConfig: `[{"field": "config_file","label": "K8S配置文件","component": "text"}]`},
	{BaseModel: mysql.BaseModel{Id: 38}, Name: "海颐堡垒机", EnName: "haiyi_pas", Type: "haiyi_pas", Icon: "/storage/app/public/icon/haiyi.png", Version: "v2.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasVulData: false, HasPersonnelData: false, HasSync: 1, Desc: "海颐堡垒机", CustomFieldConfig: `[{"field": "tenant_alias","label": "租户名","component": "input", "empty": true},{"field": "user_name","label": "用户名","component": "input"},{"field": "password","label": "密码","component": "input"}]`},
	//{BaseModel: mysql.BaseModel{Id: 37}, Name: "深信服AF防火墙", EnName: "sangfor_af", Type: "sangfor_af", Icon: "/storage/app/public/icon/sangfor_sip.ico", Version: "v1.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: false, HasSync: 1, HasVulData: false, HasRelations: true, Desc: "深信服AF防火墙。"},
	{BaseModel: mysql.BaseModel{Id: 39}, Name: "奇安信堡垒机", EnName: "qianxin_pam", Type: "qianxin_pam", Icon: "/storage/app/public/icon/qianxin.ico", Version: "v1.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasVulData: false, HasPersonnelData: true, HasSync: 1, Desc: "奇安信网神运维安全管理与审计系统（堡垒机）是采用新一代智能运维技术框架，基于认证、授权、访问、审计的管理流程设计理念，实现对企事业IT中心的网络设备、数据库、安全设备、主机系统、中间件等资源统一运维管理和审计，具备集中化运维管控、运维过程实时监管、运维访问合规性控制、运维过程图形化审计等功能。", CustomFieldConfig: `[{"field": "access_key_id","label": "客户端ID","component": "input"},{"field": "access_key_secret","label": "密钥","component": "input"}]`},
	{BaseModel: mysql.BaseModel{Id: data_source.YouYunCMDBDataSourceId}, Name: "优云CMDB", EnName: "youyun_cmdb", Type: "youyun_cmdb", Icon: "/storage/app/public/icon/uyun_cmdb.png", Version: "v1.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasSync: 1, Desc: "优云CMDB是一个灵活、易于使用的配置管理数据库，用于存储和管理IT基础架构中所有组件的详细信息。", CustomFieldConfig: `[{"field": "api_key","label": "API Key","component": "input"},{"field": "filter","label": "查询条件（JSON）","component": "text","empty":true}]`},
	//华为云-私有云
	{BaseModel: mysql.BaseModel{Id: 41}, Name: "私有云-华为云ECS", EnName: "huawei_private_cloud_ecs",
		Type: "huawei_private_cloud_ecs", Icon: "/storage/app/public/icon/huawei_cloud.png", Version: "v1.0",
		Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasSync: 1, Desc: "华为云-私有云",
		CustomFieldConfig: `[{"field": "access_key_id","label": "AccessKeyID","component": "input"},{"field": "secret_access_key","label": "AccessKeySecret","component": "input"},{"field": "iam_endpoint","label": "IAM 服务地址","component": "input"},{"field": "ecs_endpoint","label": "ECS 服务地址","component": "input"},{"field": "project_id","label": "ProjectID","component": "input"}]`},
	{BaseModel: mysql.BaseModel{Id: 42}, Name: "青藤蜂巢", EnName: "qingteng_beehive", Type: "qingteng_beehive", Icon: "/storage/app/public/icon/qingteng_beehive.png", Version: "v3.4.1.57", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: false, HasVulData: true, HasPersonnelData: false, HasSync: 1, Desc: "青藤蜂巢能够很好集成到云原生复杂多变的环境中，如PaaS云平台、OpenShift、Kubernetes、Jenkins、Harbor、JFrog等等。通过提供覆盖容器全生命周期的一站式容器安全解决方案，青藤蜂巢可实现容器安全预测、防御、检测和响应的安全闭环。", CustomFieldConfig: `[{"field": "user_name","label": "用户名","component": "input"},{"field": "password","label": "密码","component": "input"},{"field": "token","label": "登录认证令牌","component": "input"}]`},
	{BaseModel: mysql.BaseModel{Id: 43}, Name: "Tenable Security Center", EnName: "tenable_sc", Type: "tenable_sc", Icon: "/storage/app/public/icon/tenable.png", Version: "v1.0", Status: 1, Show: 1, Sort: 100, HasVulData: true, IsCronSync: true, HasAssetData: false, HasSync: 1, Desc: "Tenable Security Center是一款领先的本地漏洞管理解决方案，它提供了灵活的本地或混合部署选项，使企业能够按照所需的方式管理数据。",
		CustomFieldConfig: `[{"field": "access_key","label": "AccessKey","component": "input"},{"field": "secret_key","label": "SecretKey","component": "input"}]`},
	{BaseModel: mysql.BaseModel{Id: 44}, Name: "优越科技CMDB", EnName: "youyue_cmdb", Type: "youyue_cmdb", Icon: "/storage/app/public/icon/youyue_cmdb.png", Version: "v1.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasVulData: false, HasPersonnelData: false, HasSync: 1, Desc: "CMDB（配置管理数据库）是IT服务管理的核心系统，用于集中存储和管理IT基础设施中的所有配置项", CustomFieldConfig: `[{"field": "user_name","label": "用户名","component": "input"},{"field": "password","label": "密码","component": "input"}]`},
	{BaseModel: mysql.BaseModel{Id: data_source.SangforSSLVPNSourceId}, Name: "深信服SSL VPN", EnName: "sangfor_ssl_vpn", Type: "sangfor_ssl_vpn", Icon: "/storage/app/public/icon/sangfor_sip.ico", Version: "v1.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasVulData: false, HasPersonnelData: false, HasSync: 1, Desc: "深信服SSL VPN集SSL/IPSec于一身，帮助企业构建端到端的安全防护体系，业内拥有多项加密技术，多种认证方式、主从绑定等特色功能，保证远程系统接入的用户身份安全、终端/数据安全、传输安全、应用权限安全和审计安全，具有快速、易用、全面等优势特点。", CustomFieldConfig: `[{"field": "api_key","label": "API密钥","component": "input"}]`},
	{BaseModel: mysql.BaseModel{Id: data_source.AnhengAPT}, Name: "安恒APT", EnName: "anheng_apt", Type: "anheng_apt", Icon: "/storage/app/public/icon/anheng.jpg", Version: "v1.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasVulData: false, HasPersonnelData: false, HasSync: 1, Desc: "明御APT攻击预警平台具备实时、全面发现用户网络中的各种已知威胁和未知威胁能力，并提供完整的取证素材，对高级威胁进行溯源分析", CustomFieldConfig: `[{"field": "apikey","label": "Api Key","component": "input"}]`},
	{BaseModel: mysql.BaseModel{Id: 47}, Name: "奇安信天擎终端安全", EnName: "qianxin_tianqing", Type: "qianxin_tianqing", Icon: "/storage/app/public/icon/qianxin.ico", Version: "v1.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasVulData: false, HasPersonnelData: false, HasSync: 1, Desc: "奇安信天擎终端安全管理系统V10（简称奇安信天擎）的数据开放平台（简称ODP）作为对第三方系统提供应用编程接口数据服务，第三方系统可通过调用ODP接口查询终端安全相关状态数据。", CustomFieldConfig: `[{"field": "app_secret","label": "AppSecret","component": "input"},{"field": "app_key","label": "AppKey","component": "input"},{"field": "asset_oid","label": "组织ID","component": "input"},{"field": "asset_id","label": "资产ID","component": "input"}]`},
	{BaseModel: mysql.BaseModel{Id: 48}, Name: "腾讯UD", EnName: "tencent_ud", Type: "tencent_ud", Icon: "/storage/app/public/icon/tencent.ico", Version: "v1.0", Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: false, HasVulData: false, HasPersonnelData: true, HasSync: 1, Desc: "腾讯UD", CustomFieldConfig: `[{"field": "user_name","label": "用户名","component": "input"},{"field": "password","label": "密码","component": "input"},{"field": "secret_id","label": "SecretId","component": "input"},{"field": "secret_key","label": "SecretKey","component": "input"},{"field": "ud_service_id","label": "认证域ID","component": "input"}]`},
	{BaseModel: mysql.BaseModel{Id: 50}, Name: "私有云-华为ManagerOneECS", EnName: "huawei_manager_one",
		Type: "huawei_manager_one", Icon: "/storage/app/public/icon/huawei_cloud.png", Version: "v1.0",
		Status: 1, Show: 1, Sort: 100, IsCronSync: true, HasAssetData: true, HasSync: 1, Desc: "华为云-私有云",
		CustomFieldConfig: `[{"field": "username","label": "用户名","component": "input"},{"field": "password","label": "密码","component": "input"},{"field": "iam_endpoint","label": "IAM 服务地址","component": "input"},{"field": "ecs_endpoint","label": "ECS 服务地址","component": "input"}]`},
}

func GetDataSourceSeeder() []data_source.Source {
	return sources
}
func DataSourceSeeder() {
	for _, source := range sources {
		var old data_source.Source
		err := data_source.NewSourceModel().
			Where("id = ?", source.Id).
			First(&old).Error

		switch {
		case err == nil:
			// 存在 → 用 sources 里的最新值整体更新
			if err := data_source.NewSourceModel().Update(source); err != nil {
				fmt.Printf("update id=%d err: %v\n", source.Id, err)
			}
		case errors.Is(err, gorm.ErrRecordNotFound):
			// 不存在 → 插入
			if err := data_source.NewSourceModel().Create(&source); err != nil {
				fmt.Printf("insert id=%d err: %v\n", source.Id, err)
			}
		default:
			// 其他 DB 错误
			fmt.Printf("query id=%d err: %v\n", source.Id, err)
		}
	}
}
