package bk_cmdb_domain

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestBkCmdbDomainAssets_IndexName(t *testing.T) {
	assert.Equal(t, "bk_cmdb_domain_task_assets", NewBKCmdbDomainTaskAssetsModel().IndexName())
	assert.Equal(t, "", NewBKCmdbDomainTaskAssetsModel().String())
}

func TestBkCmdbDomainAssets_GetAssetListFields(t *testing.T) {
	fields := NewBKCmdbDomainTaskAssetsModel().GetAssetListFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "ID", "key": "bk_inst_id", "all_key": "bk_inst_id"},
		{"name": "域名", "key": "dns", "all_key": "dns"},
		{"name": "实例名", "key": "bk_inst_name", "all_key": "bk_inst_name"},
		{"name": "所属业务", "key": "biz", "all_key": "biz"},
		{"name": "IP", "key": "address", "all_key": "address"},
	}
	assert.Equal(t, expectedFields, fields, "字段不匹配")
}

func TestBkCmdbDomainAssets_GetAssetListDetailFields(t *testing.T) {
	fields := NewBKCmdbDomainTaskAssetsModel().GetAssetListDetailFields()

	// 验证返回的字段列表
	expectedFields := []map[string]string{
		{"name": "ID", "key": "bk_inst_id", "all_key": "bk_inst_id"},
		{"name": "域名", "key": "dns", "all_key": "dns"},
		{"name": "实例名", "key": "bk_inst_name", "all_key": "bk_inst_name"},
		{"name": "所属业务", "key": "biz", "all_key": "biz"},
		{"name": "IP", "key": "address", "all_key": "address"},
	}
	assert.Equal(t, expectedFields, fields, "字段不匹配")
}
