package foeye_proactive_scanning

import (
	"encoding/json"
	"fmt"
	"fobrain/fobrain/app/crontab"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/models/mysql/system_configs"
	"sort"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"

	"fobrain/fobrain/app/repository/proactive_task/proactive_task_infos"
	"fobrain/fobrain/app/repository/proactive_task/source_task"
	"fobrain/initialize/mysql"

	"fobrain/models/mysql/proactive_task_node_relations"

	"github.com/gin-gonic/gin"

	"fobrain/fobrain/app/repository/task_center/proactive_scan"
	"fobrain/fobrain/common/request"
	"fobrain/fobrain/common/response"
	crontab2 "fobrain/models/mysql/crontab"
	"fobrain/models/mysql/task"
)

type OnceListRequest struct {
	request.PageRequest
	Keyword string `json:"keyword" form:"keyword" uri:"keyword"`
	Field   string `json:"field" form:"field" uri:"field"  zh:"排序字段"`
	Order   string `json:"order" form:"order" uri:"order"  zh:"排序方式"`
}

type ScheduledListRequest struct {
	request.PageRequest
	Keyword string `json:"keyword" form:"keyword" uri:"keyword"`
	Field   string `json:"field" form:"field" uri:"field"  zh:"排序字段"`
	Order   string `json:"order" form:"order" uri:"order"  zh:"排序方式"`
}

type ScanNodeRequest struct {
	ToolId int `json:"tool_id" form:"tool_id" uri:"tool_id" validate:"required"`
	ModeId int `json:"mode_id" form:"mode_id" uri:"mode_id" validate:"required"`
}

type GetTaskIdRequest struct {
	TaskId int64 `json:"task_id" form:"task_id" uri:"task_id" validate:"required"`
}

type CreateRequest struct {
	Name            string         `json:"name" form:"name" uri:"name" validate:"required"`
	Desc            string         `json:"desc" form:"desc" uri:"desc"`
	TaskType        int            `json:"task_type" form:"task_type" uri:"task_type" validate:"required" zh:"任务类型, 1: 资产扫描, 2: 漏洞扫描, 3: 资产及漏洞扫描"`
	SourceId        int            `json:"source_id" form:"source_id" uri:"source_id" validate:"required" zh:"源扫描平台, 如: Foeye, D01"`
	RepeatEndTime   string         `json:"repeat_end_time" form:"repeat_end_time" uri:"repeat_end_time" zh:"结束重复时间"`
	ScanMode        int            `json:"scan_mode" form:"scan_mode" uri:"scan_mode" validate:"required" zh:"扫描模式 1.集中管控模式， 2. 节点模式"`
	ScanIpRangeType string         `json:"scan_ip_range_type" form:"scan_ip_range_type" uri:"scan_ip_range_type" zh:"扫描目标类型，user_input 可选项(输入ip信息为: 用户输入user_input,全部ip: all）"`
	ScanIpRanges    []string       `json:"scan_ip_ranges" form:"scan_ip_ranges" uri:"scan_ip_ranges" zh:"扫描目标, **********/24,**********/24"`
	PocScanType     string         `json:"poc_scan_type" form:"poc_scan_type" uri:"poc_scan_type"  zh:"POC范围类型： 全部PoC:all, 指定PoC:special"`
	SelectedPocs    string         `json:"selected_pocs" form:"selected_pocs" uri:"selected_pocs" zh:"该字段仅在 poc_scan_type 字段类型为special才可使用，内容为用户指定的POC"`
	ScanPort        int            `json:"scan_port" form:"scan_port" uri:"scan_port" zh:"扫描端口，记录端口分组ID"`
	ScanType        string         `json:"scan_type" form:"scan_type" uri:"scan_type" zh:"扫描类型(快速为:quick, 深度为:common, 存活扫描:ping)"`
	Bandwidth       int            `json:"bandwidth" form:"bandwidth" uri:"bandwidth" zh:"扫描兆宽，单位 kb"`
	Concurrency     int            `json:"concurrency" form:"concurrency" uri:"concurrency" zh:"并发: 0 代表根据带宽动态分配协议识别并发数其他正整数代表自定义的并发数"`
	OtherCfgs       map[string]any `json:"other_cfgs" form:"other_cfgs" uri:"other_cfgs" zh:"其他配置，存储 json 格式"`
	DataNodeIds     []int          `json:"data_node_ids" form:"data_node_ids" uri:"data_node_ids" validate:"required" zh:"节点ID"`
	SyncConfig      SyncConfig     `json:"sync_config" form:"sync_config" uri:"sync_config" validate:"required" zh:"同步配置"`
}

type ProactiveTasksConfig struct {
	task.ProactiveTasks
	SyncConfig SyncConfig `json:"sync_config" form:"sync_config" uri:"sync_config" validate:"required" zh:"同步配置"`
}

type SyncConfig struct {
	ScanPlan int      `json:"scan_plan" form:"scan_plan" uri:"scan_plan" validate:"required" zh:"扫描计划：1.立即执行、2.仅执行一次、3.每天、4.每周、5.每月"`
	Config   []Config `json:"config" form:"config" url:"config" zh:"同步任务配置详情"`
}

type Config struct {
	ScanPeriod string `json:"scan_period" form:"scan_period" uri:"scan_period" zh:"扫描周期：周几或者每月几号。"`
	ScanTime   string `json:"scan_time" form:"scan_time" uri:"scan_time" zh:"扫描时间、24小时制"`
}

type UpdateRequest struct {
	Id              int64          `json:"id" form:"id" uri:"id" validate:"required"`
	Name            string         `json:"name" form:"name" uri:"name" validate:"required"`
	Desc            string         `json:"desc" form:"desc" uri:"desc"`
	TaskType        int            `json:"task_type" form:"task_type" uri:"task_type" validate:"required" zh:"任务类型, 1: 资产扫描, 2: 漏洞扫描, 3: 资产及漏洞扫描"`
	SourceId        int            `json:"source_id" form:"source_id" uri:"source_id" validate:"required" zh:"源扫描平台, 如: Foeye, D01"`
	RepeatEndTime   string         `json:"repeat_end_time" form:"repeat_end_time" uri:"repeat_end_time" zh:"结束重复时间"`
	ScanMode        int            `json:"scan_mode" form:"scan_mode" uri:"scan_mode" validate:"required" zh:"扫描模式 1.集中管控模式， 2. 节点模式"`
	ScanIpRangeType string         `json:"scan_ip_range_type" form:"scan_ip_range_type" uri:"scan_ip_range_type" zh:"扫描目标类型，user_input 可选项(输入ip信息为: 用户输入user_input,全部ip: all）"`
	ScanIpRanges    []string       `json:"scan_ip_ranges" form:"scan_ip_ranges" uri:"scan_ip_ranges" zh:"扫描目标, **********/24,**********/24"`
	PocScanType     string         `json:"poc_scan_type" form:"poc_scan_type" uri:"poc_scan_type" zh:"POC范围类型： 全部PoC:all, 指定PoC:special"`
	SelectedPocs    string         `json:"selected_pocs" form:"selected_pocs" uri:"selected_pocs" zh:"该字段仅在 poc_scan_type 字段类型为special才可使用，内容为用户指定的POC"`
	ScanPort        int            `json:"scan_port" form:"scan_port" uri:"scan_port" zh:"扫描端口，记录端口分组ID"`
	ScanType        string         `json:"scan_type" form:"scan_type" uri:"scan_type" zh:"扫描类型(快速为:quick, 深度为:common, 存活扫描:ping)"`
	Bandwidth       int            `json:"bandwidth" form:"bandwidth" uri:"bandwidth" zh:"扫描兆宽，单位 kb"`
	Concurrency     int            `json:"concurrency" form:"concurrency" uri:"concurrency" zh:"并发: 0 代表根据带宽动态分配协议识别并发数其他正整数代表自定义的并发数"`
	OtherCfgs       map[string]any `json:"other_cfgs" form:"other_cfgs" uri:"other_cfgs" zh:"其他配置，存储 json 格式"`
	DataNodeIds     []int          `json:"data_node_ids" form:"data_node_ids" uri:"data_node_ids" validate:"required" zh:"节点ID"`
	SyncConfig      SyncConfig     `json:"sync_config" form:"sync_config" uri:"sync_config" validate:"required" zh:"同步配置"`
}

// 扫描结果
type AssetDataResponse struct {
	UID             string       `json:"uid"`
	ID              string       `json:"id"`
	IP              string       `json:"ip"`
	IPv6Raw         string       `json:"ipv6_raw"`
	State           int          `json:"state"`
	OS              string       `json:"os"`
	AssetLevel      string       `json:"asset_level"`
	PortList        []Port       `json:"port_list"`
	Domain          string       `json:"domain"`
	Hostname        string       `json:"hostname"`
	WebsiteImage    string       `json:"website_image"`
	WebsiteTitle    string       `json:"website_title"`
	RDPImage        string       `json:"rdp_image"`
	CreateTime      string       `json:"createtime"`
	LastUpdateTime  string       `json:"lastupdatetime"`
	MAC             string       `json:"mac"`
	IsIPv6          bool         `json:"is_ipv6"`
	Hosts           interface{}  `json:"hosts"` // 根据实际数据类型更改
	Name            string       `json:"name"`
	City            string       `json:"city"`
	BusinessApp     string       `json:"business_app"`
	Username        string       `json:"username"`
	ComputerRoom    string       `json:"computer_room"`
	Company         string       `json:"company"`
	ManagerEmail    string       `json:"manager_email"`
	ManagerMobile   string       `json:"manager_mobile"`
	CustomFields    interface{}  `json:"custom_fields"` // 根据实际数据类型更改
	ThreatStatus    ThreatStatus `json:"threat_status"`
	IsHoneypot      bool         `json:"is_honeypot"`
	IsFraud         bool         `json:"is_fraud"`
	IsXC            int          `json:"is_xc"`
	NodeID          int          `json:"node_id"`
	AreaID          int          `json:"area_id"`
	ProactiveTaskID int          `json:"proactive_task_id"`
}

type Port struct {
	Protocol     string     `json:"protocol"`
	Port         int        `json:"port"`
	RuleInfo     []RuleInfo `json:"rule_info"`
	Banner       string     `json:"banner"`
	Title        string     `json:"title"`
	Host         string     `json:"host"`
	Certs        string     `json:"certs"`
	CertString   string     `json:"cert_string"`
	WebsiteImage string     `json:"website_image"`
	LinkURL      string     `json:"link_url"`
}

type RuleInfo struct {
	Icon        string `json:"icon"`
	Name        string `json:"name"`
	Description string `json:"description"`
	IsThreat    bool   `json:"is_threat"`
	IsXC        int    `json:"is_xc"`
}

type ThreatStatus struct {
	ThreatIP           interface{} `json:"threat_ip"`
	ProxySoftware      interface{} `json:"proxy_software"`
	IntegrationTools   interface{} `json:"integration_tools"`
	Scanners           interface{} `json:"scanners"`
	RemoteOps          interface{} `json:"remote_ops"`
	NoStdPorts         interface{} `json:"no_std_ports"`
	RiskyPortAndServer []KeyValue  `json:"risky_port_and_server"`
}

type KeyValue struct {
	Key   int    `json:"key"`
	Value string `json:"value"`
}

func Create(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &CreateRequest{})
	if err != nil {
		return err
	}

	filterNums := 0

	// 检查时间间隔是否小于等于2小时
	if hasIntervalLessThanTwoHours(params.SyncConfig.Config) {
		return response.FailWithMessage(ctx, "时间间隔需要大于2个小时")
	}
	conf, errBan := system_configs.NewSystemConfigs().GetScanBanConfig()
	if errBan != nil {
		return response.FailWithMessage(ctx, "禁扫时间配置异常！")
	}
	if params.SyncConfig.ScanPlan != proactive_scan.ExecuteOnce {
		for _, cfg := range params.SyncConfig.Config {
			if system_configs.NewSystemConfigs().ScanTimeIsBan(conf, params.SyncConfig.ScanPlan, cfg.ScanPeriod, cfg.ScanTime) {
				return response.FailWithMessage(ctx, "任务扫描时间在禁扫时间内，请修改扫描时间！")
			}
		}
	}

	// 创建下发任务
	taskId, err, filterNums := proactive_scan.SaveTask(
		nil, params.Name, params.Desc, params.TaskType, params.SourceId, params.SyncConfig.ScanPlan,
		"", "", params.RepeatEndTime, params.ScanMode,
		params.ScanIpRangeType, params.ScanIpRanges, params.PocScanType, params.SelectedPocs,
		params.ScanPort, params.ScanType, params.Bandwidth, params.Concurrency, params.OtherCfgs,
		params.DataNodeIds, request.GetUserId(ctx),
	)
	if err != nil {
		return err
	}

	// 如果不是立即执行
	if params.SyncConfig.ScanPlan != proactive_scan.ExecuteOnce {
		for _, cfg := range params.SyncConfig.Config {
			// 创建定时任务的时间配置
			spec := generateCronSpec(cfg, params.SyncConfig.ScanPlan)
			// 创建任务的定时执行计划
			if err = createCronTask(taskId, spec, "MonitorTaskToStart", ctx); err != nil {
				return err
			}
			// 如果是仅执行一次，不需要创建删除任务
			if params.SyncConfig.ScanPlan == proactive_scan.ExecuteOnceAt {
				break
			}
			// 创建结束重复执行的定时任务
			if err = createCronTask(taskId, getEndTaskSpec(params.RepeatEndTime), "DelMonitorTask", ctx); err != nil {
				return err
			}
		}
	}

	return response.OkWithData(ctx, gin.H{
		"filter_nums": filterNums,
	})
}

// ScanTool
// @Summary 扫描工具列表
// @Route /api/v1/task_center/proactive_scan/foeye/scan_tool [GET]
func ScanTool(ctx *gin.Context) error {
	toolList, err := proactive_scan.ScanTool()
	if err != nil {
		return err
	}

	return response.OkWithData(ctx, toolList)
}

func ScanTypes(ctx *gin.Context) error {
	var param struct {
		NodeId   uint64 `json:"node_id" form:"node_id" binding:"required" zh:"节点ID"`
		SourceId uint64 `json:"source_id" form:"source_id" binding:"required" zh:"数据源ID"`
	}
	params, err := request.Validate(ctx, &param)
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}

	scanTypes, err := proactive_scan.ScanTypes(params.NodeId, params.SourceId)
	if err != nil {
		return response.FailWithMessage(ctx, err.Error())
	}

	return response.OkWithData(ctx, scanTypes)
}

// ScanNode
// @Summary 扫描节点列表
// @Route /api/v1/task_center/proactive_scan/foeye/scan_node [GET]
func ScanNode(ctx *gin.Context) error {
	params, err := request.Validate(ctx, &ScanNodeRequest{})
	if err != nil {
		return err
	}

	nodeList, err := proactive_scan.ScanNode(params.ToolId, params.ModeId)
	if err != nil {
		return err
	}

	return response.OkWithData(ctx, nodeList)
}

// ListOnce
// @Summary 单次任务列表
// @Route /api/v1/task_center/proactive_scan/foeye/once_list [GET]
func ListOnce(c *gin.Context) error {
	params, err := request.Validate(c, &OnceListRequest{})
	if err != nil {
		return err
	}
	list, total, err := task.NewProactiveTasks().ListOnce(params.Page, params.PerPage, params.Keyword, params.Field, params.Order)
	if err != nil {
		return err
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}

// GetScheduledTasksList
// @Summary 周期任务列表
// @Route /api/v1/task_center/proactive_scan/foeye/scheduled_list [GET]
func GetScheduledTasksList(c *gin.Context) error {
	params, err := request.Validate(c, &ScheduledListRequest{})
	if err != nil {
		return err
	}
	list, total, err := task.NewProactiveTasks().GetScheduledTasksList(params.Page, params.PerPage, params.Keyword, params.Field, params.Order)
	if err != nil {
		return err
	}
	return response.OkWithPageData(c, total, params.Page, params.PerPage, list)
}

// DelTask
// @Summary 删除任务
// @Route /api/v1/task_center/proactive_scan/foeye [DELETE]
func DelTask(c *gin.Context) error {
	params, err := request.Validate(c, &struct {
		Ids        []uint64 `json:"ids" uri:"ids" form:"ids"`
		DeleteType string   `json:"delete_type" uri:"delete_type" form:"delete_type"`
		Keyword    string   `json:"keyword" form:"keyword" uri:"keyword"`
	}{})
	if err != nil {
		return err
	}

	// 先删除 节点任务
	for _, taskId := range params.Ids {
		taskInfo, err := task.NewProactiveTasks().FindById(int64(taskId))
		if err != nil {
			return err
		}
		taskCli := source_task.New(taskInfo)
		_ = taskCli.DelTask()
	}

	// 在删除 平台任务
	err = task.NewProactiveTasks().DelTask(params.Ids, params.DeleteType, params.Keyword)
	if err != nil {
		return err
	}

	// 只有周期任务才有这部分操作
	if params.DeleteType == "cycle" {
		// 最后删除 定时规则
		var opts []mysql.HandleFunc
		var delInfo []string

		if len(params.Ids) == 0 { // 删除全部
			opts = append(opts, mysql.WithLike("name", "周期任务%到点执行"))
		} else { // 删除部分
			for i := 0; i < len(params.Ids); i++ {
				delInfo = append(delInfo, "周期任务"+strconv.Itoa(int(params.Ids[i]))+"到点执行")
			}
			opts = append(opts, mysql.WithValuesIn("name", delInfo))
		}

		err = crontab2.NewCrontabModel().Del(opts...)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil
			}
			return err
		}
	}

	return response.Ok(c)
}

// GetTaskDetail
// @Summary 任务详情
// @Route /api/v1/task_center/proactive_scan/foeye [GET]
func GetTaskDetail(c *gin.Context) error {
	params, err := request.Validate(c, &GetTaskIdRequest{})
	if err != nil {
		return err
	}
	taskInfo, err := task.NewProactiveTasks().GetTaskDetail(params.TaskId)
	if err != nil {
		return err
	}

	// 统计扫描结果
	assetData, _, _, _, err := proactive_task_infos.AssetList(params.TaskId, "", 1, 500)
	if err != nil {
		fmt.Println("Error fetching asset data:", err)
	}

	// 尝试将 assetData 转换为 JSON 字符串
	assetDataJSON, err := json.Marshal(assetData)
	if err != nil {
		fmt.Println("Error marshalling asset data:", err)
	}

	// 定义存储多个 Asset 结构体的切片
	var assets []AssetDataResponse

	// 反序列化 JSON 字符串为 assets 切片
	err = json.Unmarshal([]byte(assetDataJSON), &assets)
	if err != nil {
		fmt.Println("Error unmarshalling asset data:", err)
	}

	// 定义用于存储统计结果的变量
	totalPorts := 0
	totalRuleInfos := 0
	totalCompanies := 0

	// 遍历每个资产
	for _, asset := range assets {
		// 统计 port_list 中的端口数量
		totalPorts += len(asset.PortList)

		// 统计每个 port_list 中的 rule_info 数量
		for _, port := range asset.PortList {
			if port.RuleInfo != nil {
				totalRuleInfos += len(port.RuleInfo)
			}
		}

		// 统计 company 字段（根据具体需求可判断是否为空）
		if asset.Company != "" {
			totalCompanies++
		}
	}

	nodeIds, err := proactive_task_node_relations.GetNodeIdByTaskId([]uint64{(uint64(params.TaskId))})
	if err != nil {
		return err
	}
	taskInfo.NodeIds = nodeIds
	taskInfo.PortsNum = totalPorts
	taskInfo.RuleInfosNum = totalRuleInfos
	taskInfo.CompaniesNum = totalCompanies
	// 获取任务计划
	taskPlan, err := task.NewProactiveTasks().GetTaskPlan(uint64(params.TaskId))
	if err != nil {
		return err
	}
	taskInfo.TaskPlan = taskPlan

	syncConfig := SyncConfig{ScanPlan: taskInfo.ScanPlan}

	// 获取任务配置，从 crontab 表里面查询 taskId 对应的数据
	s := fmt.Sprintf(`{"taskId":%d}`, taskInfo.Id)
	opt := mysql.WithWhere("params = ? AND method = ?", s, "MonitorTaskToStart")
	cronConfig, _, err := crontab2.NewCrontabModel().List(0, 0, opt)
	if err != nil {
		return err
	}

	// 解析配置信息保存
	for _, cron := range cronConfig {
		config, err := parseCronSpec(cron.Spec, cron.CreatedAt)
		if err != nil {
			return err
		}
		syncConfig.Config = append(syncConfig.Config, config)
	}

	return response.OkWithData(c, ProactiveTasksConfig{
		ProactiveTasks: *taskInfo,
		SyncConfig:     syncConfig,
	})
}

// RetryTask
// @Summary 再次执行任务
// @Route /api/v1/task_center/proactive_scan/foeye/retry [POST]
func RetryTask(c *gin.Context) error {
	params, err := request.Validate(c, &GetTaskIdRequest{})
	if err != nil {
		return err
	}
	// 获取任务的详情
	taskInfo, err := task.NewProactiveTasks().GetTaskDetail(params.TaskId)
	if err != nil {
		return err
	}

	// 获取该任务对应的节点
	nodeIds, err := task.NewProactiveTasks().GetNodesByTask(params.TaskId)
	if err != nil {
		return err
	}

	_, err, filterNums := proactive_scan.SaveTask(nil, taskInfo.Name, taskInfo.Desc, taskInfo.TaskType, taskInfo.SourceId, taskInfo.ScanPlan, taskInfo.ScanPeriod,
		taskInfo.ScanTime, (taskInfo.RepeatEndTime).String(), taskInfo.ScanMode, taskInfo.ScanIPRangeType, taskInfo.ScanIPRanges, taskInfo.PocScanType,
		taskInfo.SelectedPocs, taskInfo.ScanPort, taskInfo.ScanType, taskInfo.Bandwidth, taskInfo.Concurrency, taskInfo.OtherCfgs, nodeIds, request.GetUserId(c))
	if err != nil {
		return err
	}
	return response.OkWithData(c, gin.H{
		"filter_nums": filterNums,
	})
}

// PortGroupList
// @Summary 后去端口分组信息
func PortGroupList(c *gin.Context) error {
	return response.OkWithData(c, []map[string]any{
		{"id": 7, "name": "全部常用端口"},
		{"id": 2, "name": "知名端口"},
		{"id": 3, "name": "常用端口TOP50"},
		{"id": 4, "name": "数据库端口"},
		{"id": 5, "name": "企业端口"},
		{"id": 6, "name": "工控端口"},
		{"id": 1, "name": "网络精简端口"},
		{"id": 8, "name": "视频网专用端口组"},
		{"id": 9, "name": "公安网专用端口组"},
		{"id": 10, "name": "运维端口"},
		{"id": 11, "name": "0-65535"},
		{"id": 12, "name": "现有端口组"},
		{"id": 13, "name": "全部预置端口组"},
		{"id": 15, "name": "存活专用端口"},
	})

}

// UpdateTask
// @Summary 修改任务信息
// @Route /api/v1/task_center/proactive_scan/foeye/update [PUT]
func UpdateTask(c *gin.Context) error {
	params, err := request.Validate(c, &UpdateRequest{})
	if err != nil {
		return err
	}
	conf, errBan := system_configs.NewSystemConfigs().GetScanBanConfig()
	if errBan != nil {
		return response.FailWithMessage(c, "禁扫时间配置异常！")
	}
	if params.SyncConfig.ScanPlan != proactive_scan.ExecuteOnce {
		for _, cfg := range params.SyncConfig.Config {
			if system_configs.NewSystemConfigs().ScanTimeIsBan(conf, params.SyncConfig.ScanPlan, cfg.ScanPeriod, cfg.ScanTime) {
				return response.FailWithMessage(c, "任务扫描时间在禁扫时间内，请修改扫描时间！")
			}
		}
	}

	_, err, _ = proactive_scan.SaveTask(
		&params.Id, params.Name, params.Desc, params.TaskType, params.SourceId, params.SyncConfig.ScanPlan,
		"", "", params.RepeatEndTime, params.ScanMode,
		params.ScanIpRangeType, params.ScanIpRanges, params.PocScanType, params.SelectedPocs,
		params.ScanPort, params.ScanType, params.Bandwidth, params.Concurrency, params.OtherCfgs,
		params.DataNodeIds, request.GetUserId(c),
	)
	if err != nil {
		return err
	}

	// 检查时间间隔是否小于等于2小时
	if hasIntervalLessThanTwoHours(params.SyncConfig.Config) {
		return response.FailWithMessage(c, "时间间隔需要大于2个小时")
	}

	s := fmt.Sprintf(`{"taskId":%d}`, params.Id)
	opt := mysql.WithWhere("params = ?", s)
	cronInfo, err := crontab2.NewCrontabModel().First(opt)

	if err != nil {
		return err
	}

	// 删除原来的配置
	if err = crontab2.NewCrontabModel().Del(opt); err != nil {
		return err
	}
	if cronInfo != nil {
		err = crontab.GetCronIns().RemoveJob(cronInfo)
		if nil != err {
			logs.GetLogger().Errorf("crontab remove crontab err:%s,name:%s,id:%d\n", err.Error(), params.Name, cronInfo.Id)
		}
	}

	// 如果不是立即执行
	if params.SyncConfig.ScanPlan != proactive_scan.ExecuteOnce {
		for _, cfg := range params.SyncConfig.Config {
			// 创建定时任务的时间配置
			spec := generateCronSpec(cfg, params.SyncConfig.ScanPlan)
			// 创建任务的定时执行计划
			if err = createCronTask(int(params.Id), spec, "MonitorTaskToStart", c); err != nil {
				return err
			}
			// 创建结束重复执行的定时任务
			if err = createCronTask(int(params.Id), getEndTaskSpec(params.RepeatEndTime), "DelMonitorTask", c); err != nil {
				return err
			}
		}
	}

	return response.Ok(c)
}

// StartTask
// @Summary 立即执行
// @Route /api/v1/task_center/proactive_scan/foeye/start [POST]
func StartTask(c *gin.Context) error {
	params, err := request.Validate(c, &GetTaskIdRequest{})
	if err != nil {
		return err
	}

	//获取任务的详情
	taskInfo, err := task.NewProactiveTasks().GetTaskDetail(params.TaskId)
	if err != nil {
		return err
	}

	// 获取该任务对应的节点
	nodeIds, err := task.NewProactiveTasks().GetNodesByTask(params.TaskId)
	if err != nil {
		return err
	}
	_, err, filterNums := proactive_scan.SaveTask(nil, taskInfo.Name, taskInfo.Desc, taskInfo.TaskType, taskInfo.SourceId, 1, "",
		taskInfo.ScanTime, (taskInfo.RepeatEndTime).String(), taskInfo.ScanMode, taskInfo.ScanIPRangeType, taskInfo.ScanIPRanges, taskInfo.PocScanType,
		taskInfo.SelectedPocs, taskInfo.ScanPort, taskInfo.ScanType, taskInfo.Bandwidth, taskInfo.Concurrency, taskInfo.OtherCfgs, nodeIds, uint64(taskInfo.UserId))

	if err != nil {
		return err
	}

	return response.OkWithData(c, gin.H{
		"filter_nums": filterNums,
	})

}

// IsOpenTask
// @Summary 开关任务
// @Route /api/v1/task_center/proactive_scan/foeye/is_open [PUT]
func IsOpenTask(c *gin.Context) error {
	params, err := request.Validate(c, &GetTaskIdRequest{})
	if err != nil {
		return err
	}
	isOpen, err := task.NewProactiveTasks().IsOpenTask(params.TaskId)
	if err != nil {
		return err
	}
	err = crontab2.NewCrontabModel().UpdateTaskStatus(params.TaskId, isOpen)
	if err != nil {
		return err
	}
	return response.Ok(c)
}

type TimeWithPeriod struct {
	Time       time.Time
	Day        int
	ScanPeriod int
}

const (
	timeLayout     = "15:04"
	twoHours       = 2 * time.Hour
	twentyTwoHours = 22 * time.Hour
)

func hasIntervalLessThanTwoHours(configs []Config) bool {

	var timeWithPeriods []TimeWithPeriod

	for _, config := range configs {
		period, err := strconv.Atoi(config.ScanPeriod)
		if err != nil {
			fmt.Printf("Invalid ScanPeriod: %s\n", config.ScanPeriod)
			continue // 跳过异常数据
		}

		parsedTime, err := time.Parse(timeLayout, config.ScanTime)
		if err != nil {
			fmt.Printf("Invalid time format in ScanTime: %s\n", config.ScanTime)
			continue // 跳过异常数据
		}

		timeWithPeriods = append(timeWithPeriods, TimeWithPeriod{
			Time:       parsedTime,
			Day:        parsedTime.Day(),
			ScanPeriod: period,
		})
	}

	// 如果有效数据不足两个，直接返回 false
	if len(timeWithPeriods) < 2 {
		return false
	}

	// 按 ScanPeriod 和时间排序
	sort.Slice(timeWithPeriods, func(i, j int) bool {
		if timeWithPeriods[i].ScanPeriod == timeWithPeriods[j].ScanPeriod {
			return timeWithPeriods[i].Time.Before(timeWithPeriods[j].Time)
		}
		return timeWithPeriods[i].ScanPeriod < timeWithPeriods[j].ScanPeriod
	})

	// 检查时间间隔
	for i := 0; i < len(timeWithPeriods)-1; i++ {
		curr := timeWithPeriods[i]
		next := timeWithPeriods[i+1]

		// 检查标准时间间隔
		if isIntervalWithinLimit(curr, next, twoHours) {
			return true
		}

		// 特殊规则：1号与28, 29, 30, 31号间隔不能小于2小时
		if isSpecialCaseInterval(curr, next, twoHours) {
			return true
		}
	}

	return false
}

// 检查两个 TimeWithPeriod 的间隔是否在限制范围内
func isIntervalWithinLimit(curr, next TimeWithPeriod, limit time.Duration) bool {
	return next.ScanPeriod-curr.ScanPeriod <= 1 && next.Time.Sub(curr.Time) <= limit
}

// 检查特殊规则：1号与28, 29, 30, 31号间隔
func isSpecialCaseInterval(curr, next TimeWithPeriod, limit time.Duration) bool {
	specialDays := map[int]bool{28: true, 29: true, 30: true, 31: true}

	if (curr.ScanPeriod == 1 && specialDays[next.ScanPeriod]) || (next.ScanPeriod == 1 && specialDays[curr.ScanPeriod]) {
		diff := next.Time.Sub(curr.Time)
		if diff < 0 {
			diff = -diff // 确保是绝对时间差
		}
		dayDiff := curr.ScanPeriod - next.ScanPeriod
		if dayDiff < 0 {
			dayDiff = -dayDiff
		}
		if dayDiff == 1 || dayDiff == 30 {
			if diff >= twentyTwoHours {
				return true
			}
		}
		return diff <= limit
	}
	return false
}

// 生成 Cron 表达式
func generateCronSpec(cfg Config, scanPlan int) string {
	monitorTimes := strings.SplitN(cfg.ScanTime, ":", 2)
	if len(monitorTimes) != 2 {
		return "* * * * *"
	}
	hour, minute := monitorTimes[0], monitorTimes[1]
	switch scanPlan {
	case proactive_scan.RunEveryWeek:
		return fmt.Sprintf("00 %s %s * * %s", minute, hour, cfg.ScanPeriod)
	case proactive_scan.RunEveryMonth:
		return fmt.Sprintf("00 %s %s %s * *", minute, hour, cfg.ScanPeriod)
	case proactive_scan.RunEveryDay:
		return fmt.Sprintf("00 %s %s * * *", minute, hour)
	case proactive_scan.ExecuteOnceAt:
		scanPeriod := strings.SplitN(cfg.ScanPeriod, "-", 3)
		if len(scanPeriod) != 3 {
			return "* * * * *"
		}
		return fmt.Sprintf("00 %s %s %s %s *", minute, hour, scanPeriod[2], scanPeriod[1])
	default:
		return "* * * * *"
	}
}

// 创建 Cron 任务
func createCronTask(taskId int, spec, method string, ctx *gin.Context) error {
	jsonString, _ := json.Marshal(map[string]uint64{"taskId": uint64(taskId)})
	data := &crontab2.CrontabModel{
		UserId: request.GetUserId(ctx),
		Name:   fmt.Sprintf("任务%d到点执行", taskId),
		Status: crontab2.StatusEnable,
		Type:   crontab2.TypeSystem,
		Spec:   spec,
		Method: method,
		Params: string(jsonString),
	}

	// 创建任务
	if err := crontab2.NewCrontabModel().Create(data); err != nil {
		return err
	}

	// 加载任务到 Cron 实例
	return crontab.GetCronIns().LoadMysqlJob(data.Id)
}

// 获取结束任务的 Cron 表达式
func getEndTaskSpec(repeatEndTime string) string {
	if repeatEndTime == "" {
		return ""
	}
	monitorEndTimes := strings.SplitN(repeatEndTime, "-", 3)
	return fmt.Sprintf("00 00 00 %s %s *", monitorEndTimes[2], monitorEndTimes[1])
}

// parseCronSpec 反向解析 Cron 表达式生成 Config
// parseCronSpec 反向解析 Cron 表达式生成 Config
func parseCronSpec(cronSpec string, createTime localtime.Time) (Config, error) {
	// 将 Cron 表达式拆分成字段（6个字段：秒、分、时、日、月、周）
	fields := strings.Fields(cronSpec)
	if len(fields) != 6 {
		return Config{}, fmt.Errorf("invalid cron expression: %s", cronSpec)
	}

	minute := fields[1] // 分钟
	hour := fields[2]   // 小时
	day := fields[3]    // 日
	month := fields[4]  // 月
	week := fields[5]   // 周

	// 初始化 Config 对象
	cfg := Config{}

	// 分析 Cron 表达式
	switch {
	case week != "*" && day == "*" && month == "*":
		// 每周扫描
		cfg.ScanTime = fmt.Sprintf("%s:%s", hour, minute)
		cfg.ScanPeriod = week
	case month == "*" && week == "*" && day != "*":
		// 每月扫描
		cfg.ScanTime = fmt.Sprintf("%s:%s", hour, minute)
		cfg.ScanPeriod = day
	case day == "*" && month == "*":
		// 每天扫描
		cfg.ScanTime = fmt.Sprintf("%s:%s", hour, minute)
	case month != "*" && week == "*" && day != "*":
		// 指定日期时间运行一次
		cfg.ScanTime = fmt.Sprintf("%s:%s", hour, minute)
		cfg.ScanPeriod = fmt.Sprintf("%s-%s-%s", createTime.Format(localtime.YearFormat), month, day)
	default:
		// 无法匹配的 Cron 表达式
		return Config{}, fmt.Errorf("unsupported cron expression: %s", cronSpec)
	}

	return cfg, nil
}
