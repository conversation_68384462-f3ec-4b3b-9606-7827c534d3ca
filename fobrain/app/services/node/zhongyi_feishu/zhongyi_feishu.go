package zhongyi_feishu

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/pkg/errors"
	"go-micro.dev/v4/logger"
)

const (
	BcocGenerateTokenApi   = "/bcoc/refreshToken"                          // 获取token
	bcocDeptDataApi        = "/bcoc/m/masterdata/dept/v1/cmhk/list"        // 查询组织(分⻚)
	bcocDeptUserRelDataApi = "/bcoc/m/masterdata/deptUserRel/v1/cmhk/list" // 查询组织⽤⼾关系(分⻚)
	bcocUserDataApi        = "/bcoc/m/masterdata/user/v1/cmhk/list"        // 查询⽤⼾信息(分⻚)
)

type BcocCommonRespInfo struct {
	Msg     string `json:"msg"`     //响应描述，异常时提示具体异常信息
	Current int    `json:"current"` //当前页数
	Total   int    `json:"total"`   //总数据量
	Code    int    `json:"code"`    //响应码，成功：200，其他失败
	Pages   int    `json:"pages"`   //总分页数量
	Size    int    `json:"size"`    //当前每页数据条数
}

// 2.1 查询组织(分⻚)
type BcocDeptDataRequest struct {
	OrderBy   string `json:"orderBy"`   //
	OrderSort string `json:"orderSort"` //
	PageNum   int    `json:"pageNum"`   //
	PageSize  int    `json:"pageSize"`  //
}

type BcocDeptDataResponse struct {
	BcocCommonRespInfo
	Data []DeptItem `json:"data"` //数据集
}
type DeptItem struct {
	TenantId           string      `json:"tenantId"`           //租户id
	DepartmentId       string      `json:"departmentId"`       //部门id
	NameCn             string      `json:"nameCn"`             //部门中文名
	NameUs             string      `json:"nameUs"`             //部门英文名
	ParentDepartmentId string      `json:"parentDepartmentId"` //上级部门id
	DeptOrder          string      `json:"deptOrder"`          //部门顺序
	LeaderUserId       interface{} `json:"leaderUserId"`       //部门管理员id
	MemberCount        string      `json:"memberCount"`        //部门下员工数量
	Status             string      `json:"status"`             //状态1-启用，2-禁用
}

// 2.2 查询组织⽤⼾关系(分⻚)
type BcocDeptUserRelRequest struct {
	DeptId   string `json:"deptId"`
	PageNum  int    `json:"pageNum"`
	PageSize int    `json:"pageSize"`
}

type BcocDeptUserRelResponse struct {
	BcocCommonRespInfo
	Data []DeptUserRelItem `json:"data"` //数据集
}

type DeptUserRelItem struct {
	TenantId     string      `json:"tenantId"`     //租户id
	DepartmentId string      `json:"departmentId"` //部门id
	UserId       string      `json:"userId"`       //用户id
	UserOrder    string      `json:"userOrder"`    //用户排序
	UserDeptType string      `json:"userDeptType"` //用户所属部门是主职部门或是兼职部门，一个用户有且仅有一个主职，0-主职，1-兼职
	TitleCn      string      `json:"titleCn"`      //岗位-中文
	TitleUs      string      `json:"titleUs"`      //岗位-英文
	ExtTitleCn   interface{} `json:"extTitleCn"`   //外部头衔-中文
	ExtTitleUs   interface{} `json:"extTitleUs"`   //外部头衔-英文
}

// 2.3 查询⽤⼾信息(分⻚)
type BcocUserDataRequest struct {
	PageNum  int `json:"pageNum"`
	PageSize int `json:"pageSize"`
}

type UserDataItem struct {
	TenantId      string      `json:"tenantId"`                //租户ID
	UserId        string      `json:"userId"`                  //用户ID
	NameCn        string      `json:"nameCn"`                  //用户中文名称
	NameUs        string      `json:"nameUs"`                  //用户英文名称
	Email         string      `json:"email"`                   //邮箱
	Mobile        string      `json:"mobile"`                  //手机号
	Gender        string      `json:"gender"`                  //性别 0-保密 1-男 2-女
	DepartmentIds string      `json:"departmentIds,omitempty"` //
	LeaderUserId  string      `json:"leaderUserId"`            //经理id
	City          string      `json:"city"`                    //城市
	Country       string      `json:"country"`                 //国家
	WorkStation   interface{} `json:"workStation"`             //办公地点
	JobTitle      string      `json:"jobTitle,omitempty"`      //
	EmployeeNo    string      `json:"employeeNo,omitempty"`    //员工编号
	EmployeeType  string      `json:"employeeType"`            //员工类型
	Status        string      `json:"status"`                  //状态 1-在职 2-离职失效 3- 虚拟人员
	Avatar72      string      `json:"avatar72"`                //头像地址
	Avatar240     string      `json:"avatar240"`               //头像地址
	Avatar640     string      `json:"avatar640"`               //头像地址
	AvatarOrigin  string      `json:"avatarOrigin"`            //原始头像地址
}
type BcocUserDataResponse struct {
	BcocCommonRespInfo
	Data []UserDataItem `json:"data"`
}

func (s *ZhongyiFeishuSdk) BcocHttpGet(url string, resp interface{}) error {
	request, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return errors.WithMessage(err, "NewRequest failed")
	}

	err = s.BcocHttpDo(request, resp)
	return err
}

func (s *ZhongyiFeishuSdk) BcocHttpDo(request *http.Request, resp interface{}) error {
	if err := s.SetBcocHeaders(request); err != nil {
		return errors.WithMessage(err, "获取token设置header失败")
	}
	cli := http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true, // 忽略证书验证
			},
		},
		Timeout: time.Second * 7,
	}
	response, err := cli.Do(request)
	if err != nil {
		return errors.WithMessage(err, "request do failed")
	}
	defer response.Body.Close()
	body, err := io.ReadAll(response.Body)
	if err != nil {
		return errors.WithMessage(err, "Unmarshal ReadAll failed")
	}
	logger.Info("response body", string(body))

	var respMap = make(map[string]interface{})
	if err = json.Unmarshal(body, &respMap); err != nil {
		return errors.WithMessage(err, "Unmarshal to map failed")
	}

	errMsg := ""
	if _, ok := respMap["msg"]; ok {
		errMsg = fmt.Sprint(respMap["msg"])
	} else {
		errMsg = fmt.Sprint(respMap["message"])
	}

	if fmt.Sprint(respMap["code"]) != "SVC-200" {
		return errors.New(errMsg)
	}

	// 判断map中是否包含outData
	if outData, ok := respMap["outData"]; ok {
		// 再把map转换为结构体
		outDataBytes, err := json.Marshal(outData)
		if err != nil {
			return errors.WithMessage(err, "Marshal failed")
		}
		if err = json.Unmarshal(outDataBytes, resp); err != nil {
			return errors.WithMessage(err, "Unmarshal failed")
		}
	} else {
		return errors.New("outData not found")
	}
	return nil
}

func (s *ZhongyiFeishuSdk) BcocHttpPost(url string, reqBody interface{}, resp interface{}) error {
	bodyBytes, err := json.Marshal(reqBody)
	if err != nil {
		return errors.WithMessage(err, "序列化失败")
	}
	//fmt.Println("-----zhongyiFeishu BcocHttpPost url:", url)
	// fmt.Println("-----zhongyiFeishu BcocHttpPost body:", string(bodyBytes))
	logger.Infof("-----zhongyiFeishu BcocHttpPost url:%s,reqBody:%v\n ", url, string(bodyBytes))

	req, err := http.NewRequest(http.MethodPost, url, bytes.NewReader(bodyBytes))
	if err != nil {
		return errors.WithMessage(err, "NewRequest failed")
	}
	req.Header.Set("bcoc-auth-id", "cli_a7e95c2721fa500b")
	err = s.BcocHttpDo(req, resp)
	return err
}
