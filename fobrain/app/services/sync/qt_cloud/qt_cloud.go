package qt_cloud

import (
	"context"
	"encoding/json"
	"fmt"
	"fobrain/fobrain/app/services/sync/qt_cloud_weak_pwd"
	"fobrain/fobrain/app/services/system_configs/device_strategy"
	"fobrain/models/elastic/device"
	"fobrain/pkg/utils"
	"fobrain/pkg/utils/handle_es_bulk_error"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/spf13/cast"

	"github.com/google/uuid"

	"github.com/olivere/elastic/v7"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"

	"fobrain/fobrain/app/services/node/qt_cloud"
	"fobrain/fobrain/common/constant"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	assetses "fobrain/models/elastic/assets"
	"fobrain/models/elastic/poc"
	qtcloudes "fobrain/models/elastic/source/qt_cloud"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
)

// SyncQTCloud 同步青藤云数据
func SyncQTCloud(node *data_source.Node, taskInfo string) error {
	logs.GetSyncLogger().Infof("SyncQTCloud start taskInfo:%s", taskInfo)
	assetTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())
	threatTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncThreat)).Int())
	peopleTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncPeople)).Int())

	//获取QTCloudClient
	cli := qt_cloud.NewQTCloud()
	//设置QTCloudClient请求的节点
	if err := cli.SetNode(node.Id); err != nil {
		return err
	}

	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncAsset)).Int() == data_sync_task.SyncAsset {
		//更新资产任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(assetTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("QTCloud syncAssets UpdateStatusById StatusDoing err:%s", err.Error())
		}

		go func() {
			wg := sync.WaitGroup{}
			wg.Add(2)
			//青藤资产分为linux win资产，所有要调用不同接口获取
			go syncAssets(constant.DefaultPageZero, cli, taskInfo, node, "linux", 0, &wg)
			go syncAssets(constant.DefaultPageZero, cli, taskInfo, node, "win", 0, &wg)

			wg.Wait()
			//更新子任务完成状态
			err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(assetTaskId, data_sync_child_task.StatusSuccess)
			if err != nil {
				logs.GetSyncLogger().Errorf("QTCloud syncAssets UpdateStatusById StatusSuccess err:%s", err.Error())
			}
		}()
	}

	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncThreat)).Int() == data_sync_task.SyncThreat {
		//更新漏洞任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(threatTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("QTCloud syncThreats UpdateStatusById StatusDoing err:%s", err.Error())
		}
		go func() {
			wg := sync.WaitGroup{}
			wg.Add(2)
			syncThreats(constant.DefaultPageZero, cli, taskInfo, node, qt_cloud.SyncAssetTypeWin, &wg)
			syncThreats(constant.DefaultPageZero, cli, taskInfo, node, qt_cloud.SyncAssetTypeLinux, &wg)

			if cast.ToString(cli.GetConfig("is_sync_weak")) == "true" {
				wg.Add(2)
				qt_cloud_weak_pwd.SyncWeakPwd(constant.DefaultPageZero, cli, taskInfo, qt_cloud.SyncAssetTypeLinux, node, &wg)
				qt_cloud_weak_pwd.SyncWeakPwd(constant.DefaultPageZero, cli, taskInfo, qt_cloud.SyncAssetTypeWin, node, &wg)
			}
			wg.Wait()
			//更新子任务完成状态
			err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(threatTaskId, data_sync_child_task.StatusSuccess)
			if err != nil {
				logs.GetSyncLogger().Errorf("QTCloud syncThreats UpdateStatusById StatusSuccess err:%s", err.Error())
			}
		}()

	}

	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncPeople)).Int() == data_sync_task.SyncPeople {
		//更新人员任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(peopleTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("QTCloud SyncPeople UpdateStatusById StatusDoing err:%s", err.Error())
		}
		go syncLinuxEmployees(constant.DefaultPageZero, cli, taskInfo, node)
	}

	return nil
}

// syncAssets 同步资产
func syncAssets(page int, cli *qt_cloud.QTCloud, taskInfo string, node *data_source.Node, syncAssetType string, successCount int, gwg *sync.WaitGroup) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())
	logs.GetSyncLogger().Infof("SyncQTCloud syncAssets start taskId:%d,taskChildId:%d,syncAssetType:%s,page:%d", taskId, taskChildId, syncAssetType, page)
	defer func() {
		if r := recover(); r != nil {
			gwg.Done()
			logs.GetSyncLogger().Errorf("SyncQTCloud syncAssets panic:%v", r)
			//更新主任务状态为失败
			taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, fmt.Sprintf("GetAssets err:%v", r))
			if taskErr != nil {
				logs.GetSyncLogger().Errorf("QTCloud syncAssets GetAssets Task UpdateFailById err:%s, page: %d", taskErr.Error(), page)
			}

			//更新子任务状态为失败
			childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, fmt.Sprintf("GetAssets err:%v", r))
			if childTaskErr != nil {
				logs.GetSyncLogger().Errorf("QTCloud syncAssets GetAssets childTask UpdateFailById err:%s", childTaskErr.Error())
			}
		}
	}()

	jarPackageChan := make(chan struct{}, 1)
	jarPackageMap := make(map[string][]*JarPackage)
	var jarPackageErr error
	go func() {
		defer func() {
			jarPackageChan <- struct{}{}
		}()
		jarPackageInfo, err := syncJarPackageInfo(cli, taskInfo)
		if err != nil {
			logs.GetSyncLogger().Errorf("SyncQTCloud syncAssets syncJarPackageInfo err:%s", err.Error())
			jarPackageErr = err
		}
		jarPackageMap = jarPackageInfo
	}()

	// 默认设置为3，智能判断
	networkType, err := node.GetNetworkType()
	if err != nil {
		logs.GetSyncLogger().Errorf("SyncQTCloud syncAssets GetNetworkType err:%s", err.Error())
	}

	//获取资产
	assets, err := cli.GetAssets(page, constant.DefaultSize, syncAssetType)
	if err != nil {
		logs.GetSyncLogger().Errorf("QTCloud syncAssets GetAssets err:%s, page: %d", err.Error(), page)
		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, err.Error())
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("QTCloud syncAssets GetAssets Task UpdateFailById err:%s, page: %d", taskErr.Error(), page)
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, err.Error())
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("QTCloud syncAssets GetAssets childTask UpdateFailById err:%s", childTaskErr.Error())
		}
		gwg.Done()
		return
	}

	total := gjson.Get(assets, "total").Int() //数据总量
	result := gjson.Get(assets, "rows")

	//子任务更新需要同步的数据总量
	if page == constant.DefaultPageZero { //仅第一次同步 win+linux 总数
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataTotal(taskChildId, int(total))
		if err != nil {
			logs.GetSyncLogger().Errorf("QTCloud syncAssets UpdateSyncDataTotal err:%s", err.Error())
		}
	}

	if result.Exists() && total > 0 {
		// 在处理结果之前确保jar包信息已经同步完成
		<-jarPackageChan
		if jarPackageErr != nil {
			logs.GetSyncLogger().Errorf("SyncQTCloud syncAssets syncJarPackageInfo error, jar package info will be empty, err:%s", jarPackageErr.Error())
			jarPackageMap = make(map[string][]*JarPackage)
		}

		// assetBulkRequest := es.GetEsClient().Bulk()
		assetTaskBulkRequest := es.GetEsClient().Bulk()
		assetProcessBulkRequest := es.GetEsClient().Bulk()
		assetProcessDeviceBulkRequest := es.GetEsClient().Bulk()
		needHandleProcessDevice := false
		uniqueId := ""
		personField := "name"
		if cli.GetConfig("person_field") != "" && cli.GetConfig("person_field") != nil {
			personField = cli.GetConfig("person_field").(string)
		}
		if cli.GetConfig("sn_field") != "" && cli.GetConfig("sn_field") != nil {
			uniqueId = cli.GetConfig("sn_field").(string)
		}
		if uniqueId == "" {
			uniqueId = "serialNumber"
		}
		var num = 0
		// 遍历 _id 字段，并根据 id 添加新的字段数据
		// 增加并发控制，5个协程
		ch := make(chan int, 5)
		wg := sync.WaitGroup{}
		netWorkType, _ := node.GetNetworkType()
		result.ForEach(func(key, value gjson.Result) bool {
			ch <- 1
			wg.Add(1)
			go func(key, value gjson.Result) {
				defer func() {
					if reErr := recover(); reErr != nil {
						logs.GetSyncLogger().Errorf("QTCloud syncAssets execAssets taskId:%v,taskChildId:%v,syncAssetType:%s,page:%d > FAIL, 返回结果: %v", taskId, taskChildId, syncAssetType, page, reErr)
					}
					<-ch
					wg.Done()
				}()
				logs.GetSyncLogger().Debugf("SyncQTCloud syncAssets value info:%s,syncAssetType:%s", value, syncAssetType)
				connectionIp := gjson.Get(value.String(), "connectionIp").String()
				if connectionIp == "" || !utils.IsIP(connectionIp) {
					logs.GetSyncLogger().Infof("QTCloud syncAssets connectionIp异常:%s, isIp:%v", connectionIp, utils.IsIP(connectionIp))
					return
				}
				successCount++
				areaId := node.AreaId
				if connectionIp != "" {
					// 获取区域
					areaId, err = node.GetAreaByIp(connectionIp)
					if err != nil {
						logs.GetSyncLogger().Errorf("QTCloud syncAssets GetAreaByIp failed,ip:%s,nodeId:%d,err:%s", connectionIp, node.Id, err)
						r := data_sync_child_task.NewDataSyncChildTaskFailRecord()
						r.Create(&data_sync_child_task.DataSyncChildTaskFailRecord{
							ChildTaskId:  taskChildId,
							DataType:     "ip",
							DataContent:  connectionIp,
							FailedReason: err.Error(),
						})
					}
				}

				data, _ := sjson.Set(value.String(), "node_id", node.Id)
				data, _ = sjson.Set(data, "area_id", areaId)
				data, _ = sjson.Set(data, "asset_type", syncAssetType)
				//写入资产库
				// asset := qtcloudes.NewQTCloudAssetsModel()
				// assetId := strings.ReplaceAll(uuid.New().String(), "-", "")
				// assetReq := elastic.NewBulkIndexRequest().Index(asset.IndexName()).Id(assetId).Doc(data)
				// assetBulkRequest = assetBulkRequest.Add(assetReq)

				//写入任务库
				assetTask := qtcloudes.NewQTCloudTaskAssetsModel()
				assetTaskId := strings.ReplaceAll(uuid.New().String(), "-", "")
				data, _ = sjson.Set(data, "task_id", taskId)
				data, _ = sjson.Set(data, "child_task_id", taskChildId)
				assetTaskReq := elastic.NewBulkIndexRequest().Index(assetTask.IndexName()).Id(assetTaskId).Doc(data)
				assetTaskBulkRequest = assetTaskBulkRequest.Add(assetTaskReq)

				//写入过程库
				assetProcess := assetses.NewProcessAssetsModel()
				assetProcessId := strings.ReplaceAll(uuid.New().String(), "-", "")
				assetProcess.Id = assetProcessId
				assetProcess.TaskDataId = assetTaskId

				assetProcess.NetworkType = netWorkType

				assetProcess.Area = int(areaId)
				assetProcess.Node = node.Id
				assetProcess.Source = node.SourceId
				assetProcess.TaskId = taskId
				assetProcess.ChildTaskId = taskChildId
				assetProcess.Ip = connectionIp
				assetProcess.NetworkType = networkType
				assetProcess.HostName = gjson.Get(value.String(), "hostname").String()
				networkCards := ""
				mac := ""
				gjson.Get(value.String(), "networkCards").ForEach(func(k, v gjson.Result) bool {
					if gjson.Get(v.String(), "ipv4").String() == gjson.Get(value.String(), "connectionIp").String() {
						networkCards = gjson.Get(v.String(), "name").String()
						mac = gjson.Get(v.String(), "mac").String()
					}
					return true
				})
				assetProcess.EthName = networkCards
				assetProcess.Os = gjson.Get(value.String(), "platform").String()
				assetProcess.Kernel = gjson.Get(value.String(), "kernelVersion").String()
				assetProcess.DeviceId = gjson.Get(value.String(), uniqueId).String()
				assetProcess.Sn = gjson.Get(value.String(), "serialNumber").String()
				assetProcess.DeviceName = gjson.Get(value.String(), "productName").String()
				assetProcess.Model = gjson.Get(value.String(), "productName").String() // 根据#3420说明，需要将productName作为model，同时保留deviceName保证兼容性
				assetProcess.Maker = gjson.Get(value.String(), "manufacturer").String()
				assetProcess.Mac = mac
				assetProcess.BusinessSystem = gjson.Get(value.String(), "bizGroup").String()
				//assetProcess.Oper = gjson.Get(value.String(), "chargeName").String()
				assetProcess.BusinessOwner = gjson.Get(value.String(), "chargeName").String()
				assetProcess.PersonField = personField
				assetProcess.MachineRoom = gjson.Get(value.String(), "hostLocation").String()

				assetProcess.Status = 1
				if int(gjson.Get(value.String(), "onlineStatus").Int()) == 0 {
					assetProcess.Status = 2
				}

				assetProcess.MemorySize = gjson.Get(value.String(), "memorySize").String()
				assetProcess.MemoryUsageRate = gjson.Get(value.String(), "memoryUsage").String()
				assetProcess.CpuMaker = gjson.Get(value.String(), "cpu.producer").String()
				assetProcess.CpuBrand = gjson.Get(value.String(), "cpu.brand").String()
				assetProcess.CpuCount = int(gjson.Get(value.String(), "cpu.core").Int())
				assetProcess.DiskCount = int(gjson.Get(value.String(), "diskCount").Int())
				assetProcess.DiskSize = int(gjson.Get(value.String(), "diskSize").Int())
				assetProcess.DiskUsageRate = strconv.Itoa(int(gjson.Get(value.String(), "diskUsage").Int()))
				assetProcess.LoadAverage = gjson.Get(value.String(), "systemLoad").String() //系统负载0 – 未知 1 – 低2 – 中 3 – 高
				lastResponseAt, _ := time.Parse(localtime.TimeFormat, gjson.Get(value.String(), "lastOnlineTime").String())
				if !lastResponseAt.IsZero() {
					assetProcess.LastResponseAt = localtime.NewLocalTime(lastResponseAt)
				}
				ct := gjson.Get(value.String(), "installTime").String()
				if ct != "" {
					createTime, _ := time.Parse(localtime.TimeFormat, ct)
					assetProcess.CreatedAt = localtime.NewLocalTime(createTime)
				} else {
					assetProcess.CreatedAt = localtime.NewLocalTime(time.Now())
				}

				// 获取jar包信息
				jarPackages, ok := jarPackageMap[connectionIp]
				if ok {
					kvs := make([]*assetses.JarPackageInfo, 0)
					for _, jarPackage := range jarPackages {
						kvs = append(kvs, &assetses.JarPackageInfo{
							JarPackageInfo: []*assetses.KV{
								{
									Key:   "name",
									Value: jarPackage.Name,
								},
								{
									Key:   "version",
									Value: jarPackage.Version,
								},
								{
									Key:   "path",
									Value: jarPackage.Path,
								},
							},
						})
					}
					assetProcess.JarPackageInfo = kvs
				} else {
					assetProcess.JarPackageInfo = make([]*assetses.JarPackageInfo, 0)
				}

				assetProcess.UpdatedAt = localtime.Now()

				assetProcessReq := elastic.NewBulkIndexRequest().Index(assetProcess.IndexName()).Id(assetProcessId).Doc(assetProcess)
				assetProcessBulkRequest = assetProcessBulkRequest.Add(assetProcessReq)

				//有sn不为空，并且不是dhcp的ip，才去提取设备
				if uniqueId != "" && device_strategy.IsNotDhcpIp(gjson.Get(value.String(), "connectionIp").String()) {
					publicParam := ConvertPublicParam{
						TaskId:       taskId,
						TaskChildId:  taskChildId,
						AssetTaskId:  assetTaskId,
						NetworkCards: networkCards,
						Mac:          mac,
					}
					needHandleProcessDevice = true
					processDevice := convertProcessDevice(node, value, publicParam, uniqueId)

					// 检查索引名称
					indexName := processDevice.IndexName()

					processDeviceReq := elastic.NewBulkIndexRequest().Index(indexName).Id(processDevice.Id).Doc(processDevice)

					assetProcessDeviceBulkRequest = assetProcessDeviceBulkRequest.Add(processDeviceReq)
				}
				num = num + 1
			}(key, value)
			return true
		})
		wg.Wait()
		if num > 0 {
			assetProcessBulkReq, err := assetProcessBulkRequest.Refresh("true").Do(context.Background())
			if err != nil || assetProcessBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(assetses.NewProcessAssetsModel().IndexName(), err, assetProcessBulkReq)
				logs.GetSyncLogger().Errorf("QTCloud syncAssets assetProcessBulkRequest.Do err:%s", errString)

				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("QTCloud syncAssets assetProcessBulkRequest.Do task UpdateFailById err:%s", taskErr)
				}

				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("QTCloud syncAssets assetProcessBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
				}
				gwg.Done()
				return
			}

			// assetBulkReq, err := assetBulkRequest.Do(context.Background())
			// if err != nil || assetBulkReq.Errors {
			// 	errString := handle_es_bulk_error.HandleBulkResp(qtcloudes.NewQTCloudAssetsModel().IndexName(), err, assetBulkReq)
			// 	logs.GetSyncLogger().Errorf("QTCloud syncAssets assetBulkRequest.Do err:%s", errString)

			// 	//更新主任务状态为失败
			// 	taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
			// 	if taskErr != nil {
			// 		logs.GetSyncLogger().Errorf("QTCloud syncAssets assetBulkRequest.Do task UpdateFailById err:%s", taskErr)
			// 	}

			// 	//更新子任务状态为失败
			// 	childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
			// 	if childTaskErr != nil {
			// 		logs.GetSyncLogger().Errorf("QTCloud syncAssets assetBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
			// 	}
			// 	gwg.Done()
			// 	return
			// }

			assetTaskBulkReq, err := assetTaskBulkRequest.Do(context.Background())
			if err != nil || assetTaskBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(qtcloudes.NewQTCloudTaskAssetsModel().IndexName(), err, assetTaskBulkReq)
				logs.GetSyncLogger().Errorf("QTCloud syncAssets assetTaskResponse.Do err:%s", errString)

				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("QTCloud syncAssets assetTaskResponse.Do task UpdateFailById err:%s", taskErr)
				}

				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("QTCloud syncAssets assetTaskResponse.Do childTask UpdateFailById err:%s", childTaskErr.Error())
				}
				gwg.Done()
				return
			}
			if err == nil {
				//更新成功数量
				err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(taskChildId, len(assetTaskBulkReq.Items))
				if err != nil {
					logs.GetSyncLogger().Errorf("QTCloud syncAssets UpdateSyncDataSuccessTotal err:%s", err.Error())
				}
			}
		}
		if needHandleProcessDevice {
			processDeviceResp, err := assetProcessDeviceBulkRequest.Refresh("true").Do(context.Background())
			if err != nil || processDeviceResp.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(device.NewProcessDeviceModel().IndexName(), err, processDeviceResp)
				logs.GetSyncLogger().Errorf("QTCloud syncAssets processDeviceResp.Do err:%s", errString)

				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("QTCloud syncAssets processDeviceResp.Do task UpdateFailById err:%s", taskErr)
				}

				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("QTCloud syncAssets processDeviceResp.Do childTask UpdateFailById err:%s", childTaskErr.Error())
				}
				gwg.Done()
				return
			}
		}
	}
	page = page + 1
	if page*constant.DefaultSize < int(total) && total > 0 {
		syncAssets(page, cli, taskInfo, node, syncAssetType, successCount, gwg)
	} else {
		logs.GetSyncLogger().Infof("SyncQTCloud syncAssets end taskId:%d,taskChildId:%d,syncAssetType:%s,successCount:%d", taskId, taskChildId, syncAssetType, successCount)
		gwg.Done()
	}
}

type JarPackagePage struct {
	Total int           `json:"total"`
	Rows  []*JarPackage `json:"rows"`
}

type JarPackage struct {
	AgentId      string      `json:"agentId"`
	DisplayIp    string      `json:"displayIp"`
	ConnectionIp string      `json:"connectionIp"`
	ExternalIp   interface{} `json:"externalIp"`
	InternalIp   string      `json:"internalIp"`
	BizGroupId   int         `json:"bizGroupId"`
	BizGroup     string      `json:"bizGroup"`
	Remark       string      `json:"remark"`
	HostTagList  []string    `json:"hostTagList"`
	Hostname     string      `json:"hostname"`
	Name         string      `json:"name"`
	Path         string      `json:"path"`
	Executable   bool        `json:"executable"`
	Version      string      `json:"version"`
	Type         int         `json:"type"`
}

// 同步jar包信息
func syncJarPackageInfo(cli *qt_cloud.QTCloud, taskInfo string) (map[string][]*JarPackage, error) {
	logger := logs.GetSyncLogger()
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())
	logger.Infof("SyncQTCloud syncJarPackageInfo start taskId:%d,taskChildId:%d,", taskId, taskChildId)

	// 用于存储结果的map，key是ConnectionIp
	result := make(map[string][]*JarPackage)

	page := constant.DefaultPageZero
	pageSize := 200

	for {
		jarPackageInfoResult, err := cli.GetJarPackageInfo(page, pageSize)
		if err != nil {
			logger.Errorf("QTCloud syncJarPackageInfo GetJarPackageInfo page:%d, err:%v", page, err)
			return nil, err
		}

		jarPackageInfoPage := &JarPackagePage{}
		err = json.Unmarshal([]byte(jarPackageInfoResult), jarPackageInfoPage)
		if err != nil {
			logger.Errorf("QTCloud syncJarPackageInfo unmarshal page:%d, err:%v", page, err)
			return nil, err
		}

		logger.Infof("SyncQTCloud syncJarPackageInfo page:%d, total:%d, currentRows:%d", page, jarPackageInfoPage.Total, len(jarPackageInfoPage.Rows))

		// 处理当前页的数据
		for _, jarPackage := range jarPackageInfoPage.Rows {
			if jarPackage != nil && jarPackage.ConnectionIp != "" {
				result[jarPackage.ConnectionIp] = append(result[jarPackage.ConnectionIp], jarPackage)
			}
		}

		// 检查是否还有下一页
		page++
		if page*pageSize >= jarPackageInfoPage.Total {
			break
		}
		if len(jarPackageInfoPage.Rows) < pageSize {
			break
		}
	}

	logger.Infof("SyncQTCloud syncJarPackageInfo completed, total IPs:%d", len(result))
	return result, nil
}

type ConvertPublicParam struct {
	TaskId       uint64
	TaskChildId  uint64
	AssetTaskId  string
	NetworkCards string
	Mac          string
}

func convertProcessDevice(node *data_source.Node, value gjson.Result, param ConvertPublicParam, uniqueKeyField string) *device.ProcessDevice {
	serialNumer := gjson.Get(value.String(), uniqueKeyField).String()
	serialNumer = strings.ToLower(strings.TrimSpace(serialNumer))
	processDevice := device.NewProcessDeviceModel()
	processDeviceId := strings.ReplaceAll(uuid.New().String(), "-", "")
	processDevice.Id = processDeviceId
	processDevice.UniqueKey = serialNumer
	processDevice.Area = int(node.AreaId)
	processDevice.Source = node.SourceId
	processDevice.Node = node.Id
	processDevice.TaskId = param.TaskId
	processDevice.ChildTaskId = param.TaskChildId
	processDevice.TaskDataId = param.AssetTaskId

	processDevice.HostName = gjson.Get(value.String(), "hostname").String()
	processDevice.EthName = param.NetworkCards
	processDevice.Os = gjson.Get(value.String(), "platform").String()
	processDevice.Kernel = gjson.Get(value.String(), "kernelVersion").String()
	processDevice.DeviceId = serialNumer
	processDevice.Sn = gjson.Get(value.String(), "serialNumber").String()
	processDevice.DeviceName = gjson.Get(value.String(), "productName").String()
	processDevice.Model = gjson.Get(value.String(), "productName").String() // 根据#3420说明，需要将productName作为model，同时保留deviceName保证兼容性
	processDevice.Maker = gjson.Get(value.String(), "manufacturer").String()
	processDevice.Mac = param.Mac
	processDevice.MachineRoom = gjson.Get(value.String(), "hostLocation").String()
	processDevice.Status = 1
	if int(gjson.Get(value.String(), "onlineStatus").Int()) == 0 {
		processDevice.Status = 2
	}

	processDevice.MemorySize = gjson.Get(value.String(), "memorySize").String()
	processDevice.MemoryUsageRate = gjson.Get(value.String(), "memoryUsage").String()
	processDevice.CpuMaker = gjson.Get(value.String(), "cpu.producer").String()
	processDevice.CpuBrand = gjson.Get(value.String(), "cpu.brand").String()
	processDevice.CpuCount = int(gjson.Get(value.String(), "cpu.core").Int())
	processDevice.DiskCount = int(gjson.Get(value.String(), "diskCount").Int())
	processDevice.DiskSize = int(gjson.Get(value.String(), "diskSize").Int())
	processDevice.DiskUsageRate = strconv.Itoa(int(gjson.Get(value.String(), "diskUsage").Int()))
	processDevice.LoadAverage = gjson.Get(value.String(), "systemLoad").String() //系统负载0 – 未知 1 – 低2 – 中 3 – 高

	var privateIp = []string{
		gjson.Get(value.String(), "internalIp").String(),
	}
	var publicIp = []string{
		gjson.Get(value.String(), "externalIp").String(),
	}

	displayIp := gjson.Get(value.String(), "displayIp").String()
	connectionIp := gjson.Get(value.String(), "connectionIp").String()
	if utils.GetNetworkType(displayIp) == assetses.NetworkTypeExternal {
		publicIp = append(publicIp, displayIp)
	} else {
		privateIp = append(privateIp, displayIp)
	}
	if utils.GetNetworkType(connectionIp) == assetses.NetworkTypeExternal {
		publicIp = append(publicIp, connectionIp)
	} else {
		privateIp = append(privateIp, connectionIp)
	}

	gjson.Get(value.String(), "networkCards").ForEach(func(k, v gjson.Result) bool {
		name := gjson.Get(v.String(), "name").String()
		if strings.ToUpper(name) == "LO" {
			return true
		}
		ipv4 := gjson.Get(v.String(), "ipv4").String()
		if utils.GetNetworkType(ipv4) == assetses.NetworkTypeExternal {
			publicIp = append(publicIp, ipv4)
		} else if utils.GetNetworkType(ipv4) == assetses.NetworkTypeInternal {
			privateIp = append(privateIp, ipv4)
		}
		var dns []string
		gjson.Get(v.String(), "dnsServer").ForEach(func(k, v gjson.Result) bool {
			dns = append(dns, v.String())
			return true
		})
		processDevice.NetworkCards = append(processDevice.NetworkCards, &device.NetworkCardInfo{
			Name:      name,
			Mac:       gjson.Get(v.String(), "mac").String(),
			Ipv4:      gjson.Get(v.String(), "ipv4").String(),
			Ipv6:      gjson.Get(v.String(), "ipv6").String(),
			Gateway:   gjson.Get(v.String(), "gateway").String(),
			DnsServer: dns,
			Netmask:   gjson.Get(v.String(), "netmask").String(),
			Broadcast: gjson.Get(v.String(), "broadcast").String(),
			Status:    gjson.Get(v.String(), "status").String(),
			Speed:     gjson.Get(v.String(), "speed").Int(),
		})
		return true
	})

	for _, v := range privateIp {
		if !utils.IsIP(v) {
			continue
		}
		processDevice.PrivateIp = append(processDevice.PrivateIp, v)
	}
	for _, v := range publicIp {
		if !utils.IsIP(v) {
			continue
		}
		processDevice.PublicIp = append(processDevice.PublicIp, v)
	}

	ct := gjson.Get(value.String(), "installTime").String()
	if ct != "" {
		createTime, _ := time.Parse(localtime.TimeFormat, ct)
		processDevice.CreatedAt = localtime.NewLocalTime(createTime)
	} else {
		processDevice.CreatedAt = localtime.NewLocalTime(time.Now())
	}

	lastOfflineTime := gjson.Get(value.String(), "lastOfflineTime").String()
	if lastOfflineTime != "" {
		updatedAt, _ := time.Parse(localtime.TimeFormat, ct)
		processDevice.UpdatedAt = localtime.NewLocalTime(updatedAt)
	} else {
		processDevice.UpdatedAt = localtime.NewLocalTime(time.Now())
	}
	return processDevice
}

// Vulnerabilities 定义漏洞类型的 map
var Vulnerabilities = map[int]string{
	1:  "未授权访问",
	2:  "敏感信息泄露",
	3:  "XML外部实体注入",
	4:  "跨站脚本攻击",
	5:  "不安全的反序列化",
	6:  "客户端请求伪造",
	7:  "服务端请求伪造",
	8:  "命令执行",
	9:  "代码执行",
	10: "任意文件上传",
	11: "任意文件读取",
	12: "拒绝服务攻击",
	13: "目录遍历",
	14: "恶意后门",
	15: "本地提权",
	16: "注入漏洞",
}

// syncThreats 同步漏洞
func syncThreats(page int, cli *qt_cloud.QTCloud, taskInfo string, node *data_source.Node, syncType string, gwg *sync.WaitGroup) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncThreat)).Int())
	logs.GetSyncLogger().Infof("SyncQTCloud syncThreats start taskId:%d,taskChildId:%d,", taskId, taskChildId)
	defer func() {
		if r := recover(); r != nil {
			gwg.Done()
			logs.GetSyncLogger().Errorf("SyncQTCloud syncThreats panic:%v", r)
			//更新主任务状态为失败
			taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, fmt.Sprintf("GetThreats err:%v", r))
			if taskErr != nil {
				logs.GetSyncLogger().Errorf("QTCloud syncThreats GetThreats Task UpdateFailById err:%s, page: %d", taskErr.Error(), page)
			}

			//更新子任务状态为失败
			childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, fmt.Sprintf("GetThreats err:%v", r))
			if childTaskErr != nil {
				logs.GetSyncLogger().Errorf("QTCloud syncThreats GetThreats childTask UpdateFailById err:%s", childTaskErr.Error())
			}
		}
	}()

	//获取漏洞
	threats, err := cli.GetThreats(page, constant.DefaultSize, syncType)
	if err != nil {
		logs.GetSyncLogger().Errorf("QTCloud syncThreats GetThreats err:%v", err)

		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, err.Error())
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("QTCloud syncThreats GetThreats UpdateFailById Task err:%s", taskErr.Error())
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, err.Error())
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("QTCloud syncThreats GetThreats UpdateFailById childTask err:%s", childTaskErr.Error())
		}
		gwg.Done()
		return
	}

	total := gjson.Get(threats, "total").Int() //数据总量
	result := gjson.Get(threats, "rows")

	//子任务更新需要同步的数据总量
	if page == constant.DefaultPageZero { //仅第一次同步
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateById(taskChildId, data_sync_child_task.DataSyncChildTask{
			SyncDataTotal: int(total),
		})

		if err != nil {
			logs.GetSyncLogger().Errorf("QTCloud syncThreats UpdateById SyncDataTotal err:%s", err.Error())
		}
	}

	if result.Exists() && total > 0 {
		// threatBulkRequest := es.GetEsClient().Bulk()
		threatTaskBulkRequest := es.GetEsClient().Bulk()
		threatProcessBulkRequest := es.GetEsClient().Bulk()
		var num = 0
		// 遍历 _id 字段，并根据 id 添加新的字段数据
		var ch = make(chan int, 10)
		var wg sync.WaitGroup
		result.ForEach(func(key, value gjson.Result) bool {
			ch <- 1
			wg.Add(1)
			logs.GetSyncLogger().Debugf("SyncQTCloud syncThreats value info:%s", value)
			go func(key, value gjson.Result) {
				defer func() {
					<-ch
					wg.Done()
				}()
				//根据id获取漏洞详情
				id := gjson.Get(value.String(), "id").String()
				start := time.Now().UnixMilli()
				details, err := cli.GetThreatDetails(id, syncType)
				end := time.Now().UnixMilli()
				logs.GetSyncLogger().Infof("SyncQTCloud syncThreats GetThreatDetails id: %s, time:%d", id, end-start)

				if err != nil {
					logs.GetSyncLogger().Errorf("QTCloud syncThreats GetThreatDetails id: %s, err:%s", id, err)
					return
				}
				ip := gjson.Get(value.String(), "connectionIp").String()
				vulId := gjson.Get(value.String(), "vulId").String()
				agentId := gjson.Get(value.String(), "agentId").String()
				firstCheckTime := gjson.Get(details, "firstCheckTime").String()
				// 青藤那个确认好了，connectionIp + vulId + agentId+ firstCheckTime
				uniquekey := utils.Md5Hash(fmt.Sprintf("%s_%s_%s_%s", ip, vulId, agentId, firstCheckTime))

				data, _ := sjson.Set(value.String(), "node_id", node.Id)
				areaId := node.AreaId
				if ip != "" {
					// 获取区域
					areaId, err = node.GetAreaByIp(ip)
					if err != nil {
						logs.GetSyncLogger().Errorf("QTCloud syncThreats GetAreaByIp failed,ip:%s,nodeId:%d,err:%s", ip, node.Id, err)
						r := data_sync_child_task.NewDataSyncChildTaskFailRecord()
						r.Create(&data_sync_child_task.DataSyncChildTaskFailRecord{
							ChildTaskId:  taskChildId,
							DataType:     "ip",
							DataContent:  ip,
							FailedReason: err.Error(),
						})
					}
				}
				data, _ = sjson.Set(data, "area_id", areaId)
				data, _ = sjson.Set(data, "details", details)
				data, _ = sjson.Set(data, "original_id", uniquekey)
				//写入漏洞库
				// threat := qtcloudes.NewQTCloudThreatsModel()
				threatId := strings.ReplaceAll(uuid.New().String(), "-", "")
				// threatReq := elastic.NewBulkIndexRequest().Index(threat.IndexName()).Id(threatId).Doc(data)
				// threatBulkRequest = threatBulkRequest.Add(threatReq)

				//写入任务库
				threatTask := qtcloudes.NewQTCloudTaskThreatsModel()
				threatTaskId := strings.ReplaceAll(uuid.New().String(), "-", "")
				data, _ = sjson.Set(data, "task_id", taskId)
				data, _ = sjson.Set(data, "child_task_id", taskChildId)
				assetTaskReq := elastic.NewBulkIndexRequest().Index(threatTask.IndexName()).Id(threatTaskId).Doc(data)
				threatTaskBulkRequest = threatTaskBulkRequest.Add(assetTaskReq)

				//写入过程库
				threatProcess := poc.NewProcessPocModel()
				threatProcess.Id = threatId
				threatProcess.TaskId = taskId
				threatProcess.ChildTaskId = taskChildId
				threatProcess.Url = uniquekey
				threatProcess.OriginalId = uniquekey
				threatProcess.TaskDataId = threatTaskId
				threatProcess.Area = areaId
				threatProcess.Node = node.Id
				threatProcess.Source = node.SourceId
				// 正则表达式：匹配数字
				re := regexp.MustCompile(`\d+`)
				// 查找所有匹配的数字
				numbers := re.FindAllString(gjson.Get(details, "category").String(), -1)
				var vulType []string
				for _, num := range numbers {
					i, _ := strconv.Atoi(num)
					v, ok := Vulnerabilities[i]
					if ok {
						vulType = append(vulType, v)
					}
				}
				threatProcess.VulType = strings.Join(vulType, " ")
				threatProcess.IsPoc = poc.NoPoc
				//execute_type为1返回否，如果execute_type为2返回是，execute_type不为1或2，返回未知
				dataValue := gjson.Get(details, "data").String()
				executeType := gjson.Get(dataValue, "execute_type").Int()
				if executeType == 2 {
					threatProcess.IsPoc = poc.YesPoc
				} else if executeType != 1 {
					threatProcess.IsPoc = poc.UnknownPoc
				}

				threatProcess.Ip = ip
				threatProcess.Name = gjson.Get(details, "vulName").String()
				threatProcess.Describe = gjson.Get(details, "desc").String()
				// 详情 从普通漏洞取checkinfo字段
				threatProcess.Details = gjson.Get(details, "checkInfo").String()

				threatProcess.Suggestions = gjson.Get(details, "remedDescription").String()
				lastResponseAt, _ := time.Parse(localtime.TimeFormat, gjson.Get(value.String(), "updatedTime").String())
				if !lastResponseAt.IsZero() {
					threatProcess.LastResponseAt = localtime.NewLocalTime(lastResponseAt)
				}
				if firstCheckTime == "" {
					threatProcess.CreatedAt = localtime.NewLocalTime(time.Now())
				} else {
					createTime, _ := time.Parse(localtime.TimeFormat, firstCheckTime)
					threatProcess.CreatedAt = localtime.NewLocalTime(createTime)
				}
				threatProcess.UpdatedAt = localtime.Now()

				threatProcess.Cve = gjson.Get(details, "cves.0").String()
				hasPoc := 0
				if gjson.Get(details, "hasPoc").Exists() {
					if gjson.Get(details, "hasPoc").Bool() {
						hasPoc = 1
					} else {
						hasPoc = 2
					}
				}
				threatProcess.HasPoc = hasPoc
				hasExp := 0
				if gjson.Get(details, "hasExp").Exists() {
					if gjson.Get(details, "hasExp").Bool() {
						hasExp = 1
					} else {
						hasExp = 2
					}
				}
				threatProcess.HasExp = hasExp
				//危险程度：0-信息；1-低危；2-中危；3-高危；4-危急 ）
				var level int
				switch gjson.Get(details, "severity").Int() {
				case 1:
					level = 1 // 低危
				case 2:
					level = 2 // 中危
				case 3:
					level = 3 // 高危
				case 4:
					level = 4 // 严重
				default:
					level = 5 // 未知
				}
				threatProcess.Level = level //漏洞等级（低危 0 中危 1 高危 2 严重 3 未知 4）
				threatProcessReq := elastic.NewBulkIndexRequest().Index(threatProcess.IndexName()).Id(threatId).Doc(threatProcess)
				threatProcessBulkRequest = threatProcessBulkRequest.Add(threatProcessReq)

				// threatIds = append(threatIds, map[string]interface{}{"id": threatId, "is_poc": poc.NoPoc, "task_id": taskChildId})
				num = num + 1
			}(key, value)
			return true
		})
		wg.Wait()
		if num > 0 {
			threatProcessBulkReq, err := threatProcessBulkRequest.Refresh("true").Do(context.Background())
			if err != nil || threatProcessBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(poc.NewProcessPocModel().IndexName(), err, threatProcessBulkReq)
				logs.GetSyncLogger().Errorf("QTCloud syncThreats threatProcessBulkRequest.Do err:%s", errString)

				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("QTCloud syncThreats threatProcessBulkRequest.Do task UpdateFailById err:%s", taskErr)
				}

				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("QTCloud syncThreats threatProcessBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
				}
				gwg.Done()
				return
			}

			// threatBulkReq, err := threatBulkRequest.Do(context.Background())
			// if err != nil || threatBulkReq.Errors {
			// 	errString := handle_es_bulk_error.HandleBulkResp(qtcloudes.NewQTCloudThreatsModel().IndexName(), err, threatBulkReq)
			// 	logs.GetSyncLogger().Errorf("QTCloud syncThreats threatBulkRequest.Do err:%s", errString)

			// 	//更新主任务状态为失败
			// 	taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
			// 	if taskErr != nil {
			// 		logs.GetSyncLogger().Errorf("QTCloud syncThreats threatBulkRequest.Do task UpdateFailById err:%s", taskErr)
			// 	}

			// 	//更新子任务状态为失败
			// 	childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
			// 	if childTaskErr != nil {
			// 		logs.GetSyncLogger().Errorf("QTCloud syncThreats threatBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
			// 	}
			// 	gwg.Done()
			// 	return
			// }

			threatTaskBulkReq, err := threatTaskBulkRequest.Do(context.Background())
			if err != nil || threatTaskBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(qtcloudes.NewQTCloudTaskThreatsModel().IndexName(), err, threatTaskBulkReq)
				logs.GetSyncLogger().Errorf("QTCloud syncThreats threatTaskBulkRequest.Do err:%s", errString)

				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("QTCloud syncThreats threatTaskBulkRequest.Do task UpdateFailById err:%s", taskErr)
				}

				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("QTCloud syncThreats threatTaskBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
				}
				gwg.Done()
				return
			}

			if err == nil {
				err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(taskChildId, len(threatTaskBulkReq.Items))
				if err != nil {
					logs.GetSyncLogger().Errorf("QTCloud syncThreats UpdateSyncDataSuccessTotal err:%s", err.Error())
				}
				// logs.GetSyncLogger().Infof("QTCloud syncThreats threatIds info:%v", threatIds)

				// // 发送任务开始标识
				// rq.Push(cfg.LoadQueue().VulnMergeQueue, []map[string]interface{}{{"task_id": taskChildId, "task_type": "sync", "is_start": true}})
				// successCount := rq.Push(cfg.LoadQueue().VulnMergeQueue, threatIds)
				// logs.GetSyncLogger().Infof("QTCloud syncThreats queue Push successCount:%d, expectCount:%d", successCount, len(threatIds))
			}
		}
	}
	page = page + 1
	if page*constant.DefaultSize < int(total) && total > 0 {
		syncThreats(page, cli, taskInfo, node, syncType, gwg)
	} else {
		// logs.GetSyncLogger().Infof("SyncQTCloud syncThreats end taskId:%d,taskChildId:%d,", taskId, taskChildId)
		gwg.Done()
	}
}

// syncLinuxEmployees 同步人员
func syncLinuxEmployees(page int, cli *qt_cloud.QTCloud, taskInfo string, node *data_source.Node) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncPeople)).Int())
	logs.GetSyncLogger().Infof("SyncQTCloud syncLinuxEmployees start taskId:%d,taskChildId:%d,", taskId, taskChildId)

	//获取人员
	employees, err := cli.GetLinuxEmployees(page, constant.DefaultSize)
	if err != nil {
		logs.GetSyncLogger().Errorf("QTCloud syncLinuxEmployees GetLinuxEmployees err:%s", err)

		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, err.Error())
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("QTCloud syncLinuxEmployees GetLinuxEmployees UpdateFailById Task err:%s", taskErr.Error())
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, err.Error())
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("QTCloud syncLinuxEmployees GetLinuxEmployees UpdateFailById childTask err:%s", childTaskErr.Error())
		}
		return
	}

	total := gjson.Get(employees, "total").Int() //数据总量
	result := gjson.Get(employees, "rows")

	//子任务更新需要同步的数据总量
	if page == constant.DefaultPageZero { //仅第一次同步
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateById(taskChildId, data_sync_child_task.DataSyncChildTask{
			SyncDataTotal: int(total),
		})

		if err != nil {
			logs.GetSyncLogger().Errorf("QTCloud syncLinuxEmployees UpdateById SyncDataTotal err:%s", err.Error())
		}
	}

	if result.Exists() && total > 0 {
		peopleBulkRequest := es.GetEsClient().Bulk()
		peopleTaskBulkRequest := es.GetEsClient().Bulk()
		var num = 0
		// 遍历 _id 字段，并根据 id 添加新的字段数据
		result.ForEach(func(key, value gjson.Result) bool {
			logs.GetSyncLogger().Debugf("SyncQTCloud syncLinuxEmployees value info:%s", value)
			data, _ := sjson.Set(value.String(), "node_id", node.Id)
			data, _ = sjson.Set(data, "area_id", node.AreaId)

			//写入资产库
			employee := qtcloudes.NewQTCloudLinuxEmployeesModel()
			employeeId := fmt.Sprintf("%d_%d_%s", node.Id, node.AreaId, gjson.Get(value.String(), "uid").String())
			employeeReq := elastic.NewBulkIndexRequest().Index(employee.IndexName()).Id(employeeId).Doc(data)
			peopleBulkRequest = peopleBulkRequest.Add(employeeReq)

			//写入任务库
			employeeTask := qtcloudes.NewQTCloudTaskLinuxEmployeesModel()
			employeeTaskId := fmt.Sprintf("%d_%s", taskId, employeeId)
			data, _ = sjson.Set(data, "task_id", taskId)
			data, _ = sjson.Set(data, "child_task_id", taskChildId)
			assetTaskReq := elastic.NewBulkIndexRequest().Index(employeeTask.IndexName()).Id(employeeTaskId).Doc(data)
			peopleTaskBulkRequest = peopleTaskBulkRequest.Add(assetTaskReq)
			num = num + 1
			return true
		})
		if num > 0 {
			peopleBulkReq, err := peopleBulkRequest.Do(context.Background())
			if err != nil || peopleBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(qtcloudes.NewQTCloudLinuxEmployeesModel().IndexName(), err, peopleBulkReq)
				logs.GetSyncLogger().Errorf("QTCloud syncLinuxEmployees peopleBulkRequest.Do err:%s", errString)

				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("QTCloud syncLinuxEmployees peopleBulkRequest.Do task UpdateFailById err:%s", taskErr)
				}

				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("QTCloud syncLinuxEmployees peopleBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
				}
				return
			}

			peopleTaskBulkReq, err := peopleTaskBulkRequest.Do(context.Background())
			if err != nil || peopleTaskBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(qtcloudes.NewQTCloudTaskLinuxEmployeesModel().IndexName(), err, peopleTaskBulkReq)
				logs.GetSyncLogger().Errorf("QTCloud syncLinuxEmployees peopleTaskBulkRequest.Do err:%s", errString)

				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("QTCloud syncLinuxEmployees peopleTaskBulkRequest.Do task UpdateFailById err:%s", taskErr)
				}

				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("QTCloud syncLinuxEmployees peopleTaskBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
				}
				return
			}
			if err == nil {
				//更新成功数量
				err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(taskChildId, len(peopleTaskBulkReq.Items))
				if err != nil {
					logs.GetSyncLogger().Errorf("QTCloud syncLinuxEmployees UpdateSyncDataSuccessTotal err:%s", err.Error())
				}
			}
		}
	}
	page = page + 1
	if page*constant.DefaultSize <= int(total) && total > 0 {
		syncLinuxEmployees(page, cli, taskInfo, node)
	} else {
		logs.GetSyncLogger().Infof("SyncQTCloud syncLinuxEmployees end taskId:%d,taskChildId:%d,", taskId, taskChildId)
		//更新子任务完成状态
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(taskChildId, data_sync_child_task.StatusSuccess)
		if err != nil {
			logs.GetSyncLogger().Errorf("QTCloud syncLinuxEmployees UpdateStatusById StatusSuccess err:%s", err.Error())
		}
	}
}
