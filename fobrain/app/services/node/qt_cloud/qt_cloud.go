package qt_cloud

import (
	"bytes"
	"context"
	"crypto/sha1"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	httputil "fobrain/pkg/http"
	"io"
	"net/http"
	"net/url"
	"sort"
	"time"

	"github.com/spf13/cast"
	"github.com/tidwall/gjson"

	"fobrain/fobrain/app/services/node"
	"fobrain/initialize/redis"
)

const (
	UriPostToken         = "%s://%s/v1/api/auth"
	UriGetAssets         = "%s://%s/external/api/assets/host/%s?%s"
	UriGetJarPackageInfo = "%s://%s/external/api/assets/jar_pkg/linux?%s"
	UriGetThreats        = "%s://%s/external/api/vul/poc/%s/list?%s"
	UriGetThreatDetails  = "%s://%s/external/api/vul/poc/%s/%s"
	UriGetLinuxEmployees = "%s://%s/external/api/assets/account/linux?%s"
	UriGetWeakPwd        = "%s://%s/external/api/vul/weakpwd/%s/list?%s"
	UriGetWeakPwdDetails = "%s://%s/external/api/vul/weakpwd/%s/%s"
	UriFixedHistory      = "%s://%s/external/api/vul/poc/fix_history" // 修复历史,用来判断漏洞是否修复（复测）
	ExpireTime           = 10 * 60
	SyncAssetTypeLinux   = "linux"
	SyncAssetTypeWin     = "win"
)

type QTCloud struct {
	node.Client
}

func NewQTCloud() *QTCloud {
	return &QTCloud{Client: node.NewDefaultClient()}
}

// GetToken 获取token
func (q *QTCloud) GetToken() (string, error) {
	// 取缓存
	if q.GetNodeId() != 0 {
		res, err := redis.GetRedisClient().Get(context.TODO(), fmt.Sprintf("token:qtcloud:%d", q.GetNodeId())).Result()
		if err == nil && res != "" {
			return gjson.Get(res, "data").String(), nil
		}
	}

	//获取 signature
	params := map[string]interface{}{
		"username": cast.ToString(q.GetConfig("account")),
		"password": cast.ToString(q.GetConfig("password")),
	}

	jsonData, err := json.Marshal(params)
	if err != nil {
		return "", err
	}

	// 发送请求
	apiUri := fmt.Sprintf(UriPostToken, cast.ToString(q.GetConfig("protocol")), cast.ToString(q.GetConfig("ip")))
	resp, err := q.Client.Client.Post(apiUri, httputil.ContentTypeJson, bytes.NewBuffer(jsonData))

	if err != nil {
		return "", err
	}

	// 解析返回数据
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	if resp.StatusCode != 200 {
		return "", errors.New(resp.Status)
	}
	if gjson.Get(string(body), "errorCode").String() != "" {
		return "", errors.New(gjson.Get(string(body), "errorDesc").String())
	}

	if !gjson.Get(string(body), "data.jwt").Exists() {
		return "", errors.New("认证失败")
	}

	// 写入缓存
	if q.GetNodeId() != 0 {
		if _, err = redis.GetRedisClient().SetEX(context.TODO(), fmt.Sprintf("token:qtcloud:%d", q.GetNodeId()), string(body), (ExpireTime-10)*time.Second).Result(); err != nil {
			return "", err
		}
	}

	return gjson.Get(string(body), "data").String(), nil
}

// GetAssets 获取资产列表
func (q *QTCloud) GetAssets(page, size int, syncAssetType string) (string, error) {
	params := map[string]interface{}{
		"size": cast.ToString(size),
		"page": cast.ToString(page),
	}

	// 创建 url.Values 并添加参数
	urlParams := url.Values{}
	for key, value := range params {
		urlParams.Add(key, cast.ToString(value))
	}

	httpClient := httputil.NewHttpClient(q.Client.Client)
	apiUri := fmt.Sprintf(UriGetAssets, cast.ToString(q.GetConfig("protocol")), cast.ToString(q.GetConfig("ip")), syncAssetType, urlParams.Encode())

	jsonStr, err := q.GetToken()
	if err != nil {
		return "", err
	}

	ts := time.Now().Unix()
	headers := map[string]string{
		"comId":         gjson.Get(jsonStr, "comId").String(),
		"timestamp":     fmt.Sprintf("%d", ts),
		"sign":          q.signature(jsonStr, params, ts, http.MethodGet),
		"Authorization": fmt.Sprintf("Bearer %s", gjson.Get(jsonStr, "jwt").String()),
	}

	resp, err := httpClient.Get(apiUri, headers)

	if err != nil {
		return "", err
	}

	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)

	if err != nil {
		return "", err
	}

	if resp.StatusCode != 200 || gjson.Get(string(body), "errorCode").String() != "" {
		if resp.StatusCode == 401 {
			return "", errors.New("Unauthorized")
		}
		return "", errors.New(gjson.Get(string(body), "errorDesc").String())
	}
	return string(body), nil
}

// GetJarPackageInfo 获取jar包信息
func (q *QTCloud) GetJarPackageInfo(page, size int) (string, error) {
	params := map[string]interface{}{
		"size": cast.ToString(size),
		"page": cast.ToString(page),
	}

	// 创建 url.Values 并添加参数
	urlParams := url.Values{}
	for key, value := range params {
		urlParams.Add(key, cast.ToString(value))
	}
	httpClient := httputil.NewHttpClient(q.Client.Client)
	apiUri := fmt.Sprintf(UriGetJarPackageInfo, cast.ToString(q.GetConfig("protocol")), cast.ToString(q.GetConfig("ip")), urlParams.Encode())

	jsonStr, err := q.GetToken()
	if err != nil {
		return "", err
	}

	ts := time.Now().Unix()
	headers := map[string]string{
		"comId":         gjson.Get(jsonStr, "comId").String(),
		"timestamp":     fmt.Sprintf("%d", ts),
		"sign":          q.signature(jsonStr, nil, ts, http.MethodGet),
		"Authorization": fmt.Sprintf("Bearer %s", gjson.Get(jsonStr, "jwt").String()),
	}

	resp, err := httpClient.Get(apiUri, headers)

	if err != nil {
		return "", err
	}

	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)

	if err != nil {
		return "", err
	}

	if resp.StatusCode != 200 || gjson.Get(string(body), "errorCode").String() != "" {
		if resp.StatusCode == 401 {
			return "", errors.New("Unauthorized")
		}
		errMsg := fmt.Sprintf("status:%d,body:%s", resp.StatusCode, string(body))
		return "", errors.New(errMsg)
	}
	return string(body), nil
}

// GetThreats 获取漏洞列表
func (q *QTCloud) GetThreats(page, size int, syncType string) (string, error) {
	params := map[string]interface{}{
		"size": cast.ToString(size),
		"page": cast.ToString(page),
	}

	// 创建 url.Values 并添加参数
	urlParams := url.Values{}
	for key, value := range params {
		urlParams.Add(key, cast.ToString(value))
	}

	httpClient := httputil.NewHttpClient(q.Client.Client)
	apiUri := fmt.Sprintf(UriGetThreats, cast.ToString(q.GetConfig("protocol")), cast.ToString(q.GetConfig("ip")), syncType, urlParams.Encode())

	jsonStr, err := q.GetToken()
	if err != nil {
		return "", err
	}

	ts := time.Now().Unix()
	headers := map[string]string{
		"comId":         gjson.Get(jsonStr, "comId").String(),
		"timestamp":     fmt.Sprintf("%d", ts),
		"sign":          q.signature(jsonStr, params, ts, http.MethodGet),
		"Authorization": fmt.Sprintf("Bearer %s", gjson.Get(jsonStr, "jwt").String()),
	}

	resp, err := httpClient.Get(apiUri, headers)

	if err != nil {
		return "", err
	}

	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)

	if err != nil {
		return "", err
	}

	if resp.StatusCode != 200 {
		return "", fmt.Errorf("status:%d,body:%s", resp.StatusCode, string(body))
	}

	return string(body), nil
}

// GetThreatDetails 获取漏洞详情
func (q *QTCloud) GetThreatDetails(id, syncType string) (string, error) {

	httpClient := httputil.NewHttpClient(q.Client.Client)
	apiUri := fmt.Sprintf(UriGetThreatDetails, cast.ToString(q.GetConfig("protocol")), cast.ToString(q.GetConfig("ip")), syncType, id)

	jsonStr, err := q.GetToken()
	if err != nil {
		return "", err
	}

	ts := time.Now().Unix()
	headers := map[string]string{
		"comId":         gjson.Get(jsonStr, "comId").String(),
		"timestamp":     fmt.Sprintf("%d", ts),
		"sign":          q.signature(jsonStr, nil, ts, http.MethodGet),
		"Authorization": fmt.Sprintf("Bearer %s", gjson.Get(jsonStr, "jwt").String()),
	}

	resp, err := httpClient.Get(apiUri, headers)

	if err != nil {
		return "", err
	}

	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)

	if err != nil {
		return "", err
	}

	if resp.StatusCode != 200 {
		return "", errors.New(gjson.Get(string(body), "errorDesc").String())
	}

	return string(body), nil
}

// GetLinuxEmployees 系统账号信息
func (q *QTCloud) GetLinuxEmployees(page, size int) (string, error) {
	params := map[string]interface{}{
		"size": cast.ToString(size),
		"page": cast.ToString(page),
	}

	// 创建 url.Values 并添加参数
	urlParams := url.Values{}
	for key, value := range params {
		urlParams.Add(key, cast.ToString(value))
	}

	httpClient := httputil.NewHttpClient(q.Client.Client)
	apiUri := fmt.Sprintf(UriGetLinuxEmployees, cast.ToString(q.GetConfig("protocol")), cast.ToString(q.GetConfig("ip")), urlParams.Encode())

	jsonStr, err := q.GetToken()
	if err != nil {
		return "", err
	}

	ts := time.Now().Unix()
	headers := map[string]string{
		"comId":         gjson.Get(jsonStr, "comId").String(),
		"timestamp":     fmt.Sprintf("%d", ts),
		"sign":          q.signature(jsonStr, params, ts, http.MethodGet),
		"Authorization": fmt.Sprintf("Bearer %s", gjson.Get(jsonStr, "jwt").String()),
	}

	resp, err := httpClient.Get(apiUri, headers)

	if err != nil {
		return "", err
	}

	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)

	if err != nil {
		return "", err
	}

	if resp.StatusCode != 200 {
		return "", errors.New(gjson.Get(string(body), "errorDesc").String())
	}

	return string(body), nil
}

func (q *QTCloud) signature(jsonStr string, params map[string]interface{}, ts int64, method string) string {
	var info string
	if params != nil {
		// 生成 info
		if method == http.MethodGet {
			keys := make([]string, 0, len(params))
			for key := range params {
				keys = append(keys, key)
			}
			sort.Strings(keys)
			for _, key := range keys {
				info += key + fmt.Sprintf("%v", params[key])
			}
		} else {
			// 对 data 进行 JSON 序列化
			jsonData, _ := json.Marshal(params)
			info = string(jsonData)
		}
	}

	// 拼接待签名字符串
	var toSign string
	if info != "" {
		toSign = gjson.Get(jsonStr, "comId").String() + info + fmt.Sprintf("%d", ts) + gjson.Get(jsonStr, "signKey").String()
	} else {
		toSign = gjson.Get(jsonStr, "comId").String() + fmt.Sprintf("%d", ts) + gjson.Get(jsonStr, "signKey").String()
	}

	// 对待签名字符串进行sha1得到签名字符串
	h := sha1.New()
	h.Write([]byte(toSign))
	return hex.EncodeToString(h.Sum(nil))
}

// GetWeakPwd 函数用于从QTCloud获取弱密码列表。
//
// 参数：
// page: int类型，表示请求的页码。
// size: int类型，表示每页显示的条目数。
// weakName: string类型，表示弱密码的名称。
//
// 返回值：
// string类型，表示从QTCloud获取的弱密码列表的JSON字符串。
// error类型，表示在获取弱密码列表过程中可能发生的错误。

func (q *QTCloud) GetWeakPwd(page, size int, weakName string) (string, error) {
	params := map[string]interface{}{
		"size": cast.ToString(size),
		"page": cast.ToString(page),
	}

	// 创建 url.Values 并添加参数
	urlParams := url.Values{}
	for key, value := range params {
		urlParams.Add(key, cast.ToString(value))
	}

	httpClient := httputil.NewHttpClient(q.Client.Client)
	apiUri := fmt.Sprintf(UriGetWeakPwd, cast.ToString(q.GetConfig("protocol")), cast.ToString(q.GetConfig("ip")), weakName, urlParams.Encode())

	jsonStr, err := q.GetToken()
	if err != nil {
		return "", err
	}

	ts := time.Now().Unix()
	headers := map[string]string{
		"comId":         gjson.Get(jsonStr, "comId").String(),
		"timestamp":     fmt.Sprintf("%d", ts),
		"sign":          q.signature(jsonStr, params, ts, http.MethodGet),
		"Authorization": fmt.Sprintf("Bearer %s", gjson.Get(jsonStr, "jwt").String()),
	}

	resp, err := httpClient.Get(apiUri, headers)

	if err != nil {
		return "", err
	}

	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)

	if err != nil {
		return "", err
	}

	if resp.StatusCode != 200 {
		return "", errors.New(gjson.Get(string(body), "errorDesc").String())
	}
	return string(body), nil
}

// GetWeakPwdDetails 从QTCloud获取指定弱密码的详细信息
//
// 参数：
// weakName: string类型，表示弱密码的名称
// id: string类型，表示弱密码的唯一标识符
//
// 返回值：
// string类型，表示从QTCloud获取的弱密码详细信息的JSON字符串
// error类型，表示在获取弱密码详细信息过程中可能发生的错误
func (q *QTCloud) GetWeakPwdDetails(weakName, id string) (string, error) {
	httpClient := httputil.NewHttpClient(q.Client.Client)
	apiUri := fmt.Sprintf(UriGetWeakPwdDetails, cast.ToString(q.GetConfig("protocol")), cast.ToString(q.GetConfig("ip")), weakName, id)

	jsonStr, err := q.GetToken()
	if err != nil {
		return "", err
	}

	ts := time.Now().Unix()
	headers := map[string]string{
		"comId":         gjson.Get(jsonStr, "comId").String(),
		"timestamp":     fmt.Sprintf("%d", ts),
		"sign":          q.signature(jsonStr, nil, ts, http.MethodGet),
		"Authorization": fmt.Sprintf("Bearer %s", gjson.Get(jsonStr, "jwt").String()),
	}

	resp, err := httpClient.Get(apiUri, headers)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	if resp.StatusCode != 200 {
		return "", errors.New(gjson.Get(string(body), "errorDesc").String())
	}
	return string(body), nil
}

func (q *QTCloud) GetFixedHistory(page, size int, ip string) ([]*FixedHistory, int, error) {
	// httpClient := httputil.NewHttpClient(q.Client.Client)
	apiUri := fmt.Sprintf(UriFixedHistory, cast.ToString(q.GetConfig("protocol")), cast.ToString(q.GetConfig("ip")))

	result := make([]*FixedHistory, 0)

	jsonStr, err := q.GetToken()
	if err != nil {
		return result, 0, err
	}

	ts := time.Now().Unix()
	headers := map[string]any{
		"comId":         gjson.Get(jsonStr, "comId").String(),
		"timestamp":     fmt.Sprintf("%d", ts),
		"sign":          q.signature(jsonStr, nil, ts, http.MethodGet),
		"Authorization": fmt.Sprintf("Bearer %s", gjson.Get(jsonStr, "jwt").String()),
	}
	q.Client.SetConfig(headers)

	req := map[string]interface{}{
		"page": page,
		"size": size,
		"search": map[string]interface{}{
			"ip": ip,
		},
	}

	jsonData, err := json.Marshal(req)
	if err != nil {
		return result, 0, err
	}

	resp, err := q.Client.Post(apiUri, httputil.ContentTypeJson, bytes.NewBuffer(jsonData))
	if err != nil {
		return result, 0, err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return result, 0, err
	}
	if resp.StatusCode != 200 {
		return result, 0, errors.New(gjson.Get(string(body), "errorDesc").String())
	}

	// 解析响应
	var response struct {
		Data struct {
			Rows  []*FixedHistory `json:"rows"`
			Total int             `json:"total"`
		} `json:"data"`
	}
	if err := json.Unmarshal(body, &response); err != nil {
		return result, 0, err
	}

	return response.Data.Rows, response.Data.Total, nil
}

type FixedHistory struct {
	Id           string `json:"id"`           // ID
	AgentId      string `json:"agentId"`      // Agent ID
	ConnectionIp string `json:"connectionIp"` // 连接IP
	VulId        string `json:"vulId"`        // 漏洞ID
	DetectedTime string `json:"detectedTime"` // 检测时间
}
