package weibu

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"fobrain/pkg/utils/handle_es_bulk_error"
	"strings"
	"time"

	assetses "fobrain/models/elastic/assets"
	"fobrain/models/elastic/poc"

	"github.com/olivere/elastic/v7"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"

	"fobrain/fobrain/app/services/node/weibu"
	nodeWeibu "fobrain/fobrain/app/services/node/weibu"
	"fobrain/fobrain/common/constant"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	weibuModel "fobrain/models/elastic/source/weibu"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
)

// SyncWeiBu 同微步数据
func SyncWeiBu(node *data_source.Node, taskInfo string) error {
	logs.GetSyncLogger().Infof("SyncWeiBu start taskInfo:%s", taskInfo)
	assetTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())
	threatTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncThreat)).Int())

	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())

	callbackAssets := func(data string) error {
		count, err := handleAssets(data, node, taskId, assetTaskId)
		if err == nil { //更新成功数量
			err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(assetTaskId, count)
			if err != nil {
				logs.GetSyncLogger().Errorf("Weibu syncAssets UpdateSyncDataSuccessTotal err:%s", err.Error())
			}

		}
		return err
	}
	callbackThreats := func(data string) error {
		count, _, err := handleThreats(data, node, threatTaskId, taskId)
		if err == nil {
			err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(threatTaskId, count)
			if err != nil {
				logs.GetSyncLogger().Errorf("Weibu syncThreats UpdateSyncDataSuccessTotal err:%s", err.Error())
			}

			// pushThreats2Redis(threatIds, threatTaskId)
		}
		return err
	}

	//获取weibuClient
	cli := nodeWeibu.NewWeiBu(callbackAssets, callbackThreats)

	//设置weibuClient请求的节点
	if err := cli.SetNode(node.Id); err != nil {
		return err
	}

	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncAsset)).Int() == data_sync_task.SyncAsset {
		//更新资产任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(assetTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("WeiBu syncAssets UpdateStatusById StatusDoing err:%s", err.Error())
		}
		go syncAssets(cli, taskInfo, node)
	}

	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncThreat)).Int() == data_sync_task.SyncThreat {
		//更新漏洞任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(threatTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("WeiBu syncThreats UpdateStatusById StatusDoing err:%s", err.Error())
		}
		go syncThreats(constant.DefaultPage, cli, taskInfo, node)
	}

	return nil
}

// syncAssets 同步资产
// @param [int64] otherTaskId 如果是同步任务资产则，通过此 ID 传递任务ID。
func syncAssets(cli *nodeWeibu.WeiBu, taskInfo string, node *data_source.Node) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())
	logs.GetSyncLogger().Infof("SyncWeiBu syncAssets start taskInfo:%d,taskChildId:%d", taskId, taskChildId)

	//获取资产
	total, err := cli.GetWeiBuAssetsAll() // GetWeiBuAssetsAll 会调用handleAssets
	if err != nil {
		logs.GetSyncLogger().Errorf("WeiBu syncAssets GetAssets err:%s", err)
		setTaskFailure("syncAssets", "GetAssets", taskId, err, taskChildId)
		return
	}

	//子任务更新需要同步的数据总量
	err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateById(taskChildId, data_sync_child_task.DataSyncChildTask{
		SyncDataTotal: int(total),
	})
	if err != nil {
		logs.GetSyncLogger().Errorf("WeiBu syncAssets UpdateById SyncDataTotal err:%s", err.Error())
	}

	logs.GetSyncLogger().Infof("SyncWeiBu syncAssets end taskInfo:%d,taskChildId:%d", taskId, taskChildId)
	//更新子任务完成状态
	err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(taskChildId, data_sync_child_task.StatusSuccess)
	if err != nil {
		logs.GetSyncLogger().Errorf("Weibu syncAssets UpdateStatusById StatusSuccess err:%s", err.Error())
	}
}

// syncThreats 同步漏洞
func syncThreats(page int, cli *weibu.WeiBu, taskInfo string, node *data_source.Node) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncThreat)).Int())
	logs.GetSyncLogger().Infof("SyncWeiBu syncThreats start taskInfo:%d,taskChildId:%d", taskId, taskChildId)

	//获取漏洞
	total, err := cli.GetWeiBuThreatsAll()
	if err != nil {
		logs.GetSyncLogger().Errorf("WeiBu syncThreats GetThreats err:%s", err)

		setTaskFailure("syncThreats", "GetThreats", taskId, err, taskChildId)
		return
	}

	//子任务更新需要同步的数据总量
	err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateById(taskChildId, data_sync_child_task.DataSyncChildTask{
		SyncDataTotal: int(total),
	})

	if err != nil {
		logs.GetSyncLogger().Errorf("WeiBu syncThreats UpdateById SyncDataTotal err:%s", err.Error())
	}

	logs.GetSyncLogger().Infof("SyncWeiBu syncThreats end taskInfo:%d,taskChildId:%d", taskId, taskChildId)

	//更新子任务完成状态
	err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(taskChildId, data_sync_child_task.StatusSuccess)
	if err != nil {
		logs.GetSyncLogger().Errorf("WeiBu syncThreats UpdateStatusById StatusSuccess err:%s", err.Error())
	}
}

func handleAssets(data string, node *data_source.Node, taskId, taskChildId uint64) (int, error) {
	defer func() {
		if r := recover(); r != nil {
			logs.GetSyncLogger().Errorf("【Weibu syncAssets handleAssets】taskChildId %d panicked: %v", taskChildId, r)
		}
	}()
	// 默认设置为3，智能判断
	networkType, err := node.GetNetworkType()
	if err != nil {
		logs.GetSyncLogger().Errorf("Weibu syncAssets GetNetworkType err:%s", err.Error())
	}
	personField := "name"
	for _, config := range node.Configs {
		if config.Key == "person_field" {
			personField = config.Value
			break
		}
	}
	items := gjson.Get(data, "items")
	if len(items.Array()) == 0 {
		return 0, nil
	}
	count := 0
	if items.Exists() {
		count = int(len(items.Array()))
		// assetBulkRequest := es.GetEsClient().Bulk()
		assetTaskBulkRequest := es.GetEsClient().Bulk()
		assetProcessBulkRequest := es.GetEsClient().Bulk()
		// 遍历 _id 字段，并根据 id 添加新的字段数据
		items.ForEach(func(key, value gjson.Result) bool {
			logs.GetSyncLogger().Debugf("SyncWeiBu syncAssets value info:%s", value)
			doc, _ := sjson.Set(value.String(), "node_id", node.Id)

			ip := gjson.Get(value.String(), "machine").String()
			areaId := node.AreaId
			if ip != "" {
				// 获取区域
				areaId, err = node.GetAreaByIp(ip)
				if err != nil {
					logs.GetSyncLogger().Errorf("Weibu syncAssets GetAreaByIp failed,ip:%s,nodeId:%d,err:%s", ip, node.Id, err)
					r := data_sync_child_task.NewDataSyncChildTaskFailRecord()
					r.Create(&data_sync_child_task.DataSyncChildTaskFailRecord{
						ChildTaskId:  taskChildId,
						DataType:     "ip",
						DataContent:  ip,
						FailedReason: err.Error(),
					})
				}
			}
			doc, _ = sjson.Set(doc, "area_id", areaId)

			//写入资产库
			// asset := weibuModel.NewWeibuAssetsModel()
			assetId := fmt.Sprintf("%d_%d_%s", node.Id, areaId, ip)
			// assetReq := elastic.NewBulkIndexRequest().Index(asset.IndexName()).Id(assetId).Doc(doc)
			// assetBulkRequest = assetBulkRequest.Add(assetReq)

			//写入任务库
			assetTask := weibuModel.NewWeibuTaskAssetsModel()
			assetTaskId := fmt.Sprintf("%d_%s", taskId, assetId)
			doc, _ = sjson.Set(doc, "task_id", taskId)
			doc, _ = sjson.Set(doc, "child_task_id", taskChildId)
			assetTaskReq := elastic.NewBulkIndexRequest().Index(assetTask.IndexName()).Id(assetTaskId).Doc(doc)
			assetTaskBulkRequest = assetTaskBulkRequest.Add(assetTaskReq)

			//写入过程库
			assetProcessId := fmt.Sprintf("%d_%d_%s", taskId, taskChildId, assetId)
			assetProcess := SetProcessAssetFields(assetProcessId, assetTaskId, ip, int(areaId), node, value, networkType, personField, taskId, taskChildId)

			assetProcessReq := elastic.NewBulkIndexRequest().Index(assetProcess.IndexName()).Id(assetProcessId).Doc(assetProcess)
			assetProcessBulkRequest = assetProcessBulkRequest.Add(assetProcessReq)

			return true
		})
		if count > 0 {
			assetProcessBulkReq, err := assetProcessBulkRequest.Refresh("true").Do(context.Background())

			if err != nil || assetProcessBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(assetses.NewProcessAssetsModel().IndexName(), err, assetProcessBulkReq)

				logs.GetSyncLogger().Errorf("Weibu syncAssets assetProcessBulkRequest.Do err:%s", errString)
				return 0, err
			}

			// assetBulkReq, err := assetBulkRequest.Do(context.Background())
			// if err != nil || assetBulkReq.Errors {
			// 	errStr := handle_es_bulk_error.HandleBulkResp(weibuModel.NewWeibuAssetsModel().IndexName(), err, assetBulkReq)
			// 	logs.GetSyncLogger().Errorf("Weibu syncAssets assetBulkRequest.Do err:%s", errStr)

			// 	return 0, err
			// }

			assetTaskBulkReq, err := assetTaskBulkRequest.Do(context.Background())

			if err != nil || assetTaskBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(weibuModel.NewWeibuTaskAssetsModel().IndexName(), err, assetTaskBulkReq)
				logs.GetSyncLogger().Errorf("Weibu syncAssets assetTaskBulkRequest.Do err:%s", errString)
				return 0, err
			}
		}

	}

	return count, nil
}

func handleThreats(data string, node *data_source.Node, taskChildId, taskId uint64) (int, []map[string]interface{}, error) {
	defer func() {
		if r := recover(); r != nil {
			logs.GetSyncLogger().Errorf("【Weibu syncThreats handleThreats】taskChildId %d panicked: %v", taskChildId, r)
		}
	}()

	items := gjson.Get(data, "items")
	if len(items.Array()) == 0 {
		return 0, nil, nil
	}
	count := 0
	var threatIds []map[string]interface{}
	if items.Exists() {
		count = len(items.Array())

		// threatBulkRequest := es.GetEsClient().Bulk()
		threatTaskBulkRequest := es.GetEsClient().Bulk()
		threatProcessBulkRequest := es.GetEsClient().Bulk()

		// 遍历 _id 字段，并根据 id 添加新的字段数据
		items.ForEach(func(key, value gjson.Result) bool {
			logs.GetSyncLogger().Debugf("SyncWeiBu syncThreats value info:%s", value)
			names := gjson.Get(value.String(), "vulnerability_name").Array()
			vulName := ""
			if len(names) > 0 {
				vulName = names[0].String()
			}
			ips := gjson.Get(value.String(), "machine").Array()
			ip := ""
			if len(ips) > 0 {
				ip = ips[0].String()
			}

			areaId := uint64(node.AreaId)
			if ip != "" {
				// 获取区域
				area, err := node.GetAreaByIp(ip)
				if err != nil {
					logs.GetSyncLogger().Errorf("Weibu syncThreats GetAreaByIp failed,ip:%s,nodeId:%d,err:%s", ip, node.Id, err)
					r := data_sync_child_task.NewDataSyncChildTaskFailRecord()
					r.Create(&data_sync_child_task.DataSyncChildTaskFailRecord{
						ChildTaskId:  taskChildId,
						DataType:     "ip",
						DataContent:  ip,
						FailedReason: err.Error(),
					})
				}
				areaId = area
			}

			idStr := fmt.Sprintf("%s_%s", vulName, ip)
			hasher := md5.New()
			hasher.Write([]byte(idStr))
			id := hex.EncodeToString(hasher.Sum(nil))

			doc, _ := sjson.Set(value.String(), "node_id", node.Id)
			doc, _ = sjson.Set(doc, "area_id", areaId)
			doc, _ = sjson.Set(doc, "original_id", id)

			//写入漏洞库
			// threat := weibuModel.NewWeibuThreatsModel()
			threatId := fmt.Sprintf("%d_%d_%s", node.Id, areaId, id)
			// threatReq := elastic.NewBulkIndexRequest().Index(threat.IndexName()).Id(threatId).Doc(doc)
			// threatBulkRequest = threatBulkRequest.Add(threatReq)

			//写入任务库
			threatTask := weibuModel.NewWeibuTaskThreatsModel()
			threatTaskId := fmt.Sprintf("%d_%s", taskId, threatId)
			doc, _ = sjson.Set(doc, "task_id", taskId)
			doc, _ = sjson.Set(doc, "child_task_id", taskChildId)
			threatTaskReq := elastic.NewBulkIndexRequest().Index(threatTask.IndexName()).Id(threatTaskId).Doc(doc)
			threatTaskBulkRequest = threatTaskBulkRequest.Add(threatTaskReq)

			//写入过程库
			threatProcess := SetProcessThreatFields(threatId, id, threatTaskId, areaId, node, value, taskId, taskChildId)

			threatProcessReq := elastic.NewBulkIndexRequest().Index(threatProcess.IndexName()).Id(threatId).Doc(threatProcess)
			threatProcessBulkRequest = threatProcessBulkRequest.Add(threatProcessReq)
			// threatIds = append(threatIds, map[string]interface{}{"id": threatId, "is_poc": poc.YesPoc, "task_id": taskChildId})
			return true
		})
		if count > 0 {
			threatProcessBulkReq, err := threatProcessBulkRequest.Refresh("true").Do(context.Background())
			if err != nil || threatProcessBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(poc.NewProcessPocModel().IndexName(), err, threatProcessBulkReq)
				logs.GetSyncLogger().Errorf("WeiBu syncThreats threatProcessBulkRequest.Do err:%s", errString)
				return 0, threatIds, err
			}

			// threatBulkReq, err := threatBulkRequest.Do(context.Background())
			// if err != nil || threatBulkReq.Errors {
			// 	errString := handle_es_bulk_error.HandleBulkResp(weibuModel.NewWeibuThreatsModel().IndexName(), err, threatBulkReq)
			// 	logs.GetSyncLogger().Errorf("WeiBu syncThreats threatBulkRequest.Do err:%s", errString)
			// 	return 0, threatIds, err
			// }

			threatTaskBulkReq, err := threatTaskBulkRequest.Do(context.Background())
			if err != nil || threatTaskBulkReq.Errors {
				errStr := handle_es_bulk_error.HandleBulkResp(weibuModel.NewWeibuTaskThreatsModel().IndexName(), err, threatTaskBulkReq)
				logs.GetSyncLogger().Errorf("WeiBu syncThreats threatTaskBulkRequest.Do err:%s", errStr)
				return 0, threatIds, err
			}
		}

	}
	return count, threatIds, nil
}

func SetProcessThreatFields(threatId string, id string, threatTaskId string, areaId uint64, node *data_source.Node, value gjson.Result, taskId, taskChildId uint64) *poc.ProcessPoc {
	threatProcess := poc.NewProcessPocModel()
	ips := gjson.Get(value.String(), "machine").Array()
	ip := ""
	if len(ips) > 0 {
		ip = ips[0].String()
	}

	threatProcess.Url = ip
	threatProcess.Id = threatId
	threatProcess.TaskId = taskId
	threatProcess.ChildTaskId = taskChildId
	threatProcess.Ip = ip
	threatProcess.OriginalId = id
	threatProcess.TaskDataId = threatTaskId
	threatProcess.Area = areaId
	threatProcess.Node = node.Id
	threatProcess.Source = node.SourceId
	threatProcess.IsPoc = poc.NoPoc
	threatProcess.HasPoc = poc.NoPoc

	names := gjson.Get(value.String(), "vulnerability_name").Array()
	if len(names) > 0 {
		threatProcess.Name = names[0].String()
	}
	//threatProcess.Port = int()

	//漏洞等级（低危 1 中危 2 高危 3 严重 4 未知 5）
	levels := gjson.Get(value.String(), "severity").Array()
	if len(levels) > 0 {
		threatProcess.Level = int(levels[0].Int())
	} else {
		threatProcess.Level = 5
	}

	types := gjson.Get(value.String(), "vulnerability_type_desc").Array()
	if len(types) > 0 {
		threatProcess.VulType = types[0].String()
	}

	timeStr := gjson.Get(value.String(), "last_occ_time_desc").String()
	if timeStr == "" {
		threatProcess.UpdatedAt = localtime.NewLocalTime(time.Now())
		threatProcess.CreatedAt = localtime.NewLocalTime(time.Now())
	} else {
		updateTime, _ := time.Parse(localtime.TimeFormat, timeStr)
		threatProcess.UpdatedAt = localtime.NewLocalTime(updateTime)
		threatProcess.CreatedAt = localtime.NewLocalTime(updateTime)
		threatProcess.LastResponseAt = localtime.NewLocalTime(updateTime)
	}

	return threatProcess
}

func SetProcessAssetFields(assetProcessId, assetTaskId, ip string, areaId int, node *data_source.Node, value gjson.Result, networkType int, personField string, taskId, taskChildId uint64) *assetses.ProcessAssets {
	assetProcess := assetses.NewProcessAssetsModel()
	assetProcess.Id = assetProcessId
	assetProcess.TaskDataId = assetTaskId
	assetProcess.Area = areaId
	assetProcess.Node = node.Id
	assetProcess.Source = node.SourceId
	assetProcess.TaskId = taskId
	assetProcess.ChildTaskId = taskChildId
	assetProcess.Ip = ip
	assetProcess.NetworkType = networkType
	assetProcess.PersonField = personField
	assetProcess.Status = 1
	var product []string
	var ports []*assetses.PortInfo
	gjson.Get(value.String(), "service_port").ForEach(func(k, v gjson.Result) bool {
		var portInfo assetses.PortInfo
		portInfo.Port = int(gjson.Get(v.String(), "port").Int())
		portInfo.Protocol = gjson.Get(v.String(), "app_proto").String()
		domainArr := []string{}
		domains := gjson.Get(v.String(), "machine_name").Array()
		for _, domain := range domains {
			domainArr = append(domainArr, domain.String())
		}
		portInfo.Domain = strings.Join(domainArr, ",")
		ports = append(ports, &portInfo)
		return true
	})
	assetProcess.Product = product
	assetProcess.Ports = ports
	assetProcess.Mac = gjson.Get(value.String(), "assets.mac").String()

	osArr := gjson.Get(value.String(), "machine_os").Array()
	if len(osArr) > 0 {
		assetProcess.Os = osArr[0].String()
	}

	assetProcess.CreatedAt = localtime.NewLocalTime(time.Now())
	assetProcess.UpdatedAt = localtime.NewLocalTime(time.Now())

	return assetProcess
}

func setTaskFailure(category, operation string, taskId uint64, err error, taskChildId uint64) {
	//更新主任务状态为失败
	taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, operation+" "+err.Error())
	if taskErr != nil {
		logs.GetSyncLogger().Errorf("WeiBu "+category+" "+operation+" Task UpdateFailById err:%s", taskErr.Error())
	}

	//更新子任务状态为失败
	childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, operation+" "+err.Error())
	if childTaskErr != nil {
		logs.GetSyncLogger().Errorf("WeiBu "+category+" "+operation+" childTask UpdateFailById err:%s", childTaskErr.Error())
	}
}
