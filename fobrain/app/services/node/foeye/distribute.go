package foeye

import (
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/fobrain/app/services/node/foeyev2"
	httputil "fobrain/pkg/http"
	"io"
	"net/http"
	"net/url"
	"strings"

	"github.com/jinzhu/copier"

	"fobrain/fobrain/logs"

	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

// NodeMode
// @Summary 获取节点模式
func (f *Foeye) NodeMode() (int, error) {
	if f.IsFoeyeV2() {
		return 2, nil
	}
	httpClient := httputil.NewHttpClient(f.Client.Client)
	apiUri := fmt.Sprintf(UriGetConfig, cast.ToString(f.GetConfig("protocol")), cast.ToString(f.GetConfig("ip")))

	headers, _ := f.Headers()

	resp, err := httpClient.Get(apiUri, headers)
	if err != nil {
		return 0, err
	}

	if resp.StatusCode != 200 {
		msg, _ := json.Marshal(resp.Body)
		return 0, fmt.<PERSON><PERSON><PERSON>("failed to push task to foeye, status code: %d, errInfo %s", resp.StatusCode, string(msg))
	}

	type Response struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Data struct {
				ID          int    `json:"id"`
				MID         string `json:"mid"`
				Host        string `json:"host"`
				Keys        string `json:"keys"`
				SwitchModel int    `json:"switch_model"`
				CreatedAt   string `json:"created_at"`
			} `json:"data"`
		} `json:"data"`
	}
	var response Response

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return 0, err
	}

	// Foeye 1 为节点模式、2为集中管控模式
	if response.Data.Data.SwitchModel == 1 {
		return 2, nil
	}

	return 1, nil
}

// ClearBlankIp
// @Summary 清除节点的禁扫IP
func (f *Foeye) ClearBlankIp() bool {
	httpClient := httputil.NewHttpClient(f.Client.Client)
	apiUri := fmt.Sprintf(Blacklists, cast.ToString(f.GetConfig("protocol")), cast.ToString(f.GetConfig("ip")))

	headers, _ := f.Headers()

	resp, err := httpClient.Delete(apiUri, headers)
	if err != nil {
		logs.GetCrontabLogger().Errorf("清除禁扫IP到foeye 节点失败 err:  %v", err.Error())
		return false
	}

	if resp.StatusCode != 200 {
		logs.GetCrontabLogger().Errorf("清除禁扫IP到foeye 节点失败 %v", resp.StatusCode)
		return false
	}

	return true
}

// SyncBlankIp
// @Summary 同步禁扫IP到节点中。
func (f *Foeye) SyncBlankIp(ips []string) bool {
	// 先清除节点中的禁扫IP
	f.ClearBlankIp()

	// 然后再同步平台的禁扫IP到节点中
	httpClient := httputil.NewHttpClient(f.Client.Client)
	apiUri := fmt.Sprintf(Blacklists, cast.ToString(f.GetConfig("protocol")), cast.ToString(f.GetConfig("ip")))

	headers, _ := f.Headers()

	body := strings.Join(ips, "\n")
	resp, err := httpClient.Post(apiUri, body, headers)
	if err != nil {
		logs.GetCrontabLogger().Errorf("同步禁扫IP到foeye 节点失败 err:  %v", err.Error())
		return false
	}

	if resp.StatusCode != 200 {
		logs.GetCrontabLogger().Errorf("同步禁扫IP到foeye 节点失败 %v", resp.StatusCode)
		return false
	}

	return true
}

func (f *Foeye) StopTask(nodeTaskId int) error {
	if f.IsFoeyeV2() {
		f2 := foeyev2.NewFoeyeV2Task()
		if err := f2.Client.SetConfig(map[string]any{
			"protocol": cast.ToString(f.GetConfig("protocol")),
			"ip":       cast.ToString(f.GetConfig("ip")),
			"api_key":  cast.ToString(f.GetConfig("api_key")),
		}); err != nil {
			return err
		}
		return f2.DelTask(nodeTaskId)
	}
	httpClient := httputil.NewHttpClient(f.Client.Client)

	headers, _ := f.Headers()

	apiUri2 := fmt.Sprintf(UriDelTask, cast.ToString(f.GetConfig("protocol")), cast.ToString(f.GetConfig("ip")), nodeTaskId)

	_, err := httpClient.Post(apiUri2, map[string]any{"type": "pause"}, headers)

	if err != nil {
		return err
	}

	return nil
}

func (f *Foeye) StartTask(nodeTaskId int) error {
	if f.IsFoeyeV2() {
		f2 := foeyev2.NewFoeyeV2Task()
		if err := f2.Client.SetConfig(map[string]any{
			"protocol": cast.ToString(f.GetConfig("protocol")),
			"ip":       cast.ToString(f.GetConfig("ip")),
			"api_key":  cast.ToString(f.GetConfig("api_key")),
		}); err != nil {
			return err
		}
		return f2.StartTask(nodeTaskId)
	}
	httpClient := httputil.NewHttpClient(f.Client.Client)

	headers, _ := f.Headers()

	apiUri2 := fmt.Sprintf(UriBeginTask, cast.ToString(f.GetConfig("protocol")), cast.ToString(f.GetConfig("ip")), nodeTaskId)

	_, err := httpClient.Post(apiUri2, nil, headers)

	if err != nil {
		return err
	}

	return nil
}

// Push
// @Summary 推送任务信息到 Foeye
func (f *Foeye) Push(body any) (int64, error) {
	if f.IsFoeyeV2() {
		f2 := foeyev2.NewFoeyeV2Task()
		if err := f2.Client.SetConfig(map[string]any{
			"protocol": cast.ToString(f.GetConfig("protocol")),
			"ip":       cast.ToString(f.GetConfig("ip")),
			"api_key":  cast.ToString(f.GetConfig("api_key")),
		}); err != nil {
			return 0, err
		}
		convertBody := ConvertParamsToV2(body.(map[string]any))
		return f2.PushTask(convertBody)
	}

	httpClient := httputil.NewHttpClient(f.Client.Client)
	apiUri := fmt.Sprintf(PushTasking, cast.ToString(f.GetConfig("protocol")), cast.ToString(f.GetConfig("ip")))

	headers, _ := f.Headers()

	resp, err := httpClient.Post(apiUri, body, headers)
	if err != nil {
		return 0, err
	}

	if resp.StatusCode != 200 {
		msg, err := io.ReadAll(resp.Body)
		if err != nil {
			return 0, err
		}
		return 0, fmt.Errorf("failed to push task to foeye, status code: %d, errInfo %s", resp.StatusCode, string(msg))
	}

	var response struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			ID int64 `json:"id"`
		} `json:"data"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return 0, err
	}

	fmt.Println("push task to foeye success ", response.Data.ID)

	return response.Data.ID, nil
}

type TaskInfoResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Data struct {
			Task TaskInfoTask `json:"task"`
		}
	} `json:"data"`
}

type DelNodeTaskResponse struct {
	Code    int      `json:"code"`
	Message string   `json:"message"`
	Data    struct{} `json:"data"`
}

type TaskInfoTask struct {
	ID              int     `json:"id"`
	TemplateID      int     `json:"template_id"`
	Title           string  `json:"title"`
	Progress        float64 `json:"progress"`
	TaskType        int     `json:"task_type"`
	UserID          int     `json:"user_id"`
	UserName        string  `json:"user_name"`
	RealBeginTime   string  `json:"real_begin_time"`
	RealEndTime     *string `json:"real_end_time"` // 可以为 null，所以使用 *string
	UseSeconds      string  `json:"use_seconds"`
	State           int     `json:"state"`
	RunType         string  `json:"run_type"`
	IPCount         int     `json:"ip_count"`
	AssetNum        int     `json:"asset_num"`
	ThreatNum       int     `json:"threat_num"`
	RuleNum         int     `json:"rule_num"`
	ScanStep        string  `json:"scan_step"`
	Ready           bool    `json:"ready"`
	CreatedAt       string  `json:"created_at"`
	UpdatedAt       string  `json:"updated_at"`
	EstimateTimeStr string  `json:"estimate_time_str"`
	OtherCfgs       string  `json:"other_cfgs"`
	ScanType        string  `json:"scan_type"`
}

// GetTaskInfo
// @summary 获取任务情况
func (f *Foeye) GetTaskInfo(clientTaskId int64) (*TaskInfoTask, error) {
	if f.IsFoeyeV2() {
		f2 := foeyev2.NewFoeyeV2Task()
		if err := f2.Client.SetConfig(map[string]any{
			"protocol": cast.ToString(f.GetConfig("protocol")),
			"ip":       cast.ToString(f.GetConfig("ip")),
			"api_key":  cast.ToString(f.GetConfig("api_key")),
		}); err != nil {
			return nil, err
		}
		task, err := f2.GetTaskInfo(clientTaskId)
		if err != nil {
			return nil, err
		}
		taskInfo := &TaskInfoTask{}
		if err = copier.Copy(taskInfo, task); err != nil {
			return nil, err
		}
		return taskInfo, nil
	}
	httpClient := httputil.NewHttpClient(f.Client.Client)

	apiUri := fmt.Sprintf(GetTaskInfo, cast.ToString(f.GetConfig("protocol")), cast.ToString(f.GetConfig("ip")), clientTaskId)

	headers, _ := f.Headers()

	resp, err := httpClient.Get(apiUri, headers)

	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()

	var task *TaskInfoResponse
	if err := json.NewDecoder(resp.Body).Decode(&task); err != nil {
		return nil, err
	}

	if resp.StatusCode != 200 {
		return nil, errors.New(task.Message)
	}

	return &task.Data.Data.Task, nil
}

// DelNodeTask 删除 节点任务
func (f *Foeye) DelNodeTask(nodeTaskId int) error {
	if f.IsFoeyeV2() {
		f2 := foeyev2.NewFoeyeV2Task()
		if err := f2.Client.SetConfig(map[string]any{
			"protocol": cast.ToString(f.GetConfig("protocol")),
			"ip":       cast.ToString(f.GetConfig("ip")),
			"api_key":  cast.ToString(f.GetConfig("api_key")),
		}); err != nil {
			return err
		}
		if err := f2.DelFinTask(nodeTaskId); err != nil {
			return err
		}
		if err := f2.DelTask(nodeTaskId); err != nil {
			return err
		}
		return nil
	}
	httpClient := httputil.NewHttpClient(f.Client.Client)

	// 第一次请求 - 任务执行中去删除
	apiUri := fmt.Sprintf(UriDelFinTask, cast.ToString(f.GetConfig("protocol")), cast.ToString(f.GetConfig("ip")), nodeTaskId)

	headers, _ := f.Headers()
	_, err := httpClient.Delete(apiUri, headers)

	if err != nil {
		return err
	}

	// 第二次请求 - 任务执行完毕去删除
	apiUri2 := fmt.Sprintf(UriDelTask, cast.ToString(f.GetConfig("protocol")), cast.ToString(f.GetConfig("ip")), nodeTaskId)

	_, err = httpClient.Post(apiUri2, nil, headers)

	if err != nil {
		return err
	}

	return nil
}

// Retest 复测
func (f *Foeye) Retest(ids []string) (string, error) {
	if f.IsFoeyeV2() {
		f2 := foeyev2.NewFoeyeV2Task()
		if err := f2.Client.SetConfig(map[string]any{
			"protocol": cast.ToString(f.GetConfig("protocol")),
			"ip":       cast.ToString(f.GetConfig("ip")),
			"api_key":  cast.ToString(f.GetConfig("api_key")),
		}); err != nil {
			return "", err
		}
		return "", f2.Repair(ids)
	}

	body := map[string]any{
		"ids":    ids,
		"type":   "threat",
		"switch": "unfixed",
	}
	apiUri := fmt.Sprintf(UriThreatRepair, cast.ToString(f.GetConfig("protocol")), cast.ToString(f.GetConfig("ip")))
	logs.GetLogger().Infow("ReTest ", "apiUri", apiUri)
	logs.GetLogger().Infow("ReTest ", "body", body)
	resp, err := f.Post(apiUri, body)
	if err != nil {
		logs.GetLogger().Errorf("ReTest Post:%s", err.Error())
		return "", err
	}
	defer resp.Body.Close()

	var response struct {
		Code    int      `json:"code"`
		Message string   `json:"message"`
		Data    struct{} `json:"data"`
	}

	if err = json.NewDecoder(resp.Body).Decode(&response); err != nil {
		logs.GetLogger().Errorf("ReTest NewDecoder:%s", err.Error())
		return "", err
	}

	if resp.StatusCode != 200 {
		return "", fmt.Errorf("failed to push threat repair to foeye, status code: %d, errInfo %s", resp.StatusCode, response.Message)
	}

	fmt.Println("push threat repair to foeye success ", ids)

	return "", nil
}

// GetCheckProgress 获取复测进度
func (f *Foeye) GetCheckProgress(ids []string) (int, error) {
	if f.IsFoeyeV2() {
		f2 := foeyev2.NewFoeyeV2Task()
		if err := f2.Client.SetConfig(map[string]any{
			"protocol": cast.ToString(f.GetConfig("protocol")),
			"ip":       cast.ToString(f.GetConfig("ip")),
			"api_key":  cast.ToString(f.GetConfig("api_key")),
		}); err != nil {
			return 0, err
		}
		return f2.GetCheckProgress()
	}
	apiUri := fmt.Sprintf(UriThreatRepairProgress, cast.ToString(f.GetConfig("protocol")), cast.ToString(f.GetConfig("ip")))
	resp, err := f.Get(apiUri)
	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()

	var response struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Fixed    int `json:"fixed"`
			NotFixed int `json:"not_fixed"`
			Offline  int `json:"offline"`
			Unknown  int `json:"unknown"`
			Progress int `json:"progress"`
			Total    int `json:"total"`
		} `json:"data"`
	}

	if err = json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return 0, err
	}

	if resp.StatusCode != 200 {
		return 0, fmt.Errorf("failed to get repair progress from foeye, status code: %d, errInfo %s", resp.StatusCode, response.Message)
	}

	return response.Data.Progress, nil
}

// GetCheckResult 获取复测结果
func (f *Foeye) GetCheckResult(ids []string, pocObj map[string]interface{}) (gjson.Result, int, error) {
	if f.IsFoeyeV2() {
		f2 := foeyev2.NewFoeyeV2Task()
		if err := f2.Client.SetConfig(map[string]any{
			"protocol": cast.ToString(f.GetConfig("protocol")),
			"ip":       cast.ToString(f.GetConfig("ip")),
			"api_key":  cast.ToString(f.GetConfig("api_key")),
		}); err != nil {
			return gjson.Result{}, 0, err
		}
		return f2.GetCheckResult()
	}

	params := url.Values{
		"ids": ids,
	}

	result := gjson.Result{}

	apiUri := fmt.Sprintf(UriGetThreats, cast.ToString(f.GetConfig("protocol")), cast.ToString(f.GetConfig("ip")), params.Encode())
	logs.GetLogger().Infow("ReTest foeye", "apiUri", apiUri)
	resp, err := f.Get(apiUri)
	if err != nil {
		return result, 0, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return result, 0, err
	}

	if resp.StatusCode != 200 || gjson.Get(string(body), "code").Int() != 200 {
		return result, 0, errors.New(gjson.Get(string(body), "message").String())
	}

	fmt.Println("get repair result from foeye success ", ids)

	data := gjson.Get(string(body), "data").String()
	total, r := getItems(data)

	return r, total, nil
}

func getItems(threats string) (int, gjson.Result) {
	total := gjson.Get(threats, "total").Int()
	if total > 0 {
		result := gjson.Get(threats, "data.info")
		return int(total), result
	}

	return 0, gjson.Result{}
}

func (f *Foeye) Post(apiUri string, body any) (*http.Response, error) {
	httpClient := httputil.NewHttpClient(f.Client.Client)

	headers, _ := f.Headers()

	resp, err := httpClient.Post(apiUri, body, headers)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (f *Foeye) Get(apiUri string) (*http.Response, error) {
	httpClient := httputil.NewHttpClient(f.Client.Client)

	headers, _ := f.Headers()

	resp, err := httpClient.Get(apiUri, headers)

	if err != nil {
		return nil, err
	}
	return resp, nil
}

// ConvertParamsToV2 将 v3 的参数转换为 v2 的格式
func ConvertParamsToV2(params map[string]any) map[string]any {
	b, _ := json.Marshal(params)
	body := string(b)

	selectedIpRanges := ""
	ipRanges := gjson.Get(body, "selected_ip_ranges").Array()
	for i, ipRange := range ipRanges {
		if i != 0 {
			selectedIpRanges += ","
		}
		selectedIpRanges = selectedIpRanges + ipRange.String()
	}

	pocScanType := gjson.Get(body, "poc_scan_type").String()
	if pocScanType == "" {
		pocScanType = "normal"
	}

	ping := "off"
	if gjson.Get(body, "other_cfgs.is_ping_recognition").Bool() {
		ping = "on"
	}

	return map[string]any{
		"selected_ip_ranges": selectedIpRanges,
		"poc_scan_type":      pocScanType,
		"selected_pocs":      gjson.Get(body, "selected_pocs").String(),
		"tasking": map[string]any{
			"title":                 gjson.Get(body, "title").String(),
			"task_type":             gjson.Get(body, "task_type").Int(),
			"scan_port_template_id": gjson.Get(body, "scan_port_template_id").Int(),
			"bandwidth":             gjson.Get(body, "bandwidth").Int(),
			"scan_type":             gjson.Get(body, "scan_type").String(),
			"deep_scan":             "off",
			"ip_type":               gjson.Get(body, "ip_type").Int(),
			"other_cfgs": map[string]any{
				"protocol_concurrency": gjson.Get(body, "concurrency").Int(),
				"ping":                 ping,
			},
		},
	}
}
