package compliance_monitor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	assetses "fobrain/models/elastic/assets"
	"fobrain/models/mysql/compliance_monitor"
	"fobrain/models/mysql/network_areas"
	"fobrain/pkg/utils"
	pgidservice "fobrain/services/people_pgid"
	"git.gobies.org/caasm/fobrain-components/utils/localtime"
	"github.com/olivere/elastic/v7"
	"io"
	"strconv"
	"strings"
)

type (
	ComplianceMonitorTaskRecords struct {
		*es.BaseModel           `json:"-"`
		ComplianceMonitorId     uint64                        `json:"compliance_monitor_id"`      //合规监测ID
		ComplianceMonitorTaskId uint64                        `json:"compliance_monitor_task_id"` //合规监测任务ID
		Product                 []string                      `json:"product"`
		Id                      string                        `json:"id"` // ID
		AssetId                 string                        `json:"asset_id"`
		Fid                     string                        `json:"fid"`                     // 资产唯一标识，IP+区域 唯一值
		FidHash                 string                        `json:"fid_hash"`                // 资产唯一标识，IP+区域 唯一值
		Area                    int                           `json:"area"`                    // 区域
		Ip                      string                        `json:"ip"`                      // IP
		IpType                  int                           `json:"ip_type"`                 // IP类型(1是IPv4，2是IPv6,3是其他)
		IpSegment               []string                      `json:"ip_segment"`              // IP段
		RuleInfos               []*assetses.RuleInfo          `json:"rule_infos"`              //组件信息
		Business                []*assetses.Business          `json:"business"`                // 业务系统
		BusinessDepartment      []*assetses.DepartmentBase    `json:"business_department"`     // 业务系统部门
		OperDepartment          []*assetses.DepartmentBase    `json:"oper_department"`         // 运维人员部门
		OperInfo                []*assetses.PersonBase        `json:"oper_info"`               // 运维人员信息
		OperWithMapping         []*assetses.PersonWithMapping `json:"oper_with_mapping"`       // 运维人员映射
		Status                  int                           `json:"status"`                  // 状态 在线1 离线2
		Ports                   []*assetses.PortInfo          `json:"ports"`                   // 端口
		NetworkType             int                           `json:"network_type"`            // 网络类型(1是内网，2是外网，3是其他) 自己判断
		DeletedAt               *localtime.Time               `json:"deleted_at"`              // 删除时间 - 软删除时间-回收站列表展示的是存在这个字段内容的数据
		CreatedAt               *localtime.Time               `json:"created_at"`              // 创建时间
		UpdatedAt               *localtime.Time               `json:"updated_at"`              // 更新时间
		LastResponseAt          *localtime.Time               `json:"last_response_at"`        // 最后一次响应时间，来自数据源上报，属于业务属性
		DataSourceResponseAt    *localtime.Time               `json:"data_source_response_at"` // 数据源最后一次响应时间，来自数据源更新动作，属于审计属性
		IsDeviceExtracted       int                           `json:"is_device_extracted"`     // 是否参与了实体提取,1是未参与，2是参与了
		PersonLimit             []string                      `json:"person_limit"`            // 人员限制-全局统一字段，使用人员 fid 来限制数据权限
		PersonLimitHash         []string                      `json:"person_limit_hash"`       // 人员限制-全局统一字段，使用人员 fid_hash 来限制数据权限
		Tags                    []string                      `json:"tag"`
		CustomFields            map[string]string             `json:"custom_fields"`           // 自定义字段
		OperStaffIds            []string                      `json:"oper_staff_ids"`          // 运维人员ids
		BusinessStaffIds        []string                      `json:"business_staff_ids"`      // 业务系统负责人ids
		OperDepartmentIds       []uint64                      `json:"oper_department_ids"`     // 运维人员部门ids
		BusinessDepartmentIds   []uint64                      `json:"business_department_ids"` // 业务系统部门ids

		FlowStatus            int                        `json:"flow_status"`             // 合规检测流转状态
		PersonInfo            []*assetses.PersonBase     `json:"person_info"`             // 修复负责人信息
		PersonDepartment      []*assetses.DepartmentBase `json:"person_department"`       // 修复负责人部门
		RepairDepartmentIds   []uint64                   `json:"repair_department_ids"`   // 漏洞修复人部门ids
		CcPerson              map[string]string          `json:"cc_person"`               // 抄送人
		LimitDate             *localtime.Time            `json:"limit_date"`              // 修复时间要求,来自状态流转
		ProcessedTime         *localtime.Time            `json:"processed_time"`          // 状态变为已处理的时间
		FlowStatusChangeTime  *localtime.Time            `json:"flow_status_change_time"` // 状态变更时间
		HostName              []string                   `json:"hostname"`                // 主机名
		ComplianceMonitorName string                     `json:"compliance_monitor_name"` // 合规检测任务名
	}
	// QueryRule 结构体定义
	QueryRule struct {
		RuleName  string `json:"rule_name"`
		Whether   string `json:"whether"`
		Content   string `json:"content"`
		Condition string `json:"condition"`
	}
)

// IndexName 索引名
func (p *ComplianceMonitorTaskRecords) IndexName() string {
	return "compliance_monitor_task_records"
}

func (p *ComplianceMonitorTaskRecords) String() string {
	if p == nil {
		return ""
	}
	return ""
}

func NewComplianceMonitorTaskRecords() *ComplianceMonitorTaskRecords {
	return &ComplianceMonitorTaskRecords{}
}

func (p *ComplianceMonitorTaskRecords) SampleHash() map[string]interface{} {
	if len(p.Business) > 0 {
		for _, value := range p.Business {
			if value != nil && value.BusinessInfo != nil {
				//增加业务系统可信状态展示到内外网资产
				businessInfo, ok := value.BusinessInfo.(map[string]interface{})
				businessInfoBusinessName, _ := businessInfo["business_name"].(string)
				if ok && len(businessInfoBusinessName) > 0 { //存在业务系统
					businessInfo["reliability"] = businessInfo["status"]
					value.BusinessInfo = businessInfo //回填
				}
			}
		}
	}
	return map[string]interface{}{
		"compliance_monitor_id":      p.ComplianceMonitorId,
		"compliance_monitor_task_id": p.ComplianceMonitorTaskId,
		"id":                         p.Id,
		"fid":                        p.Fid,
		"fid_hash":                   p.FidHash,
		"area":                       network_areas.NetworkAreaName(uint64(p.Area)),
		"ip":                         p.Ip,
		"ip_type":                    p.IpType,
		"ip_segment":                 p.IpSegment,
		"rule_infos":                 p.RuleInfos,
		"business": func() []*assetses.Business {
			if len(p.Business) == 0 {
				return make([]*assetses.Business, 0)
			}
			for _, business := range p.Business {
				if business == nil {
					continue
				}
				if business.PersonBase == nil {
					business.PersonBase = make([]*assetses.PersonBase, 0)
				}
				if business.DepartmentBase == nil {
					business.DepartmentBase = make([]*assetses.DepartmentBase, 0)
				}
				for _, base := range business.PersonBase {
					base.Pgid, _ = pgidservice.GetPgidById(base.Id)
					if base.FindInfo == nil {
						base.FindInfo = make([]*assetses.PersonFindInfo, 0)
					}
					if base.Department == nil {
						base.Department = make([]*assetses.DepartmentBase, 0)
					}
				}
			}
			return p.Business
		}(),
		"business_department":     p.BusinessDepartment,
		"oper_department":         p.OperDepartment,
		"oper_info":               assetses.DistinctOperInfo(p.OperInfo),
		"oper_with_mapping":       p.OperWithMapping,
		"ports":                   p.Ports,
		"status":                  p.StatusDesc(),
		"network_type":            p.NetworkTypeDesc(),
		"created_at":              p.CreatedAt.String(),
		"updated_at":              p.UpdatedAt.String(),
		"deleted_at":              p.DeletedAt.String(),
		"last_response_at":        p.LastResponseAt.String(),
		"data_source_response_at": p.DataSourceResponseAt.String(),
		"is_device_extracted":     p.IsDeviceExtracted,
		"person_limit":            p.PersonLimit,
		"person_limit_hash":       p.PersonLimitHash,
		"tag":                     p.Tags,
		"person_fid":              utils.Md5Hash(p.PersonFid()),
		"person_name": func() string {
			if p.PersonInfo == nil {
				return ""
			}
			var names = make([]string, 0)
			for _, person := range p.PersonInfo {
				names = append(names, person.Name)

			}
			return strings.Join(names, ",")
		}(),
		"person_info": func() []*assetses.PersonBase {
			if p.PersonInfo == nil {
				return make([]*assetses.PersonBase, 0)
			}
			for _, person := range p.PersonInfo {
				person.Pgid, _ = pgidservice.GetPgidById(person.Id)
			}
			return p.PersonInfo
		}(),
		"person_department":       p.PersonDepartment,
		"cc_person_name":          p.CcPersonName(),
		"cc_person_fid":           utils.Md5Hash(p.CcPersonFid()),
		"custom_fields":           p.CustomFields,
		"limit_date":              p.LimitDate.String(),
		"processed_time":          p.ProcessedTime.String(),
		"flow_status_change_time": p.FlowStatusChangeTime.String(),
		"flow_status":             p.FlowStatus,
		"repair_department_ids":   p.RepairDepartmentIds,
	}
}

func (p *ComplianceMonitorTaskRecords) StatusDesc() string {
	switch p.Status {
	case 1:
		return "在线"
	default:
		return "离线"
	}
}
func (p *ComplianceMonitorTaskRecords) NetworkTypeDesc() string {
	switch p.NetworkType {
	case 1:
		return "内网"
	case 2:
		return "外网"
	default:
		return "未知"
	}
}

// PersonFid 获取修复负责人Fid
func (p *ComplianceMonitorTaskRecords) PersonFid() string {
	if len(p.PersonInfo) > 0 && p.PersonInfo[0] != nil {
		return p.PersonInfo[0].Fid
	}
	return ""
}

// CcPersonName 获取抄送人姓名
func (p *ComplianceMonitorTaskRecords) CcPersonName() string {
	if p.CcPerson != nil {
		if name, ok := p.CcPerson["name"]; ok {
			return name
		}
	}
	return ""
}

// CcPersonFid 获取抄送人Fid
func (p *ComplianceMonitorTaskRecords) CcPersonFid() string {
	if p.CcPerson != nil {
		if fid, ok := p.CcPerson["fid"]; ok {
			return fid
		}
	}
	return ""
}

type PortEntry struct {
	Port      string `json:"port"`
	RuleInfos string `json:"related_service"`
}

// 互联网高危端口列表
var InternetHighRiskPorts = []PortEntry{
	{"111", "NFS"},
	{"389", "LDAP"},
	{"445", "SMB"},
	{"873", "Rsync"},
	{"1099", "rmi"},
	{"1433", "SQL Server"},
	{"1521", "Oracle"},
	{"2049", "NFS"},
	{"2181", "Zookeeper"},
	{"2222", "DA虚拟主机管理系统登陆"},
	{"2375", "docker"},
	{"2379", "etcd"},
	{"2888", "zookeeper"},
	{"3128", "Squid"},
	{"3306", "mysql"},
	{"3389", "RDP"},
	{"3690", "SVN"},
	{"3888", "Zookeeper"},
	{"4000", "spark historyserver"},
	{"4040", "spark webUI"},
	{"4440", "Spark"},
	{"4848", "GlassFish"},
	{"4899", "Radmin"},
	{"5000", "sybase"},
	{"5000", "DB2"},
	{"5005", "JDWP"},
	{"5432", "PostgreSQL"},
	{"5601", "Kibana"},
	{"5631", "symantecpcanywhere"},
	{"5632", "Pcanywhere"},
	{"5900", "VNC"},
	{"5984", "CouchDB"},
	{"6123", "flink"},
	{"6379", "redis"},
	{"7001", "weblogic"},
	{"7051", "Kudu"},
	{"7077", "Spark"},
	{"7180", "CDH"},
	{"7182", "CDH"},
	{"7848", "Nacos JRAFT"},
	{"8019", "Hadoop"},
	{"8020", "Hadoop"},
	{"8042", "Hadoop Yarn"},
	{"8048", "kafka-eagle"},
	{"8051", "kudu"},
	{"8069", "zabbix"},
	{"8081", "flink"},
	{"8088", "Hadoop"},
	{"8161", "Apache Group ActiveMQ"},
	{"8649", "ganglia"},
	{"9000", "Hadoop HDFS"},
	{"9001", "Supervisor"},
	{"9042", "Cassandra"},
	{"9043", "Websphere"},
	{"9083", "Hive"},
	{"9092", "Kafka"},
	{"9100", "elasticSearch-head"},
	{"9200", "Elasticsearch"},
	{"9300", "Elasticsearch"},
	{"9990", "jboss"},
	{"10000", "hive client"},
	{"11000", "oozie"},
	{"11111", "canal"},
	{"11211", "memcached"},
	{"18080", "Spark"},
	{"19888", "Hadoop"},
	{"19888", "historyserver"},
	{"20880", "Dubbo"},
	{"25000", "impala"},
	{"25010", "impala"},
	{"27017", "MongoDB"},
	{"50000", "DB2"},
	{"50030", "hadoop"},
	{"50070", "hadoop"},
	{"50090", "Hadoop"},
	{"60000", "Hbase"},
	{"60010", "Hbase"},
	{"60030", "Hbase"},
	{"27017", "Mongodb"},
	{"27018", "Mongodb"},
	{"8083", "influxDB"},
	{"8086", "influxDB"},
}

// 内网高危端口列表
var IntranetHighRiskPorts = []PortEntry{
	{"5037", "ADB"},
	{"8009", "AJP"},
	{"61616", "Apache ActiveMQ"},
	{"8161", "Apache ActiveMQ"},
	{"20880", "Apache Dubbo"},
	{"11111", "Canal"},
	{"9042", "Cassandra"},
	{"7180", "CDH"},
	{"7182", "CDH"},
	{"8123", "Clickhouse"},
	{"9000", "Clickhouse"},
	{"9004", "Clickhouse"},
	{"5984", "CouchDB"},
	{"2375", "Docker"},
	{"9200", "ElasticSearch"},
	{"9300", "ElasticSearch"},
	{"9100", "ElasticSearch"},
	{"4369", "Erlang epmd"},
	{"2379", "ETCD"},
	{"6123", "Flink"},
	{"8081", "Flink"},
	{"21", "FTP"},
	{"8649", "Ganglia"},
	{"8080", "GlassFish"},
	{"4848", "GlassFish"},
	{"25000", "Haddop Impala"},
	{"25010", "Haddop Impala"},
	{"8019", "Hadoop"},
	{"8020", "Hadoop"},
	{"8088", "Hadoop"},
	{"8443", "Hadoop Azkaban"},
	{"9000", "Hadoop HDFS"},
	{"19888", "Hadoop Historyserver"},
	{"8888", "Hadoop HUE"},
	{"8051", "Hadoop Kudu"},
	{"7051", "Hadoop Kudu"},
	{"11000", "Hadoop Oozie"},
	{"8042", "Hadoop Yarn"},
	{"443", "Harbor"},
	{"9083", "Hive"},
	{"143", "IMAP"},
	{"9990", "JBoss"},
	{"8080", "JBoss"},
	{"8000", "JDWP"},
	{"5005", "JDWP"},
	{"9092", "Kafka"},
	{"5601", "Kibana"},
	{"8080", "Kubernetes"},
	{"389", "LDAP"},
	{"11211", "Memcached"},
	{"502", "MODBUS"},
	{"27017", "MongoDB"},
	{"1433", "MSSQL"},
	{"3306", "MYSQL"},
	{"8848", "Nacos"},
	{"7848", "Nacos JRAFT"},
	{"443", "Nagios"},
	{"137", "Netbios"},
	{"111", "NFS"},
	{"2049", "NFS"},
	{"1521", "Oracle"},
	{"5632", "Pcanywhere"},
	{"8999", "php-fpm"},
	{"110", "POP3"},
	{"5432", "PostgreSQL"},
	{"4899", "Radmin"},
	{"8080", "Rancher"},
	{"3389", "RDP"},
	{"6379", "Redis"},
	{"1099", "RMI"},
	{"10911", "RocketMQ"},
	{"873", "RSYNC"},
	{"554", "RTSP"},
	{"445", "SMB"},
	{"587", "SMTP"},
	{"161", "SNMP"},
	{"1080", "SOCKS 4/5"},
	{"8080", "Solar"},
	{"4440", "Spark"},
	{"7077", "Spark"},
	{"18080", "Spark"},
	{"4040", "Spark"},
	{"4000", "Spark"},
	{"3128", "Squid"},
	{"22", "SSH"},
	{"9001", "Supervisord"},
	{"3690", "SVN"},
	{"5000", "SYbase"},
	{"5000", "DB2"},
	{"5631", "Symantec PCanywhere"},
	{"23", "Telnet"},
	{"69", "TFTP"},
	{"5900", "VNC"},
	{"7001", "WebLogic"},
	{"9043", "WebSphere"},
	{"8880", "WebSphere"},
	{"8069", "Zabbix"},
	{"2181", "Zookeeper"},
	{"2888", "Zookeeper"},
	{"3888", "Zookeeper"},
}

func (p *ComplianceMonitorTaskRecords) BuildESQuery(rules []QueryRule, ruleType int) *elastic.BoolQuery {
	boolQuery := elastic.NewBoolQuery()

	switch ruleType {
	case compliance_monitor.RuleTypePort:
		// 遍历所有规则
		for _, rule := range rules {
			if rule.RuleName == "port" {
				rule.RuleName = "ports.port"
			}
			if rule.RuleName == "protocol" {
				rule.RuleName = "ports.protocol"
			}

			var query elastic.Query
			if rule.RuleName == "product" {
				rule.RuleName = "rule_infos.product"
				query = elastic.NewNestedQuery("rule_infos",
					elastic.NewBoolQuery().Must(
						elastic.NewTermsQueryFromStrings("rule_infos.product", strings.Split(rule.Content, ",")...),
					),
				)
			} else {
				query = elastic.NewTermsQueryFromStrings(rule.RuleName, strings.Split(rule.Content, ",")...)
			}

			switch rule.Whether {
			case "in":
				if rule.Condition == "or" {
					boolQuery.Should(query)
				} else {
					boolQuery.Must(query)
				}
			case "not in":
				boolQuery.MustNot(query)
			}
		}

	case compliance_monitor.RuleTypeIp:
		// 遍历所有规则
		for _, rule := range rules {
			boolQuery.Must(elastic.NewTermsQueryFromStrings(rule.RuleName, strings.Split(rule.Content, ",")...))

			switch rule.Whether {
			case "on_line":
				boolQuery.Must(elastic.NewTermQuery("status", assetses.StatusOnLine))
			case "off_line":
				boolQuery.Must(elastic.NewTermQuery("status", assetses.StatusOffLine))
			}
		}
	case compliance_monitor.RuleTypeValueExposureWeek:
		query := elastic.NewBoolQuery()
		appendConditions := func(source []PortEntry) {
			for _, s := range source {
				if s.Port == "" || s.RuleInfos == "" {
					continue
				}
				portCond := elastic.NewTermQuery("ports.port", s.Port)
				protocolCond := elastic.NewTermQuery("ports.protocol", s.RuleInfos).CaseInsensitive(true)
				productCond := elastic.NewNestedQuery("rule_infos",
					elastic.NewBoolQuery().Must(
						elastic.NewTermQuery("rule_infos.product", s.RuleInfos).CaseInsensitive(true),
					),
				)

				// 文档中包含 port 且 (protocol 或 product) 就匹配
				protocolOrProduct := elastic.NewBoolQuery().Should(protocolCond, productCond).MinimumNumberShouldMatch(1)
				combined := elastic.NewBoolQuery().Must(portCond, protocolOrProduct)
				query = query.Should(combined)
			}
		}
		appendConditions(InternetHighRiskPorts)
		appendConditions(IntranetHighRiskPorts)
		boolQuery.Must(query.MinimumNumberShouldMatch(1))
	}

	return boolQuery
}

func (p *ComplianceMonitorTaskRecords) NewKeywordQuery(keyword string, boolQuery *elastic.BoolQuery) *elastic.BoolQuery {
	escapedKeyword := fmt.Sprintf("*%s*", utils.Escape(keyword))
	boolQuery.Should(
		elastic.NewWildcardQuery("ip.keyword", escapedKeyword).CaseInsensitive(true),
		elastic.NewNestedQuery("rule_infos",
			elastic.NewWildcardQuery("rule_infos.product", escapedKeyword).CaseInsensitive(true),
		),
		elastic.NewWildcardQuery("business.owner", escapedKeyword).CaseInsensitive(true),
		func() *elastic.TermQuery {
			portNum, err := strconv.ParseInt(keyword, 10, 64)
			if err != nil {
				// keyword 不是数字，不能匹配端口，返回一个不会命中的 TermQuery
				return elastic.NewTermQuery("ports.port", -1)
			}
			return elastic.NewTermQuery("ports.port", portNum)
		}(),
	).MinimumNumberShouldMatch(1)

	return boolQuery
}

// FindAllByQuery 根据条件查询所有数据，滚动查询，性能较高
func (p *ComplianceMonitorTaskRecords) FindAllByQuery(ctx context.Context, query *elastic.BoolQuery) ([]*ComplianceMonitorTaskRecords, int64) {
	scrollService := p.GetClient().Scroll().Index(p.IndexName()).Query(query).Scroll("1m").Size(1000).Sort("id", true)
	defer scrollService.Clear(context.Background())
	results := make([]*ComplianceMonitorTaskRecords, 0)
	var total int64
	for {
		searchResult, err := scrollService.Do(ctx)
		if errors.Is(err, io.EOF) {
			err = nil
			break
		}
		if err != nil || len(searchResult.Hits.Hits) == 0 {
			break
		}
		if len(searchResult.Hits.Hits) == 0 {
			break
		}
		total = searchResult.TotalHits()
		for _, hit := range searchResult.Hits.Hits {
			item, err := convertToAssetsModel(hit)
			if err != nil {
				logs.GetLogger().Warnf(fmt.Sprintf("资产信息解析失败,跳过,数据:%v,错误:%v", hit, err))
				continue
			}
			results = append(results, item)
		}
		// Update scrollService with new ScrollId
		//scrollService = p.GetClient().Scroll().ScrollId(searchResult.ScrollId).Scroll("1m")
	}
	return results, total
}

// convertToAssetsModel ES查询结果转换为Assets结构体
func convertToAssetsModel(hit *elastic.SearchHit) (*ComplianceMonitorTaskRecords, error) {
	item := NewComplianceMonitorTaskRecords()
	err := json.Unmarshal(hit.Source, item)
	if err != nil {
		return nil, err
	}
	return item, nil
}

// FilterAssetDataForListMode 为列表模式过滤资产数据
// 标记模式：保留完整资产数据（前端负责高亮显示）
// 列表模式：根据规则过滤，只保留匹配的数据，考虑端口、协议、组件之间的关联关系
func (p *ComplianceMonitorTaskRecords) FilterAssetDataForListMode(asset *assetses.Assets, rules []QueryRule, ruleType int) *assetses.Assets {
	// 复制基础资产信息
	filtered := &assetses.Assets{
		Id:                    asset.Id,
		Fid:                   asset.Fid,
		FidHash:               asset.FidHash,
		Area:                  asset.Area,
		Ip:                    asset.Ip,
		IpType:                asset.IpType,
		IpSegment:             asset.IpSegment,
		Business:              asset.Business,
		BusinessDepartment:    asset.BusinessDepartment,
		OperDepartment:        asset.OperDepartment,
		OperInfo:              asset.OperInfo,
		OperWithMapping:       asset.OperWithMapping,
		Status:                asset.Status,
		NetworkType:           asset.NetworkType,
		DeletedAt:             asset.DeletedAt,
		CreatedAt:             asset.CreatedAt,
		UpdatedAt:             asset.UpdatedAt,
		LastResponseAt:        asset.LastResponseAt,
		DataSourceResponseAt:  asset.DataSourceResponseAt,
		IsDeviceExtracted:     asset.IsDeviceExtracted,
		PersonLimit:           asset.PersonLimit,
		PersonLimitHash:       asset.PersonLimitHash,
		Tags:                  asset.Tags,
		CustomFields:          asset.CustomFields,
		OperStaffIds:          asset.OperStaffIds,
		BusinessStaffIds:      asset.BusinessStaffIds,
		OperDepartmentIds:     asset.OperDepartmentIds,
		BusinessDepartmentIds: asset.BusinessDepartmentIds,
	}

	// 只处理端口协议组件监测
	if ruleType == compliance_monitor.RuleTypePort {
		// 基于数据实际关联进行过滤
		filtered.Ports, filtered.RuleInfos = p.filterPortsAndComponentsWithAssociation(asset.Ports, asset.RuleInfos, rules)
		filtered.Product = p.extractProductFromRuleInfos(filtered.RuleInfos)
	} else {
		// 其他规则类型：保留所有端口和组件数据
		filtered.Ports = asset.Ports
		filtered.RuleInfos = asset.RuleInfos
		filtered.Product = asset.Product
	}

	return filtered
}

// filterPortsAndComponentsWithAssociation 基于数据实际关联过滤端口和组件
// 实现逻辑：
// 1. 只有当规则中明确包含某类型的监测项时，才对该类型进行过滤
// 2. 没有明确规则的数据类型保留全部数据
// 3. 有明确规则的数据类型严格过滤，只保留匹配的
func (p *ComplianceMonitorTaskRecords) filterPortsAndComponentsWithAssociation(
	ports []*assetses.PortInfo,
	ruleInfos []*assetses.RuleInfo,
	rules []QueryRule,
) ([]*assetses.PortInfo, []*assetses.RuleInfo) {

	if len(rules) == 0 {
		return ports, ruleInfos
	}

	// 检查规则中包含哪些类型的监测项
	hasPortRule := false
	hasProtocolRule := false
	hasProductRule := false

	for _, rule := range rules {
		if rule.Whether != "in" {
			continue
		}

		switch rule.RuleName {
		case "port", "ports.port":
			hasPortRule = true
		case "protocol", "ports.protocol":
			hasProtocolRule = true
		case "product", "rule_infos.product":
			hasProductRule = true
		}
	}

	// 根据规则类型决定过滤策略
	var resultPorts []*assetses.PortInfo
	var resultRuleInfos []*assetses.RuleInfo

	// 处理端口和协议过滤
	if hasPortRule || hasProtocolRule {
		// 有端口或协议规则，需要过滤端口数据
		matchedPorts := make(map[string]*assetses.PortInfo)

		for _, rule := range rules {
			if rule.Whether != "in" {
				continue
			}

			targetValues := strings.Split(rule.Content, ",")

			switch rule.RuleName {
			case "port", "ports.port":
				p.processPortRule(ports, targetValues, matchedPorts)
			case "protocol", "ports.protocol":
				p.processProtocolRule(ports, targetValues, matchedPorts)
			}
		}

		// 转换为切片
		for _, port := range matchedPorts {
			resultPorts = append(resultPorts, port)
		}
	} else {
		// 没有端口或协议规则，保留所有端口
		resultPorts = ports
	}

	// 处理组件过滤
	if hasProductRule {
		// 有组件规则，需要过滤组件数据
		matchedRuleInfos := make(map[string]*assetses.RuleInfo)

		for _, rule := range rules {
			if rule.Whether != "in" {
				continue
			}

			if rule.RuleName == "product" || rule.RuleName == "rule_infos.product" {
				targetValues := strings.Split(rule.Content, ",")
				p.processProductRule(ruleInfos, targetValues, matchedRuleInfos)
			}
		}

		// 转换为切片
		for _, ruleInfo := range matchedRuleInfos {
			resultRuleInfos = append(resultRuleInfos, ruleInfo)
		}
	} else {
		// 没有组件规则，保留所有组件
		resultRuleInfos = ruleInfos
	}

	return resultPorts, resultRuleInfos
}

// processPortRule 处理端口规则 - 只保留匹配端口及其实际协议
func (p *ComplianceMonitorTaskRecords) processPortRule(
	ports []*assetses.PortInfo,
	targetPorts []string,
	matchedPorts map[string]*assetses.PortInfo,
) {
	for _, targetPort := range targetPorts {
		targetPort = strings.TrimSpace(targetPort)

		// 找到匹配的端口，保留其实际协议
		for _, port := range ports {
			if fmt.Sprintf("%d", port.Port) == targetPort {
				key := fmt.Sprintf("%d_%s", port.Port, port.Protocol)
				matchedPorts[key] = port
			}
		}
	}
}

// processProtocolRule 处理协议规则 - 只保留匹配协议及其实际端口
func (p *ComplianceMonitorTaskRecords) processProtocolRule(
	ports []*assetses.PortInfo,
	targetProtocols []string,
	matchedPorts map[string]*assetses.PortInfo,
) {
	for _, targetProtocol := range targetProtocols {
		targetProtocol = strings.TrimSpace(targetProtocol)

		// 找到匹配的协议，保留其实际端口
		for _, port := range ports {
			if strings.EqualFold(port.Protocol, targetProtocol) {
				key := fmt.Sprintf("%d_%s", port.Port, port.Protocol)
				matchedPorts[key] = port
			}
		}
	}
}

// processProductRule 处理组件规则 - 只保留匹配的组件
func (p *ComplianceMonitorTaskRecords) processProductRule(
	ruleInfos []*assetses.RuleInfo,
	targetProducts []string,
	matchedRuleInfos map[string]*assetses.RuleInfo,
) {
	for _, targetProduct := range targetProducts {
		targetProduct = strings.TrimSpace(targetProduct)

		// 找到匹配的组件
		for _, ruleInfo := range ruleInfos {
			if ruleInfo != nil && strings.Contains(strings.ToLower(ruleInfo.Product), strings.ToLower(targetProduct)) {
				matchedRuleInfos[ruleInfo.Product] = ruleInfo
			}
		}
	}
}

// extractProductFromRuleInfos 从RuleInfos中提取产品名称
func (p *ComplianceMonitorTaskRecords) extractProductFromRuleInfos(ruleInfos []*assetses.RuleInfo) []string {
	var products []string
	for _, ruleInfo := range ruleInfos {
		if ruleInfo != nil && ruleInfo.Product != "" {
			products = append(products, ruleInfo.Product)
		}
	}
	return utils.ListDistinct(products)
}
