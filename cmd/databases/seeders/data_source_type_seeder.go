package seeders

import (
	"fobrain/models/mysql/data_source"
)

func DataSourceTypeSeeder() {

	sourceTypes := []data_source.SourceType{
		{Name: "文件导入", Type: "file_import"},
		{Name: "资产扫描", Type: "asset"},
		{Name: "CMDB", Type: "cmdb"},
		{Name: "主机安全", Type: "hids"},
		{Name: "漏洞扫描", Type: "vulnerability"},
		{Name: "云平台", Type: "cloud"},
		{Name: "堡垒机", Type: "jump_server"},
		{Name: "防火墙", Type: "firewall"},
		{Name: "公有云", Type: "public_cloud"},
		{Name: "负载均衡/安全策略管理平台", Type: "load"},
		{Name: "LDAP", Type: "ldap"},
		{Name: "通讯类", Type: "communication"},
		{Name: "流量分析", Type: "flow_analysis"},
		{Name: "容器编排", Type: "container_orchestration"},
		{Name: "其他", Type: "others"},
	}
	for _, sourceType := range sourceTypes {
		sourceTypeItem := data_source.SourceType{}
		err := data_source.NewSourceTypeModel().Where("type = ?", sourceType.Type).First(&sourceTypeItem).Error
		if err != nil {
			err = data_source.NewSourceTypeModel().Create(&sourceType).Error
			if err != nil {
				continue
			}
		} else {
			sourceType.Id = sourceTypeItem.Id
			err = data_source.NewSourceTypeModel().Update(sourceType)
			if err != nil {
				continue
			}
		}
	}
}
