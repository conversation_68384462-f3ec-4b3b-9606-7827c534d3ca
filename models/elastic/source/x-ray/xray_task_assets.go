package xray

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"fobrain/pkg/utils"

	"github.com/olivere/elastic/v7"
)

type (
	TaskAssets struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`         // NODE+区域+IP+Port 作为唯一值去更新，用作数据对比
		CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
		UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
	}
)

// IndexName 索引名
func (t *TaskAssets) IndexName() string {
	return "xray_task_assets"
}

func (t *TaskAssets) String() string {
	if t == nil {
		return ""
	}
	return ""
}

func NewTaskAssetsModel() *TaskAssets {
	return &TaskAssets{}
}

func (asset *TaskAssets) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("ip").
		Field("information.keyword").
		Field("mac_address.app_proto.keyword")

	return field
}

func (asset *TaskAssets) GetAssetListFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ip", "all_key": "ip"},
		{"name": "操作系统", "key": "information", "all_key": "information"},
		{"name": "设备类型", "key": "device_type", "all_key": "device_type.*.name"},
		{"name": "mac 地址", "key": "mac_address", "all_key": "mac_address"},
		{"name": "端口", "key": "port", "all_key": "product.*.port"},
		{"name": "协议", "key": "protocol", "all_key": "product.*.service_name"},
		{"name": "组件信息", "key": "application", "all_key": "product.*.application"},
	}
}

func (asset *TaskAssets) GetAssetListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ip", "all_key": "ip"},
		{"name": "操作系统", "key": "information", "all_key": "information"},
		{"name": "设备类型", "key": "device_type", "all_key": "device_type.*.name"},
		{"name": "mac 地址", "key": "mac_address", "all_key": "mac_address"},
		{"name": "端口", "key": "port", "all_key": "product.*.port"},
		{"name": "协议", "key": "protocol", "all_key": "product.*.service_name"},
		{"name": "组件信息", "key": "application", "all_key": "product.*.application"},
	}
}
