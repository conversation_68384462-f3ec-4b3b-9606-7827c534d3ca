package main

import (
	"context"
	"errors"
	"fmt"
	"fobrain/fobrain/app/controller/system_configs/networks"
	"fobrain/fobrain/app/repository/threat_history"
	"fobrain/fobrain/app/services/backup"
	"fobrain/fobrain/app/services/compliance_center"
	"fobrain/pkg/scheduler"
	"fobrain/services/strategy_business"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	license2 "fobrain/fobrain/app/repository/settings/license"
	"fobrain/fobrain/app/services/system_configs/cascade_api_token"
	"fobrain/fobrain/app/services/task"
	redis2 "fobrain/initialize/redis"
	redis_helper "fobrain/models/redis"
	"fobrain/webhook"
	"fobrain/webhook/dao"

	"fobrain/fobrain/app/controller/auth"
	"fobrain/fobrain/app/crontab"
	"fobrain/fobrain/common/license"
	"fobrain/fobrain/config"
	"fobrain/fobrain/logs"
	"fobrain/initialize/engine"
	"fobrain/pkg/cfg"

	"go-micro.dev/v4/logger"
	"go-micro.dev/v4/web"
)

var Version = "No Version Provided"

var BuildTime = " No Build Time Provided"

const (
	// ServiceName 服务名称
	ServiceName = "fobrain.api.gateway"
	// ServiceVersion 服务版本
	ServiceVersion = "latest"
)

func main() {
	fmt.Println("Version:", Version)
	auth.VersionStr = Version
	auth.BuildTime = BuildTime

	// 统一设置时区
	location, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		panic(err)
	}
	time.Local = location
	// 设置编译版本
	config.SetVersion(Version)
	// 设置编译时间
	config.SetBuildTime(BuildTime)

	cfg.InitLoadCfg()
	// 初始化备份服务
	initBackupService()
	// 初始化定时任务
	crontab.Init()
	//启动定时任务服务
	startCrontabService()
	//启动更新资产上限标记监听
	schedulerUpdateLicenseAssetLimitReached()
	// 业务系统批量写入
	strategy_business.NewStrategy().WriteResult()
	// foeye批量复测brpop
	threat_history.StartFoeyeBatchRetestConsumer()
	//清理执行中任务标记为失败
	task.CleanRunningTask()
	// 更新docker容器主机配置
	_ = networks.UpdateHostsFile("")
	// 启动合规风险闭环流转状态更新消费队列
	compliance_center.NewComplianceRiskUpdateStatus().Consumer()
	// 启动API服务
	srv := web.NewService(
		web.Address(cfg.LoadCommon().Listen),
		web.Name(ServiceName),
		web.Version(ServiceVersion),
		web.Handler(engine.GetEngine()),
		web.Logger(logger.DefaultLogger),
	)

	redisConf := cfg.LoadRedis()
	mysqlConf := cfg.LoadMysql()
	w := webhook.NewWebhook(&webhook.Config{
		Redis: webhook.RedisConfig{
			Address:            redisConf.Address,
			Port:               redisConf.Port,
			Password:           redisConf.Password,
			Database:           redisConf.Database,
			MergeRuleCacheTime: redisConf.MergeRuleCacheTime,
		},
		Mysql: dao.MysqlConfig{
			Address:  mysqlConf.Address,
			Port:     mysqlConf.Port,
			UserName: mysqlConf.UserName,
			Password: mysqlConf.Password,
			Database: mysqlConf.Database,
			CharSet:  mysqlConf.CharSet,
			LogLevel: mysqlConf.LogLevel,
			SlowTime: mysqlConf.SlowTime,
		},
	})

	w.Start()

	// 检查级联Token
	cascade_api_token.CheckCascadeApiToken()

	if err := srv.Init(); err != nil {
		panic(err)
	}

	// 设置优雅关闭
	setupGracefulShutdown(w, srv)

	if err := srv.Run(); err != nil {
		panic(err)
	}

	logger.Info("[Webhook] Shutdown webhook ...")
	w.Stop()
	logger.Info("[Webhook] Shutdown webhook ...")

}

func exitMonitor(srv *http.Server) {
	// 等待中断信号以优雅地关闭服务器（设置 5 秒的超时时间）
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt)
	<-quit
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	fmt.Println("")
	logs.GetLogger().WithOutCaller().Info("Shutdown Server ...")
	if err := srv.Shutdown(ctx); err != nil {
		logs.GetLogger().WithOutCaller().Error("Server Shutdown:", err)
	}
	license.GetLicense().ClearTmpBinary()
	logs.GetLogger().WithOutCaller().Info("Server exited")
}

// setupGracefulShutdown 设置优雅关闭处理
func setupGracefulShutdown(w *webhook.Webhook, srv web.Service) {
	// 创建信号通道
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动信号监听协程
	go func() {
		sig := <-sigChan
		logger.Infof("收到信号 %v，开始优雅关闭...", sig)

		// 1. 停止 micro 服务
		logger.Info("正在停止 micro 服务...")
		if err := srv.Stop(); err != nil {
			logger.Errorf("停止 micro 服务失败: %v", err)
		} else {
			logger.Info("micro 服务已停止")
		}

		// 2. 停止 webhook 服务
		logger.Info("正在关闭 webhook 服务...")
		w.Stop()
		logger.Info("webhook 服务已关闭")

		// 3. 关闭备份服务
		logger.Info("正在关闭备份服务...")
		backup.ShutdownService()
		logger.Info("备份服务已关闭")

		// 4. 停止定时任务调度器
		logger.Info("正在停止定时任务调度器...")
		cronIns := crontab.GetCronIns()
		if cronIns != nil && cronIns.Scheduler != nil {
			cronIns.Scheduler.Stop()
		}
		logger.Info("定时任务调度器已停止")

		// 5. 关闭任务服务
		logger.Info("正在关闭任务服务...")
		task.Shutdown()
		logger.Info("任务服务已关闭")

		// 6. 停止所有调度器任务
		logger.Info("正在停止调度器任务...")
		// scheduler 包只有 Stop 方法停止单个任务，这里我们需要停止所有任务
		// 由于没有全局停止方法，我们依赖程序退出时的自然清理
		logger.Info("调度器任务将随程序退出自然清理")

		// 7. 关闭数据库连接（如果有关闭方法）
		logger.Info("正在关闭数据库连接...")
		// engine 包没有 Close 方法，依赖程序退出时的自然清理
		logger.Info("数据库连接将随程序退出自然清理")

		// 8. 关闭 Redis 连接
		logger.Info("正在关闭 Redis 连接...")
		redis2.Close()
		logger.Info("Redis 连接已关闭")

		logger.Info("所有服务已优雅关闭")
	}()
}

// schedulerUpdateLicenseAssetLimitReached 定时更新资产是否达到上限标记
func schedulerUpdateLicenseAssetLimitReached() {
	redisClient := redis2.GetRedisClient()
	key := redis_helper.GetIsLicenseAssetLimitReachedKey()
	redisClient.Set(context.Background(), key, "false", 0)
	key = redis_helper.GetIsLicenseProcessAssetLimitReachedKey()
	redisClient.Set(context.Background(), key, "false", 0)
	//更新资产上限标记
	scheduler.Start("update_license_asset_limit", 15*time.Second, true, func() {
		err := license2.UpdateAssetWriteState()
		if err != nil {
			logs.GetLogger().WithOutCaller().Errorf("license check limit Error: %v", err)
		}
	})
}

// startCrontabService 启动定时任务
func startCrontabService() {
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logs.GetLogger().WithOutCaller().Errorf("Server Error: %v", err)
				os.Exit(1)
			}
		}()
		if err := crontab.GetCronIns().Start(); err != nil {
			panic(err)
		}
	}()
}

// startApiServer 启动API服务
func startApiServer() *http.Server {
	srv := &http.Server{
		Addr:    config.Get().Listen,
		Handler: engine.GetEngine(),
	}
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logs.GetLogger().WithOutCaller().Errorf("Server Error: %v", err)
				os.Exit(1)
			}
		}()
		if err := srv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			panic(err)
		}
	}()
	return srv
}

// initBackupService 初始化备份服务
func initBackupService() {
	if err := backup.InitService(); err != nil {
		logs.GetLogger().Errorf("备份服务初始化失败: %v", err)
		// 备份服务初始化失败不影响系统启动
	}
}
