package helper

import (
	"fobrain/initialize/mysql"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/personnel_departments"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

func TestGetDepartment(t *testing.T) {
	patch := gomonkey.NewPatches()
	defer patch.Reset()

	department1 := &personnel_departments.PersonnelDepartments{
		BaseModel: mysql.BaseModel{
			Id: 1,
		},
		FullName: "公司",
		ParentId: 0,
	}
	department2 := &personnel_departments.PersonnelDepartments{
		BaseModel: mysql.BaseModel{
			Id: 2,
		},
		FullName: "公司/行政中心",
		ParentId: 1,
	}

	patch.ApplyFunc(GetDepartmentById, func(id uint64) *personnel_departments.PersonnelDepartments {
		if id == 0 {
			return nil
		}
		if id == 1 {
			return department1
		}
		if id == 2 {
			return department2
		}
		return nil
	})
	// 测试空部门列表
	result := GetDepartment(0, "test", "1", "", "")
	if result != nil {
		t.Errorf("Expected nil for empty departments list, got %v", result)
	}

	// 测试存在匹配部门的情况
	result = GetDepartment(1, "test", "1", "", "")
	if result == nil {
		t.Errorf("Expected department, got nil")
	} else if result.Id != 1 || result.Name != "公司" {
		t.Errorf("Expected department with Id 1 and Name '公司', got %v", result)
	}

	// 测试存在子部门的情况
	result = GetDepartment(2, "test", "1", "", "")
	if result == nil {
		t.Errorf("Expected department, got nil")
	} else if result.Id != 2 || result.Name != "公司/行政中心" {
		t.Errorf("Expected department with Id 2 and Name '公司/行政中心', got %v", result)
	} else if len(result.Parents) != 1 {
		t.Errorf("Expected 1 parent department, got %d", len(result.Parents))
	} else if result.Parents[0].Id != 1 || result.Parents[0].Name != "公司" {
		t.Errorf("Expected parent department with Id 1 and Name '公司', got %v", result.Parents[0])
	}
}

func TestGetPersonDepartmentInfo(t *testing.T) {
	patch := gomonkey.NewPatches()
	defer patch.Reset()

	// Mock GetDepartment function
	department1 := &assets.DepartmentBase{
		Id:   1,
		Name: "部门1",
	}
	department2 := &assets.DepartmentBase{
		Id:   2,
		Name: "部门2",
	}

	patch.ApplyFunc(GetDepartment, func(departmentId uint64, personName string, personId string, businessName string, businessId string) *assets.DepartmentBase {
		if departmentId == 1 {
			return department1
		} else if departmentId == 2 {
			return department2
		}
		return nil
	})

	// Test case 1: Staff with valid department IDs
	staff1 := &staff.Staff{
		Id:             "1",
		Name:           "张三",
		DepartmentsIds: []string{"1", "2", "invalid"},
	}

	result := getPersonDepartmentInfo(staff1)

	// Should return two departments (the valid ones)
	if len(result) != 2 {
		t.Errorf("Expected 2 departments, got %d", len(result))
	}

	// Check if departments are correctly returned
	assert.Equal(t, department1, result[0])
	assert.Equal(t, department2, result[1])

	// Test case 2: Staff with no valid department IDs
	staff2 := &staff.Staff{
		Id:             "2",
		Name:           "李四",
		DepartmentsIds: []string{"invalid", "not_a_number"},
	}

	result = getPersonDepartmentInfo(staff2)

	// Should return empty slice
	assert.Empty(t, result)

	// Test case 3: Staff with nil department IDs
	staff3 := &staff.Staff{
		Id:   "3",
		Name: "王五",
	}

	result = getPersonDepartmentInfo(staff3)

	// Should return empty slice
	assert.Empty(t, result)
}

func TestAppendPersonToPersonBase(t *testing.T) {
	// Test case 1: Append person info with existing person
	person := &staff.Staff{
		Id:   "1",
		Fid:  "fid1",
		Name: "张三",
	}

	findBy := &assets.PersonWithMapping{
		SourceId:     1,
		NodeId:       2,
		SourceValue:  "张三",
		MappingField: "name",
	}

	pb := &assets.PersonBase{}

	result := AppendPersonToPersonBase(pb, person, findBy, 1)

	// Check person info is correctly appended
	assert.Equal(t, "1", result.Id)
	assert.Equal(t, "fid1", result.Fid)
	assert.Equal(t, "张三", result.Name)

	// Check find info is correctly appended
	assert.Equal(t, 1, len(result.FindInfo))
	assert.Equal(t, uint64(1), result.FindInfo[0].SourceId)
	assert.Equal(t, uint64(2), result.FindInfo[0].NodeId)
	assert.Equal(t, "张三", result.FindInfo[0].SourceValue)
	assert.Equal(t, "name", result.FindInfo[0].MappingField)
	assert.Equal(t, 1, result.FindInfo[0].FindCount)

	// Test case 2: Append person info with nil person
	pb = &assets.PersonBase{}

	result = AppendPersonToPersonBase(pb, nil, findBy, 2)

	// Check person info is empty
	assert.Equal(t, "", result.Id)
	assert.Equal(t, "", result.Fid)
	assert.Equal(t, "", result.Name)

	// Check find info is correctly appended
	assert.Equal(t, 1, len(result.FindInfo))
	assert.Equal(t, uint64(1), result.FindInfo[0].SourceId)
	assert.Equal(t, uint64(2), result.FindInfo[0].NodeId)
	assert.Equal(t, "张三", result.FindInfo[0].SourceValue)
	assert.Equal(t, "name", result.FindInfo[0].MappingField)
	assert.Equal(t, 2, result.FindInfo[0].FindCount)

	// Test case 3: Append person info with nil findBy
	pb = &assets.PersonBase{}

	result = AppendPersonToPersonBase(pb, person, nil, 0)

	// Check person info is correctly appended
	assert.Equal(t, "1", result.Id)
	assert.Equal(t, "fid1", result.Fid)
	assert.Equal(t, "张三", result.Name)

	// Check find info is empty
	assert.Equal(t, 0, len(result.FindInfo))

	// Test case 4: Multiple appends to the same PersonBase
	pb = &assets.PersonBase{}

	// First append
	pb = AppendPersonToPersonBase(pb, person, findBy, 1)

	// Second append with different findBy
	findBy2 := &assets.PersonWithMapping{
		SourceId:     3,
		NodeId:       4,
		SourceValue:  "<EMAIL>",
		MappingField: "email",
	}

	result = AppendPersonToPersonBase(pb, person, findBy2, 1)

	// Check person info is still correct
	assert.Equal(t, "1", result.Id)
	assert.Equal(t, "fid1", result.Fid)
	assert.Equal(t, "张三", result.Name)

	// Check both find infos are present
	assert.Equal(t, 2, len(result.FindInfo))
	assert.Equal(t, uint64(1), result.FindInfo[0].SourceId)
	assert.Equal(t, uint64(3), result.FindInfo[1].SourceId)
}

func TestAppendDepartmentToDepartmentBase(t *testing.T) {
	// Test case 1: Append departments to nil PersonBase
	departments := []*assets.DepartmentBase{
		{
			Id:   1,
			Name: "部门1",
		},
	}

	var pb *assets.PersonBase

	result := AppendDepartmentToDepartmentBase(pb, departments)

	// Check a new PersonBase is created
	assert.NotNil(t, result)

	// Check department is correctly appended
	assert.Equal(t, 1, len(result.Department))
	assert.Equal(t, uint64(1), result.Department[0].Id)
	assert.Equal(t, "部门1", result.Department[0].Name)

	// Test case 2: Append nil departments
	pb = &assets.PersonBase{}

	result = AppendDepartmentToDepartmentBase(pb, nil)

	// Check PersonBase is returned unchanged
	assert.Equal(t, pb, result)
	assert.Empty(t, result.Department)

	// Test case 3: Append departments to existing PersonBase
	pb = &assets.PersonBase{}
	departments = []*assets.DepartmentBase{
		{
			Id:   1,
			Name: "部门1",
		},
		{
			Id:   2,
			Name: "部门2",
		},
	}

	result = AppendDepartmentToDepartmentBase(pb, departments)

	// Check departments are correctly appended
	assert.Equal(t, 2, len(result.Department))
	assert.Equal(t, uint64(1), result.Department[0].Id)
	assert.Equal(t, "部门1", result.Department[0].Name)
	assert.Equal(t, uint64(2), result.Department[1].Id)
	assert.Equal(t, "部门2", result.Department[1].Name)

	// Test case 4: Append duplicate departments
	departments = []*assets.DepartmentBase{
		{
			Id:   1,          // Duplicate ID
			Name: "部门1 - 重复", // Different name
		},
		{
			Id:   3,
			Name: "部门3",
		},
	}

	result = AppendDepartmentToDepartmentBase(result, departments)

	// Check only new department is appended (no duplicates by ID)
	assert.Equal(t, 3, len(result.Department))

	// Check the original department with ID 1 is preserved
	found := false
	for _, dept := range result.Department {
		if dept.Id == 1 && dept.Name == "部门1" {
			found = true
			break
		}
	}
	assert.True(t, found, "Original department with ID 1 should be preserved")

	// Check new department is appended
	found = false
	for _, dept := range result.Department {
		if dept.Id == 3 && dept.Name == "部门3" {
			found = true
			break
		}
	}
	assert.True(t, found, "New department with ID 3 should be appended")
}
