package elastic

import (
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"strings"

	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/spf13/cast"

	"github.com/olivere/elastic/v7"
	"go-micro.dev/v4/logger"
)

// BusinessDepartmentQuery 返回查询部门的query，输入是 a/b/c 这样的格式
func BusinessDepartmentQuery(name, field string) elastic.Query {
	index := strings.Index(field, ".")
	field = field[:index]
	return elastic.NewBoolQuery().Should(elastic.NewNestedQuery(
		field+".parents",
		elastic.NewBoolQuery().Must(elastic.NewTermQuery(field+".parents.name.keyword", name)),
	), elastic.NewNestedQuery(
		field,
		elastic.NewBoolQuery().Must(elastic.NewTermQuery(field+".name.keyword", name)),
	))
}

// BusinessDepartmentTermsQuery 返回查询部门的query，输入是 a/b/c 这样的格式
func BusinessDepartmentTermsQuery(names []interface{}, field string) elastic.Query {
	// 将 interface{} 切片转换为 string 切片
	strSlice := make([]string, len(names))
	for i, v := range names {
		strSlice[i] = fmt.Sprint(v)
	}
	index := strings.Index(field, ".")
	field = field[:index]
	return elastic.NewBoolQuery().Should(elastic.NewNestedQuery(
		field+".parents",
		elastic.NewBoolQuery().Must(
			elastic.NewTermsQueryFromStrings(field+".parents.name.keyword", strSlice...),
		),
	), elastic.NewNestedQuery(
		field,
		elastic.NewBoolQuery().Must(
			elastic.NewTermsQueryFromStrings(field+".name.keyword", strSlice...),
		),
	))
}

func BuildBoolQuery(field, operationTypeString, logicalConnective string, params interface{}, boolQuery *elastic.BoolQuery) *elastic.BoolQuery {
	var p []interface{}
	switch v := params.(type) {
	case []string:
		p = convertToInterfaceSlice(v)
	case []int:
		p = convertToInterfaceSlice(v)
	case []uint64:
		p = convertToInterfaceSlice(v)
	case []interface{}:
		p = convertToInterfaceSlice(v)
	default:
		return boolQuery
	}
	var query elastic.Query
	var p0 string
	if p != nil && len(p) > 0 {
		p0, _ = p[0].(string)
	}
	var p1 string
	if p != nil && len(p) > 1 {
		p1, _ = p[1].(string)
	}
	if field == "limit_date" {
		if p0 != "" {
			// YYYY-MM-DD 转化为 YYYY-MM-DD HH:MM:SS
			p0 = p0 + " 00:00:00"
		}
		if p1 != "" {
			// YYYY-MM-DD 转化为 YYYY-MM-DD HH:MM:SS
			p1 = p1 + " 23:59:59"
		}
	}

	switch operationTypeString {
	case "in":
		switch field {
		case "department.name.keyword", "business.system", "person_base.name", "oper_info.id", "business.person_base.id", "opers.id", "opers.oper_info.id":
			// nestedBoolQuery := elastic.NewBoolQuery()
			// nestedBoolQuery = nestedBoolQuery.Must(
			// 	elastic.NewTermsQuery(field, p...),
			// )
			// // 将 BoolQuery 放入 NestedQuery 中
			// query = elastic.NewNestedQuery(
			// 	strings.Split(field, ".")[0],
			// 	nestedBoolQuery,
			// )
			query = BuildNestedTermsQuery(field, p)
		case "business_department.name.keyword", "oper_department.name.keyword", "department_base.name.keyword", "department_base.name":
			query = BusinessDepartmentTermsQuery(p, field)
		case "vulType":
			query = elastic.NewWildcardQuery(field, fmt.Sprintf("*%s*", p0))
		case "limit_date":
			// 日期范围查询，p0为开始日期，p1为结束日期
			if p0 != "" && p1 != "" {
				// 大于等于开始日期，小于等于结束日期
				query = elastic.NewRangeQuery(field).Gte(p0).Lte(p1)
			} else if p0 != "" {
				// 大于等于开始日期
				query = elastic.NewRangeQuery(field).Gte(p0)
			} else if p1 != "" {
				// 小于等于结束日期
				query = elastic.NewRangeQuery(field).Lte(p1)
			}
		default:
			if strings.Contains(field, "rule_infos.") {
				nestedBoolQuery := elastic.NewBoolQuery()
				nestedBoolQuery = nestedBoolQuery.Must(
					elastic.NewTermsQuery(field, p...),
				)
				// 将 BoolQuery 放入 NestedQuery 中
				query = elastic.NewNestedQuery(
					strings.Split(field, ".")[0],
					nestedBoolQuery,
				)
			} else {
				query = elastic.NewTermsQuery(field, p...)
			}
		}
	case "not_in":
		switch field {
		case "department.name.keyword", "business.system", "person_base.name", "oper_info.id", "business.person_base.id", "opers.id", "opers.oper_info.id":
			query = BuildNestedTermsQuery(field, p)
		case "business_department.name.keyword", "oper_department.name.keyword", "department_base.name.keyword", "department_base.name":
			query = BusinessDepartmentTermsQuery(p, field)
		case "vulType":
			query = elastic.NewWildcardQuery(field, fmt.Sprintf("*%s*", p0))
		case "limit_date":
			// 日期范围查询，p0为开始日期，p1为结束日期
			if p0 != "" && p1 != "" {
				// 大于等于开始日期，小于等于结束日期
				query = elastic.NewRangeQuery(field).Gte(p0).Lte(p1)
			} else if p0 != "" {
				// 大于等于开始日期
				query = elastic.NewRangeQuery(field).Gte(p0)
			} else if p1 != "" {
				// 小于等于结束日期
				query = elastic.NewRangeQuery(field).Lte(p1)
			}
		default:
			if strings.Contains(field, "rule_infos.") {
				query = BuildNestedTermsQuery(field, p)
			} else {
				query = elastic.NewTermsQuery(field, p...)
			}
		}
	case "==":
		switch field {
		case "department.name.keyword", "business.system", "person_base.name", "oper_info.id", "business.person_base.id", "opers.id", "opers.oper_info.id":
			// 使用BuildNestedTermQuery处理嵌套字段的精确匹配查询
			query = BuildNestedTermQuery(field, p[0])
		case "business_department.name.keyword", "oper_department.name.keyword", "department_base.name.keyword", "department_base.name":
			query = BusinessDepartmentQuery(p0, field)
		case "poc_num":
			bugNum, _ := p[0].(float64)
			if bugNum == 1 {
				query = elastic.NewRangeQuery("poc_num").Gt(0)
			} else {
				query = elastic.NewBoolQuery().Should(
					elastic.NewRangeQuery("poc_num").Lte(0),
					elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("poc_num")),
				)
			}
		case "all_source_ids":
			q1 := elastic.NewBoolQuery()
			scriptQuery := elastic.NewScriptQuery(
				elastic.NewScript("doc['all_source_ids'].size() == 1").Lang("painless"),
			)
			q1.Must(elastic.NewTermQuery("all_source_ids", p[0]), scriptQuery)
			q2 := elastic.NewBoolQuery()
			scriptQuery2 := elastic.NewScriptQuery(
				elastic.NewScript("doc['all_source_ids'].size() == 2").Lang("painless"),
			)
			q2.Must(elastic.NewTermQuery("all_source_ids", p[0]), elastic.NewTermQuery("all_source_ids", 0), scriptQuery2)
			q := elastic.NewBoolQuery()
			q.Should(q1, q2)
			query = q
		case "source_count":
			script := elastic.NewScript(`
				def filtered = [];
				if (doc.containsKey("all_source_ids") && doc["all_source_ids"].size() > 0) {
					for (def item : doc["all_source_ids"]) {
						if (item != params.exclude) {
							filtered.add(item);
						}
					}
				}
				if (params.operationType.equals("=")) {
					return filtered.size() == params.size;
				} else if (params.operationType.equals(">")) {
					return filtered.size() > params.size;
				} else if (params.operationType.equals("<")) {
					return filtered.size() < params.size;
				} else {
					return false;
				}
				`)
			if p[0].(float64) == 1 {
				query = elastic.NewScriptQuery(script.Params(map[string]interface{}{
					"exclude":       "0",
					"operationType": "=",
					"size":          1,
				}))
			} else {
				query = elastic.NewScriptQuery(script.Params(map[string]interface{}{
					"exclude":       "0",
					"operationType": ">",
					"size":          1,
				}))
			}
		case "business.business_trusted_state":
			query = BuildNestedTermQuery(field, p[0])
		default:
			// 检查是否为嵌套字段，如果是则使用BuildNestedTermQuery field的数据可能是个数组，需要加入检测
			if strings.Contains(field, "rule_infos.") || detectNestedPath(field) != "" {
				query = BuildNestedTermQuery(field, p[0])
			} else {
				query = elastic.NewTermQuery(field, p[0])
			}
		}
	case "!==":
		switch field {
		case "business.system", "person_base.name", "oper_info.id", "business.person_base.id", "opers.id", "opers.oper_info.id":
			// 使用BuildNestedTermQuery处理嵌套字段的精确匹配查询
			query = BuildNestedTermQuery(field, p[0])
		case "business_department.name.keyword", "oper_department.name.keyword", "department_base.name.keyword", "department_base.name":
			query = BusinessDepartmentQuery(p0, field)
		case "all_source_ids":
			// 查询all_source_ids字段大小为1的记录
			q1 := elastic.NewBoolQuery()
			scriptQuery := elastic.NewScriptQuery(
				elastic.NewScript("doc['all_source_ids'].size() == 1").Lang("painless"),
			)
			q1.Must(elastic.NewTermsQuery("all_source_ids", p...), scriptQuery)
			// 查询all_source_ids字段大小为2的记录,当前源+人工校准的情况
			q2 := elastic.NewBoolQuery()
			scriptQuery2 := elastic.NewScriptQuery(
				elastic.NewScript("doc['all_source_ids'].size() == 2").Lang("painless"),
			)
			q2.Must(elastic.NewTermsQuery("all_source_ids", p...), elastic.NewTermQuery("all_source_ids", 0), scriptQuery2)
			// 两个条件都不能满足的记录
			q := elastic.NewBoolQuery()
			q.Should(q1, q2)
			boolQuery = boolQuery.MustNot(q)
			// 这里直接返回，因为已经处理了boolQuery
			return boolQuery
		default:
			// 检查是否为嵌套字段，如果是则使用BuildNestedTermQuery
			if strings.Contains(field, "rule_infos.") {
				query = BuildNestedTermQuery(field, p[0])
			} else if detectNestedPath(field) != "" {
				// 处理其他嵌套字段
				query = BuildNestedTermQuery(field, p[0])
			} else {
				query = elastic.NewTermQuery(field, p[0])
			}
		}

	case "null":
		switch field {
		case "business.system", "person_base.name", "business_department.name.keyword", "oper_department.name.keyword", "department_base.name.keyword", "department_base.name", "opers.oper_info.id", "opers.id", "business.person_base.id", "oper_info.id", "rule_infos.product", "rule_infos.first_tag", "rule_infos.second_tag":
			// 获取嵌套字段路径
			nestedPath := detectNestedPath(field)

			// 查询nestedPath不存在的文档，
			fieldQuery := elastic.NewBoolQuery().MustNot(
				// 如果 oper_info 是非空数组 ⇒ 会命中。
				// 如果 oper_info 是空数组 ⇒ 不会命中。
				// 如果 oper_info 字段根本不存在 ⇒ 不会命中
				elastic.NewNestedQuery(nestedPath, elastic.NewMatchAllQuery()),
			)
			// 判断field是否存在或为空
			fieldValueQuery := elastic.NewBoolQuery().Should(
				elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery(field)),
				elastic.NewTermQuery(field, ""),
			).MinimumNumberShouldMatch(1)

			query = elastic.NewBoolQuery().Should(
				// 1. oper_info是空数组，或者oper_info根本不存在
				fieldQuery,

				// 2. fieldExistQueryoper_info中存在id为空或不存在的子文档
				elastic.NewNestedQuery(nestedPath, fieldValueQuery),
			).MinimumNumberShouldMatch(1)
		case "ports.domain.keyword", "ports.protocol", "work_number", "email", "repair_priority.keyword":
			query = elastic.NewBoolQuery().
				Should(
					// 字段不存在
					elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery(field)),
					// 字段为空字符串
					elastic.NewBoolQuery().Must(elastic.NewTermQuery(field, "")),
				).MinimumNumberShouldMatch(1)
		case "limit_date":
			query = elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery(field))
		case "area", "port", "continuity_level", "risk_num", "level", "status", "ip_type":
			query = elastic.NewTermQuery(field, 0)
		case "ports.port":
			query = elastic.NewBoolQuery().
				Should(
					elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("ports")),
					elastic.NewTermQuery(field, 0),
				).MinimumNumberShouldMatch(1)
		case "vulType": // 类型为keyword的字段
			query = elastic.NewBoolQuery().Should(
				elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery(field)),
				elastic.NewTermQuery(field, ""),
			).MinimumNumberShouldMatch(1)
		default:
			if strings.Contains(field, "rule_infos.") {
				query = elastic.NewBoolQuery().MustNot(
					elastic.NewNestedQuery(strings.Split(field, ".")[0], elastic.NewMatchAllQuery()),
				)
			} else {
				query = elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery(field))
			}
		}

	case "not_null":
		switch field {
		// 嵌套字段
		case "business.system", "person_base.name", "business_department.name.keyword", "oper_department.name.keyword", "department_base.name.keyword", "department_base.name", "opers.id", "opers.oper_info.id", "business.person_base.id", "oper_info.id", "rule_infos.product", "rule_infos.first_tag", "rule_infos.second_tag":
			nestedPath := detectNestedPath(field)
			nestedBoolQuery := elastic.NewBoolQuery().Must(
				elastic.NewExistsQuery(field), // 检查嵌套字段是否存在
			).MustNot(elastic.NewTermQuery(field, "")) // 值不为空
			// 将 BoolQuery 放入 NestedQuery 中
			query = elastic.NewNestedQuery(
				nestedPath, // 嵌套字段的根路径
				nestedBoolQuery,
			)
		// int类型的字段
		case "area", "port", "continuity_level", "ports.port", "risk_num", "level", "status", "ip_type":
			query = elastic.NewBoolQuery().MustNot(elastic.NewTermQuery(field, 0))
		case "limit_date", "private_ip", "public_ip", "ip":
			query = elastic.NewExistsQuery(field) // 检查字段是否存在
		// 普通非嵌套字段
		default:
			query = elastic.NewBoolQuery().Must(elastic.NewExistsQuery(field)). // 检查字段是否存在
												MustNot(elastic.NewTermQuery(field, "")) // 值不为空
		}

	case "=":
		// 检查是否为嵌套字段
		if detectNestedPath(field) != "" {
			// 对于嵌套字段，构建嵌套的通配符查询
			nestedPath := detectNestedPath(field)
			nestedBoolQuery := elastic.NewBoolQuery().Must(
				elastic.NewWildcardQuery(field, fmt.Sprintf("*%s*", p[0])),
			)
			query = elastic.NewNestedQuery(nestedPath, nestedBoolQuery)
		} else {
			// 对于普通字段，使用普通的通配符查询
			query = elastic.NewWildcardQuery(field, fmt.Sprintf("*%s*", p[0]))
		}
	case "!=":
		// 检查是否为嵌套字段
		if detectNestedPath(field) != "" {
			// 对于嵌套字段，构建嵌套的通配符查询
			nestedPath := detectNestedPath(field)
			nestedBoolQuery := elastic.NewBoolQuery().Must(
				elastic.NewWildcardQuery(field, fmt.Sprintf("*%s*", p[0])),
			)
			query = elastic.NewNestedQuery(nestedPath, nestedBoolQuery)
		} else {
			// 对于普通字段，使用普通的通配符查询
			query = elastic.NewWildcardQuery(field, fmt.Sprintf("*%s*", p[0]))
		}
	}

	// 根据 logicalConnective 添加到 boolQuery
	switch logicalConnective {
	case "or":
		boolQuery.Should(query).MinimumShouldMatch("1")
	default:
		if operationTypeString == "not_in" || operationTypeString == "!==" {
			if query != nil {
				boolQuery.MustNot(query)
			}
		} else {
			if query != nil {
				boolQuery.Must(query)
			}
		}
	}
	if query != nil {
		// 打印生成的查询语句
		source, err := query.Source()
		if err != nil {
			logger.Errorf("filtrate BuildBoolQuery 生成查询语句时发生错误: %s", err.Error())
		}
		queryJSON, _ := json.MarshalIndent(source, "", "  ") // 格式化输出
		queryStr := string(queryJSON)
		logger.Debugf("filtrate BuildBoolQuery 生成的查询语句: %s", queryStr)
	}
	return boolQuery
}

// 构建一个 NestedQuery 的 Wrapper 方法
func BuildNestedTermQuery(field string, value interface{}) elastic.Query {
	nestedPath := detectNestedPath(field)
	if nestedPath == "" {
		// 不嵌套，直接构造 TermQuery
		return elastic.NewTermQuery(field, value)
	}

	// 构造内部 BoolQuery
	nestedBoolQuery := elastic.NewBoolQuery().Must(
		elastic.NewTermQuery(field, value),
	)

	// 构造 NestedQuery
	return elastic.NewNestedQuery(nestedPath, nestedBoolQuery)
}

func BuildNestedTermsQuery(field string, values []interface{}) elastic.Query {
	nestedPath := detectNestedPath(field)
	if nestedPath == "" {
		// 不嵌套，直接构造 TermsQuery
		return elastic.NewTermsQuery(field, values...)
	}

	// 构造内部 BoolQuery
	nestedBoolQuery := elastic.NewBoolQuery().Must(
		elastic.NewTermsQuery(field, values...),
	)

	// 构造 NestedQuery
	return elastic.NewNestedQuery(nestedPath, nestedBoolQuery)
}

// detectNestedPath 根据字段路径返回最近的嵌套路径（如 business.person_base.id ➜ business.person_base）
func detectNestedPath(field string) string {
	parts := strings.Split(field, ".")
	for i := len(parts) - 2; i >= 0; i-- {
		prefix := strings.Join(parts[:i+1], ".")
		if isNestedField(prefix) {
			return prefix
		}
	}
	return ""
}

// nestedFieldPaths 定义你的所有嵌套字段路径（根据映射配置）
var nestedFieldPaths = []string{
	"opers",
	// "opers.oper_info", // 注意：device中的 opers.oper_info 不是嵌套字段
	"oper_info",
	"business",
	"business.person_base",
	"person_base",
	"department_base",
	"business.person_base.department",
	"business.person_base.department.parents",
	"business.person_base.find_info",
	"rule_infos",
	"business.system",
	"person_base.name",
	"business_department",
	"oper_department",
	"department_base",
}

// isNestedField 判断一个字段路径是否是 nested 类型
func isNestedField(path string) bool {
	for _, nested := range nestedFieldPaths {
		if nested == path {
			return true
		}
	}
	return false
}

func convertToInterfaceSlice[T any](s []T) []interface{} {
	var p []interface{}
	for _, v := range s {
		p = append(p, v)
	}
	return p
}

// ParseQueryConditions 解析 JSON 数据并返回 QueryCondition 切片
func ParseQueryConditions(data []string) ([]QueryCondition, error) {
	var conditions []QueryCondition
	for _, jsonStr := range data {
		if jsonStr == "" {
			continue
		}
		var raw map[string]interface{}
		if err := json.Unmarshal([]byte(jsonStr), &raw); err != nil {
			return nil, fmt.Errorf("failed to unmarshal JSON: %v", err)
		}
		operationtypestring, _ := raw["operation_type_string"].(string)
		logicalconnective, _ := raw["condition"].(string)
		condition := QueryCondition{
			OperationTypeString: operationtypestring,
			LogicalConnective:   logicalconnective,
		}

		// 动态提取字段名和值
		for key, value := range raw {
			if key != "operation_type_string" && key != "condition" {
				if key == "ports.port" || key == "port" {
					// interface 类型 "["80", "90"]"转切片
					var ports []string
					switch v := value.(type) {
					case []interface{}:
						// 直接是数组类型
						for _, p := range v {
							portStr := cast.ToString(p)
							if strings.Contains(portStr, ",") {
								for _, port := range strings.Split(portStr, ",") {
									if !utils.IsValidatePort(port) {
										return nil, fmt.Errorf("无效的端口值: %s", port)
									}
								}
							} else if !utils.IsValidatePort(portStr) {
								return nil, fmt.Errorf("无效的端口值: %s", portStr)
							}
							ports = append(ports, portStr)
						}
					case string:
						// JSON 字符串类型
						if err := json.Unmarshal([]byte(v), &ports); err != nil {
							return nil, fmt.Errorf("端口格式解析失败: %v", err)
						}
						// 验证每个端口
						for _, port := range ports {
							if !utils.IsValidatePort(port) {
								return nil, fmt.Errorf("无效的端口值: %s", port)
							}
						}
					default:
						return nil, errors.New("端口格式不正确，应为数组或 JSON 字符串格式")
					}
				}
				if key == "ip.keyword" {
					key = "ip"
				}

				var tmpStr []string
				for _, v := range cast.ToStringSlice(value) {
					if strings.Contains(v, ",") {
						tmpStr = append(tmpStr, strings.Split(v, ",")...)
						continue
					}
					tmpStr = append(tmpStr, v)
				}
				if len(tmpStr) > 1 {
					value = tmpStr
				}
				if key == "ip" {
					for _, ip := range tmpStr {
						_, _, ipRangeErr := net.ParseCIDR(ip)
						if !utils.IsIP(ip) && ipRangeErr != nil {
							return nil, fmt.Errorf("非标准格式的ip: %s", ip)
						}
					}
				}
				condition.Value = value
				condition.Field = key
			}
		}

		conditions = append(conditions, condition)
	}
	return conditions, nil
}

type QueryCondition struct {
	Field               string      `json:"field"`                 // 查询的字段名
	Value               interface{} `json:"value"`                 // 查询的值
	OperationTypeString string      `json:"operation_type_string"` // 查询操作类型（e.g., "in", "match", etc.）
	LogicalConnective   string      `json:"logical_connective"`    // 查询的逻辑连接符（e.g., "and", "or"）
}
