package crontab

import (
	"fobrain/fobrain/app/repository/proactive_task/source_task"
	"fobrain/fobrain/logs"
	"fobrain/initialize/mysql"
	mcron "fobrain/models/mysql/crontab"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/proactive_task_node_relations"
	"fobrain/models/mysql/task"
)

// MonitorProactiveScanningSyncTasks
// @Summary 主动扫描任务完成后，进行资产及漏洞数据的同步工作
func (j *Job) MonitorProactiveScanningSyncTasks(_ *Cron, _ *mcron.CrontabModel) string {
	j.ProactiveSync()
	j.ProactiveSyncStatus()
	return "同步成功"
}

func (j *Job) ProactiveSync() string {
	logs.GetLogger().Infof("[ProactiveSync]主动扫描任务同步开始")
	proactiveTasks := make([]*task.ProactiveTasks, 0)
	query := task.NewProactiveTasks().Where("sync_status =?", task.SyncStatusUnSync).Where("status = ?", task.FinishedStatus)
	query.Find(&proactiveTasks)
	logs.GetLogger().Infof("[ProactiveSync]主动扫描任务同步开始,数量:%d", len(proactiveTasks))
	for _, t := range proactiveTasks {
		nodeTaskIds := proactive_task_node_relations.GetNodeTaskIds(int(t.Id))
		for _, nodeTask := range nodeTaskIds {
			node, _ := data_source.NewNodeModel().First(mysql.WithWhere("id =?", nodeTask["node_id"]))
			if node.Id == 0 {
				// 节点不存在时，标注为同步失败
				proactive_task_node_relations.UpdateSyncStatus(t.Id, nodeTask["node_id"], task.SyncStatusSyncFailed, task.SyncStatusSyncFailed)
				logs.GetLogger().Errorf("同步失败，没有节占信息 %+v %+v", t, nodeTask)
				continue
			}
			if err := source_task.New(t).SyncResult(node, map[string]any{
				"task_id":                         t.Id,
				"proactive_task_node_relation_id": nodeTask["id"],
			}); err != nil {
				logs.GetLogger().Errorf("同步失败: %v", err)
				return ""
			}
		}
	}

	return "同步成功"
}

// ProactiveSyncStatus
// @Summary 主动扫描任务同步状态更新
func (j *Job) ProactiveSyncStatus() {
	proactiveTasks := make([]*task.ProactiveTasks, 0)
	task.NewProactiveTasks().Where("sync_status = ?", task.SyncStatusSyncing).Find(&proactiveTasks)

	for _, pt := range proactiveTasks {
		allSyncSuccess := func(taskID uint64, syncTypes ...string) bool {
			for _, syncType := range syncTypes {
				if !proactive_task_node_relations.ObtainAllNodeSyncStatus(taskID, syncType, proactive_task_node_relations.SyncSuccessStatus) {
					return false
				}
			}
			return true
		}

		// 判断任务类型并检查同步状态
		switch pt.TaskType {
		case task.AssetScanType:
			if allSyncSuccess(pt.Id, "sync_asset_status") {
				pt.SyncStatus = task.SyncStatusSuccess
			}
		case task.ThreatScanType:
			if allSyncSuccess(pt.Id, "sync_threat_status") {
				pt.SyncStatus = task.SyncStatusSuccess
			}
		case task.AssetAndThreatScanType:
			if allSyncSuccess(pt.Id, "sync_threat_status", "sync_asset_status") {
				pt.SyncStatus = task.SyncStatusSuccess
			}
		default:
			pt.SyncStatus = task.SyncStatusSuccess
		}

		// 统一保存操作
		_ = task.UpdateSyncStatus(pt.Id, pt.SyncStatus)
	}
}

// RegisterProactiveSyncStatus 注册主动扫描任务同步状态更新定时任务
func RegisterProactiveSyncStatus() *mcron.CrontabModel {
	return NewSchedulerJob("主动扫描任务同步状态更新定时任务", "*/5 * * * * *", "MonitorProactiveScanningSyncTasks", "")
}
