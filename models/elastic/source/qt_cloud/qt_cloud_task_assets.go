package qt_cloud

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"fobrain/pkg/utils"

	"github.com/olivere/elastic/v7"
)

type (
	QTCloudTaskAssets struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`      //NODE+IP+区域+Task 作为唯一值去更新，用作记录每次任务拉取数据
		TaskId        string          `json:"task_id"` //
		ChildTaskId   string          `json:"child_task_id"`
		CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
		UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
	}
)

// IndexName 索引名
func (f *QTCloudTaskAssets) IndexName() string {
	return "qt_cloud_task_assets"
}

func (f *QTCloudTaskAssets) String() string {
	if f == nil {
		return ""
	}
	return ""
}

func NewQTCloudTaskAssetsModel() *QTCloudTaskAssets {
	return &QTCloudTaskAssets{}
}

func (f *QTCloudTaskAssets) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("displayIp.keyword").
		Field("hostname.keyword").
		Field("connectionIp.keyword").
		Field("agentId.keyword").
		Field("asset_type.keyword").
		Field("memories.bank.keyword").
		Field("networkCards.netmask.keyword").
		Field("networkCards.name.keyword")

	return field
}

func (f *QTCloudTaskAssets) GetQTCloudAssetListFields() []map[string]string {
	return []map[string]string{
		{"name": "主机IP地址", "key": "displayIp"},
		{"name": "外网IP地址", "key": "connectionIp"},
		{"name": "内网IP地址", "key": "internalIp"},
		{"name": "主机名", "key": "hostname"},
		{"name": "序列号", "key": "serialNumber"},
		{"name": "设备UUID", "key": "uuid"},
		{"name": "CPU信息", "key": "cpu"}, //处理器个数+厂商+品牌
		{"name": "硬盘大小", "key": "diskSize"},
		{"name": "内存大小", "key": "memorySize"},
		{"name": "Agent状态", "key": "agentStatus"}, //Agent状态，0-在线 1-离线 2-停用 3-删除中
		{"name": "Agent版本", "key": "agentVersion"},
		{"name": "最后在线时间", "key": "lastOnlineTime"},
		{"name": "机房位置", "key": "hostLocation"},
		{"name": "业务系统", "key": "bizGroup"},
		{"name": "业务负责人", "key": "chargeName"},
	}
}

func (f *QTCloudTaskAssets) GetQTCloudAssetListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "主机IP地址", "key": "displayIp"},
		{"name": "外网IP地址", "key": "connectionIp"},
		{"name": "内网IP地址", "key": "internalIp"},
		{"name": "主机名", "key": "hostname"},
		{"name": "序列号", "key": "serialNumber"},
		{"name": "设备UUID", "key": "uuid"},
		{"name": "CPU信息", "key": "cpu"}, //处理器个数+厂商+品牌
		{"name": "硬盘大小", "key": "diskSize"},
		{"name": "内存大小", "key": "memorySize"},
		{"name": "Agent状态", "key": "agentStatus"}, //Agent状态，0-在线 1-离线 2-停用 3-删除中
		{"name": "Agent版本", "key": "agentVersion"},
		{"name": "最后在线时间", "key": "lastOnlineTime"},
		{"name": "机房位置", "key": "hostLocation"},
		{"name": "业务系统", "key": "bizGroup"},
		{"name": "业务负责人", "key": "chargeName"},
		{"name": "备注", "key": "remark"},
		{"name": "标签", "key": "hostTagList"},
		{"name": "操作系统信息", "key": "platform"},
		{"name": "内核版本", "key": "kernelVersion"},
		{"name": "硬盘个数", "key": "diskCount"},
		{"name": "硬盘使用率", "key": "diskUsage"},
		{"name": "内存使用率", "key": "memoryUsage"},
		{"name": "通信状态", "key": "onlineStatus"}, // 1 – 在线 0 - 离线
		{"name": "安装时间", "key": "installTime"},
		{"name": "不在线天数", "key": "offlineDays"},
		{"name": "生产商", "key": "manufacturer"},
		{"name": "硬件配置信息", "key": "memories"},
	}
}
