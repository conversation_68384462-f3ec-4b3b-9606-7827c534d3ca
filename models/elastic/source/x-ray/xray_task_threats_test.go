package xray

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTaskThreats_IndexName(t *testing.T) {
	assert.Equal(t, "xray_task_threats", NewTaskThreatsModel().IndexName())
	assert.Equal(t, "", NewTaskThreatsModel().String())
}

func TestTaskThreats_GetThreatListFields(t *testing.T) {
	m := NewTaskThreatsModel()
	fields := m.GetThreatListFields()
	assert.Equal(t, fields, []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "漏洞名称", "key": "title"},
		{"name": "CVE编号", "key": "cveId"},
		{"name": "漏洞类型", "key": "vulType"},
		{"name": "漏洞等级", "key": "level_desc"},
		{"name": "是否存在Poc", "key": "poc"},
		{"name": "是否存在EXP", "key": "exp"},
		{"name": "漏洞地址", "key": "url"},
		{"name": "发现时间", "key": "created_at"},
	})
}

func TestTaskThreats_GetThreatListDetailFields(t *testing.T) {
	m := NewTaskThreatsModel()
	fields := m.GetThreatListDetailFields()
	assert.Equal(t, fields, []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "漏洞名称", "key": "title"},
		{"name": "CVE编号", "key": "cveId"},
		{"name": "漏洞类型", "key": "vulType"},
		{"name": "漏洞等级", "key": "level_desc"},
		{"name": "是否存在Poc", "key": "poc"},
		{"name": "是否存在EXP", "key": "exp"},
		{"name": "漏洞地址", "key": "url"},
		{"name": "发现时间", "key": "created_at"},
		// {"name": "通报时间", "key": "notice_time"},
		// {"name": "上次扫描时间", "key": "lastupdatetime"},
		{"name": "备注信息", "key": "summary"},
		// {"name": "MAC地址", "key": "mac"},
		// {"name": "地理位置", "key": "city"},
		// {"name": "资产等级", "key": "asset_level"},
		// {"name": "管理单元", "key": "company"},
	})
}
