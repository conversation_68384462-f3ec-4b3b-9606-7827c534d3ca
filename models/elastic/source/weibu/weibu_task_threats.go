package weibu

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"fobrain/pkg/utils"

	"github.com/olivere/elastic/v7"
)

type (
	WeibuTaskThreats struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`      // TaskId+NODE+区域+IP+Port 作为唯一值去更新，用作记录每次任务拉取数据
		TaskId        string          `json:"task_id"` //
		ChildTaskId   string          `json:"child_task_id"`
		CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
		UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
	}
)

// IndexName 索引名
func (f *WeibuTaskThreats) IndexName() string {
	return "weibu_task_threats"
}

func (f *WeibuTaskThreats) String() string {
	if f == nil {
		return ""
	}
	return ""
}

func NewWeibuTaskThreatsModel() *WeibuTaskThreats {
	return &WeibuTaskThreats{}
}

func (f *WeibuTaskThreats) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("machine").
		Field("vulnerability_name.keyword").
		Field("vulnerability_type_desc.keyword").
		Field("severity_desc.keyword")

	return field
}

func (f *WeibuTaskThreats) GetWeibuThreatListFields() []map[string]string {
	return []map[string]string{
		{"name": "主机IP地址", "key": "machine", "all_key": "machine.*"},
		{"name": "处置状态", "key": "status", "all_key": "status"},
		{"name": "漏洞级别", "key": "severity_desc", "all_key": "severity_desc.*"},
		{"name": "漏洞类型", "key": "vulnerability_type_desc", "all_key": "vulnerability_type_desc.*"},
		{"name": "漏洞名称", "key": "vulnerability_name", "all_key": "vulnerability_name.*"},
	}
}

func (f *WeibuTaskThreats) GetWeibuThreatListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "主机IP地址", "key": "machine", "all_key": "machine.*"},
		{"name": "资产机器信息", "key": "assets_machine", "all_key": "assets_machine"},
		{"name": "处置状态", "key": "status", "all_key": "status"},
		{"name": "威胁类型", "key": "threat_type", "all_key": "threat_type"},
		{"name": "主机数", "key": "machine_count", "all_key": "machine_count"},
		{"name": "漏洞级别", "key": "severity_desc", "all_key": "severity_desc.*"},
		{"name": "漏洞类型", "key": "vulnerability_type_desc", "all_key": "vulnerability_type_desc.*"},
		{"name": "漏洞名称", "key": "vulnerability_name.*", "all_key": "vulnerability_name.*"},
		{"name": "最近发现时间", "key": "last_occ_time", "all_key": "last_occ_time"},
	}
}
