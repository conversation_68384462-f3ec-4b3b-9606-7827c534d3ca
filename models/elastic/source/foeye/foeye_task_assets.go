package foeye

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/olivere/elastic/v7"
)

type (
	FoeyeTaskAssets struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`      // TaskId+NODE+区域+IP 作为唯一值去更新，用作记录每次任务拉取数据
		TaskId        string          `json:"task_id"` //
		ChildTaskId   string          `json:"child_task_id"`
		CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
		UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
	}
)

// IndexName 索引名
func (f *FoeyeTaskAssets) IndexName() string {
	return "foeye_task_assets"
}

func (f *FoeyeTaskAssets) String() string {
	if f == nil {
		return ""
	}
	return ""
}

func NewFoeyeTaskAssetsModel() *FoeyeTaskAssets {
	return &FoeyeTaskAssets{}
}

func (f *FoeyeTaskAssets) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("ip").
		Field("port_list.protocol.keyword").
		Field("port_list.title.keyword").
		Field("port_list.rule_info.description.keyword")

	return field
}

func (f *FoeyeTaskAssets) GetFoeyeAssetListFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "端口", "key": "port", "show_value": "port_list"},
		{"name": "协议", "key": "protocol", "show_value": "port_list"},
		{"name": "组件", "key": "rule_info", "show_value": "port_list", "show_key": "name"},
		{"name": "域名", "key": "domain"},
		{"name": "操作系统", "key": "os"},
		{"name": "MAC地址", "key": "mac"},
	}
}

func (f *FoeyeTaskAssets) GetFoeyeAssetListFieldsV2Rule() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "端口", "key": "port", "show_value": "port_list"},
		{"name": "协议", "key": "protocol", "show_value": "port_list"},
		{"name": "组件", "key": "rule_infos"},
		{"name": "域名", "key": "domain"},
		{"name": "操作系统", "key": "os"},
		{"name": "MAC地址", "key": "mac"},
	}
}

func (f *FoeyeTaskAssets) GetFoeyeAssetListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "端口", "key": "port", "show_value": "port_list"},
		{"name": "协议", "key": "protocol", "show_value": "port_list"},
		{"name": "组件信息", "key": "rule_info", "show_value": "port_list", "show_key": "name"},
		{"name": "域名", "key": "domain"},
		{"name": "操作系统", "key": "os"},
		{"name": "MAC地址", "key": "mac"},
		{"name": "资产标识", "key": "is_honeypot/is_fraud"},
		{"name": "风险属性", "key": "threat_status"},
		{"name": "网站截图", "key": "website_image"},
		{"name": "RDP截图", "key": "rdp_image"},
		{"name": "发现时间", "key": "createtime"},
		{"name": "上次扫描时间", "key": "lastupdatetime"},
		{"name": "网站标题", "key": "website_title"},
		{"name": "备注信息", "key": "name"},
		{"name": "地理位置", "key": "city"},
		{"name": "资产等级", "key": "asset_level"},
		{"name": "机房信息", "key": "computer_room"},
		{"name": "管理单元", "key": "company"},
		{"name": "业务系统", "key": "business_app"},
		{"name": "业务系统负责人", "key": "username"},
		{"name": "电话", "key": "manager_mobile"},
		{"name": "邮箱", "key": "manager_email"},
	}
}

func (f *FoeyeTaskAssets) GetFoeyeAssetListDetailFieldsV2() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "端口", "key": "port", "show_value": "port_list"},
		{"name": "协议", "key": "protocol", "show_value": "port_list"},
		{"name": "组件信息", "key": "title", "show_value": "rule_infos"},
		{"name": "域名", "key": "domain"},
		{"name": "操作系统", "key": "os"},
		{"name": "MAC地址", "key": "mac"},
		{"name": "资产标识", "key": "is_honeypot/is_fraud"},
		{"name": "风险属性", "key": "threat_status"},
		{"name": "网站截图", "key": "website_image"},
		{"name": "RDP截图", "key": "rdp_image"},
		{"name": "发现时间", "key": "createtime"},
		{"name": "上次扫描时间", "key": "lastupdatetime"},
		{"name": "网站标题", "key": "website_title"},
		{"name": "备注信息", "key": "name"},
		{"name": "地理位置", "key": "city"},
		{"name": "资产等级", "key": "asset_level"},
		{"name": "机房信息", "key": "computer_room"},
		{"name": "管理单元", "key": "company"},
		{"name": "业务系统", "key": "business_app"},
		{"name": "业务系统负责人", "key": "username"},
		{"name": "电话", "key": "manager_mobile"},
		{"name": "邮箱", "key": "manager_email"},
	}
}

func (f *FoeyeTaskAssets) GetFoeyeAssetListFieldsV2() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ip"},
		{"name": "端口", "key": "port", "show_value": "port_list"},
		{"name": "协议", "key": "protocol", "show_value": "port_list"},
		{"name": "组件", "key": "title", "show_value": "rule_infos"},
		{"name": "域名", "key": "domain"},
		{"name": "操作系统", "key": "os"},
		{"name": "MAC地址", "key": "mac"},
	}
}
