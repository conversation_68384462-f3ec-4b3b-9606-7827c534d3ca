package seeders

import (
	"fobrain/models/mysql/data_source"
)

func DataSourceTypeMapSeeder() {
	sourceTypeMap := map[string][]string{
		"asset":                   {"foeye", "d01", "xray", "sangfor_sip", "qianxin_server", "qianxin_tianqing"},
		"cmdb":                    {"bk_cmdb", "jw_cmdb", "uyun", "suyi_cmdb", "aspire", "bk_cmdb_domain", "bk_cmdb_f5_vs", "bk_cmdb_business", "bk_cmdb_cloud_ecs", "bk_cmdb_vm_machine", "bk_cmdb_f5_pool", "youyun_cmdb", "youyue_cmdb"},
		"hids":                    {"qty", "weibu_onesec"},
		"flow_analysis":           {"tsa", "atd", "tfa"},
		"vulnerability":           {"foeye", "foradar_saas", "d01", "tenable", "nsfocus_rsas", "xray", "yuntu", "sangfor_sip", "qingteng_beehive", "tenable_sc"},
		"jump_server":             {"risaca", "jump_server", "aisifort", "qizhi_uaudithost", "haiyi_pas", "qianxin_pam"},
		"firewall":                {"h3c_firewall", "changting_waf", "anheng_apt"},
		"cloud":                   {"huawei_manager_one", "ecloud", "ucloud", "aliyun_ecs", "bon_cloud", "huawei_cloud", "aliyun_cloud", "bk_cmdb_cloud_ecs", "bk_cmdb_vm_machine", "huawei_hk_cloud", "huawei_private_cloud_ecs"},
		"load":                    {"citrix", "zeepolicy", "aliyun_slb"},
		"public_cloud":            {"public_tencent_clb", "public_tencent_cvm", "public_huawei_ecs"}, //"public_aliyun_slb", "public_aliyun_ecs"
		"ldap":                    {"ldap"},
		"communication":           {"dingtalk", "zhongyi_feishu", "tencent_ud"},
		"file_import":             {"file_import"},
		"container_orchestration": {"k8s"},
		"others":                  {"tdp", "rizhiyi", "sangfor_ssl_vpn"},
	}
	data_source.NewSourceTypeMapModel().Delete(data_source.SourceTypeMap{}, "id != 0")
	for types, sources := range sourceTypeMap {
		var sourceType data_source.SourceType
		err := data_source.NewSourceTypeModel().Where("type=?", types).First(&sourceType).Error
		if err != nil {
			continue
		}
		for _, sType := range sources {
			var source data_source.Source
			err = data_source.NewSourceModel().Where("type=?", sType).First(&source).Error
			if err != nil {
				continue
			}
			sourceMap := data_source.SourceTypeMap{
				SourceId:     source.Id,
				SourceTypeId: sourceType.Id,
			}
			err = data_source.NewSourceTypeMapModel().Create(&sourceMap)
			if err != nil {
				continue
			}
		}

	}
}
