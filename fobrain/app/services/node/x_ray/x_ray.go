package x_ray

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"

	"github.com/spf13/cast"

	"fobrain/fobrain/app/services/node"
)

const (
	PushTasking           = "%s://%s/api/v2/plan/create/"
	TaskProgress          = "%s://%s/api/v2/xprocess/%d/progress/"
	TaskInfo              = "%s://%s/api/v2/xprocess/%d/"
	UriXrayDelTask        = "%s://%s/api/v2/plan/%d/"         // 删除任务
	UriStopTask           = "%s://%s/api/v2/xprocess/pause/"  // 暂停任务
	UriStartTask          = "%s://%s/api/v2/xprocess/resume/" // 开启任务
	UriThreatRepair       = "%s://%s/api/v2/vuln/retest/"     // 复测
	UriThreatRetestResult = "%s://%s/api/v2/vuln/retest/%s/"  // 复测结果
	TaskPlan              = "%s://%s/api/v2/plan/%d/"
	TaskResult            = "%s://%s/api/v2/result/filter/"
	VulnerabilityList     = "%s://%s/api/v2/vuln/filter/"
	TemplateList          = "%s://%s/api/v2/template/"                     // 同步 x-ray 扫描任务模板
	AssetsList            = "%s://%s/api/v2/ip/filter/?project_id=%s"      // 同步 x-ray 资产结果中的端口服务信息
	ServicesList          = "%s://%s/api/v2/service/filter/?project_id=%s" // 同步 x-ray 资产结果中的端口服务信息
)

type XRay struct {
	node.Client
	CallBackThreatsFunc func([]map[string]interface{}) error
	CallBackAssetsFunc  func([]map[string]interface{}) error
	Total               int64
	AssetTotal          int64
	ServiceTotal        int64
}

func New() *XRay {
	return &XRay{Client: node.NewDefaultClient()}
}

func NewCallbackXRay(callbackThreatFunc, callbackAssetsFunc func([]map[string]interface{}) error) *XRay {
	return &XRay{
		Client:              node.NewDefaultClient(),
		CallBackThreatsFunc: callbackThreatFunc,
		CallBackAssetsFunc:  callbackAssetsFunc,
	}
}

func (x *XRay) TemplateAll() ([]map[string]any, error) {
	res := make([]map[string]any, 0)

	apiUri := fmt.Sprintf(TemplateList, cast.ToString(x.GetConfig("protocol")), cast.ToString(x.GetConfig("ip")))
	params := url.Values{}
	params.Add("limit", fmt.Sprintf("%d", 5))
	params.Add("offset", fmt.Sprintf("%d", 0))

	apiUri += "?" + params.Encode()

	// 创建 HTTP 请求
	req, err := http.NewRequest("GET", apiUri, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %v", err)
	}

	// 设置请求头信息
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("token", x.GetToken()) // 根据文档要求使用 'token' 作为请求头

	// 跳过 TLS 证书验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return res, fmt.Errorf("failed to send HTTP request: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(resp.Body)
		return res, fmt.Errorf("received non-OK response: %s", string(body))
	}

	// 读取响应体
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return res, fmt.Errorf("failed to read response body: %v", err)
	}

	//fmt.Println(string(body))

	// 定义结构体用于解析 JSON
	type Response struct {
		Err  string `json:"err"`
		Msg  string `json:"msg"`
		Data struct {
			Content []struct {
				ID           int            `json:"id"`
				Name         string         `json:"name"`
				Remark       string         `json:"remark"`
				TaskSetting  map[string]any `json:"task_setting"`
				Strategy     map[string]any `json:"strategy"`
				TemplateType string         `json:"template_type"`
			} `json:"content"`
		} `json:"data"`
	}

	// 解析 JSON 响应
	var respData Response
	if err := json.Unmarshal(body, &respData); err != nil {
		return res, fmt.Errorf("failed to unmarshal response: %v", err)
	}

	if respData.Err != "" {
		return res, fmt.Errorf(respData.Msg)
	}

	for _, item := range respData.Data.Content {
		res = append(res, map[string]any{
			"id":            item.ID,
			"name":          item.Name,
			"scan_type":     item.Strategy["name"],
			"template_type": item.TemplateType,
		})
	}

	return res, nil
}

// TemplateResponse 定义结构体用于解析 JSON
type TemplateResponse struct {
	Err  string                `json:"err"`
	Msg  string                `json:"msg"`
	Data *TemplateResponseData `json:"data"`
}

type TemplateResponseData struct {
	ID           int            `json:"id"`
	Name         string         `json:"name"`
	Remark       string         `json:"remark"`
	TaskSetting  map[string]any `json:"task_setting"`
	Strategy     map[string]any `json:"strategy"`
	TemplateType string         `json:"template_type"`
}

func (x *XRay) Template(templateId int64) (res *TemplateResponseData, err error) {
	apiUri := fmt.Sprintf(TemplateList, cast.ToString(x.GetConfig("protocol")), cast.ToString(x.GetConfig("ip")))

	apiUri += fmt.Sprintf("%d/", templateId)

	// 创建 HTTP 请求
	req, err := http.NewRequest("GET", apiUri, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %v", err)
	}

	// 设置请求头信息
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("token", x.GetToken()) // 根据文档要求使用 'token' 作为请求头

	// 跳过 TLS 证书验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return res, fmt.Errorf("failed to send HTTP request: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(resp.Body)
		return res, fmt.Errorf("received non-OK response: %s", string(body))
	}

	// 读取响应体
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return res, fmt.Errorf("failed to read response body: %v", err)
	}

	// 解析 JSON 响应
	var respData TemplateResponse
	if err := json.Unmarshal(body, &respData); err != nil {
		return res, fmt.Errorf("failed to unmarshal response: %v", err)
	}

	if respData.Err != "" {
		return res, fmt.Errorf(respData.Msg)
	}

	return respData.Data, nil
}

// GetVulnerabilityAll
// @Summary: 获取漏洞总数
func (x *XRay) GetVulnerabilityAll() (int64, error) {
	apiUri := fmt.Sprintf(VulnerabilityList, cast.ToString(x.GetConfig("protocol")), cast.ToString(x.GetConfig("ip")))
	// 获取配置的每页数量,默认200
	limit := int64(200)
	pageSize := x.GetConfig("page_size")
	if pageSize != nil && pageSize != "" {
		limit = cast.ToInt64(pageSize)
	}
	return x.GetResponse(apiUri, limit)
}

// GetAssetAll
// @Summary: 获取资产总数
func (x *XRay) GetAssetAll() (int64, error) {
	projectId := x.GetConfig("project_id")
	if projectId == nil || projectId == "" {
		projectId = "1"
	}
	apiUri := fmt.Sprintf(AssetsList, cast.ToString(x.GetConfig("protocol")), cast.ToString(x.GetConfig("ip")), projectId)
	// 获取配置的每页数量,默认200
	limit := int64(200)
	pageSize := x.GetConfig("page_size")
	if pageSize != nil && pageSize != "" {
		limit = cast.ToInt64(pageSize)
	}
	return x.GetResponseAsset(apiUri, limit)
}

// GetServiceAll
// @Summary: 获取服务总数 - 与 GetAssetAll 结合，组装资产数据
func (x *XRay) GetServiceAll(ip string) []map[string]interface{} {
	projectId := x.GetConfig("project_id")
	if projectId == nil || projectId == "" {
		projectId = "1"
	}
	apiUri := fmt.Sprintf(ServicesList, cast.ToString(x.GetConfig("protocol")), cast.ToString(x.GetConfig("ip")), projectId)
	return x.GetResponseService(apiUri, 1000, ip)
}

func (x *XRay) GetResponseService(apiURL string, limit int64, ip string) (res []map[string]interface{}) {
	offset := int64(0)
	for {
		filter := map[string]any{
			"limit":  limit,
			"offset": offset,
			"ip": []map[string]any{
				{
					"is_partial": false,
					"content":    ip,
				},
			},
		}

		response, _ := x.HttpRequest("POST", apiURL, filter)

		var responseData map[string]interface{}
		if err := json.Unmarshal(response, &responseData); err != nil {
			break
		}

		data, dataExists := responseData["data"].(map[string]interface{})
		if !dataExists {
			break
		}

		if offset == 0 {
			total, totalExists := data["total"].(float64)
			if !totalExists {
				break
			}

			x.ServiceTotal = int64(total)
		}

		content, contentExists := data["content"].([]interface{})
		if contentExists {
			for _, item := range content {
				if itemMap, ok := item.(map[string]interface{}); ok {
					res = append(res, itemMap)
				}
			}
		}

		offset += limit
		if offset >= int64(x.ServiceTotal) {
			break
		}
	}

	return res
}

func (x *XRay) GetResponseAsset(apiURL string, limit int64) (total int64, err error) {
	offset := int64(0)
	for {
		filter := map[string]int64{
			"limit":  limit,
			"offset": offset,
		}

		var res = make([]map[string]interface{}, 0)

		response, _ := x.HttpRequest("POST", apiURL, filter)

		var responseData map[string]interface{}
		if err := json.Unmarshal(response, &responseData); err != nil {
			break
		}

		data, dataExists := responseData["data"].(map[string]interface{})
		if !dataExists {
			break
		}

		if offset == 0 {
			total, totalExists := data["total"].(float64)
			if !totalExists {
				break
			}

			x.AssetTotal = int64(total)
		}

		content, contentExists := data["content"].([]interface{})
		if contentExists {
			for _, item := range content {
				if itemMap, ok := item.(map[string]interface{}); ok {
					res = append(res, itemMap)
				}
			}
		}

		if x.CallBackAssetsFunc != nil {
			err = x.CallBackAssetsFunc(res)
			if err != nil {
				return x.AssetTotal, err
			}
		}

		offset += limit
		if offset >= int64(x.AssetTotal) {
			break
		}
	}

	return x.AssetTotal, nil
}

func (x *XRay) GetResponse(apiURL string, limit int64) (total int64, err error) {
	offset := int64(0)
	for {
		filter := map[string]int64{
			"limit":  limit,
			"offset": offset,
		}

		var res = make([]map[string]interface{}, 0)
		response, _ := x.HttpRequest("POST", apiURL, filter)

		var responseData map[string]interface{}
		if err := json.Unmarshal(response, &responseData); err != nil {
			break
		}

		data, dataExists := responseData["data"].(map[string]interface{})
		if !dataExists {
			break
		}

		if offset == 0 {
			total, totalExists := data["total"].(float64)
			if !totalExists {
				break
			}

			x.Total = int64(total)
		}

		content, contentExists := data["content"].([]interface{})
		if contentExists {
			for _, item := range content {
				if itemMap, ok := item.(map[string]interface{}); ok {
					res = append(res, itemMap)
				}
			}
		}

		if x.CallBackThreatsFunc != nil {
			err = x.CallBackThreatsFunc(res)
			if err != nil {
				return x.Total, err
			}
		}

		offset += limit
		if offset >= int64(x.Total) {
			break
		}
	}

	return x.Total, nil
}

// HttpRequest 通用请求方法
func (x *XRay) HttpRequest(method, apiUrl string, payload any) ([]byte, error) {
	// 序列化请求数据
	var reqBody *bytes.Buffer
	if payload != nil {
		jsonData, err := json.Marshal(payload)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal payload: %v", err)
		}
		reqBody = bytes.NewBuffer(jsonData)
	} else {
		reqBody = bytes.NewBuffer(nil)
	}

	// 创建 HTTP 请求
	req, err := http.NewRequest(method, apiUrl, reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("token", x.GetToken())

	// 跳过 TLS 证书验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send HTTP request: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("received non-OK response: %s", string(body))
	}

	// 读取响应体
	return io.ReadAll(resp.Body)
}
