package x_ray

import (
	testcommon "fobrain/fobrain/tests/common_test"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestXRay_DelNodeTask(t *testing.T) {
	cli := New()
	if err := cli.SetConfig(map[string]any{
		"api_key":  "xray_api_key",
		"ip":       "************",
		"protocol": "https",
	}); err != nil {
		t.<PERSON>rro<PERSON>(err.<PERSON>rror())
	}

	err := cli.DelNodeTask(10)
	if err != nil {
		t.<PERSON>rf(err.<PERSON>rror())
	}
}

func TestXRay_StartTask(t *testing.T) {
	cli := New()
	if err := cli.SetConfig(map[string]any{
		"api_key":  "xray_api_key",
		"ip":       "************",
		"protocol": "https",
	}); err != nil {
		t.<PERSON>rf(err.<PERSON>())
	}

	err := cli.StartTask(10)
	if err != nil {
		t.<PERSON><PERSON>rf(err.<PERSON>rror())
	}
}

func TestXRay_StopTask(t *testing.T) {
	cli := New()
	if err := cli.SetConfig(map[string]any{
		"api_key":  "xray_api_key",
		"ip":       "************",
		"protocol": "https",
	}); err != nil {
		t.Errorf(err.Error())
	}

	err := cli.StopTask(10)
	if err != nil {
		t.Errorf(err.Error())
	}
}

func TestXRay_Retest(t *testing.T) {
	// 1. 准备测试数据
	tests := []struct {
		name       string
		response   string
		statusCode int
		input      []string
		wantErr    bool
		wantTaskId string
	}{
		{
			name: "successful retest",
			response: `{
				"data": {
					"task_id": "task_123"
				}
			}`,
			statusCode: http.StatusOK,
			input:      []string{"123"},
			wantErr:    false,
			wantTaskId: "task_123",
		},
		{
			name: "server returns error",
			response: `{
				"err": "retest failed"
			}`,
			statusCode: http.StatusOK,
			input:      []string{"123"},
			wantErr:    true,
			wantTaskId: "",
		},
		{
			name:       "invalid json response",
			response:   `invalid json`,
			statusCode: http.StatusOK,
			input:      []string{"123"},
			wantErr:    true,
			wantTaskId: "",
		},
		{
			name: "http error status",
			response: `{
				"message": "internal server error"
			}`,
			statusCode: http.StatusInternalServerError,
			input:      []string{"123"},
			wantErr:    true,
			wantTaskId: "",
		},
	}

	// 2. 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置Mock服务器
			server := testcommon.SetupMockServer(tt.response, tt.statusCode)
			defer server.Close()

			// 创建测试客户端
			cli := New()
			cli.SetConfig(map[string]any{
				"protocol": "http",
				"ip":       testcommon.ExtractAfterProtocol(server.URL),
			})

			// 执行测试
			got, err := cli.Retest(tt.input)

			// 3. 验证结果
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, got)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantTaskId, got)
			}
		})
	}
}

// 保持向后兼容的简化测试（可选）
func TestXRay_Retest_Error(t *testing.T) {
	server := testcommon.SetupMockServer(`{
		"err": "123"
	}`, http.StatusOK)
	defer server.Close()

	cli := New()
	cli.SetConfig(map[string]any{"protocol": "http", "ip": testcommon.ExtractAfterProtocol(server.URL), "project_id": 1})
	_, err := cli.Retest([]string{"123"})

	// 使用 assert 替代 t.Errorf
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "x-ray retest failed")
}

func TestXRay_GetCheckProgress(t *testing.T) {
	// 1. 准备测试数据
	tests := []struct {
		name         string
		response     string
		statusCode   int
		input        []string
		wantErr      bool
		wantProgress int
	}{
		{
			name: "task completed successfully",
			response: `{
				"data": {
					"task_id": 123,
					"running": false,
					"result": "FIXED",
					"created_time": "2024-01-01 12:00:00"
				}
			}`,
			statusCode:   http.StatusOK,
			input:        []string{"123"},
			wantErr:      false,
			wantProgress: 100,
		},
		{
			name: "task still running",
			response: `{
				"data": {
					"task_id": 123,
					"running": true,
					"result": "",
					"created_time": "2024-01-01 12:00:00"
				}
			}`,
			statusCode:   http.StatusOK,
			input:        []string{"123"},
			wantErr:      false,
			wantProgress: 50,
		},
		{
			name: "server returns error",
			response: `{
				"err": "task not found"
			}`,
			statusCode:   http.StatusOK,
			input:        []string{"123"},
			wantErr:      true,
			wantProgress: 0,
		},
		{
			name:         "http error status",
			response:     `{"message": "internal server error"}`,
			statusCode:   http.StatusInternalServerError,
			input:        []string{"123"},
			wantErr:      true,
			wantProgress: 0,
		},
	}

	// 2. 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置Mock服务器
			server := testcommon.SetupMockServer(tt.response, tt.statusCode)
			defer server.Close()

			// 创建测试客户端
			cli := New()
			cli.SetConfig(map[string]any{
				"protocol": "http",
				"ip":       testcommon.ExtractAfterProtocol(server.URL),
			})

			// 执行测试
			got, err := cli.GetCheckProgress(tt.input)

			// 3. 验证结果
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.wantProgress, got)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantProgress, got)
			}
		})
	}
}

func TestGetRetestResult(t *testing.T) {
	// 1. 准备测试数据
	tests := []struct {
		name       string
		response   string
		statusCode int
		input      []string
		wantErr    bool
		wantData   struct {
			TaskId  int64
			Running bool
			Result  string
		}
	}{
		{
			name: "successful result retrieval",
			response: `{
				"data": {
					"task_id": 123,
					"running": false,
					"result": "FIXED",
					"created_time": "2024-01-01 12:00:00"
				}
			}`,
			statusCode: http.StatusOK,
			input:      []string{"123"},
			wantErr:    false,
			wantData: struct {
				TaskId  int64
				Running bool
				Result  string
			}{
				TaskId:  123,
				Running: false,
				Result:  "FIXED",
			},
		},
		{
			name: "task still running",
			response: `{
				"data": {
					"task_id": 456,
					"running": true,
					"result": "",
					"created_time": "2024-01-01 12:00:00"
				}
			}`,
			statusCode: http.StatusOK,
			input:      []string{"456"},
			wantErr:    false,
			wantData: struct {
				TaskId  int64
				Running bool
				Result  string
			}{
				TaskId:  456,
				Running: true,
				Result:  "",
			},
		},
		{
			name:       "http error status",
			response:   `{"message": "not found"}`,
			statusCode: http.StatusNotFound,
			input:      []string{"999"},
			wantErr:    true,
		},
	}

	// 2. 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置Mock服务器
			server := testcommon.SetupMockServer(tt.response, tt.statusCode)
			defer server.Close()

			// 创建测试客户端
			cli := New()
			cli.SetConfig(map[string]any{
				"protocol": "http",
				"ip":       testcommon.ExtractAfterProtocol(server.URL),
			})

			// 执行测试
			got, err := cli.getRetestResult(tt.input)

			// 3. 验证结果
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, got)
				assert.Equal(t, tt.wantData.TaskId, got.Data.TaskId)
				assert.Equal(t, tt.wantData.Running, got.Data.Running)
				assert.Equal(t, tt.wantData.Result, got.Data.Result)
			}
		})
	}
}
