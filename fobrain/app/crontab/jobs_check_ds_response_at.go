package crontab

import (
	"context"
	"fmt"
	"fobrain/fobrain/logs"
	"fobrain/models/elastic/assets"
	"fobrain/models/elastic/staff"
	mcron "fobrain/models/mysql/crontab"
	"fobrain/models/mysql/system_configs"
	"strconv"
	"time"
)

// 检查数据源最后一次响应时间 #4481
// 如果某条数据最后的数据源上报时间大于设置的阈值，则把资产置为离线，人员置为离职
func (j *Job) JobsCheckDsResponseAt(_ *Cron, _ *mcron.CrontabModel) string {
	logger := logs.GetLogger()
	// 获取开关配置
	enable, err := system_configs.NewSystemConfigs().GetConfig("ds_response_at_enable")
	if err != nil {
		msg := fmt.Sprint("获取开关配置失败", err)
		logger.Error(msg)
		return msg
	}
	if enable == "false" {
		msg := "数据源最后响应时间检查开关关闭，跳过"
		logger.Info(msg)
		return msg
	}
	// 获取配置阈值
	threshold, err := system_configs.NewSystemConfigs().GetConfig("ds_response_at_threshold")
	if err != nil {
		msg := fmt.Sprint("获取配置阈值失败", err)
		logger.Error(msg)
		return msg
	}
	// 判断阈值是否为整数
	if threshold == "" {
		msg := "阈值为空"
		logger.Error(msg)
		return msg
	}
	thresholdInt, err := strconv.Atoi(threshold)
	if err != nil {
		msg := fmt.Sprint("阈值不是整数", err)
		logger.Error(msg)
		return msg
	}
	// 将阈值转换为timeDuration
	thresholdDuration := time.Duration(thresholdInt) * 24 * time.Hour
	// 检查资产数据源最后响应时间，将超过阈值的资产设置为离线
	if _, err := assets.NewAssets().UpdateStatusByDataSourceResponseAt(context.Background(), thresholdDuration); err != nil {
		logger.Error("更新资产状态失败", err)
	} else {
		logger.Info("资产状态更新完成")
	}
	// 检查人员数据源最后响应时间，将超过阈值的人员设置为离职
	if _, err := staff.NewStaff().UpdateStatusByDataSourceResponseAt(context.Background(), thresholdDuration); err != nil {
		logger.Error("更新人员状态失败", err)
	} else {
		logger.Info("人员状态更新完成")
	}
	return ""
}

func RegJobsCheckDsResponseAt() *mcron.CrontabModel {
	// 每天凌晨4点执行，与大部分数据源的定时同步错开
	return NewSchedulerJob("定时检查数据源最后响应时间", "0 4 * * *", "JobsCheckDsResponseAt", "")
}
