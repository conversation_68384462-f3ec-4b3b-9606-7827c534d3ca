package foeye

import (
	"context"
	"fmt"
	"fobrain/fobrain/app/services/rule_infos_level"
	"fobrain/fobrain/app/services/sync/proactive_public"
	"fobrain/initialize/mysql"
	pb "fobrain/mergeService/proto"
	"fobrain/models/mysql/data_sync_task"
	"fobrain/pkg/utils/handle_es_bulk_error"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"

	"fobrain/fobrain/app/services/node/foeye"
	"fobrain/fobrain/common/constant"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	assetses "fobrain/models/elastic/assets"
	"fobrain/models/elastic/poc"
	foeyees "fobrain/models/elastic/source/foeye"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/proactive_task_node_relations"
	"fobrain/models/mysql/task"

	"github.com/olivere/elastic/v7"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
)

type SyncFoeyeTask struct {
	cli                       *foeye.Foeye
	node                      *data_source.Node
	proactiveTaskNodeRelation *proactive_task_node_relations.ProactiveTaskNodeRelations
	defaultPage               int
	proactiveTask             *task.ProactiveTasks
}

// NewSyncFoeyeTask
// @Summary 同步 Foeye 任务维度数据
//
//	@Param taskInfo map true map[string]any{
//		"task_id": "proactive_task 表的Id, 通过proactive task 表中的任务类型来处理需要同步资产还是同步漏洞，还是两者一起同步",
//		"proactive_task_node_relation_id": "节点ID",
//	}
func NewSyncFoeyeTask(node *data_source.Node, taskInfo map[string]any) error {
	cli := foeye.NewFoeye()
	err := cli.SetNode(node.Id)
	if err != nil {
		return err
	}

	proactiveTask, err := task.NewProactiveTasks().FindById(int64(taskInfo["task_id"].(uint64)))
	if err != nil {
		return err
	}

	proactiveTaskNodeRelation, err := proactive_task_node_relations.FindById(int64(taskInfo["proactive_task_node_relation_id"].(int)))
	if err != nil {
		return err
	}

	sync := SyncFoeyeTask{
		cli:                       cli,
		proactiveTask:             proactiveTask,
		proactiveTaskNodeRelation: proactiveTaskNodeRelation,
		defaultPage:               constant.DefaultPage,
		node:                      node,
	}

	// 同步资产 或者 漏洞
	switch proactiveTask.TaskType {
	case 1:
		go sync.syncTaskAsset(sync.defaultPage, proactiveTaskNodeRelation.NodeTaskId)
	case 2:
		go sync.syncTaskThreat(sync.defaultPage, proactiveTaskNodeRelation.NodeTaskId)
	default:
		go sync.syncTaskAsset(sync.defaultPage, proactiveTaskNodeRelation.NodeTaskId)
		go sync.syncTaskThreat(sync.defaultPage, proactiveTaskNodeRelation.NodeTaskId)
	}

	proactiveTask.SyncStatus = task.SyncStatusSyncing
	task.NewProactiveTasks().Save(proactiveTask)

	return nil
}

// syncTaskAsset
// @Summary 同步 foeye 任务资产
func (sync SyncFoeyeTask) syncTaskAsset(page int, nodeTaskId int) {
	assetsTaskIndex := foeyees.NewFoeyeTaskAssetsModel().IndexName()
	assetsFinishIndex := foeyees.NewFoeyeFinishedAssetsModel().IndexName()
	isV2 := sync.cli.IsFoeyeV2()
	if isV2 {
		assetsTaskIndex = foeyees.FoeyeV2SourceTaskAssetsIndex
		assetsFinishIndex = foeyees.FoeyeV2FinishedAssets
	}
	// 获取任务资产
	assets, err := sync.cli.GetTaskAssets(page, constant.DefaultSize, nodeTaskId)
	if err != nil {
		logs.GetLogger().Infof("sync foeye task asset error: %v", err.Error())
		return
	}

	// 获取资产总数
	total := gjson.Get(assets, "total").Int()

	// 如果是第一页，更新资产总数
	if page == constant.DefaultPage {
		if err := proactive_task_node_relations.UpdateAssetSum(sync.proactiveTaskNodeRelation.Id, int(total)); err != nil {
			logs.GetLogger().Infof("sync foeye task asset - save proactiveTaskNodeRelation Asset sum error: %v", err.Error())
		}
	}

	// 解析数据
	result := gjson.Get(assets, "data.info")
	if total <= 0 {
		proactive_task_node_relations.UpdateNodeDataSyncAssetStatus(sync.proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncSuccessStatus)
		return
	}

	assetTaskBulkRequest := es.GetEsClient().Bulk()
	assetFinishTaskBulkRequest := es.GetEsClient().Bulk()
	assetProcessBulkRequest := es.GetEsClient().Bulk()

	resultCount := 0
	// 遍历资产信息，逐个处理
	result.ForEach(func(key, value gjson.Result) bool {
		resultCount++
		data, _ := sjson.Set(value.String(), "node_id", sync.node.Id)
		data, _ = sjson.Set(data, "area_id", sync.node.AreaId)

		assetId := strings.ReplaceAll(uuid.New().String(), "-", "")
		sourceTask := sync.handleSourceTask(data_sync_task.SyncAsset, value.String(), assetId)
		assetTaskReq := elastic.NewBulkIndexRequest().Index(assetsTaskIndex).Id(assetId).Doc(sourceTask)
		assetTaskBulkRequest = assetTaskBulkRequest.Add(assetTaskReq)

		assetTaskId := strings.ReplaceAll(uuid.New().String(), "-", "")
		data, _ = sjson.Set(data, "proactive_task_id", sync.proactiveTask.Id)
		assetFinishTaskReq := elastic.NewBulkIndexRequest().Index(assetsFinishIndex).Id(assetTaskId).Doc(data)
		assetFinishTaskBulkRequest = assetFinishTaskBulkRequest.Add(assetFinishTaskReq)

		assetProcessId := strings.ReplaceAll(uuid.New().String(), "-", "")
		assetProcessReq := prepareAssetProcessRequest(isV2, sync, assetProcessId, value)
		assetProcessBulkRequest = assetProcessBulkRequest.Add(assetProcessReq)

		return true
	})

	// 执行 Bulk 请求并处理错误
	if err := processBulkRequests(assetProcessBulkRequest, assetTaskBulkRequest, assetFinishTaskBulkRequest); err != nil {
		logs.GetLogger().Errorf("Foeye syncAssets bulk request error: %s", err)
		proactive_task_node_relations.UpdateNodeDataSyncAssetStatus(sync.proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncFailStatus)
		return
	}

	// 更新成功状态
	proactive_task_node_relations.UpdateNodeDataSyncAssetStatus(sync.proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncSuccessStatus)
	logs.GetLogger().Info("Foeye task syncAssets success")

	// 递归处理分页
	// resultCount 为0 表示当前页没有数据,foeye接口特点：如果请求的页码大于实际的页码，会返回空数据
	if page*constant.DefaultSize < int(total) && total > 0 && resultCount > 0 {
		sync.syncTaskAsset(page+1, nodeTaskId)
	} else {
		// 发送任务结束标识
		mergeResult, err := pb.GetProtoClient().TriggerMergeForAsset(context.Background(), &pb.TriggerMergeForAssetRequest{
			TriggerMergeBaseRequest: &pb.TriggerMergeBaseRequest{
				SourceId:     sync.node.SourceId,
				NodeId:       sync.node.Id,
				TriggerEvent: "主动扫描任务结束",
				TaskId:       fmt.Sprint(sync.proactiveTask.Id + mysql.ProactiveTaskIdAdd),
				ChildTaskId:  fmt.Sprint(sync.proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd),
			},
			DataRangeByTask: &pb.DataRangeByTask{
				TaskId:      fmt.Sprint(sync.proactiveTask.Id + mysql.ProactiveTaskIdAdd),
				ChildTaskId: fmt.Sprint(sync.proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd),
				NodeId:      sync.node.Id,
			},
		}, pb.ClientWithAddress)
		if err != nil {
			logs.GetLogger().Errorf("foeye proactive task end Send to TriggerMergeRequest error:%v", err)
		}
		if !mergeResult.Success {
			logs.GetLogger().Errorf("foeye proactive task end Send to TriggerMergeRequest Message error:%v", mergeResult.Message)
		}
	}
}

func (sync SyncFoeyeTask) handleSourceTask(syncType int64, sourceTask, assetId string) string {
	if syncType == data_sync_task.SyncAsset {
		sourceTask, _ = sjson.Set(sourceTask, "asset_task_id", assetId)
	} else if syncType == data_sync_task.SyncThreat {
		sourceTask, _ = sjson.Set(sourceTask, "poc_task_id", assetId)
		sourceTask, _ = sjson.Set(sourceTask, "original_id", gjson.Get(sourceTask, "id").String())
	}

	sourceTask, _ = sjson.Set(sourceTask, "source", sync.node.SourceId)
	sourceTask, _ = sjson.Set(sourceTask, "source_id", sync.node.SourceId)
	sourceTask, _ = sjson.Set(sourceTask, "node_id", sync.node.Id)
	sourceTask, _ = sjson.Set(sourceTask, "node", sync.node.Id)
	sourceTask, _ = sjson.Set(sourceTask, "area_id", sync.node.AreaId)
	sourceTask, _ = sjson.Set(sourceTask, "area", sync.node.AreaId)
	sourceTask, _ = sjson.Set(sourceTask, "task_id", sync.proactiveTask.Id+mysql.ProactiveTaskIdAdd)
	sourceTask, _ = sjson.Set(sourceTask, "child_task_id", sync.proactiveTaskNodeRelation.Id+mysql.ProactiveTaskIdAdd)
	sourceTask, _ = sjson.Set(sourceTask, "sync_created_at", localtime.NewLocalTime(time.Now()))
	sourceTask, _ = sjson.Set(sourceTask, "sync_updated_at", localtime.NewLocalTime(time.Now()))
	return sourceTask
}

// 准备资产处理请求
func prepareAssetProcessRequest(isV2 bool, sync SyncFoeyeTask, assetProcessId string, value gjson.Result) *elastic.BulkIndexRequest {
	assetProcess := assetses.NewProcessAssetsModel()
	assetProcess.Id = assetProcessId
	assetProcess.Area = int(sync.node.AreaId)
	assetProcess.Node = sync.node.Id
	assetProcess.Source = sync.node.SourceId
	assetProcess.TaskId = sync.proactiveTask.Id + mysql.ProactiveTaskIdAdd
	assetProcess.ChildTaskId = sync.proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd
	assetProcess.Ip = gjson.Get(value.String(), "ip").String()
	assetProcess.Os = gjson.Get(value.String(), "os").String()
	assetProcess.Mac = gjson.Get(value.String(), "mac").String()
	assetProcess.BusinessSystem = gjson.Get(value.String(), "business_app").String()
	assetProcess.Oper = gjson.Get(value.String(), "username").String()
	assetProcess.BusinessOwner = gjson.Get(value.String(), "username").String()
	assetProcess.MachineRoom = gjson.Get(value.String(), "computer_room").String()
	assetProcess.Status = proactive_public.GetStatus(gjson.Get(value.String(), "state").Int())

	// 处理端口和产品信息
	if isV2 {
		assetProcess.RuleInfos, assetProcess.Ports = ExtractPortsAndProductsV2(value)
	} else {
		assetProcess.RuleInfos, assetProcess.Ports = extractPortsAndProducts(value)
	}

	// 处理时间
	assetProcess.CreatedAt = proactive_public.GetCreateTime(value)
	assetProcess.UpdatedAt = proactive_public.GetUpdateTime(value)

	return elastic.NewBulkIndexRequest().Index(assetProcess.IndexName()).Id(assetProcessId).Doc(assetProcess)
}

// 提取端口和产品信息
func extractPortsAndProducts(value gjson.Result) ([]*assetses.RuleInfo, []*assetses.PortInfo) {
	var ruleInfos []*assetses.RuleInfo
	var ports []*assetses.PortInfo

	gjson.Get(value.String(), "port_list").ForEach(func(k, v gjson.Result) bool {
		var portInfo assetses.PortInfo
		gjson.Get(v.String(), "rule_info").ForEach(func(key, val gjson.Result) bool {
			var ruleInfo assetses.RuleInfo
			var description string
			var tags []string
			//组件名称
			if gjson.Get(val.String(), "name").String() != "" {
				ruleInfo.Product = gjson.Get(val.String(), "name").String()
				ruleInfo.Level = rule_infos_level.GetRuleInfoLevel(ruleInfo.Product)
			}
			//组件分类
			if gjson.Get(val.String(), "description").String() != "" {
				description = gjson.Get(val.String(), "description").String()
				tags = strings.Split(description, "|")
				if len(tags) > 1 {
					ruleInfo.FirstTag = tags[1]
					ruleInfo.SecondTag = tags[0]
				}
			}
			if ruleInfo.Product != "" {
				ruleInfos = append(ruleInfos, &ruleInfo)
			}
			return true
		})
		portInfo.Port = int(v.Get("port").Int())
		portInfo.Protocol = v.Get("protocol").String()
		portInfo.Title = v.Get("title").String()
		portInfo.Url = v.Get("link_url").String()
		ports = append(ports, &portInfo)
		return true
	})

	return ruleInfos, ports
}

// 提取端口和产品信息
func ExtractPortsAndProductsV2(value gjson.Result) ([]*assetses.RuleInfo, []*assetses.PortInfo) {
	var ruleInfos []*assetses.RuleInfo
	var ports []*assetses.PortInfo

	gjson.Get(value.String(), "port_list").ForEach(func(key, value gjson.Result) bool {
		var portInfo assetses.PortInfo
		portInfo.Port = int(value.Get("port").Int())
		portInfo.Protocol = value.Get("protocol").String()
		ports = append(ports, &portInfo)
		return true
	})

	gjson.Get(value.String(), "rule_infos").ForEach(func(key, value gjson.Result) bool {
		var ruleInfo assetses.RuleInfo
		ruleInfo.Product = gjson.Get(value.String(), "title").String()
		levelCode := gjson.Get(value.String(), "level_code").Int()
		ruleInfo.Level = strconv.FormatInt(levelCode, 10)
		ruleInfo.FirstTag = gjson.Get(value.String(), "first_cat_tag").String()
		ruleInfo.SecondTag = gjson.Get(value.String(), "second_cat_tag").String()
		ruleInfos = append(ruleInfos, &ruleInfo)
		return true
	})

	return ruleInfos, ports
}

// 处理 Bulk 请求并返回错误
func processBulkRequests(bulkRequests ...*es.SafeBulkService) error {
	for _, bulkRequest := range bulkRequests {
		if bulkRequest == nil {
			continue
		}
		if bulkRequest.NumberOfActions() == 0 {
			continue
		}
		bulkReq, err := bulkRequest.Refresh("true").Do(context.Background())
		if err != nil {
			return err
		}
		if bulkReq.Errors {
			return proactive_public.ExtractBulkErrors(bulkReq)
		}
	}
	return nil
}

// syncTaskThreat
// @Summary 同步 foeye 任务漏洞
func (sync SyncFoeyeTask) syncTaskThreat(page int, nodeTaskId int) {
	threatsIndex := foeyees.NewFoeyeTaskThreatsModel().IndexName()
	threatTaskIndex := foeyees.NewFoeyeTaskThreatsModel().IndexName()
	threatsFinishIndex := foeyees.NewFoeyeFinishedThreatsModel().IndexName()
	if sync.cli.IsFoeyeV2() {
		threatTaskIndex = foeyees.FoeyeV2SourceTaskThreatsIndex
		threatsFinishIndex = foeyees.FoeyeV2FinishedThreats
	}

	// 获取漏洞数据
	threats, err := sync.cli.GetTaskThreats(page, constant.DefaultSize, nodeTaskId)
	if err != nil {
		logs.GetLogger().Errorf("Foeye syncThreats GetThreats err:%s", err)
		return
	}

	total := gjson.Get(threats, "total").Int() // 数据总量
	result := gjson.Get(threats, "data.info")

	// 如果是第一页，更新漏洞总数
	if page == constant.DefaultPage {
		if err := proactive_task_node_relations.UpdateThreatSum(sync.proactiveTaskNodeRelation.Id, int(total)); err != nil {
			logs.GetLogger().Errorf("sync foeye task threat - save proactiveTaskNodeRelation Asset sum error: %v", err.Error())
		}
	}

	if total <= 0 {
		proactive_task_node_relations.UpdateNodeDataSyncThreatStatus(sync.proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncSuccessStatus)
		return
	}
	if result.Exists() {
		threatIds := make([]map[string]interface{}, 0)
		threatBulkRequest := es.GetEsClient().Bulk()
		sourceThreatTaskBulkRequest := es.GetEsClient().Bulk()
		threatProcessBulkRequest := es.GetEsClient().Bulk()
		threatTaskBulkRequest := es.GetEsClient().Bulk()

		// 遍历 _id 字段，并根据 id 添加新的字段数据
		var num = 0
		result.ForEach(func(key, value gjson.Result) bool {
			data, _ := sjson.Set(value.String(), "node_id", sync.node.Id)
			data, _ = sjson.Set(data, "area_id", sync.node.AreaId)

			// 写入资产库
			threatId := strings.ReplaceAll(uuid.New().String(), "-", "")
			threatReq := elastic.NewBulkIndexRequest().Index(threatsIndex).Id(threatId).Doc(data)
			threatBulkRequest = threatBulkRequest.Add(threatReq)

			// 生成漏洞处理模型
			threatProcess := generateThreatProcess(sync, value, threatId)
			threatProcessReq := elastic.NewBulkIndexRequest().Index(threatProcess.IndexName()).Id(threatId).Doc(threatProcess)
			threatProcessBulkRequest = threatProcessBulkRequest.Add(threatProcessReq)

			sourceTask := sync.handleSourceTask(data_sync_task.SyncThreat, value.String(), threatId)
			sourceTask, _ = sjson.Set(sourceTask, "id", threatId)
			sourceThreatTaskReq := elastic.NewBulkIndexRequest().Index(threatTaskIndex).Id(threatId).Doc(sourceTask)
			sourceThreatTaskBulkRequest = sourceThreatTaskBulkRequest.Add(sourceThreatTaskReq)

			threatTaskId := strings.ReplaceAll(uuid.New().String(), "-", "")
			data, _ = sjson.Set(data, "proactive_task_id", sync.proactiveTask.Id)
			data, _ = sjson.Set(data, "level", threatProcess.Level)
			threatTaskReq := elastic.NewBulkIndexRequest().Index(threatsFinishIndex).Id(threatTaskId).Doc(data)
			threatTaskBulkRequest = threatTaskBulkRequest.Add(threatTaskReq)

			threatIds = append(threatIds, map[string]interface{}{"id": threatId, "is_poc": poc.YesPoc, "task_id": fmt.Sprintf("%s_%s", "task_threat_sync", strconv.Itoa(int(sync.proactiveTask.Id)))})
			num = num + 1
			return true
		})
		if num > 0 {
			if err := sync.handleBulkRequest(threatProcessBulkRequest, "threatProcessBulkRequest"); err != nil {
				return
			}

			if err := sync.handleBulkRequest(threatTaskBulkRequest, "threatTaskBulkRequest"); err != nil {
				return
			}

			if err := sync.handleBulkRequest(threatBulkRequest, "threatBulkRequest"); err != nil {
				return
			}
			if err := sync.handleBulkRequest(sourceThreatTaskBulkRequest, "sourceThreatTaskBulkRequest"); err != nil {
				return
			}
		}

		//if successCount := rq.Push(cfg.LoadQueue().VulnMergeQueue, threatIds); successCount > 0 {
		//	logs.GetSyncLogger().Infof("Foeye syncThreats successCount:%d, expectedCount:%d", successCount, len(threatIds))
		//}
	}
	// 分页继续同步
	if page*constant.DefaultSize <= int(total) && total > 0 {
		sync.syncTaskThreat(page+1, nodeTaskId)
	} else {
		proactive_task_node_relations.UpdateNodeDataSyncThreatStatus(sync.proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncSuccessStatus)
		// 发送任务结束标识
		mergeResult, err := pb.GetProtoClient().TriggerMergeForVuln(context.Background(), &pb.TriggerMergeForVulnRequest{
			TriggerMergeBaseRequest: &pb.TriggerMergeBaseRequest{
				SourceId:     sync.node.SourceId,
				NodeId:       sync.node.Id,
				TriggerEvent: "主动扫描任务结束",
				TaskId:       fmt.Sprint(sync.proactiveTask.Id + mysql.ProactiveTaskIdAdd),
				ChildTaskId:  fmt.Sprint(sync.proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd),
			},
			DataRangeByTask: &pb.DataRangeByTask{
				TaskId:      fmt.Sprint(sync.proactiveTask.Id + mysql.ProactiveTaskIdAdd),
				ChildTaskId: fmt.Sprint(sync.proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd),
				NodeId:      sync.node.Id,
			},
		}, pb.ClientWithAddress)
		if err != nil {
			logs.GetLogger().Errorf("proactive task end Send to TriggerMergeRequest error:%v", err)
		}
		if !mergeResult.Success {
			logs.GetLogger().Errorf("proactive task end Send to TriggerMergeRequest Message error:%v", mergeResult.Message)
		}
	}
}

// generateThreatProcess 生成 threatProcess 结构体并设置其属性
func generateThreatProcess(sync SyncFoeyeTask, value gjson.Result, threatId string) poc.ProcessPoc {
	threatProcess := poc.NewProcessPocModel()
	threatProcess.Id = threatId
	threatProcess.TaskId = sync.proactiveTask.Id + mysql.ProactiveTaskIdAdd
	threatProcess.ChildTaskId = sync.proactiveTaskNodeRelation.Id + mysql.ProactiveTaskIdAdd
	threatProcess.Area = sync.node.AreaId
	threatProcess.Node = sync.node.Id
	threatProcess.Source = sync.node.SourceId
	threatProcess.IsPoc = poc.YesPoc
	threatProcess.Ip = gjson.Get(value.String(), "ip").String()
	threatProcess.Port = int(gjson.Get(value.String(), "port").Int())
	threatProcess.Url = gjson.Get(value.String(), "url").String()
	threatProcess.Name = gjson.Get(value.String(), "common_title").String()
	threatProcess.Cve = gjson.Get(value.String(), "cveId").String()
	threatProcess.HasExp = int(gjson.Get(value.String(), "has_exp").Int())
	threatProcess.Status = int(gjson.Get(value.String(), "state").Int())
	threatProcess.Describe = gjson.Get(value.String(), "common_description").String()
	threatProcess.Details = gjson.Get(value.String(), "common_impact").String()
	threatProcess.Suggestions = gjson.Get(value.String(), "recommandation").String()
	threatProcess.LastResponseAt = proactive_public.ParseTimeOrNow(gjson.Get(value.String(), "last_response").String())
	threatProcess.OriginalId = gjson.Get(value.String(), "id").String()
	threatProcess.Level = proactive_public.MapLevel(gjson.Get(value.String(), "level").Int())
	threatProcess.VulType = gjson.Get(value.String(), "vulType").String()

	ct := gjson.Get(value.String(), "createtime").String()
	threatProcess.CreatedAt = proactive_public.ParseTimeOrNow(ct)

	updateTime := gjson.Get(value.String(), "lastupdatetime").String()
	threatProcess.UpdatedAt = proactive_public.ParseTimeOrNow(updateTime)

	return *threatProcess
}

// handleBulkRequest 处理批量请求并检查错误
func (sync SyncFoeyeTask) handleBulkRequest(bulkRequest *es.SafeBulkService, requestName string) error {
	bulkResp, err := bulkRequest.Refresh("true").Do(context.Background())
	if err != nil || bulkResp.Errors {
		errString := handle_es_bulk_error.HandleBulkResp(requestName, err, bulkResp)

		proactive_task_node_relations.UpdateNodeDataSyncThreatStatus(sync.proactiveTaskNodeRelation.Id, proactive_task_node_relations.SyncFailStatus)
		return fmt.Errorf("%s Do err: %v", requestName, errString)
	}
	return nil
}
