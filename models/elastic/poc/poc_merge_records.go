package poc

import (
	"context"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	es_model "fobrain/models/elastic"
	"fobrain/models/mysql/strategy"

	"github.com/olivere/elastic/v7"
)

type (
	MergeRecords struct {
		*es.BaseModel     `json:"-"`
		Id                string                   `json:"id"`                  // id
		PocId             string                   `json:"poc_id"`              // 结果表ID
		MergeResult       string                   `json:"merge_result"`        // 融合结果数据
		MergeResultSource string                   `json:"merge_result_source"` // 融合结果数据来源
		PocRecordId       string                   `json:"poc_record_id"`       // 结果表ID
		Status            int                      `json:"status"`              // 状态 成功1 失败2
		Message           string                   `json:"message"`             // 失败信息
		ProcessIds        []string                 `json:"process_ids"`         // 过程表ids
		SourceIds         []uint64                 `json:"source_ids"`          // 数据源ids
		NodeIds           []uint64                 `json:"node_ids"`            // 节点ids
		PocTaskIds        []string                 `json:"poc_task_ids"`        // 漏洞任务ids
		IsPoc             int                      `json:"is_poc"`              // 是否poc漏洞 1是 2否
		Cve               string                   `json:"cve"`                 // cve编号
		Cnvd              string                   `json:"cnvd"`                // cnvd编号
		Cnnvd             string                   `json:"cnnvd"`               // cnnvd编号
		OriginalIds       []string                 `json:"original_ids"`        // 原始ID
		Strategies        []*strategy.Strategy     `json:"strategies"`          // 融合规则
		CreatedAt         *localtime.Time          `json:"created_at"`          // 创建时间
		UpdatedAt         *localtime.Time          `json:"updated_at"`          // 更新时间
		MergeMode         string                   `json:"merge_mode"`          // 融合模式,auto:自动模式,manual:手动模式(修改策略立即融合),calibration:手动校准
		BatchNo           string                   `json:"batch_no"`            // 批次号,仅用于手动模式(修改策略立即融合)
		PocIds            []string                 `json:"poc_ids"`             // 受影响的资产 ID，用于手动校准
		TiggerSourceId    uint64                   `json:"tigger_source_id"`    // 触发源ID
		FieldValInfoList  []*es_model.FieldValInfo `json:"field_val_info_list"` // 字段采信信息
	}
)

// IndexName 索引名
func (p *MergeRecords) IndexName() string {
	return "poc_merge_record"
}

func (p *MergeRecords) String() string {
	if p == nil {
		return ""
	}
	return ""
}

func NewMergeRecordsModel() *MergeRecords {
	return &MergeRecords{}
}

func (p *MergeRecords) Insert(ctx context.Context, data *MergeRecords) (*elastic.IndexResponse, error) {
	rsp, err := p.GetClient().Index().Index(p.IndexName()).BodyJson(data).Do(ctx)
	return rsp, err
}
