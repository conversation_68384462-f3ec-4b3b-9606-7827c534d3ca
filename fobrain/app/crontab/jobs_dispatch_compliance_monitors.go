package crontab

import (
	"fmt"
	"strconv"
	"time"

	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/mysql"
	"fobrain/models/mysql/compliance_monitor"
	"fobrain/models/mysql/compliance_monitor_task"
	mcron "fobrain/models/mysql/crontab"
)

// DispatchComplianceMonitors 合规监测周期任务下发
func (j *Job) DispatchComplianceMonitors(_ *Cron, _ *mcron.CrontabModel) string {
	//获取所有启用中的周期任务
	var opts []mysql.HandleFunc

	opts = append(opts, mysql.WithWhere("status = ?", compliance_monitor.StatusEnable)) //status 状态：1 启用 2 禁用
	opts = append(opts, mysql.WithWhere("event in (?)", []int{
		//event 触发类型: 1:立即执行 2:仅执行一次 3:每天 4:每周 5:每月
		compliance_monitor.EventEveryDay,
		compliance_monitor.EventEveryWeek,
		compliance_monitor.EventEveryMonth,
	}))

	list, _, err := compliance_monitor.NewComplianceMonitorModel().List(0, 0, opts...)
	if err != nil {
		logs.GetLogger().Errorf("crontab DispatchComplianceMonitors List err:%s", err)
		return fmt.Sprintf("合规监测周期任务下发 > 获取合规监测列表 Error：%s ", err)
	}

	for _, monitor := range list {
		// 获取开始时间
		startAt, err := compliance_monitor.NewComplianceMonitorModel().GetLocalTimeByEvent(monitor.Event, monitor.Date, monitor.Time)
		if err != nil {
			logs.GetLogger().Errorf("crontab DispatchComplianceMonitors GetLocalTimeByEvent err:%s", err)
			return fmt.Sprintf("合规监测周期任务下发 > 获取周期任务开始时间 Error：%s ", err)
		}

		count, err := compliance_monitor_task.NewComplianceMonitorTaskModel().Count(mysql.WithWhere("compliance_monitor_id = ?", monitor.Id), mysql.WithWhere("start_at = ?", startAt))
		if err != nil {
			logs.GetLogger().Errorf("crontab DispatchComplianceMonitors Count err:%s", err)
			return fmt.Sprintf("合规监测周期任务下发 > 统计周期任务是否被下发 Error：%s ", err)
		}

		//周期任务被下发了
		if count > 0 {
			continue
		}

		switch monitor.Event {
		case compliance_monitor.EventEveryDay:

			if localtime.NewLocalTime(time.Now()).String() > startAt.String() {
				err = compliance_monitor_task.NewComplianceMonitorTaskModel().Create(&compliance_monitor_task.ComplianceMonitorTask{
					ComplianceMonitorId: monitor.Id,
					StartAt:             &startAt,
					Status:              compliance_monitor_task.StatusNotStart,
				})

				if err != nil {
					logs.GetLogger().Errorf("crontab DispatchComplianceMonitors Create EventEveryDay err:%s", err)
					return fmt.Sprintf("合规监测周期任务下发 > 创建每天任务 Error：%s ", err)
				}

			}
		case compliance_monitor.EventEveryWeek:

			if strconv.Itoa(int(time.Now().Weekday())) == monitor.Date && localtime.NewLocalTime(time.Now()).String() > startAt.String() {
				err = compliance_monitor_task.NewComplianceMonitorTaskModel().Create(&compliance_monitor_task.ComplianceMonitorTask{
					ComplianceMonitorId: monitor.Id,
					StartAt:             &startAt,
					Status:              compliance_monitor_task.StatusNotStart,
				})
				if err != nil {
					logs.GetLogger().Errorf("crontab DispatchComplianceMonitors Create EventEveryDay err:%s", err)
					return fmt.Sprintf("合规监测周期任务下发 > 创建每周任务 Error：%s ", err)
				}

			}
		case compliance_monitor.EventEveryMonth:

			if strconv.Itoa(time.Now().Day()) == monitor.Date && localtime.NewLocalTime(time.Now()).String() > startAt.String() {
				err = compliance_monitor_task.NewComplianceMonitorTaskModel().Create(&compliance_monitor_task.ComplianceMonitorTask{
					ComplianceMonitorId: monitor.Id,
					StartAt:             &startAt,
					Status:              compliance_monitor_task.StatusNotStart,
				})
				if err != nil {
					logs.GetLogger().Errorf("crontab DispatchComplianceMonitors Create EventEveryDay err:%s", err)
					return fmt.Sprintf("合规监测周期任务下发 > 创建每月任务 Error：%s ", err)
				}

			}
		}
	}

	return ""
}

// RegisterDispatchComplianceMonitors 注册合规监测周期任务下发定时任务
func RegisterDispatchComplianceMonitors() *mcron.CrontabModel {
	return NewSchedulerJob("DispatchComplianceMonitors", "01 * * * * *", "DispatchComplianceMonitors", "")
}
