package seeders

import (
	"testing"

	"fobrain/models/mysql/strategy"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

func TestGetDisplayName(t *testing.T) {
	// Happy path tests
	tests := []struct {
		businessType string
		fieldName    string
		expected     string
	}{
		{"asset_merge", "Ip", "IP"},
		{"asset_merge", "IpType", "IP类型"},
		{"asset_merge", "IpSegment", "IP段"},
		{"asset_merge", "MapIp", "映射IP"},
		{"asset_merge", "Hostname", "主机名"},
		{"asset_merge", "EthName", "网卡名"},
		{"asset_merge", "Os", "操作系统"},
		{"asset_merge", "Kernel", "内核"},
		{"asset_merge", "DeviceId", "设备ID"},
		{"asset_merge", "DeviceName", "设备名"},
		{"asset_merge", "Model", "型号"},
		{"asset_merge", "Maker", "制造商"},
		{"asset_merge", "Sn", "序列号"},
		{"asset_merge", "Mac", "MAC地址"},
		{"asset_merge", "Product", "组件"},
		{"asset_merge", "Business", "业务系统"},
		{"asset_merge", "BusinessSystem", "业务系统名称"},
		{"asset_merge", "Oper", "运维人员"},
		{"asset_merge", "BusinessOwner", "业务负责人"},
		{"asset_merge", "MachineRoom", "机房"},
		{"asset_merge", "Status", "资产状态"},
		{"asset_merge", "Ports", "端口"},
		{"asset_merge", "MemorySize", "内存大小"},
		{"asset_merge", "MemoryUsageRate", "内存使用率"},
		{"asset_merge", "CpuMaker", "CPU厂商"},
		{"asset_merge", "CpuBrand", "CPU品牌"},
		{"asset_merge", "CpuCount", "CPU数量"},
		{"asset_merge", "DiskCount", "磁盘数量"},
		{"asset_merge", "DiskSize", "磁盘大小"},
		{"asset_merge", "DiskUsageRate", "磁盘使用率"},
		{"asset_merge", "LoadAverage", "负载"},
		{"asset_merge", "IsPublic", "是否外网IP"},
		{"asset_merge", "JarPackageInfo", "jar包信息"},
		{"device_merge", "Ip", "IP"},
		{"device_merge", "Oper", "运维人员"},
		{"device_merge", "HostName", "主机名"},
		{"device_merge", "SN", "序列号"},
		{"device_merge", "Os", "操作系统"},
		{"device_merge", "Kernel", "内核"},
		{"device_merge", "Model", "型号"},
		{"device_merge", "Maker", "制造商"},
		{"device_merge", "Mac", "MAC地址"},
		{"device_merge", "MachineRoom", "机房"},
		{"device_merge", "MemorySize", "内存大小"},
		{"device_merge", "MemoryUsageRate", "内存使用率"},
		{"device_merge", "CpuMaker", "CPU厂商"},
		{"device_merge", "CpuBrand", "CPU品牌"},
		{"device_merge", "CpuCount", "CPU数量"},
		{"device_merge", "DiskCount", "磁盘数量"},
		{"device_merge", "DiskSize", "磁盘大小"},
		{"device_merge", "DiskUsageRate", "磁盘使用率"},
		{"device_merge", "LoadAverage", "负载"},
		{"vuln_merge", "Ip", "IP"},
		{"vuln_merge", "Ports", "端口"},
		{"vuln_merge", "IsPoc", "是否poc漏洞"},
		{"vuln_merge", "Url", "漏洞地址"},
		{"vuln_merge", "Level", "漏洞等级"},
		{"vuln_merge", "Name", "漏洞名称"},
		{"vuln_merge", "VulType", "漏洞类型"},
		{"vuln_merge", "Cve", "cve编号"},
		{"vuln_merge", "Cnvd", "cnvd编号"},
		{"vuln_merge", "Cnnvd", "cnnvd编号"},
		{"vuln_merge", "HasExp", "是否存在exp"},
		{"vuln_merge", "HasPoc", "是否存在poc"},
		{"vuln_merge", "Status", "漏洞流转状态"},
		{"vuln_merge", "Describe", "描述"},
		{"vuln_merge", "Details", "详情"},
		{"vuln_merge", "Hazard", "危险性"},
		{"vuln_merge", "Suggestions", "修复建议"},
		{"vuln_merge", "LastResponse", "最后一次响应"},
		{"person_merge", "Name", "姓名"},
		{"person_merge", "EnglishName", "英文名"},
		{"person_merge", "Title", "职位"},
		{"person_merge", "Mobile", "手机号"},
		{"person_merge", "Email", "邮箱"},
		{"person_merge", "Department", "部门"},
		{"person_merge", "Status", "在职状态"},
		{"person_merge", "Area", "区域"},
	}

	for _, test := range tests {
		result := getDisplayName(test.businessType, test.fieldName)
		if result != test.expected {
			t.Errorf("getDisplayName(%s, %s) = %s; want %s", test.businessType, test.fieldName, result, test.expected)
		}
	}

	// Edge case tests
	edgeTests := []struct {
		businessType string
		fieldName    string
		expected     string
	}{
		{"asset_merge", "UnknownField", "UnknownField"},
		{"vuln_merge", "UnknownField", "UnknownField"},
		{"person_merge", "UnknownField", "UnknownField"},
		{"unknown_type", "Name", "Name"},
		{"", "Name", "Name"},
		{"asset_merge", "", ""},
	}

	for _, test := range edgeTests {
		result := getDisplayName(test.businessType, test.fieldName)
		if result != test.expected {
			t.Errorf("getDisplayName(%s, %s) = %s; want %s", test.businessType, test.fieldName, result, test.expected)
		}
	}
}

func TestInitAssetMergeStrategy(t *testing.T) {
	// Test the happy path
	strategies := initAssetMergeStrategy()
	if len(strategies) != 24 {
		t.Errorf("Expected 24 strategies, got %d", len(strategies))
	}

	// Test the edge case for each field
	expectedFields := []string{"IpSegment", "Hostname", "EthName", "Os", "Kernel", "Model", "Maker", "Sn", "Mac", "Product", "Business", "Oper", "MachineRoom", "Ports", "MemorySize", "MemoryUsageRate", "CpuMaker", "CpuBrand", "CpuCount", "DiskCount", "DiskSize", "DiskUsageRate", "LoadAverage", "Status"}
	for i, field := range expectedFields {
		if strategies[i].FieldName != field {
			t.Errorf("Expected field name %s, got %s", field, strategies[i].FieldName)
		}
		if strategies[i].DisplayName != getDisplayName(strategy.BusinessType_AssetMerge, field) {
			t.Errorf("Expected display name %s, got %s", getDisplayName(strategy.BusinessType_AssetMerge, field), strategies[i].DisplayName)
		}
	}

	// Test the specific strategy for "Status" field
	statusStrategy := strategies[len(strategies)-1]
	if statusStrategy.FieldName != "Status" {
		t.Errorf("Expected field name Status, got %s", statusStrategy.FieldName)
	}
	if statusStrategy.SourcePriority["7"] != 1 || statusStrategy.SourcePriority["1"] != 2 ||
		statusStrategy.SourcePriority["2"] != 3 || statusStrategy.SourcePriority["10"] != 4 ||
		statusStrategy.SourcePriority["6"] != 5 || statusStrategy.SourcePriority["3"] != 6 ||
		statusStrategy.SourcePriority["5"] != 7 || statusStrategy.SourcePriority["11"] != 8 ||
		statusStrategy.SourcePriority["8"] != 9 || statusStrategy.SourcePriority["13"] != 10 || statusStrategy.SourcePriority["9"] != 11 {
		t.Errorf("Unexpected source priority for Status field")
	}
}

func TestInitVulnMergeStrategy(t *testing.T) {
	// Test cases
	testCases := []struct {
		name          string
		expectedCount int
		expectedField string
	}{
		{"HappyPath", 6, "Ip"},
		{"EdgeCaseEmptyField", 6, ""},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := initVulnMergeStrategy()

			// Check the number of strategies
			if len(result) != tc.expectedCount {
				t.Errorf("Expected %d strategies, but got %d", tc.expectedCount, len(result))
			}

			// Check if the expected field is present
			found := false
			for _, strategy := range result {
				if strategy.FieldName == tc.expectedField {
					found = true
					break
				}
			}
			if !found && tc.expectedField != "" {
				t.Errorf("Expected field %s not found in the strategies", tc.expectedField)
			}
		})
	}
}

func TestInitPersonMergeStrategy(t *testing.T) {
	// Test happy path
	strategies := initPersonMergeStrategy()
	if len(strategies) != 6 {
		t.Errorf("Expected 6 strategies, got %d", len(strategies))
	}

	// Test each strategy field
	expectedFields := []string{"EnglishName", "Department", "Title", "Email", "Status", "WorkNumber"}
	for i, s := range strategies {
		if s.FieldName != expectedFields[i] {
			t.Errorf("Expected field name %s, got %s", expectedFields[i], s.FieldName)
		}
		if s.BusinessType != strategy.BusinessType_PersonMerge {
			t.Errorf("Expected business type %s, got %s", strategy.BusinessType_PersonMerge, s.BusinessType)
		}
	}

	// Test edge case: empty field name
	if getDisplayName(strategy.BusinessType_PersonMerge, "") != "" {
		t.Errorf("Expected empty display name for empty field name, got %s", getDisplayName(strategy.BusinessType_PersonMerge, ""))
	}

	// Test edge case: unknown field name
	if getDisplayName(strategy.BusinessType_PersonMerge, "UnknownField") != "UnknownField" {
		t.Errorf("Expected 'UnknownField' display name for unknown field name, got %s", getDisplayName(strategy.BusinessType_PersonMerge, "UnknownField"))
	}

	// Test edge case: unknown business type
	if getDisplayName("unknown_type", "Name") != "Name" {
		t.Errorf("Expected 'Name' display name for unknown business type, got %s", getDisplayName("unknown_type", "Name"))
	}
}

func TestAppendDatasourceToStrategy(t *testing.T) {
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyMethodReturn(strategy.NewStrategyModel(), "ListAllDistinct", []*strategy.Strategy{
		{
			FieldName: "Name",
			SourcePriority: strategy.SourcePriority{
				"1": 1,
				"2": 1,
			},
			UntrustedSource: []string{
				"100",
			},
		},
	}, nil)

	t.Run("追加新数据源", func(t *testing.T) {
		updateStragegies := appendDatasourceToStrategy("asset_merge", 3)
		assert.Equal(t, uint64(2), updateStragegies[0].SourcePriority["3"])
	})

	t.Run("追加已存在的数据源", func(t *testing.T) {
		updateStragegies := appendDatasourceToStrategy("asset_merge", 1)
		assert.Equal(t, 0, len(updateStragegies))
	})

	t.Run("要追加的数据源已经在不信任列表", func(t *testing.T) {
		updateStragegies := appendDatasourceToStrategy("asset_merge", 100)
		assert.Equal(t, 0, len(updateStragegies))
	})

}
