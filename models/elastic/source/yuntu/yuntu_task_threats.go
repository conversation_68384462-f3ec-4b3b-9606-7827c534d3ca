package yuntu

import (
	"fmt"
	"fobrain/initialize/es"
	"git.gobies.org/caasm/fobrain-components/utils"
	"git.gobies.org/caasm/fobrain-components/utils/localtime"
	"github.com/olivere/elastic/v7"
)

type (
	TaskThreats struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`      // TaskId+NODE+区域+IP+Port 作为唯一值去更新，用作记录每次任务拉取数据
		TaskId        string          `json:"task_id"` //
		ChildTaskId   string          `json:"child_task_id"`
		CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
		UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
	}
)

// IndexName 索引名
func (f *TaskThreats) IndexName() string {
	return "yuntu_task_threats"
}

func (f *TaskThreats) String() string {
	if f == nil {
		return ""
	}
	return ""
}

func NewTaskThreatsModel() *TaskThreats {
	return &TaskThreats{}
}

func (f *TaskThreats) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("assets").Field("vuln.title")

	return field
}

func (f *TaskThreats) GetThreatListFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "assets"},
		{"name": "ID", "key": "id"},
		{"name": "资产类型", "key": "assets_type"},
		{"name": "漏洞等级", "key": "severity"},
		{"name": "状态", "key": "status"},
		{"name": "目标", "key": "target"},
		{"name": "漏洞标题", "key": "title"},
		{"name": "披露时间", "key": "disclosure_date", "show_value": "vuln"},
		{"name": "漏洞分类", "key": "category", "show_value": "vuln"},
	}
}

func (f *TaskThreats) GetThreatListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "assets"},
		{"name": "ID", "key": "id"},
		{"name": "资产类型", "key": "assets_type"},
		{"name": "漏洞等级", "key": "severity"},
		{"name": "状态", "key": "status"},
		{"name": "目标", "key": "target"},
		{"name": "漏洞标题", "key": "title"},
		{"name": "披露时间", "key": "disclosure_date", "show_value": "vuln"},
		{"name": "漏洞分类", "key": "category", "show_value": "vuln"},
	}
}
