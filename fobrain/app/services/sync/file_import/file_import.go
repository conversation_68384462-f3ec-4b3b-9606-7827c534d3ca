package file_import

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"fobrain/fobrain/app/services/rule_infos_level"
	"fobrain/fobrain/app/services/system_configs/device_strategy"
	"fobrain/models/elastic/device"
	"fobrain/pkg/utils/handle_es_bulk_error"
	"net"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/mitchellh/mapstructure"

	"github.com/google/uuid"

	sync_service "fobrain/fobrain/app/services/sync"
	"fobrain/models/mysql/asset_mapping"

	"github.com/olivere/elastic/v7"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"

	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/common/validate"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/initialize/mysql"
	assetses "fobrain/models/elastic/assets"
	"fobrain/models/elastic/poc"
	fileimportes "fobrain/models/elastic/source/file_import"
	"fobrain/models/elastic/staff"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
	"fobrain/models/mysql/network_areas"
	"git.gobies.org/caasm/fobrain-components/utils"
)

type AssetValidate struct {
	Area            string            `json:"area" validate:"required" zh:"区域"  mapstructure:"area"`
	Ip              string            `json:"ip" validate:"required,ip" zh:"IP" mapstructure:"ip"`
	Port            string            `json:"port" validate:"omitempty,number,min=0,max=65535" zh:"端口" mapstructure:"port"`
	Protocol        string            `json:"protocol" validate:"omitempty" zh:"协议" mapstructure:"protocol"`
	Status          string            `json:"status" validate:"omitempty,number" zh:"状态" mapstructure:"status"`
	OnLineStatus    string            `json:"on_line_status" validate:"omitempty,number,oneof=1 2" zh:"在线状态" mapstructure:"on_line_status"`
	Url             string            `json:"url" validate:"omitempty,url" zh:"URL" mapstructure:"url"`
	Domain          string            `json:"domain" validate:"omitempty,domain" zh:"域名" mapstructure:"domain"`
	Title           string            `json:"title" validate:"omitempty" zh:"标题" mapstructure:"title"`
	IpType          string            `json:"ip_type" validate:"omitempty,oneof=1 2" zh:"IP类型" mapstructure:"ip_type"`
	IpSegment       string            `json:"ip_segment" validate:"omitempty,ipSegment" zh:"IP段" mapstructure:"ip_segment"`
	HostName        string            `json:"hostname" validate:"omitempty" zh:"主机名" mapstructure:"host_name"`
	EthName         string            `json:"eth_name" validate:"omitempty" zh:"网卡名" mapstructure:"eth_name"`
	Os              string            `json:"os" validate:"omitempty" zh:"操作系统" mapstructure:"os"`
	Kernel          string            `json:"kernel" validate:"omitempty" zh:"内核" mapstructure:"kernel"`
	DeviceId        string            `json:"device_id" validate:"omitempty" zh:"设备ID" mapstructure:"device_id"`
	DeviceName      string            `json:"device_name" validate:"omitempty" zh:"设备名" mapstructure:"device_name"`
	Model           string            `json:"model" validate:"omitempty" zh:"型号" mapstructure:"model"`
	Maker           string            `json:"maker" validate:"omitempty" zh:"制造商" mapstructure:"maker"`
	Sn              string            `json:"sn" validate:"omitempty" zh:"序列号" mapstructure:"sn"`
	Mac             string            `json:"mac" validate:"omitempty,mac" zh:"MAC地址" mapstructure:"mac"`
	Product         string            `json:"product" validate:"omitempty" zh:"组件" mapstructure:"product"`
	BusinessSystem  string            `json:"business_system" validate:"omitempty" zh:"业务系统" mapstructure:"business_system"`
	BusinessOwner   string            `json:"business_owner" validate:"omitempty" zh:"业务负责人" mapstructure:"business_owner"`
	Oper            string            `json:"oper" validate:"omitempty" zh:"运维人员" mapstructure:"oper"`
	MachineRoom     string            `json:"machine_room" validate:"omitempty" zh:"机房" mapstructure:"machine_room"`
	MemorySize      string            `json:"memory_size" validate:"omitempty,number" zh:"内存大小" mapstructure:"memory_size"`
	MemoryUsageRate string            `json:"memory_usage_rate" validate:"omitempty" zh:"内存使用率" mapstructure:"memory_usage_rate"`
	CpuMaker        string            `json:"cpu_maker" validate:"omitempty" zh:"CPU厂商" mapstructure:"cpu_maker"`
	CpuBrand        string            `json:"cpu_brand" validate:"omitempty" zh:"CPU品牌" mapstructure:"cpu_brand"`
	CpuCount        string            `json:"cpu_count" validate:"omitempty,number" zh:"CPU数量" mapstructure:"cpu_count"`
	DiskCount       string            `json:"disk_count" validate:"omitempty,number" zh:"磁盘数量" mapstructure:"disk_count"`
	DiskSize        string            `json:"disk_size" validate:"omitempty,number" zh:"磁盘大小" mapstructure:"disk_size"`
	DiskUsageRate   string            `json:"disk_usage_rate" validate:"omitempty" zh:"磁盘使用率" mapstructure:"disk_usage_rate"`
	LoadAverage     string            `json:"load_average" validate:"omitempty" zh:"负载" mapstructure:"load_average"`
	IsPublic        string            `json:"is_public" validate:"omitempty,oneof=1 2" zh:"是否外网IP" mapstructure:"is_public"`
	NetworkType     string            `json:"network_type" validate:"omitempty,oneof=1 2 3" zh:"网络类型" mapstructure:"network_type"`
	NetworkCardIps  string            `json:"network_card_ips" validate:"omitempty,ips" zh:"其他网卡IP" mapstructure:"network_card_ips"`
	PersonField     string            `json:"person_field" validate:"omitempty" zh:"人员字段" mapstructure:"person_field"`
	CustomFields    map[string]string `json:"custom_fields" validate:"omitempty" zh:"自定义字段" mapstructure:"custom_fields"`
}
type ThreatValidate struct {
	Area         string            `json:"area" validate:"required" zh:"区域"`
	Ip           string            `json:"ip" validate:"required,ip" zh:"IP"`
	Port         string            `json:"port" validate:"omitempty,number,min=0,max=65535" zh:"端口"`
	Url          string            `json:"url" validate:"required" zh:"URL"`
	Level        string            `json:"level" validate:"required,number,oneof=0 1 2 3 4" zh:"漏洞等级"`
	Name         string            `json:"common_title" validate:"required" zh:"漏洞名称"`
	VulType      string            `json:"vulType" validate:"required" zh:"漏洞类型"`
	Cve          string            `json:"cve" validate:"omitempty" zh:"cve编号"`
	Cnvd         string            `json:"cnvd" validate:"omitempty" zh:"cnvd编号"`
	Cnnvd        string            `json:"cnnvd" validate:"omitempty" zh:"cnnvd编号"`
	HasExp       string            `json:"has_exp" validate:"omitempty,number,oneof=1 2" zh:"是否存在exp"`
	HasPoc       string            `json:"has_poc" validate:"omitempty,number,oneof=1 2" zh:"是否存在poc"`
	Status       string            `json:"status" validate:"omitempty,number,oneof=1 2 3 4 5" zh:"状态"`
	Describe     string            `json:"describe" validate:"omitempty" zh:"描述"`
	Details      string            `json:"details" validate:"omitempty" zh:"详情"`
	Hazard       string            `json:"hazard" validate:"omitempty" zh:"危险性"`
	Suggestions  string            `json:"suggestions" validate:"omitempty" zh:"修复建议"`
	LastResponse string            `json:"last_response" validate:"omitempty" zh:"最后一次响应"`
	IsPoc        string            `json:"is_poc" validate:"required,number,oneof=1 2" zh:"是否poc漏洞"`
	CustomFields map[string]string `json:"custom_fields" validate:"omitempty" zh:"自定义字段"`
}
type PeopleValidate struct {
	Area         string            `json:"area" validate:"required" zh:"区域"`
	Name         string            `json:"name" validate:"required" zh:"名称"`
	EnglishName  string            `json:"english_name" validate:"omitempty" zh:"英文名"`
	Title        string            `json:"title" validate:"omitempty" zh:"职位"`
	Mobile       string            `json:"mobile" validate:"required" zh:"手机号"`
	Email        string            `json:"email" validate:"omitempty,email" zh:"邮箱"`
	Department   string            `json:"department" validate:"omitempty" zh:"部门"`
	WorkNumber   string            `json:"work_number" validate:"omitempty" zh:"工号"`
	Status       string            `json:"status" validate:"omitempty,number,oneof=1 2" zh:"状态"`
	SsoId        string            `json:"sso_id" validate:"omitempty" zh:"单点登录ID"`
	SsoName      string            `json:"sso_name" validate:"omitempty" zh:"单点登录Name"`
	CustomFields map[string]string `json:"custom_fields" validate:"omitempty" zh:"自定义字段"`
	UniqueField  string            `json:"unique_field" validate:"omitempty" zh:"唯一字段"`
}

type BusinessSystems struct {
	BusinessName    string                 `json:"business_name" validate:"required" zh:"业务系统名称"`
	Address         string                 `json:"address" validate:"omitempty" zh:"业务系统地址"`
	RunningState    string                 `json:"running_state" validate:"omitempty,number,oneof=0 1" zh:"运行状态"`           //系统状态 1-运行中/ 0-已下线
	SystemVersion   string                 `json:"system_version" validate:"omitempty" zh:"系统版本"`                           //系统版本
	OperatingEnv    string                 `json:"operating_env" validate:"omitempty,number,oneof=1 2" zh:"运行环境"`           //运行环境 1-生产环境/2-开发环境
	PurchaseType    string                 `json:"purchase_type" validate:"omitempty,number,oneof=1 2 3" zh:"采购类型"`         //采购类型：''-空/ 1-自研/ 2-外包/ 3-第三方采购
	ImportantTypes  string                 `json:"important_types" validate:"omitempty,number,oneof=1 2 3" zh:"重要性"`        //重要性 ''-空/ 1-非常重要/ 2- 重要/ 3- 一般
	InsuranceLevel  string                 `json:"insurance_level" validate:"omitempty,number,oneof=0 1 2 3 4 5" zh:"等保级别"` //等保级别: ''- 空/ 1-一级/2- 二级/3- 三级
	IsGj            string                 `json:"is_gj" validate:"omitempty,number,oneof=1 2" zh:"是否关基设施"`                 //是否关基设施：''-空/ 1-是/ 2-否
	IsXc            string                 `json:"is_xc" validate:"omitempty,number,oneof=1 2" zh:"是否信创"`                   //是否信创：''- 空/ 1-是/ 2- 否
	IntranetIps     string                 `json:"intranet_ips" form:"intranet_ips" validate:"omitempty" zh:"关联内网IP"`
	InternetIps     string                 `json:"internet_ips" form:"internet_ips" validate:"omitempty" zh:"关联互联网IP"`
	ContinuityLevel string                 `json:"continuity_level" form:"continuity_level" validate:"omitempty" zh:"连续性级别"`
	UseMark         string                 `json:"use_mark" form:"use_mark" validate:"omitempty" zh:"规划用途"`
	PersonNames     string                 `json:"person_names" form:"person_names" validate:"omitempty" zh:"业务系统负责人"`
	DepartmentNames string                 `json:"department_names" form:"department_names" validate:"required" zh:"业务系统部门"`
	CustomFields    map[string]interface{} `json:"custom_fields" form:"custom_fields" validate:"omitempty" zh:"自定义字段"`
}

func CheckBusinessSystem(rows [][]string) ([]BusinessSystems, error) {
	if len(rows) < 4 {
		// 空模板
		return nil, errors.New("数据行数不足4行")
	}
	var assets []BusinessSystems
	var errMsg string
	num := 0
	customFieldsIndex := make(map[int]string) // 自定义字段列,列索引->字段名
	for i, cell := range rows[1] {
		if strings.HasPrefix(cell, "custom_fields.") {
			customFieldsIndex[i] = cell
		}
	}

	for i, row := range rows {
		if i < 4 {
			continue
		}
		var businessSystem BusinessSystems
		businessSystem.BusinessName = utils.SafeGetCellValue(row, 0)
		businessSystem.Address = utils.SafeGetCellValue(row, 1)
		businessSystem.IntranetIps = strings.TrimSpace(utils.SafeGetCellValue(row, 2))
		if businessSystem.IntranetIps != "" {
			ips := strings.Split(businessSystem.IntranetIps, ",")
			for _, ip := range ips {
				if !utils.IsValidIPs(ip) {
					num++
					errMsg += fmt.Sprintf("第 %d 行存在格式错误，错误信息：%s 内网ip格式错误", i+1, ip)
					logs.GetLogger().Infof(fmt.Sprintf("第 %d 行存在格式错误，错误信息：%s 内网ip格式错误", i+1, ip))
					continue
				}
			}
		}
		businessSystem.InternetIps = strings.TrimSpace(utils.SafeGetCellValue(row, 3))
		if businessSystem.InternetIps != "" {
			ips := strings.Split(businessSystem.InternetIps, ",")
			for _, ip := range ips {
				if !utils.IsValidIPs(ip) {
					num++
					errMsg += fmt.Sprintf("第 %d 行存在格式错误，错误信息：%s 互联网ip格式错误", i+1, ip)
					logs.GetLogger().Infof(fmt.Sprintf("第 %d 行存在格式错误，错误信息：%s 互联网ip格式错误", i+1, ip))
					continue
				}
			}
		}
		businessSystem.PersonNames = utils.SafeGetCellValue(row, 4)
		businessSystem.DepartmentNames = utils.SafeGetCellValue(row, 5)
		businessSystem.RunningState = utils.SafeGetCellValue(row, 6)
		businessSystem.SystemVersion = utils.SafeGetCellValue(row, 7)
		businessSystem.OperatingEnv = utils.SafeGetCellValue(row, 8)
		businessSystem.PurchaseType = utils.SafeGetCellValue(row, 9)
		businessSystem.ImportantTypes = utils.SafeGetCellValue(row, 10)
		businessSystem.InsuranceLevel = utils.SafeGetCellValue(row, 11)
		businessSystem.ContinuityLevel = utils.SafeGetCellValue(row, 12)
		businessSystem.IsGj = utils.SafeGetCellValue(row, 13)
		businessSystem.IsXc = utils.SafeGetCellValue(row, 14)
		businessSystem.UseMark = utils.SafeGetCellValue(row, 15)
		// 补充自定义字段
		businessSystem.CustomFields = make(map[string]interface{})
		for index, field := range customFieldsIndex {
			fieldName := strings.TrimPrefix(field, "custom_fields.")
			businessSystem.CustomFields[fieldName] = utils.SafeGetCellValue(row, index)
		}
		if ok, msg := validate.Validator(businessSystem); !ok {
			num++
			errMsg += fmt.Sprintf("第 %d 行存在格式错误，错误信息：%s", i+1, msg)
			logs.GetLogger().Infof(fmt.Sprintf("第 %d 行存在格式错误，错误信息：%s", i+1, msg))
			continue
		}

		assets = append(assets, businessSystem)
	}

	if num > 0 {
		return assets, errors.New(errMsg)
	}
	return assets, nil
}

func CheckAssets(rows [][]string, mappingField string) (string, bool, error) {
	// 处理标准资产内外网导出模板
	if len(rows) > 0 && len(rows[0]) != 1 {
		asset, err := HandleExportTemplate(rows, mappingField)
		if err != nil {
			return "", false, err
		}
		d, err := json.Marshal(asset)
		if err != nil {
			return "", false, err
		}
		return string(d), false, nil
	}
	if len(rows) < 4 {
		// 空模板
		return "", false, errors.New("数据行数不足4行")
	}
	var assets []AssetValidate
	mapArea := make(map[string]struct{})
	arealist, _, err := network_areas.NewNetworkAreaModel().List(0, 0)
	if err != nil {
		return "", false, err
	}
	for _, area := range arealist {
		mapArea[area.Name] = struct{}{}
	}

	// 检查row[1]，所有以"custom_fields."开头的字段作为自定义字段，记录列的索引
	customFieldsIndex := make(map[int]string) // 自定义字段列,列索引->字段名
	for i, cell := range rows[1] {
		if strings.HasPrefix(cell, "asset.custom_fields.") || strings.HasPrefix(cell, "device.custom_fields.") {
			customFieldsIndex[i] = cell
		}
	}

	hasDevice := false
	for i, row := range rows {
		if i < 4 {
			continue
		}

		var assetValidate AssetValidate
		assetValidate.CustomFields = make(map[string]string) // 初始化自定义字段

		assetValidate.Area = utils.SafeGetCellValue(row, 0)
		if _, ok := mapArea[assetValidate.Area]; !ok {
			return "", false, errors.New(fmt.Sprintf("第 %d 行存在格式错误，错误信息：%s", i+1, "所属区域不存在"))
		}
		assetValidate.Ip = utils.SafeGetCellValue(row, 1)
		assetValidate.NetworkType = utils.SafeGetCellValue(row, 2)
		assetValidate.Port = utils.SafeGetCellValue(row, 3)
		assetValidate.Status = utils.SafeGetCellValue(row, 4)
		assetValidate.Protocol = utils.SafeGetCellValue(row, 5)
		assetValidate.Url = utils.SafeGetCellValue(row, 6)
		assetValidate.Domain = utils.SafeGetCellValue(row, 7)
		assetValidate.Title = utils.SafeGetCellValue(row, 8)
		assetValidate.IpType = utils.SafeGetCellValue(row, 9)
		assetValidate.IpSegment = utils.SafeGetCellValue(row, 10)
		assetValidate.HostName = utils.SafeGetCellValue(row, 11)
		assetValidate.EthName = utils.SafeGetCellValue(row, 12)
		assetValidate.Os = utils.SafeGetCellValue(row, 13)
		assetValidate.Kernel = utils.SafeGetCellValue(row, 14)
		assetValidate.DeviceId = utils.SafeGetCellValue(row, 15)
		assetValidate.DeviceName = utils.SafeGetCellValue(row, 16)
		assetValidate.Model = utils.SafeGetCellValue(row, 17)
		assetValidate.Maker = utils.SafeGetCellValue(row, 18)
		assetValidate.Sn = utils.SafeGetCellValue(row, 19)
		assetValidate.Mac = utils.SafeGetCellValue(row, 20)
		assetValidate.Product = utils.SafeGetCellValue(row, 21)
		assetValidate.BusinessSystem = utils.SafeGetCellValue(row, 22)
		assetValidate.BusinessOwner = utils.SafeGetCellValue(row, 23)
		assetValidate.Oper = utils.SafeGetCellValue(row, 24)
		assetValidate.MachineRoom = utils.SafeGetCellValue(row, 25)
		assetValidate.OnLineStatus = utils.SafeGetCellValue(row, 26)
		assetValidate.MemorySize = utils.SafeGetCellValue(row, 27)
		assetValidate.MemoryUsageRate = utils.SafeGetCellValue(row, 28)
		assetValidate.CpuMaker = utils.SafeGetCellValue(row, 29)
		assetValidate.CpuBrand = utils.SafeGetCellValue(row, 30)
		assetValidate.CpuCount = utils.SafeGetCellValue(row, 31)
		assetValidate.DiskCount = utils.SafeGetCellValue(row, 32)
		assetValidate.DiskSize = utils.SafeGetCellValue(row, 33)
		assetValidate.DiskUsageRate = utils.SafeGetCellValue(row, 34)
		assetValidate.LoadAverage = utils.SafeGetCellValue(row, 35)
		assetValidate.NetworkCardIps = strings.TrimSpace(utils.SafeGetCellValue(row, 36))
		assetValidate.PersonField = mappingField // 人员映射字段

		// 补充自定义字段
		for index, field := range customFieldsIndex {
			assetValidate.CustomFields[field] = utils.SafeGetCellValue(row, index)
		}

		// 处理多端口的验证
		if err = CheckPortInfo(&assetValidate); err != nil {
			return "", false, errors.New(fmt.Sprintf("第 %d 行存在格式错误，错误信息：%s", i+1, err.Error()))
		}
		assets = append(assets, assetValidate)
		if assetValidate.Sn != "" {
			hasDevice = true
		}
	}

	marshal, err := json.Marshal(assets)
	if err != nil {
		return "", hasDevice, err
	}

	return string(marshal), hasDevice, nil
}

func CheckPortInfo(a *AssetValidate) error {
	t := make(map[string][]string)
	t["port"] = strings.Split(a.Port, ",")
	t["status"] = strings.Split(a.Status, ",")
	t["url"] = strings.Split(a.Url, ",")
	t["domain"] = strings.Split(a.Domain, ",")
	mx := max(len(t["port"]), len(t["protocol"]), len(t["status"]), len(t["url"]), len(t["domain"]))
	for i := range mx {
		if i < len(t["port"]) {
			_, err := strconv.Atoi(t["port"][i])
			if err != nil && t["port"][i] != "" {
				return errors.New("端口格式错误")
			}
		}
		if i < len(t["status"]) {
			_, err := strconv.Atoi(t["status"][i])
			if err != nil && t["status"][i] != "" {
				return errors.New("状态格式错误")
			}
		}
		if i < len(t["url"]) {
			parsedUrl, err := url.Parse(t["url"][i])
			if !(err == nil && (parsedUrl.Scheme == "http" || parsedUrl.Scheme == "https") && parsedUrl.Host != "") && t["url"][i] != "" {
				return errors.New("URL格式错误")
			}
		}
		if i < len(t["domain"]) {
			if t["domain"][i] != "" && !utils.IsDomain(t["domain"][i]) {
				return errors.New("域名格式错误")
			}
		}
	}
	// IP检查
	if a.Ip == "" || !utils.IsValidIPs(a.Ip) {
		return errors.New("IP格式错误或未填写")
	}
	return nil
}

// FieldMap 导出模板字段映射 后续对导出模板有修改需要对此进行修改
var FieldMap = map[string]string{
	"IP地址":  "ip",
	"端口":    "port",
	"协议":    "protocol",
	"URL":   "url",
	"网站标题":  "title",
	"域名":    "domain",
	"组件信息":  "product",
	"在线状态":  "on_line_status",
	"主机名":   "host_name",
	"IP类型":  "ip_type",
	"MAC地址": "mac",
	"所属区域":  "area",
	"业务系统":  "business_system",
	"业务负责人": "business_owner",
	"运维人员":  "oper",
}

// HandleExportTemplate 标准导出模板导入处理
func HandleExportTemplate(rows [][]string, mappingField string) ([]*AssetValidate, error) {
	// 处理自定义字段
	// 首行为字段
	if len(rows) < 2 {
		return nil, errors.New("不存在数据")
	}
	res := make([]*AssetValidate, 0, len(rows)-1)
	// 从第二行开始为数据
	col := len(rows[0]) // 固定列长度，防止存在越界行为
	for i, row := range rows {
		if i == 0 {
			continue
		}
		t := make(map[string]any)
		for j, d := range row {
			// 超出字段列的数据舍弃
			if j > col {
				break
			}
			t[FieldMap[rows[0][j]]] = d
		}
		// 必填字段判断
		if v, ok := t["ip"]; !ok || v == "" {
			return nil, errors.New(fmt.Sprintf("第 %d 行存在格式错误，错误信息：%s", i+1, "IP地址不允许为空"))
		}
		if v, ok := t["area"]; !ok || v == "" {
			return nil, errors.New(fmt.Sprintf("第 %d 行存在格式错误，错误信息：%s", i+1, "所属区域不允许为空"))
		}
		// 处理ip类型
		if ipType, ok := t["ip_type"]; ok {
			if ipType == "IPv4" {
				t["ip_type"] = "1"
			} else if ipType == "IPv6" {
				t["ip_type"] = "2"
			} else {
				return nil, errors.New(fmt.Sprintf("第 %d 行存在格式错误，错误信息：%s", i+1, "IP类型错误"))
			}
		}
		// 处理在线类型
		if status, ok := t["on_line_status"]; ok {
			if status == "在线" {
				t["on_line_status"] = "1"
			} else if status == "离线" {
				t["on_line_status"] = "2"
			}
		}
		asset := &AssetValidate{}
		if err := mapstructure.Decode(t, asset); err != nil {
			return nil, err
		}
		if err := CheckPortInfo(asset); err != nil {
			return nil, errors.New(fmt.Sprintf("第 %d 行存在格式错误，错误信息：%s", i+1, err.Error()))
		}
		asset.PersonField = mappingField
		res = append(res, asset)
	}
	return res, nil
}

func CheckThreats(rows [][]string) (string, error) {
	if len(rows) < 4 {
		// 空模板
		return "", errors.New("数据行数不足4行")
	}

	// 检查row[1]，所有以"custom_fields."开头的字段作为自定义字段，记录列的索引
	customFieldsIndex := make(map[int]string) // 自定义字段列,列索引->字段名
	for i, cell := range rows[1] {
		if strings.HasPrefix(cell, "custom_fields.") {
			customFieldsIndex[i] = cell
		}
	}

	var threats []ThreatValidate
	for i, row := range rows {
		if i < 4 {
			continue
		}

		var threatValidate ThreatValidate
		threatValidate.CustomFields = make(map[string]string)

		threatValidate.Area = utils.SafeGetCellValue(row, 0)
		threatValidate.Ip = utils.SafeGetCellValue(row, 1)
		threatValidate.Port = utils.SafeGetCellValue(row, 2)
		threatValidate.Url = utils.SafeGetCellValue(row, 3)
		threatValidate.Level = utils.SafeGetCellValue(row, 4)
		threatValidate.Name = utils.SafeGetCellValue(row, 5)
		threatValidate.VulType = utils.SafeGetCellValue(row, 6)
		threatValidate.IsPoc = utils.SafeGetCellValue(row, 7)
		threatValidate.Cve = utils.SafeGetCellValue(row, 8)
		threatValidate.Cnvd = utils.SafeGetCellValue(row, 9)
		threatValidate.Cnnvd = utils.SafeGetCellValue(row, 10)
		threatValidate.HasExp = utils.SafeGetCellValue(row, 11)
		threatValidate.HasPoc = utils.SafeGetCellValue(row, 12)
		threatValidate.Status = utils.SafeGetCellValue(row, 13)
		threatValidate.Describe = utils.SafeGetCellValue(row, 14)
		threatValidate.Details = utils.SafeGetCellValue(row, 15)
		threatValidate.Hazard = utils.SafeGetCellValue(row, 16)
		threatValidate.Suggestions = utils.SafeGetCellValue(row, 17)
		threatValidate.LastResponse = utils.SafeGetCellValue(row, 18)

		// 补充自定义字段
		for index, field := range customFieldsIndex {
			fieldName := strings.TrimPrefix(field, "custom_fields.")
			threatValidate.CustomFields[fieldName] = utils.SafeGetCellValue(row, index)
		}

		if ok, msg := validate.Validator(threatValidate); !ok {
			return "", errors.New(fmt.Sprintf("第 %d 行存在格式错误，错误信息：%s", i+1, msg))
		}
		threats = append(threats, threatValidate)
	}

	marshal, err := json.Marshal(threats)
	if err != nil {
		return "", err
	}

	return string(marshal), nil
}

func CheckPeoples(rows [][]string, uniqueField string) (string, error) {
	if len(rows) < 4 {
		// 空模板
		return "", errors.New("数据行数不足4行")
	}

	// 检查row[1]，所有以"custom_fields."开头的字段作为自定义字段，记录列的索引
	customFieldsIndex := make(map[int]string) // 自定义字段列,列索引->字段名
	for i, cell := range rows[1] {
		if strings.HasPrefix(cell, "custom_fields.") {
			customFieldsIndex[i] = cell
		}
	}

	var peoples []PeopleValidate
	for i, row := range rows {
		if i < 4 {
			continue
		}

		var peopleValidate PeopleValidate
		peopleValidate.CustomFields = make(map[string]string)

		peopleValidate.Area = utils.SafeGetCellValue(row, 0)
		peopleValidate.Name = utils.SafeGetCellValue(row, 1)
		peopleValidate.EnglishName = utils.SafeGetCellValue(row, 2)
		peopleValidate.Title = utils.SafeGetCellValue(row, 3)
		peopleValidate.Mobile = strings.Trim(utils.SafeGetCellValue(row, 4), "'’‘")
		peopleValidate.Email = utils.SafeGetCellValue(row, 5)
		peopleValidate.Department = utils.SafeGetCellValue(row, 6)
		peopleValidate.Status = utils.SafeGetCellValue(row, 7)
		peopleValidate.SsoId = utils.SafeGetCellValue(row, 8)
		peopleValidate.SsoName = utils.SafeGetCellValue(row, 9)
		peopleValidate.WorkNumber = utils.SafeGetCellValue(row, 10)
		if peopleValidate.Status != "2" {
			peopleValidate.Status = "1"
		}
		peopleValidate.UniqueField = uniqueField // 人员唯一字段

		// 补充自定义字段
		for index, field := range customFieldsIndex {
			fieldName := strings.TrimPrefix(field, "custom_fields.")
			peopleValidate.CustomFields[fieldName] = utils.SafeGetCellValue(row, index)
		}

		depts := strings.Split(peopleValidate.Department, ",")
		peopleValidate.Department = strings.Join(depts, "&")
		if ok, msg := validate.Validator(peopleValidate); !ok {
			return "", errors.New(fmt.Sprintf("第 %d 行存在格式错误，错误信息：%s", i+1, msg))
		}
		peoples = append(peoples, peopleValidate)
	}

	marshal, err := json.Marshal(peoples)
	if err != nil {
		return "", err
	}

	return string(marshal), nil
}

func CheckAssetMapping(rows [][]string) ([]asset_mapping.AssetMapping, int, error) {
	var assetMapping []asset_mapping.AssetMapping
	for i, row := range rows {
		if i < 2 {
			continue
		}

		var assetMappingValidate asset_mapping.AssetMapping

		// 验证 HostIp 是否为合法的 IP
		hostIp := utils.SafeGetCellValue(row, 0)
		if hostIp == "" {
			return nil, i, errors.New("主机IP不允许为空")
		}
		if err := checkIP(hostIp, i); err != nil {
			return nil, i, err
		}
		assetMappingValidate.HostIp = hostIp

		// 处理 HostPort
		hostPortString := utils.SafeGetCellValue(row, 1)
		hostPort, err := parsePort(hostPortString, i)
		if err != nil {
			return nil, i, err
		}
		if hostPort == -1 {
			return nil, 0, errors.New(fmt.Sprintf("第%d行格式错误，主机端口不允许为空", i))
		}
		assetMappingValidate.HostPort = &hostPort

		floatIp := utils.SafeGetCellValue(row, 2)
		if err = checkIP(floatIp, i); err != nil {
			return nil, i, err
		}
		assetMappingValidate.FloatIp = floatIp

		floatPortString := utils.SafeGetCellValue(row, 3)
		floatPort, err := parsePort(floatPortString, i)
		if err != nil {
			return nil, i, err
		}
		if floatPort != -1 {
			assetMappingValidate.FloatPort = &floatPort
		}

		loadIp := utils.SafeGetCellValue(row, 4)
		if err = checkIP(loadIp, i); err != nil {
			return nil, i, err
		}
		assetMappingValidate.LoadIp = loadIp

		loadPortString := utils.SafeGetCellValue(row, 5)
		loadPort, err := parsePort(loadPortString, i)
		if err != nil {
			return nil, i, err
		}
		if loadPort != -1 {
			assetMappingValidate.LoadPort = &loadPort
		}

		sslIp := utils.SafeGetCellValue(row, 6)
		if err = checkIP(sslIp, i); err != nil {
			return nil, i, err
		}
		assetMappingValidate.SslIp = sslIp

		sslPortString := utils.SafeGetCellValue(row, 7)
		sslPort, err := parsePort(sslPortString, i)
		if err != nil {
			return nil, i, err
		}
		if sslPort != -1 {
			assetMappingValidate.SslPort = &sslPort
		}

		dmzIp := utils.SafeGetCellValue(row, 8)
		if err = checkIP(dmzIp, i); err != nil {
			return nil, i, err
		}
		assetMappingValidate.DmzIp = dmzIp

		dmzPortString := utils.SafeGetCellValue(row, 9)
		dmzPort, err := parsePort(dmzPortString, i)
		if err != nil {
			return nil, i, err
		}
		if dmzPort != -1 {
			assetMappingValidate.DmzPort = &dmzPort
		}

		financeIp := utils.SafeGetCellValue(row, 10)
		if err = checkIP(financeIp, i); err != nil {
			return nil, i, err
		}
		assetMappingValidate.FinanceIp = financeIp

		financePortString := utils.SafeGetCellValue(row, 11)
		financePort, err := parsePort(financePortString, i)
		if err != nil {
			return nil, i, err
		}
		if financePort != -1 {
			assetMappingValidate.FinancePort = &financePort
		}

		assetMappingValidate.Domain = utils.SafeGetCellValue(row, 12)

		// 调用验证方法
		if ok, msg := validate.Validator(assetMappingValidate); !ok {
			return nil, i, fmt.Errorf("第 %d 行存在格式错误，错误信息：%s", i+1, msg)
		}

		assetMapping = append(assetMapping, assetMappingValidate)
	}
	asset, err := asset_mapping.NewAssetMappingModel().GetAllAssetMapping()
	if err != nil {
		return nil, 0, err
	}
	assetMappingCopy := assetMapping
	assetMappingCopy = append(assetMappingCopy, asset...)
	// 检查是否有重复项
	isRepeat := hasDuplicateHostIPPort(assetMappingCopy)
	if isRepeat {
		return nil, 0, errors.New("资产映射已存在")
	}
	return assetMapping, 0, nil
}

// parsePort 将字符串转换为整数并返回，如果转换失败则返回错误
func parsePort(portString string, rowIndex int) (int, error) {
	if portString == "" {
		return -1, nil // 如果字符串为空，返回 0，不视为错误
	}
	port, err := strconv.Atoi(portString)
	if err != nil {
		return 0, errors.New("第 " + strconv.Itoa(rowIndex) + " 行数据格式有问题")
	}
	if port < 1 || port > 65535 {
		return 0, errors.New("第 " + strconv.Itoa(rowIndex) + " 行端口号无效")
	}
	return port, nil
}

func checkIP(ipString string, row int) error {
	if ipString == "" {
		return nil
	}
	if net.ParseIP(ipString) == nil {
		return errors.New("第 " + strconv.Itoa(row) + " 行IP地址无效")
	}
	return nil
}

// hasDuplicateHostIPPort 判断 主机ip+端口 有无重复
func hasDuplicateHostIPPort(assets []asset_mapping.AssetMapping) bool {
	seen := make(map[string]bool)
	for _, asset := range assets {
		key := asset.HostIp + ":" + strconv.Itoa(*asset.HostPort)
		if seen[key] {
			return true // 找到重复的 HostIp 和 HostPort 组合
		}
		seen[key] = true
	}
	return false // 无重复组合
}

// SyncFileImport 同步文件导入数据
func SyncFileImport(node *data_source.Node, taskInfo string, rows string) error {
	logs.GetSyncLogger().Infof("SyncFileImport start taskInfo:%s", taskInfo)

	assetTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())
	threatTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncThreat)).Int())
	peopleTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncPeople)).Int())

	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncAsset)).Int() == data_sync_task.SyncAsset {
		//更新资产任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(assetTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("SyncFileImport syncAssets UpdateStatusById StatusDoing err:%s", err.Error())
		}
		go syncAssets(rows, taskInfo, node)
	}

	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncThreat)).Int() == data_sync_task.SyncThreat {
		//更新漏洞任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(threatTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("SyncFileImport syncThreats UpdateStatusById StatusDoing err:%s", err.Error())
		}
		go syncThreats(rows, taskInfo, node)
	}

	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncPeople)).Int() == data_sync_task.SyncPeople {
		//更新人员任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(peopleTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("SyncFileImport syncEmployees UpdateStatusById StatusDoing err:%s", err.Error())
		}
		go syncEmployees(rows, taskInfo, node)
	}
	return nil
}

// syncEmployees 同步人员
func syncEmployees(rows string, taskInfo string, node *data_source.Node) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncPeople)).Int())
	logs.GetSyncLogger().Infof("SyncFileImport syncEmployees start taskId:%d,taskChildId:%d", taskId, taskChildId)

	result := gjson.Parse(rows)
	total := gjson.Get(rows, "#").Int() //数据总量

	err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateById(taskChildId, data_sync_child_task.DataSyncChildTask{
		SyncDataTotal: int(total),
	})

	if err != nil {
		logs.GetSyncLogger().Errorf("SyncFileImport syncEmployees UpdateById SyncDataTotal err:%s", err.Error())
	}

	// client := redis.GetRedisClient()
	// rq := &queue.RedisQueue{Client: client}
	if result.Exists() {
		// var peopleIds []map[string]interface{}
		var num int //分页插入
		employeeTaskBulkRequest := es.GetEsClient().Bulk()
		employeeProcessBulkRequest := es.GetEsClient().Bulk()
		// 遍历 _id 字段，并根据 id 添加新的字段数据
		result.ForEach(func(key, value gjson.Result) bool {
			logs.GetSyncLogger().Debugf("SyncFileImport syncEmployees value info:%s", value)
			data, _ := sjson.Set(value.String(), "node_id", node.Id)
			area, _ := network_areas.NewNetworkAreaModel().First(mysql.WithWhere("name", gjson.Get(data, "area").String()))
			data, _ = sjson.Delete(data, "area")
			data, _ = sjson.Set(data, "area_id", area.Id)
			data, _ = sjson.Set(data, "created_at", localtime.NewLocalTime(time.Now()))
			data, _ = sjson.Set(data, "updated_at", localtime.NewLocalTime(time.Now()))
			employeeId := strings.ReplaceAll(uuid.New().String(), "-", "")
			//写入任务库
			employeeTask := fileimportes.NewFileImportTaskPeoplesModel()
			employeeTaskId := strings.ReplaceAll(uuid.New().String(), "-", "")
			data, _ = sjson.Set(data, "task_id", taskId)
			assetTaskReq := elastic.NewBulkIndexRequest().Index(employeeTask.IndexName()).Id(employeeTaskId).Doc(data)
			employeeTaskBulkRequest = employeeTaskBulkRequest.Add(assetTaskReq)
			//写入过程库
			staffProcess := staff.NewProcessStaffModel()
			data, _ = sjson.Set(data, "id", employeeId)
			data, _ = sjson.Set(data, "staff_task_id", employeeTaskId)
			data, _ = sjson.Set(data, "area", area.Id)
			data, _ = sjson.Set(data, "node", node.Id)
			data, _ = sjson.Set(data, "source", node.SourceId)
			staffProcess.Id = employeeId
			staffProcess.TaskDataId = employeeTaskId
			staffProcess.TaskId = taskId
			staffProcess.ChildTaskId = taskChildId
			staffProcess.Area = int(node.AreaId)
			staffProcess.Node = node.Id
			staffProcess.Source = node.SourceId
			staffProcess.OriginalId = gjson.Get(data, "sso_id").String()
			staffProcess.SsoId = gjson.Get(data, "sso_id").String()
			staffProcess.SsoName = gjson.Get(data, "sso_name").String()
			staffProcess.Name = gjson.Get(data, "name").String()
			staffProcess.EnglishName = gjson.Get(data, "english_name").String()
			staffProcess.Title = gjson.Get(data, "title").String()
			staffProcess.Mobile = gjson.Get(data, "mobile").String()
			staffProcess.Email = gjson.Get(data, "email").String()
			staffProcess.Department = gjson.Get(data, "department").String()
			staffProcess.WorkNumber = gjson.Get(data, "work_number").String()
			staffProcess.Status = int(gjson.Get(data, "status").Int())
			// 唯一标识
			uniqueField := gjson.Get(data, "unique_field").String()
			staffProcess.UniqueKey, _ = sync_service.GetUniqueKey("file_import", uniqueField, data)
			json.Unmarshal([]byte(gjson.Get(data, "custom_fields").String()), &staffProcess.CustomFields)
			staffProcess.CreatedAt = localtime.NewLocalTime(time.Now())
			staffProcess.UpdatedAt = localtime.NewLocalTime(time.Now())
			employeeProcessReq := elastic.NewBulkIndexRequest().Index(staffProcess.IndexName()).Id(employeeId).Doc(staffProcess)
			employeeProcessBulkRequest = employeeProcessBulkRequest.Add(employeeProcessReq)
			num++
			// peopleIds = append(peopleIds, map[string]interface{}{"name": gjson.Get(data, "name").String(), "mobile": gjson.Get(data, "mobile").String(), "task_id": taskChildId})

			if num >= 200 {
				err := insertPeopleBulkRequest(employeeTaskBulkRequest, employeeProcessBulkRequest, taskId, taskChildId)
				if err != nil {
					data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataDropTotal(taskChildId, num)
					return false
				}
				err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(taskChildId, num)
				if err != nil {
					logs.GetSyncLogger().Errorf("SyncFileImport syncEmployees UpdateSyncDataSuccessTotal err:%s", err.Error())
				}
				num = 0
			}

			return true
		})

		if num > 0 {
			err := insertPeopleBulkRequest(employeeTaskBulkRequest, employeeProcessBulkRequest, taskId, taskChildId)
			if err != nil {
				data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataDropTotal(taskChildId, num)
				return
			}

			if err == nil {
				//更新成功数量
				err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(taskChildId, num)
				if err != nil {
					logs.GetSyncLogger().Errorf("SyncFileImport syncEmployees UpdateSyncDataSuccessTotal err:%s", err.Error())
				}
				// logs.GetSyncLogger().Infof("SyncFileImport syncEmployees peopleIds info:%v", peopleIds)

				// successCount := rq.Push(cfg.LoadQueue().PersonMergeQueue, peopleIds)
				// logs.GetSyncLogger().Infof("SyncFileImport syncEmployees queue Push successCount:%d, expectCount:%d", successCount, len(peopleIds))
			}
		}
	}

	logs.GetSyncLogger().Infof("SyncFileImport syncEmployees end taskId:%d,taskChildId:%d", taskId, taskChildId)
	// // 发送任务结束标识
	// rq.Push(cfg.LoadQueue().PersonMergeQueue, []map[string]interface{}{{"task_id": taskChildId, "task_type": "sync", "total": total, "is_end": true}})
	//更新子任务完成状态
	err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(taskChildId, data_sync_child_task.StatusSuccess)
	if err != nil {
		logs.GetSyncLogger().Errorf("SyncFileImport syncEmployees UpdateStatusById StatusSuccess err:%s", err.Error())
	}
}

// syncThreats 同步漏洞
func syncThreats(rows string, taskInfo string, node *data_source.Node) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncThreat)).Int())
	logs.GetSyncLogger().Infof("SyncFileImport syncThreats start taskInfo:%d,taskChildId:%d", taskId, taskChildId)

	result := gjson.Parse(rows)
	total := gjson.Get(rows, "#").Int() //数据总量

	err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateById(taskChildId, data_sync_child_task.DataSyncChildTask{
		SyncDataTotal: int(total),
	})

	if err != nil {
		logs.GetSyncLogger().Errorf("SyncFileImport syncThreats UpdateById SyncDataTotal err:%s", err.Error())
	}

	// client := redis.GetRedisClient()
	// rq := &queue.RedisQueue{Client: client}
	if result.Exists() {
		var num int //分页插入
		// var threatIds []map[string]interface{}
		threatTaskBulkRequest := es.GetEsClient().Bulk()
		threatProcessBulkRequest := es.GetEsClient().Bulk()

		// 遍历 _id 字段，并根据 id 添加新的字段数据
		result.ForEach(func(key, value gjson.Result) bool {
			logs.GetSyncLogger().Debugf("SyncFileImport syncThreats value info:%s", value)
			data, _ := sjson.Set(value.String(), "node_id", node.Id)
			area, _ := network_areas.NewNetworkAreaModel().First(mysql.WithWhere("name", gjson.Get(data, "area").String()))
			node.AreaId = area.Id
			data, _ = sjson.Delete(data, "area")
			data, _ = sjson.Set(data, "area_id", node.AreaId)
			data, _ = sjson.Set(data, "created_at", localtime.NewLocalTime(time.Now()))
			data, _ = sjson.Set(data, "updated_at", localtime.NewLocalTime(time.Now()))
			// 生成id，uuid去除-
			// 2025-04-02，隆基反应相同ip+url，但是漏洞名称不同，cve\cnvd\cnnvd都为空，导入后过程表仅有一条记录
			// 秀坤、左志富讨论确定，过程表id随机生成，达到效果：文件中有多少条数据，过程表就生成多少条记录
			id := strings.ReplaceAll(uuid.New().String(), "-", "")
			//写入资产库
			threatId := fmt.Sprintf("%d_%d_%s", node.Id, node.AreaId, id)

			//写入任务库
			threatTask := fileimportes.NewFileImportTaskThreatsModel()
			threatTaskId := fmt.Sprintf("%d_%s", taskId, threatId)
			data, _ = sjson.Set(data, "task_id", taskId)
			data, _ = sjson.Set(data, "child_task_id", taskChildId)
			threatTaskReq := elastic.NewBulkIndexRequest().Index(threatTask.IndexName()).Id(threatTaskId).Doc(data)
			threatTaskBulkRequest = threatTaskBulkRequest.Add(threatTaskReq)

			//写入过程库
			threatProcess := poc.NewProcessPocModel()
			data, _ = sjson.Set(data, "id", threatId)
			data, _ = sjson.Set(data, "poc_task_id", threatTaskId)
			data, _ = sjson.Set(data, "area", area.Id)
			data, _ = sjson.Set(data, "node", node.Id)
			data, _ = sjson.Set(data, "source", node.SourceId)

			threatProcess.Id = threatId
			threatProcess.TaskDataId = threatTaskId
			threatProcess.TaskId = taskId
			threatProcess.ChildTaskId = taskChildId
			threatProcess.Area = node.AreaId
			threatProcess.Node = node.Id
			threatProcess.Source = node.SourceId
			threatProcess.Ip = gjson.Get(data, "ip").String()
			threatProcess.Port = int(gjson.Get(data, "port").Int())
			threatProcess.IsPoc = int(gjson.Get(data, "is_poc").Int())
			threatProcess.Url = gjson.Get(data, "url").String()
			threatProcess.Level = int(gjson.Get(data, "level").Int())
			threatProcess.Name = gjson.Get(data, "common_title").String()
			threatProcess.VulType = gjson.Get(data, "vulType").String()
			threatProcess.Cve = gjson.Get(data, "cve").String()
			threatProcess.Cnvd = gjson.Get(data, "cnvd").String()
			threatProcess.Cnnvd = gjson.Get(data, "cnnvd").String()
			threatProcess.HasExp = int(gjson.Get(data, "has_exp").Int())
			threatProcess.HasPoc = int(gjson.Get(data, "has_poc").Int())
			threatProcess.Status = int(gjson.Get(data, "status").Int())
			threatProcess.Describe = gjson.Get(data, "describe").String()
			threatProcess.Details = gjson.Get(data, "details").String()
			threatProcess.Hazard = gjson.Get(data, "hazard").String()
			threatProcess.Suggestions = gjson.Get(data, "suggestions").String()
			json.Unmarshal([]byte(gjson.Get(data, "custom_fields").String()), &threatProcess.CustomFields)
			lastResponseAt, err := time.Parse(localtime.TimeFormat, gjson.Get(data, "last_response").String())
			if err == nil {
				threatProcess.LastResponseAt = localtime.NewLocalTime(lastResponseAt)
			}
			threatProcess.CreatedAt = localtime.NewLocalTime(time.Now())
			threatProcess.UpdatedAt = localtime.NewLocalTime(time.Now())
			threatProcess.OriginalId = gjson.Get(data, "original_id").String()
			//如果cve cnvd cnnvd都为空的话，将手动生成 OriginalId
			if gjson.Get(data, "cve").String() == "" && gjson.Get(data, "cnvd").String() == "" && gjson.Get(data, "cnnvd").String() == "" && gjson.Get(data, "original_id").String() == "" || threatProcess.IsPoc == 1 {
				// 用“漏洞名称+IP地址+端口+漏洞地址”生成一个唯一值，issue:#4984
				threatProcess.OriginalId = fmt.Sprintf("%s_%s_%d_%s", gjson.Get(data, "common_title").String(), gjson.Get(data, "ip").String(), gjson.Get(data, "port").Int(), gjson.Get(data, "url").String())
				// 计算hash
				threatProcess.OriginalId = utils.Get32MD5Encode(threatProcess.OriginalId)
			}

			threatProcessReq := elastic.NewBulkIndexRequest().Index(threatProcess.IndexName()).Id(threatId).Doc(threatProcess)
			threatProcessBulkRequest = threatProcessBulkRequest.Add(threatProcessReq)
			num++

			// threatIds = append(threatIds, map[string]interface{}{"id": threatId, "is_poc": gjson.Get(value.String(), "is_poc").String(), "task_id": taskChildId})

			if num == 200 {
				err := insertThreatBulkRequest(threatTaskBulkRequest, threatProcessBulkRequest, taskId, taskChildId)
				if err != nil {
					return false
				}
				err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(taskChildId, num)
				if err != nil {
					logs.GetSyncLogger().Errorf("SyncFileImport syncThreats UpdateSyncDataSuccessTotal err:%s", err.Error())
				}

				//清空桶
				threatTaskBulkRequest = es.GetEsClient().Bulk()
				threatProcessBulkRequest = es.GetEsClient().Bulk()
				num = 0
			}

			return true
		})

		if num > 0 {
			err := insertThreatBulkRequest(threatTaskBulkRequest, threatProcessBulkRequest, taskId, taskChildId)
			if err != nil {
				return
			}

			err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(taskChildId, num)
			if err != nil {
				logs.GetSyncLogger().Errorf("SyncFileImport syncThreats UpdateSyncDataSuccessTotal err:%s", err.Error())
			}

			// logs.GetSyncLogger().Infof("SyncFileImport syncThreats threatIds info:%v", threatIds)
			// // 发送任务开始标识
			// rq.Push(cfg.LoadQueue().VulnMergeQueue, []map[string]interface{}{{"task_id": taskChildId, "task_type": "sync", "is_start": true}})
			// successCount := rq.Push(cfg.LoadQueue().VulnMergeQueue, threatIds)
			// logs.GetSyncLogger().Infof("SyncFileImport syncThreats queue Push successCount:%d, expectCount:%d", successCount, len(threatIds))

		}

	}

	logs.GetSyncLogger().Infof("SyncFileImport syncThreats end taskInfo:%d,taskChildId:%d", taskId, taskChildId)
	// // 发送任务结束标识
	// rq.Push(cfg.LoadQueue().VulnMergeQueue, []map[string]interface{}{{"task_id": taskChildId, "task_type": "sync", "total": total, "is_end": true}})
	//更新子任务完成状态
	err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(taskChildId, data_sync_child_task.StatusSuccess)
	if err != nil {
		logs.GetSyncLogger().Errorf("SyncFileImport syncThreats UpdateStatusById StatusSuccess err:%s", err.Error())
	}
}

func genFilterKey(data string) string {
	areaName := gjson.Get(data, "area").String()
	ip := gjson.Get(data, "ip").String()
	return areaName + "_" + ip
}

// syncAssets 同步资产
func syncAssets(rows string, taskInfo string, node *data_source.Node) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())
	logs.GetSyncLogger().Infof("SyncFileImport syncAssets start taskId:%d,taskChildId:%d", taskId, taskChildId)

	chunkSize := 10000                                    //定义每个 Goroutine 处理的数据块大小
	start := 0                                            //起始索引
	end := chunkSize                                      //结束索引
	rowsIndexTotal := int(gjson.Get(rows, "#").Int()) - 1 //数据索引总量
	numChunks := (rowsIndexTotal + chunkSize) / chunkSize //计算计划需要开启的 Goroutine 数量
	isEnd := false                                        //是否处理完所有数据

	// 使用 WaitGroup 来等待所有 Goroutines 完成
	var wg sync.WaitGroup
	result := gjson.Parse(rows).Array()
	areasMap := make(map[string]network_areas.NetworkArea)
	areas, _, _ := network_areas.NewNetworkAreaModel().List(0, 0)
	for _, area := range areas {
		areasMap[area.Name] = area
	}

	// 将业务信息合并到BusinessInfo中,以支持同一个IP存在多个业务系统的情况
	businessInfoMap := make(map[string][]*assetses.BusinessInfo)
	for _, asset := range result {
		ip := gjson.Get(asset.String(), "ip").String()
		if ip == "" {
			continue
		}

		system := gjson.Get(asset.String(), "business_system").String()
		owner := gjson.Get(asset.String(), "business_owner").String()
		if system == "" && owner == "" {
			continue
		}
		businessInfoMap[ip] = append(businessInfoMap[ip], &assetses.BusinessInfo{
			BusinessSystem: system,
			BusinessOwner:  owner,
		})
	}

	var filterMap = make(map[string]struct{}, len(result))
	var rwmux sync.RWMutex
	for i := 0; i < numChunks; i++ {
		//首次循环判断数据是否大于数据索引总量
		if end > rowsIndexTotal {
			end = rowsIndexTotal + 1
		}

		if isEnd { //处理完所有数据直接结束
			break
		}

		if end == rowsIndexTotal {
			isEnd = true
			end = end + 1
		}

		wg.Add(1)
		// 下一个IP和当前IP是否同一个IP
		if end < rowsIndexTotal {
			if strIsEq(result[end].Get("ip").String(), result[end+1].Get("ip").String()) {
				end++
				if end == rowsIndexTotal { //需要校验最后一个数据
					isEnd = true
					end = end + 1
				} else {
					strIsEq(result[end].Get("ip").String(), result[end+1].Get("ip").String())
				}
			}
		}

		go func(i int, result []gjson.Result, node *data_source.Node) {
			defer wg.Done()
			logs.GetSyncLogger().Infof("SyncFileImport syncAssets start i=:%d", i)
			logs.GetSyncLogger().Infof("SyncFileImport syncAssets result Total:%d", len(result))
			total := 0

			if len(result) > 0 {
				var ports []assetses.PortInfo
				var num int //分页插入
				var mergeIps []map[string]interface{}
				assetTaskBulkRequest := es.GetEsClient().Bulk()
				assetProcessBulkRequest := es.GetEsClient().Bulk()
				assetProcessDeviceBulkRequest := es.GetEsClient().Bulk()

				for key, value := range result {
					logs.GetSyncLogger().Infof("SyncFileImport syncAssets value info:%s", value)
					// 临时处理 支持多端口

					t := make(map[string][]string)
					t["port"] = strings.Split(gjson.Get(value.String(), "port").String(), ",")
					t["protocol"] = strings.Split(gjson.Get(value.String(), "protocol").String(), ",")
					t["status"] = strings.Split(gjson.Get(value.String(), "status").String(), ",")
					t["url"] = strings.Split(gjson.Get(value.String(), "url").String(), ",")
					t["domain"] = strings.Split(gjson.Get(value.String(), "domain").String(), ",")
					t["title"] = strings.Split(gjson.Get(value.String(), "title").String(), ",")
					mx := max(len(t["port"]), len(t["protocol"]), len(t["status"]), len(t["url"]), len(t["domain"]), len(t["title"]))
					for j := range mx {
						p := assetses.PortInfo{}
						if j < len(t["port"]) {
							p.Port, _ = strconv.Atoi(t["port"][j])
						}

						if j < len(t["status"]) {
							p.Status, _ = strconv.Atoi(t["status"][j])
						}
						if j < len(t["protocol"]) {
							p.Protocol = t["protocol"][j]
						}
						if j < len(t["url"]) {
							p.Url = t["url"][j]
						}
						if j < len(t["domain"]) {
							p.Domain = t["domain"][j]
						}
						if j < len(t["title"]) {
							p.Title = t["title"][j]
						}
						ports = append(ports, p)
					}
					data := addPortsAndDelPort(value.String(), ports)
					filterKey := genFilterKey(data)
					rwmux.RLock()
					_, ok := filterMap[filterKey]
					rwmux.RUnlock()
					if len(result) == key+1 || !ok {
						rwmux.Lock()
						filterMap[filterKey] = struct{}{}
						rwmux.Unlock()
						ports = []assetses.PortInfo{}
						area := areasMap[gjson.Get(data, "area").String()]
						if area.Id > 0 {
							node.AreaId = area.Id
						}
						addBulkRequest(assetTaskBulkRequest, assetProcessBulkRequest, assetProcessDeviceBulkRequest, node, data, taskId, taskChildId, businessInfoMap)
						num++
						total++
						// bulk size 不能过大，过大第一会有性能问题，第二，因为我们使用了嵌套文档，会超过ES的嵌套文档数量限制
						if num == 5000 {
							// 这是引用，不能开协程，开了协程，下面马上重新赋值，可能会导致数据丢失
							insertAssetBulkRequest(assetTaskBulkRequest, assetProcessBulkRequest, assetProcessDeviceBulkRequest, taskId, taskChildId, mergeIps, num)
							//清空桶
							assetTaskBulkRequest = es.GetEsClient().Bulk()
							assetProcessBulkRequest = es.GetEsClient().Bulk()
							assetProcessDeviceBulkRequest = es.GetEsClient().Bulk()
							num = 0

						}
					}
				}

				if num > 0 { //存在插入
					go insertAssetBulkRequest(assetTaskBulkRequest, assetProcessBulkRequest, assetProcessDeviceBulkRequest, taskId, taskChildId, mergeIps, num)
				}
			}

			//子任务更新需要同步的数据总量
			err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataTotal(taskChildId, total)
			if err != nil {
				logs.GetSyncLogger().Errorf("SyncFileImport syncAssets UpdateSyncDataTotal err:%s", err.Error())
			}
			logs.GetSyncLogger().Infof("SyncFileImport syncAssets end i=:%d", i)
		}(i, result[start:end], node)

		start = end
		end = end + chunkSize
		if end >= rowsIndexTotal {
			end = rowsIndexTotal
		}
	}

	// 等待所有 Goroutines 完成
	wg.Wait()

	//检测任务完成
	go func() {
		for {
			time.Sleep(5 * time.Second)
			//更新子任务完成状态
			isEnd, err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusByIdToResult(taskChildId, data_sync_child_task.StatusSuccess)

			if err != nil {
				logs.GetSyncLogger().Errorf("SyncFileImport syncAssets UpdateStatusById StatusSuccess err:%s", err.Error())
				break
			}

			if isEnd {
				logs.GetSyncLogger().Infof("SyncFileImport syncAssets end taskId:%d,taskChildId:%d", taskId, taskChildId)
				break
			}
		}
	}()
}

func strIsEq(a string, b string) bool {
	return a == b
}
func addPortsAndDelPort(data string, ports []assetses.PortInfo) string {
	data, _ = sjson.Delete(data, "port")
	data, _ = sjson.Delete(data, "protocol")
	data, _ = sjson.Delete(data, "status")
	data, _ = sjson.Delete(data, "url")
	data, _ = sjson.Delete(data, "domain")
	data, _ = sjson.Delete(data, "title")
	data, _ = sjson.Set(data, "ports", ports)
	return data
}

type ConvertPublicParam struct {
	TaskId       uint64
	TaskChildId  uint64
	AssetTaskId  string
	NetworkCards string
	Mac          string
}

func addBulkRequest(assetTaskBulkRequest, assetProcessBulkRequest, assetProcessDeviceBulkRequest *es.SafeBulkService, node *data_source.Node, data string, taskId uint64, taskChildId uint64, businessInfoMap map[string][]*assetses.BusinessInfo) {
	assetId := fmt.Sprintf("%d_%d_%s", node.Id, node.AreaId, gjson.Get(data, "ip").String())
	data, _ = sjson.Delete(data, "area")
	data, _ = sjson.Set(data, "product", strings.Split(gjson.Get(data, "product").String(), ","))
	data, _ = sjson.Set(data, "source_id", node.SourceId)
	data, _ = sjson.Set(data, "node_id", node.Id)
	data, _ = sjson.Set(data, "area_id", node.AreaId)
	data, _ = sjson.Set(data, "created_at", localtime.NewLocalTime(time.Now()))
	data, _ = sjson.Set(data, "updated_at", localtime.NewLocalTime(time.Now()))

	//写入任务库
	assetTask := fileimportes.NewFileImportTaskAssetsModel()
	assetTaskId := fmt.Sprintf("%d_%s", taskId, assetId)

	data, _ = sjson.Set(data, "task_id", taskId)
	data, _ = sjson.Set(data, "child_task_id", taskChildId)

	assetTaskReq := elastic.NewBulkIndexRequest().Index(assetTask.IndexName()).Id(assetTaskId).Doc(data)
	assetTaskBulkRequest = assetTaskBulkRequest.Add(assetTaskReq)

	ip := gjson.Get(data, "ip").String()
	//写入过程库
	assetProcess := assetses.NewProcessAssetsModel()

	assetProcessId := fmt.Sprintf("%d_%d_%s", taskId, taskChildId, assetId)
	assetProcess.Id = assetProcessId
	assetProcess.Area = int(node.AreaId)
	assetProcess.Node = node.Id
	assetProcess.Source = node.SourceId
	assetProcess.TaskId = taskId
	assetProcess.ChildTaskId = taskChildId
	assetProcess.Status = int(gjson.Get(data, "on_line_status").Int())
	_ = json.Unmarshal([]byte(gjson.Get(data, "ports").String()), &assetProcess.Ports)
	assetProcess.Ip = gjson.Get(data, "ip").String()
	assetProcess.IpSegment = gjson.Get(data, "ip_segment").String()
	assetProcess.HostName = gjson.Get(data, "hostname").String()
	assetProcess.EthName = gjson.Get(data, "eth_name").String()
	assetProcess.Os = gjson.Get(data, "os").String()
	assetProcess.Kernel = gjson.Get(data, "kernel").String()
	assetProcess.DeviceId = gjson.Get(data, "device_id").String()
	assetProcess.DeviceName = gjson.Get(data, "device_name").String()
	assetProcess.Model = gjson.Get(data, "model").String()
	assetProcess.Maker = gjson.Get(data, "maker").String()
	assetProcess.Sn = gjson.Get(data, "sn").String()
	assetProcess.Mac = gjson.Get(data, "mac").String()
	_ = json.Unmarshal([]byte(gjson.Get(data, "product").String()), &assetProcess.Product)
	for _, product := range assetProcess.Product {
		if product != "" {
			ruleInfo := &assetses.RuleInfo{}
			ruleInfo.Product = product
			ruleInfo.Level = rule_infos_level.GetRuleInfoLevel(product)
			assetProcess.RuleInfos = append(assetProcess.RuleInfos, ruleInfo)
		}
	}
	assetProcess.BusinessSystem = gjson.Get(data, "business_system").String()
	assetProcess.Oper = gjson.Get(data, "oper").String()
	assetProcess.BusinessOwner = gjson.Get(data, "business_owner").String()
	assetProcess.BusinessInfo = businessInfoMap[ip]
	assetProcess.MachineRoom = gjson.Get(data, "machine_room").String()
	assetProcess.MemorySize = gjson.Get(data, "memory_size").String()
	assetProcess.MemoryUsageRate = gjson.Get(data, "memory_usage_rate").String()
	assetProcess.CpuMaker = gjson.Get(data, "cpu_maker").String()
	assetProcess.CpuBrand = gjson.Get(data, "cpu_brand").String()
	assetProcess.CpuCount = int(gjson.Get(data, "cpu_count").Int())
	assetProcess.DiskCount = int(gjson.Get(data, "disk_count").Int())
	assetProcess.DiskSize = int(gjson.Get(data, "disk_size").Int())
	assetProcess.DiskUsageRate = gjson.Get(data, "disk_usage_rate").String()
	assetProcess.LoadAverage = gjson.Get(data, "load_average").String()
	assetProcess.NetworkType = int(gjson.Get(data, "network_type").Int())
	assetProcess.TaskDataId = assetTaskId
	assetProcess.CustomFields = map[string]string{}
	assetAndDeviceCustomField := gjson.Get(data, "custom_fields").String()
	if assetAndDeviceCustomField != "" {
		customFields := map[string]string{}
		_ = json.Unmarshal([]byte(assetAndDeviceCustomField), &customFields)
		for k, v := range customFields {
			if strings.HasPrefix(k, "asset.custom_fields.") {
				fn := strings.TrimPrefix(k, "asset.custom_fields.")
				assetProcess.CustomFields[fn] = v
			}
		}
	}

	assetProcess.CreatedAt = localtime.NewLocalTime(time.Now())
	assetProcess.UpdatedAt = localtime.NewLocalTime(time.Now())

	assetProcess.PersonField = gjson.Get(data, "person_field").String()

	assetProcessReq := elastic.NewBulkIndexRequest().Index(assetProcess.IndexName()).Id(assetProcessId).Doc(assetProcess)
	assetProcessBulkRequest = assetProcessBulkRequest.Add(assetProcessReq)
	networkCardIps := gjson.Get(data, "network_card_ips").String()
	//有sn不为空，并且不是dhcp的ip，才去提取设备
	if assetProcess.Sn != "" && device_strategy.IsNotDhcpIp(assetProcess.Ip) {
		publicParam := ConvertPublicParam{
			NetworkCards: networkCardIps,
			TaskId:       taskId,
			TaskChildId:  taskChildId,
			AssetTaskId:  assetTaskId,
		}
		// 设备的自定义字段
		deviceCustomFields := map[string]string{}
		if assetAndDeviceCustomField != "" {
			customFields := map[string]string{}
			_ = json.Unmarshal([]byte(assetAndDeviceCustomField), &customFields)
			for k, v := range customFields {
				if strings.HasPrefix(k, "device.custom_fields.") {
					fn := strings.TrimPrefix(k, "device.custom_fields.")
					deviceCustomFields[fn] = v
				}
			}
		}
		processDevice := convertProcessDevice(node, assetProcess, publicParam, deviceCustomFields)
		processDeviceReq := elastic.NewBulkIndexRequest().Index(processDevice.IndexName()).Id(processDevice.Id).Doc(processDevice)
		assetProcessDeviceBulkRequest = assetProcessDeviceBulkRequest.Add(processDeviceReq)
	}
}

func convertProcessDevice(node *data_source.Node, assetProcess *assetses.ProcessAssets, param ConvertPublicParam, deviceCustomFields map[string]string) *device.ProcessDevice {
	processDevice := device.NewProcessDeviceModel()
	processDeviceId := strings.ReplaceAll(uuid.New().String(), "-", "")
	processDevice.Id = processDeviceId
	processDevice.UniqueKey = assetProcess.Sn
	processDevice.Area = int(node.AreaId)
	processDevice.Source = node.SourceId
	processDevice.Node = node.Id
	processDevice.TaskId = param.TaskId
	processDevice.ChildTaskId = param.TaskChildId
	processDevice.TaskDataId = param.AssetTaskId

	processDevice.HostName = assetProcess.HostName
	processDevice.EthName = ""
	processDevice.Os = assetProcess.Os
	processDevice.Kernel = assetProcess.Kernel
	processDevice.DeviceId = assetProcess.DeviceId
	processDevice.Sn = assetProcess.Sn
	processDevice.DeviceName = assetProcess.DeviceName
	processDevice.Maker = assetProcess.Maker
	processDevice.Mac = assetProcess.Mac
	processDevice.MachineRoom = assetProcess.MachineRoom
	processDevice.Status = assetProcess.Status
	processDevice.MemorySize = assetProcess.MemorySize
	processDevice.MemoryUsageRate = assetProcess.MemoryUsageRate
	processDevice.CpuMaker = assetProcess.CpuMaker
	processDevice.CpuBrand = assetProcess.CpuBrand
	processDevice.CpuCount = assetProcess.CpuCount
	processDevice.DiskCount = assetProcess.DiskCount
	processDevice.DiskSize = assetProcess.DiskSize
	processDevice.DiskUsageRate = assetProcess.DiskUsageRate
	processDevice.LoadAverage = assetProcess.LoadAverage
	processDevice.CustomFields = deviceCustomFields

	var privateIp []string
	var publicIp []string
	networkCardIps := strings.Split(param.NetworkCards, ",")
	for _, ipv4 := range networkCardIps {
		if utils.GetNetworkType(ipv4) == assetses.NetworkTypeExternal {
			publicIp = append(publicIp, ipv4)
		} else if utils.GetNetworkType(ipv4) == assetses.NetworkTypeInternal {
			privateIp = append(privateIp, ipv4)
		}
		processDevice.NetworkCards = append(processDevice.NetworkCards, &device.NetworkCardInfo{
			Ipv4: ipv4,
		})
	}
	if utils.GetNetworkType(assetProcess.Ip) == assetses.NetworkTypeExternal {
		publicIp = append(publicIp, assetProcess.Ip)
	} else if utils.GetNetworkType(assetProcess.Ip) == assetses.NetworkTypeInternal {
		privateIp = append(privateIp, assetProcess.Ip)
	}

	for _, v := range privateIp {
		if !utils.IsIP(v) {
			continue
		}
		processDevice.PrivateIp = append(processDevice.PrivateIp, v)
	}
	for _, v := range publicIp {
		if !utils.IsIP(v) {
			continue
		}
		processDevice.PublicIp = append(processDevice.PublicIp, v)
	}
	processDevice.CreatedAt = assetProcess.CreatedAt
	processDevice.UpdatedAt = assetProcess.UpdatedAt
	return processDevice
}

func convertIsPublicToNetwork(isPublic int) int {
	networkType := 3
	if isPublic == 1 {
		networkType = 1
	} else if isPublic == 2 {
		networkType = 2
	} else {
		networkType = 3
	}
	return networkType
}

func insertAssetBulkRequest(assetTaskBulkRequest *es.SafeBulkService, assetProcessBulkRequest, assetProcessDeviceBulkRequest *es.SafeBulkService, taskId uint64, taskChildId uint64, mergeIps []map[string]interface{}, num int) {
	assetProcessBulkReq, err := assetProcessBulkRequest.Refresh("true").Do(context.Background())
	if err != nil || assetProcessBulkReq.Errors {
		errString := handle_es_bulk_error.HandleBulkResp(assetses.NewProcessAssetsModel().IndexName(), err, assetProcessBulkReq)
		logs.GetSyncLogger().Errorf("SyncFileImport syncAssets assetProcessBulkRequest.Do err:%s", errString)
		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("SyncFileImport syncAssets assetProcessBulkRequest.Do task UpdateFailById err:%s", taskErr)
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("SyncFileImport syncAssets assetProcessBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
		}
	}

	assetTaskBulkReq, err := assetTaskBulkRequest.Refresh("true").Do(context.Background())
	if err != nil || assetTaskBulkReq.Errors {
		errString := handle_es_bulk_error.HandleBulkResp(fileimportes.NewFileImportTaskAssetsModel().IndexName(), err, assetTaskBulkReq)
		logs.GetSyncLogger().Errorf("SyncFileImport syncAssets assetTaskResponse.Do err:%s", errString)
		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("SyncFileImport syncAssets assetTaskBulkReq.Do task UpdateFailById err:%s", taskErr)
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("SyncFileImport syncAssets assetTaskBulkReq.Do childTask UpdateFailById err:%s", childTaskErr.Error())
		}
	}
	if assetProcessDeviceBulkRequest.NumberOfActions() > 0 {
		processDeviceResp, err := assetProcessDeviceBulkRequest.Refresh("true").Do(context.Background())
		if err != nil || processDeviceResp.Errors {
			errString := handle_es_bulk_error.HandleBulkResp(device.NewProcessDeviceModel().IndexName(), err, processDeviceResp)
			logs.GetSyncLogger().Errorf("SyncFileImport syncAssets processDeviceResp.Do err:%s", errString)

			//更新主任务状态为失败
			taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
			if taskErr != nil {
				logs.GetSyncLogger().Errorf("SyncFileImport syncAssets processDeviceResp.Do task UpdateFailById err:%s", taskErr)
			}

			//更新子任务状态为失败
			childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
			if childTaskErr != nil {
				logs.GetSyncLogger().Errorf("SyncFileImport syncAssets processDeviceResp.Do childTask UpdateFailById err:%s", childTaskErr.Error())
			}
			return
		}
	}
	if err == nil {
		//更新成功数量
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(taskChildId, num)
		if err != nil {
			logs.GetSyncLogger().Errorf("SyncFileImport syncAssets UpdateSyncDataSuccessTotal err:%s", err.Error())
		}
		logs.GetSyncLogger().Infof("SyncFileImport syncAssets mergeIps info:%v", mergeIps)
	}
}

func insertThreatBulkRequest(threatTaskBulkRequest *es.SafeBulkService, threatProcessBulkRequest *es.SafeBulkService, taskId uint64, taskChildId uint64) error {
	threatProcessBulkReq, err := threatProcessBulkRequest.Refresh("true").Do(context.Background())
	if err != nil || threatProcessBulkReq.Errors {
		errString := handle_es_bulk_error.HandleBulkResp(poc.NewProcessPocModel().IndexName(), err, threatProcessBulkReq)
		logs.GetSyncLogger().Errorf("SyncFileImport syncThreats threatProcessBulkRequest.Do err:%s", errString)

		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("SyncFileImport syncThreats threatProcessBulkRequest.Do task UpdateFailById err:%s", taskErr)
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("SyncFileImport syncThreats threatProcessBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
		}
		return err
	}

	threatTaskBulkReq, err := threatTaskBulkRequest.Refresh("true").Do(context.Background())
	if err != nil || threatTaskBulkReq.Errors {
		errString := handle_es_bulk_error.HandleBulkResp(fileimportes.NewFileImportTaskThreatsModel().IndexName(), err, threatTaskBulkReq)
		logs.GetSyncLogger().Errorf("SyncFileImport syncThreats threatTaskBulkRequest.Do err:%s", errString)

		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("SyncFileImport syncThreats threatTaskBulkRequest.Do task UpdateFailById err:%s", taskErr)
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("SyncFileImport syncThreats threatTaskBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
		}
		return err
	}
	return nil
}

func insertPeopleBulkRequest(employeeTaskBulkRequest *es.SafeBulkService, employeeProcessBulkRequest *es.SafeBulkService, taskId uint64, taskChildId uint64) error {
	employeeProcessBulkReq, err := employeeProcessBulkRequest.Refresh("true").Do(context.Background())
	if err != nil || employeeProcessBulkReq.Errors {
		errString := handle_es_bulk_error.HandleBulkResp(staff.NewProcessStaffModel().IndexName(), err, employeeProcessBulkReq)
		logs.GetSyncLogger().Errorf("SyncFileImport syncEmployees employeeProcessBulkRequest.Do err:%s", errString)

		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("SyncFileImport syncEmployees employeeProcessBulkRequest.Do task UpdateFailById err:%s", taskErr)
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("SyncFileImport syncEmployees employeeProcessBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
		}
		return err
	}

	employeeTaskBulkReq, err := employeeTaskBulkRequest.Refresh("true").Do(context.Background())
	if err != nil || employeeTaskBulkReq.Errors {
		errString := handle_es_bulk_error.HandleBulkResp(fileimportes.NewFileImportTaskPeoplesModel().IndexName(), err, employeeTaskBulkReq)
		logs.GetSyncLogger().Errorf("SyncFileImport syncEmployees employeeTaskBulkRequest.Do err:%s", errString)

		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("SyncFileImport syncEmployees employeeTaskBulkRequest.Do task UpdateFailById err:%s", taskErr)
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("SyncFileImport syncEmployees employeeTaskBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
		}
		return err
	}
	return nil
}
