package crontab

import (
	"fmt"
	license2 "fobrain/fobrain/app/repository/settings/license"
	"fobrain/fobrain/common/license"
	"fobrain/fobrain/logs"
	mcron "fobrain/models/mysql/crontab"
	"fobrain/pkg/cfg"
	"slices"

	testcommon "fobrain/fobrain/tests/common_test"
)

func RegisterList() []*mcron.CrontabModel {
	list := []*mcron.CrontabModel{
		RegisterNewAssetAlarm(),
		RegisterIpMappingAlarm(),
		RegisterClearExpiredTokens(),
		RegisterDispatchComplianceMonitors(),
		RegisterExecComplianceMonitorTasks(),
		RegisterMonitorLogsQcSettingTasks(),
		RegisterMonitorBanTimeStopTasks(),
		RegisterMonitorNodeMode(),
		RegisterProactiveSyncStatus(),
		RegisterMonitorProactiveTasks(),
		RegisterMonitorSyncTaskExpires(),
		// RegisterQueryAsset(), //2025-02-19,所有环境都不存在任务对应的数据库表，任务无意义
		// RegisterGoSendEmail(), //2025-05-30 该任务注册错误导致无法执行，经与测试确认，暂未发现有业务受影响，暂时注释掉
		RegisterDispatchTasks(),
		RegisterPocAlarm(),
		RegisterPocAutoDistributeTasksTasks(),
		RegisterLicenseAlarm(),
		RegisterNetMappingAlarm(),
		RegisterNtpUpdateTasks(),
		RegisterClearAuditLogs(),
		RegJobsCheckDsResponseAt(),
		RegisterRefreshIPRangeProbeMapping(),
		RegisterBackupScheduleCheck(),
		RegisterBackupLockStatusCheck(),
	}
	// 获取产品授权模块
	license := license.GetLicense()
	models, err := license.GetProductModels()
	if err != nil {
		fmt.Println("获取产品授权模块失败", err)
	}
	fmt.Println("产品授权模块：", models)
	// 授权存在级联模块 或者 是dev模式 或者 是测试环境
	// cascade_admin 是级联上级模块，cascade是级联下级模块
	if slices.Contains(models, "enable_cascade") || slices.Contains(models, "enable_cascade-admin") || license2.CheckDevModel(cfg.LoadCommon().Env) || testcommon.IsTest() {
		fmt.Println("授权存在级联模块 或者 是dev模式 或者 是测试环境")
		list = append(list, RegisterCascadNodeTasks())
	}
	return list
}

func NewSchedulerJob(name, spec, method, params string) *mcron.CrontabModel {
	return &mcron.CrontabModel{
		UserId:  1,
		Name:    name,
		Spec:    spec,
		Type:    mcron.TypeSystem,
		Status:  mcron.StatusEnable,
		Method:  method,
		Params:  params,
		Running: mcron.RunningNo,
	}
}

func Init() {
	cronModel := mcron.NewCrontabModel()
	// 获取已经注册的定时任务列表
	registeredList, _, _ := cronModel.List(0, 0)
	registeredMap := make(map[string]*mcron.CrontabModel)
	for _, v := range registeredList {
		registeredMap[v.Method] = v
	}

	// 获取需要注册的定时任务列表
	list := RegisterList()

	// 遍历需要注册的定时任务列表，如果已经注册，则跳过，否则注册
	for _, v := range list {
		if v == nil {
			continue
		}
		if r, ok := registeredMap[v.Method]; ok {
			if r.Spec != v.Spec {
				v.Id = r.Id
				err := cronModel.Updates(v)
				if err != nil {
					logs.GetLogger().Errorf("更新定时任务失败: %v", err)
				}
			}
			continue
		}
		err := cronModel.Create(v)
		if err != nil {
			logs.GetLogger().Errorf("注册定时任务失败: %v", err)
		}
	}
}
