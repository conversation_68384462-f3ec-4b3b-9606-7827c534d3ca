package x_ray

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"fobrain/fobrain/logs"
	"io/ioutil"
	"net/http"

	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

// Push
// @Summary: 推送任务信息到 x-ray
func (x *XRay) Push(body []byte) (int64, error) {
	apiUri := fmt.Sprintf(PushTasking, cast.ToString(x.GetConfig("protocol")), cast.ToString(x.GetConfig("ip")))

	// 创建 HTTP 请求
	req, err := http.NewRequest("POST", apiUri, bytes.NewBuffer(body))
	if err != nil {
		return 0, fmt.Errorf("failed to create HTTP request: %v", err)
	}

	// 设置请求头信息
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("token", x.GetToken())

	// 打印详细的请求信息
	logs.GetLogger().Infow("x-ray push request details",
		"method", req.Method,
		"url", req.URL.String(),
		"headers", req.Header,
		"body", string(body))

	// 跳过 TLS 证书验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		logs.GetLogger().Errorw("x-ray push request failed",
			"method", req.Method,
			"url", req.URL.String(),
			"error", err.Error())
		return 0, fmt.Errorf("failed to send HTTP request: %v", err)
	}
	defer resp.Body.Close()

	response, _ := ioutil.ReadAll(resp.Body)

	// 打印详细的响应信息
	logs.GetLogger().Infow("x-ray push response details",
		"method", req.Method,
		"url", req.URL.String(),
		"status_code", resp.StatusCode,
		"status", resp.Status,
		"headers", resp.Header,
		"body", string(response),
		"content_length", resp.ContentLength)

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("received non-OK response: %s", string(response))
	}

	type CreateTaskResponse struct {
		Err  string `json:"err"`
		Msg  string `json:"msg"`
		Data struct {
			Id int64 `json:"id"`
		} `json:"data"`
	}

	// 解析响应体
	var createTaskResp CreateTaskResponse

	json.Unmarshal(response, &createTaskResp)

	// 如果对接放返回失败，则标记任务为失败。
	if createTaskResp.Err != "" {
		return 0, fmt.Errorf(createTaskResp.Msg)
	}

	return createTaskResp.Data.Id, nil
}

// Plugin
// @Summary 获取 x-ray 插件信息
func (x *XRay) Plugin(nodeId int64) (res interface{}, err error) {
	template, err := x.Template(nodeId)
	if err != nil {
		return nil, err
	}

	return template.TaskSetting["pluginSetting"], nil
}

type TaskInfoResponse struct {
	Err  string               `json:"err"`
	Msg  string               `json:"msg"`
	Data TaskInfoResponseData `json:"data"`
}

type TaskInfoResponseData struct {
	Id          int64   `json:"id"`
	PlanId      int64   `json:"plan_id"`
	CreatedTime string  `json:"created_time"`
	StartTime   string  `json:"start_time"`
	EndTime     string  `json:"end_time"`
	Status      string  `json:"status"`
	Xprocess    []int64 `json:"xprocess"`
}

func (x *XRay) GetTaskInfo(taskId int64) (*TaskInfoResponseData, error) {
	apiUri := fmt.Sprintf(TaskInfo, cast.ToString(x.GetConfig("protocol")), cast.ToString(x.GetConfig("ip")), x.XProgressId(taskId))

	// 创建 HTTP 请求
	req, err := http.NewRequest("GET", apiUri, nil)
	if err != nil {
		return &TaskInfoResponseData{}, fmt.Errorf("failed to create HTTP request: %v", err)
	}

	// 设置请求头信息
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("token", x.GetToken())

	// 打印详细的请求信息
	logs.GetLogger().Infow("x-ray get task info request details",
		"method", req.Method,
		"url", req.URL.String(),
		"headers", req.Header,
		"task_id", taskId)

	// 跳过 TLS 证书验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		logs.GetLogger().Errorw("x-ray get task info request failed",
			"method", req.Method,
			"url", req.URL.String(),
			"task_id", taskId,
			"error", err.Error())
		return &TaskInfoResponseData{}, fmt.Errorf("failed to send HTTP request: %v", err)
	}
	defer resp.Body.Close()

	response, _ := ioutil.ReadAll(resp.Body)

	// 打印详细的响应信息
	logs.GetLogger().Infow("x-ray get task info response details",
		"method", req.Method,
		"url", req.URL.String(),
		"task_id", taskId,
		"status_code", resp.StatusCode,
		"status", resp.Status,
		"headers", resp.Header,
		"body", string(response),
		"content_length", resp.ContentLength)

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return &TaskInfoResponseData{}, fmt.Errorf("received non-OK response: %s", string(response))
	}

	// 解析响应体
	var progressResp TaskInfoResponse
	json.Unmarshal(response, &progressResp)

	return &progressResp.Data, nil
}

func (x *XRay) GetProgress(id int64) (float64, error) {
	apiUri := fmt.Sprintf(TaskProgress, cast.ToString(x.GetConfig("protocol")), cast.ToString(x.GetConfig("ip")), x.XProgressId(id))

	// 创建 HTTP 请求
	req, err := http.NewRequest("GET", apiUri, nil)
	if err != nil {
		return 0, fmt.Errorf("failed to create HTTP request: %v", err)
	}

	// 设置请求头信息
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("token", x.GetToken())

	// 打印详细的请求信息
	logs.GetLogger().Infow("x-ray get progress request details",
		"method", req.Method,
		"url", req.URL.String(),
		"headers", req.Header,
		"task_id", id)

	// 跳过 TLS 证书验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		logs.GetLogger().Errorw("x-ray get progress request failed",
			"method", req.Method,
			"url", req.URL.String(),
			"task_id", id,
			"error", err.Error())
		return 0, fmt.Errorf("failed to send HTTP request: %v", err)
	}
	defer resp.Body.Close()

	response, _ := ioutil.ReadAll(resp.Body)

	// 打印详细的响应信息
	logs.GetLogger().Infow("x-ray get progress response details",
		"method", req.Method,
		"url", req.URL.String(),
		"task_id", id,
		"status_code", resp.StatusCode,
		"status", resp.Status,
		"headers", resp.Header,
		"body", string(response),
		"content_length", resp.ContentLength)

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("received non-OK response: %s", string(response))
	}

	type ProgressResponse struct {
		Err  string `json:"err"`
		Msg  string `json:"msg"`
		Data struct {
			Progress float64 `json:"progress"`
			Status   string  `json:"status"`
		} `json:"data"`
	}

	// 解析响应体
	var progressResp ProgressResponse
	json.Unmarshal(response, &progressResp)

	return progressResp.Data.Progress, nil
}

// XProgressId
// @Summary 获取 x-ray 任务详情
func (x *XRay) XProgressId(taskId int64) int64 {
	apiUri := fmt.Sprintf(TaskPlan, cast.ToString(x.GetConfig("protocol")), cast.ToString(x.GetConfig("ip")), taskId)

	req, err := http.NewRequest("GET", apiUri, nil)
	if err != nil {
		logs.GetLogger().Errorw("x-ray get progress id create request failed",
			"task_id", taskId,
			"error", err.Error())
		return 0
	}

	// 设置请求头信息
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("token", x.GetToken()) // 根据文档要求使用 'token' 作为请求头

	// 打印详细的请求信息
	logs.GetLogger().Infow("x-ray get progress id request details",
		"method", req.Method,
		"url", req.URL.String(),
		"headers", req.Header,
		"task_id", taskId)

	// 跳过 TLS 证书验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		logs.GetLogger().Errorw("x-ray get progress id request failed",
			"method", req.Method,
			"url", req.URL.String(),
			"task_id", taskId,
			"error", err.Error())
		return 0
	}
	defer resp.Body.Close()

	body, _ := ioutil.ReadAll(resp.Body)

	// 打印详细的响应信息
	logs.GetLogger().Infow("x-ray get progress id response details",
		"method", req.Method,
		"url", req.URL.String(),
		"task_id", taskId,
		"status_code", resp.StatusCode,
		"status", resp.Status,
		"headers", resp.Header,
		"body", string(body),
		"content_length", resp.ContentLength)

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return 0
	}

	// 解析响应体
	var createTaskResp TaskInfoResponse
	json.Unmarshal(body, &createTaskResp)

	if (len(createTaskResp.Data.Xprocess) - 1) >= 0 {
		return createTaskResp.Data.Xprocess[len(createTaskResp.Data.Xprocess)-1]
	} else {
		return 0
	}

}

type ResultResponse struct {
	Err  string `json:"err"`
	Msg  string `json:"msg"`
	Data struct {
		Count  int64       `json:"count"`
		Result []ResultRes `json:"result"`
	} `json:"data"`
}

type ResultRes struct {
	Id     int64 `json:"id"`
	Target []struct {
		Url    string `json:"url"`
		Params []any  `json:"Params"`
	} `json:"target"`
	Title string `json:"title"`
}

// GetTaskThreats
// @Summary 获取漏洞结果
func (x *XRay) GetTaskThreats(taskId, limit, offset int64) (string, error) {
	params := map[string]interface{}{
		"limit":       limit,
		"offset":      offset,
		"xprocess_id": x.XProgressId(taskId),
		"result_type": "vuln",
	}

	// 将任务结构转为 JSON 格式
	jsonData, err := json.Marshal(params)
	if err != nil {
		return "", err
	}

	apiUri := fmt.Sprintf(TaskResult, cast.ToString(x.GetConfig("protocol")), cast.ToString(x.GetConfig("ip")))
	// 创建 HTTP 请求
	req, err := http.NewRequest("POST", apiUri, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}
	// 设置请求头信息
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("token", x.GetToken()) // 根据文档要求使用 'token' 作为请求头

	// 打印详细的请求信息
	logs.GetLogger().Infow("x-ray get task threats request details",
		"method", req.Method,
		"url", req.URL.String(),
		"headers", req.Header,
		"body", string(jsonData),
		"task_id", taskId,
		"limit", limit,
		"offset", offset)

	// 跳过 TLS 证书验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		logs.GetLogger().Errorw("x-ray get task threats request failed",
			"method", req.Method,
			"url", req.URL.String(),
			"task_id", taskId,
			"error", err.Error())
		return "", err
	}
	defer resp.Body.Close()

	body, _ := ioutil.ReadAll(resp.Body)

	// 打印详细的响应信息
	logs.GetLogger().Infow("x-ray get task threats response details",
		"method", req.Method,
		"url", req.URL.String(),
		"task_id", taskId,
		"status_code", resp.StatusCode,
		"status", resp.Status,
		"headers", resp.Header,
		"body", string(body),
		"content_length", resp.ContentLength)

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return "", err
	}

	return gjson.Get(string(body), "data").String(), nil
}

func (x *XRay) GetToken() string {
	return cast.ToString(x.GetConfig("appkey"))
}

func (x *XRay) DelNodeTask(nodeTaskId int) error {
	apiUri := fmt.Sprintf(UriXrayDelTask, cast.ToString(x.GetConfig("protocol")), cast.ToString(x.GetConfig("ip")), nodeTaskId)

	// 创建 HTTP 请求
	req, err := http.NewRequest("DELETE", apiUri, nil)
	if err != nil {
		return fmt.Errorf("failed to create HTTP request: %v", err)
	}

	// 设置请求头信息
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("token", x.GetToken())

	// 打印详细的请求信息
	logs.GetLogger().Infow("x-ray delete node task request details",
		"method", req.Method,
		"url", req.URL.String(),
		"headers", req.Header,
		"node_task_id", nodeTaskId)

	// 跳过 TLS 证书验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		logs.GetLogger().Errorw("x-ray delete node task request failed",
			"method", req.Method,
			"url", req.URL.String(),
			"node_task_id", nodeTaskId,
			"error", err.Error())
		return fmt.Errorf("failed to send HTTP request: %v", err)
	}
	defer resp.Body.Close()

	body, _ := ioutil.ReadAll(resp.Body)

	// 打印详细的响应信息
	logs.GetLogger().Infow("x-ray delete node task response details",
		"method", req.Method,
		"url", req.URL.String(),
		"node_task_id", nodeTaskId,
		"status_code", resp.StatusCode,
		"status", resp.Status,
		"headers", resp.Header,
		"body", string(body),
		"content_length", resp.ContentLength)

	return nil
}

// StopTask 暂停任务
func (x *XRay) StopTask(nodeTaskId int) error {
	apiUri := fmt.Sprintf(UriStopTask, cast.ToString(x.GetConfig("protocol")), cast.ToString(x.GetConfig("ip")))
	progressId := x.XProgressId(int64(nodeTaskId))
	body := map[string]any{"id": progressId}
	jsonBody, err := json.Marshal(body)
	if err != nil {
		return err
	}

	// 创建 HTTP 请求
	req, err := http.NewRequest("POST", apiUri, bytes.NewBuffer(jsonBody))
	if err != nil {
		return fmt.Errorf("failed to create HTTP request: %v", err)
	}

	// 设置请求头信息
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("token", x.GetToken())

	// 打印详细的请求信息
	logs.GetLogger().Infow("x-ray stop task request details",
		"method", req.Method,
		"url", req.URL.String(),
		"headers", req.Header,
		"body", string(jsonBody),
		"node_task_id", nodeTaskId,
		"progress_id", progressId)

	// 跳过 TLS 证书验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		logs.GetLogger().Errorw("x-ray stop task request failed",
			"method", req.Method,
			"url", req.URL.String(),
			"node_task_id", nodeTaskId,
			"error", err.Error())
		return fmt.Errorf("failed to send HTTP request: %v", err)
	}
	defer resp.Body.Close()

	responseBody, _ := ioutil.ReadAll(resp.Body)

	// 打印详细的响应信息
	logs.GetLogger().Infow("x-ray stop task response details",
		"method", req.Method,
		"url", req.URL.String(),
		"node_task_id", nodeTaskId,
		"status_code", resp.StatusCode,
		"status", resp.Status,
		"headers", resp.Header,
		"body", string(responseBody),
		"content_length", resp.ContentLength)

	return nil
}

// StartTask 开始任务
func (x *XRay) StartTask(nodeTaskId int) error {
	apiUri := fmt.Sprintf(UriStartTask, cast.ToString(x.GetConfig("protocol")), cast.ToString(x.GetConfig("ip")))
	progressId := x.XProgressId(int64(nodeTaskId))
	body := map[string]any{"id": progressId}
	jsonBody, err := json.Marshal(body)
	if err != nil {
		return err
	}

	// 创建 HTTP 请求
	req, err := http.NewRequest("POST", apiUri, bytes.NewBuffer(jsonBody))
	if err != nil {
		return fmt.Errorf("failed to create HTTP request: %v", err)
	}

	// 设置请求头信息
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("token", x.GetToken())

	// 打印详细的请求信息
	logs.GetLogger().Infow("x-ray start task request details",
		"method", req.Method,
		"url", req.URL.String(),
		"headers", req.Header,
		"body", string(jsonBody),
		"node_task_id", nodeTaskId,
		"progress_id", progressId)

	// 跳过 TLS 证书验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		logs.GetLogger().Errorw("x-ray start task request failed",
			"method", req.Method,
			"url", req.URL.String(),
			"node_task_id", nodeTaskId,
			"error", err.Error())
		return fmt.Errorf("failed to send HTTP request: %v", err)
	}
	defer resp.Body.Close()

	responseBody, _ := ioutil.ReadAll(resp.Body)

	// 打印详细的响应信息
	logs.GetLogger().Infow("x-ray start task response details",
		"method", req.Method,
		"url", req.URL.String(),
		"node_task_id", nodeTaskId,
		"status_code", resp.StatusCode,
		"status", resp.Status,
		"headers", resp.Header,
		"body", string(responseBody),
		"content_length", resp.ContentLength)

	return nil
}

// Retest 复测
func (x *XRay) Retest(ids []string) (string, error) {
	apiUri := fmt.Sprintf(UriThreatRepair, cast.ToString(x.GetConfig("protocol")), cast.ToString(x.GetConfig("ip")))
	logs.GetLogger().Infow("ReTest ", "apiUri", apiUri)

	body := map[string]any{
		"http_config": map[string]any{
			"cookies": []string{},
			"headers": []string{},
		},
		"vuln_id": ids[0],
	}
	jsonBody, err := json.Marshal(body)
	if err != nil {
		return "", err
	}
	// 创建 HTTP 请求
	req, err := http.NewRequest("POST", apiUri, bytes.NewBuffer(jsonBody))
	if err != nil {
		return "", fmt.Errorf("failed to create HTTP request: %v", err)
	}

	// 设置请求头信息
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("token", x.GetToken())

	// 打印详细的请求信息
	logs.GetLogger().Infow("x-ray retest request details",
		"method", req.Method,
		"url", req.URL.String(),
		"headers", req.Header,
		"body", string(jsonBody),
		"vuln_ids", ids)

	// 跳过 TLS 证书验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}

	resp, err := client.Do(req)
	if err != nil {
		logs.GetLogger().Errorw("x-ray retest request failed",
			"method", req.Method,
			"url", req.URL.String(),
			"vuln_ids", ids,
			"error", err.Error())
		return "", fmt.Errorf("failed to send HTTP request: %v", err)
	}
	defer resp.Body.Close()

	responseBody, _ := ioutil.ReadAll(resp.Body)

	// 打印详细的响应信息
	logs.GetLogger().Infow("x-ray retest response details",
		"method", req.Method,
		"url", req.URL.String(),
		"vuln_ids", ids,
		"status_code", resp.StatusCode,
		"status", resp.Status,
		"headers", resp.Header,
		"body", string(responseBody),
		"content_length", resp.ContentLength)

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("received non-OK response: %s", string(responseBody))
	}

	var response struct {
		Err  string `json:"err"`
		Msg  string `json:"msg"`
		Data struct {
			TaskId string `json:"task_id"`
		} `json:"data"`
	}

	if err = json.Unmarshal(responseBody, &response); err != nil {
		return "", err
	}

	// 检查响应中的错误字段
	if response.Err != "" {
		return "", fmt.Errorf("x-ray retest failed: %s", response.Err)
	}

	return response.Data.TaskId, nil
}

// GetCheckProgress 获取复测进度
func (x *XRay) GetCheckProgress(ids []string) (int, error) {
	response, err := x.getRetestResult(ids)
	if err != nil {
		return 0, err
	}
	if response.Err != "" {
		return 0, fmt.Errorf("x-ray get progress failed: %s", response.Err)
	}
	if response.Data.Running {
		return 50, nil
	}
	return 100, nil
}

// GetCheckResult 获取复测结果
func (x *XRay) GetCheckResult(ids []string, pocObj map[string]interface{}) (gjson.Result, int, error) {
	response, err := x.getRetestResult(ids)
	if err != nil {
		return gjson.Result{}, 0, err
	}
	jsonStr, _ := json.Marshal(response)
	return gjson.ParseBytes(jsonStr), 0, nil
}

type RetestResultResponse struct {
	Err  string `json:"err"`
	Msg  string `json:"msg"`
	Data struct {
		TaskId      int64  `json:"task_id"`
		Running     bool   `json:"running"`      // 漏洞复测任务是否正在执行,true 为正在执行，false 为执行完成
		Result      string `json:"result"`       // 漏洞复测任务的结果,NOTFIXED（未解决），FIXED（已修复），UNKNOW（未知）
		CreatedTime string `json:"created_time"` // 漏洞复测任务的创建时间
	} `json:"data"`
}

func (x *XRay) getRetestResult(ids []string) (*RetestResultResponse, error) {
	apiUri := fmt.Sprintf(UriThreatRetestResult, cast.ToString(x.GetConfig("protocol")), cast.ToString(x.GetConfig("ip")), ids[0])

	// 创建 HTTP 请求
	req, err := http.NewRequest("GET", apiUri, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %v", err)
	}

	// 设置请求头信息
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("token", x.GetToken())

	// 打印详细的请求信息
	logs.GetLogger().Infow("x-ray get retest result request details",
		"method", req.Method,
		"url", req.URL.String(),
		"headers", req.Header,
		"vuln_ids", ids)

	// 跳过 TLS 证书验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		logs.GetLogger().Errorw("x-ray get retest result request failed",
			"method", req.Method,
			"url", req.URL.String(),
			"vuln_ids", ids,
			"error", err.Error())
		return nil, fmt.Errorf("failed to send HTTP request: %v", err)
	}
	defer resp.Body.Close()

	response, _ := ioutil.ReadAll(resp.Body)

	// 打印详细的响应信息
	logs.GetLogger().Infow("x-ray get retest result response details",
		"method", req.Method,
		"url", req.URL.String(),
		"vuln_ids", ids,
		"status_code", resp.StatusCode,
		"status", resp.Status,
		"headers", resp.Header,
		"body", string(response),
		"content_length", resp.ContentLength)

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("received non-OK response: %s", string(response))
	}

	fmt.Println("x-ray retest result response: ", string(response))
	// 解析响应体
	var progressResp RetestResultResponse
	json.Unmarshal(response, &progressResp)

	return &progressResp, nil
}
