package foradar

import (
	"context"
	"fmt"
	"fobrain/fobrain/app/services/rule_infos_level"
	"fobrain/pkg/utils/handle_es_bulk_error"
	"time"

	"github.com/olivere/elastic/v7"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"

	"fobrain/fobrain/app/services/node/foradar"
	"fobrain/fobrain/common/constant"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	assetses "fobrain/models/elastic/assets"
	"fobrain/models/elastic/poc"
	foradares "fobrain/models/elastic/source/foradar"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
)

// SyncForadar 同步foradar数据
func SyncForadar(node *data_source.Node, taskInfo string) error {
	logs.GetSyncLogger().Infof("SyncForadar start taskInfo:%s", taskInfo)
	assetTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())
	threatTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncThreat)).Int())

	//获取foradarClient
	cli := foradar.NewFORadar()

	//设置fradarClient请求的节点
	if err := cli.SetNode(node.Id); err != nil {
		return err
	}

	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncAsset)).Int() == data_sync_task.SyncAsset {
		//更新资产任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(assetTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("Foeye syncAssets UpdateStatusById StatusDoing err:%s", err.Error())
		}
		go syncAssets(constant.DefaultPage, cli, taskInfo, node, 0)
	}

	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncThreat)).Int() == data_sync_task.SyncThreat {
		//更新漏洞任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(threatTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("Foeye syncThreats UpdateStatusById StatusDoing err:%s", err.Error())
		}
		go syncThreats(constant.DefaultPage, cli, taskInfo, node, 0)
	}

	return nil
}

// syncAssets 同步资产
func syncAssets(page int, cli *foradar.FORadar, taskInfo string, node *data_source.Node, successCount int) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())
	logs.GetSyncLogger().Infof("SyncForadar syncAssets start taskInfo:%d,taskChildId:%d", taskId, taskChildId)
	// 默认设置为3，智能判断
	networkType, err := node.GetNetworkType()
	if err != nil {
		logs.GetSyncLogger().Errorf("BkCmdb syncAssets GetNetworkType err:%s", err.Error())
	}
	personField := "name"
	if cli.GetConfig("person_field") != "" && cli.GetConfig("person_field") != nil {
		personField = cli.GetConfig("person_field").(string)
	}
	//获取资产
	assets, err := cli.GetAssets(page, constant.DefaultSize)
	if err != nil {
		logs.GetSyncLogger().Errorf("Foradar syncAssets GetAssets err:%s", err)

		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, "GetAssets:"+err.Error())
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("Foradar syncAssets GetAssets Task UpdateFailById err:%s", taskErr.Error())
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, "GetAssets:"+err.Error())
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("Foradar syncAssets GetAssets childTask UpdateFailById err:%s", childTaskErr.Error())
		}
	}

	total := gjson.Get(assets, "total").Int() //数据总量
	result := gjson.Get(assets, "items")

	//子任务更新需要同步的数据总量
	if page == constant.DefaultPage { //仅第一次同步
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateById(taskChildId, data_sync_child_task.DataSyncChildTask{
			SyncDataTotal: int(total),
		})

		if err != nil {
			logs.GetSyncLogger().Errorf("Foradar syncAssets UpdateById SyncDataTotal err:%s", err.Error())
		}
	}

	if result.Exists() && total > 0 {
		assetTaskBulkRequest := es.GetEsClient().Bulk()
		assetProcessBulkRequest := es.GetEsClient().Bulk()
		// 遍历 _id 字段，并根据 id 添加新的字段数据
		var num = 0
		result.ForEach(func(key, value gjson.Result) bool {
			logs.GetSyncLogger().Debugf("SyncForadar syncAssets value info:%s", value)
			data, _ := sjson.Set(value.String(), "node_id", node.Id)
			data, _ = sjson.Delete(data, "_id")
			data, _ = sjson.Delete(data, "host_reflect")

			ip := gjson.Get(value.String(), "ip").String()
			areaId := node.AreaId
			if ip != "" {
				// 获取区域
				areaId, err = node.GetAreaByIp(ip)
				if err != nil {
					logs.GetSyncLogger().Errorf("Foradar syncAssets GetAreaByIp failed,ip:%s,nodeId:%d,err:%s", ip, node.Id, err)
					r := data_sync_child_task.NewDataSyncChildTaskFailRecord()
					r.Create(&data_sync_child_task.DataSyncChildTaskFailRecord{
						ChildTaskId:  taskChildId,
						DataType:     "ip",
						DataContent:  ip,
						FailedReason: err.Error(),
					})
				}
			}
			data, _ = sjson.Set(data, "area_id", areaId)

			//兼容logo.hash返回类型不统一
			gjson.Get(data, "detail").ForEach(func(key, value gjson.Result) bool {
				data, _ = sjson.Delete(data, fmt.Sprintf("detail.%d.logo.hash", key.Int()))
				data, _ = sjson.Delete(data, fmt.Sprintf("host_list.%d.logo.hash", key.Int()))
				return true
			})
			assetId := fmt.Sprintf("%d_%d_%s", node.Id, areaId, ip)
			//写入任务库
			assetTask := foradares.NewForadarTaskAssetsModel()
			assetTaskId := fmt.Sprintf("%d_%s", taskId, assetId)
			data, _ = sjson.Set(data, "task_id", taskId)
			data, _ = sjson.Set(data, "child_task_id", taskChildId)
			assetTaskReq := elastic.NewBulkIndexRequest().Index(assetTask.IndexName()).Id(assetTaskId).Doc(data)
			assetTaskBulkRequest = assetTaskBulkRequest.Add(assetTaskReq)

			//写入过程库
			assetProcess := assetses.NewProcessAssetsModel()
			assetProcessId := fmt.Sprintf("%d_%d_%s", taskId, taskChildId, assetId)
			assetProcess.Id = assetProcessId
			assetProcess.TaskDataId = assetTaskId

			assetProcess.NetworkType = networkType
			assetProcess.PersonField = personField

			assetProcess.Area = int(areaId)
			assetProcess.Node = node.Id
			assetProcess.Source = node.SourceId
			assetProcess.TaskId = taskId
			assetProcess.ChildTaskId = taskChildId
			assetProcess.Ip = ip

			assetProcess.Status = 1
			if "在线" != gjson.Get(value.String(), "state").String() {
				assetProcess.Status = 2
			}

			var product []string
			var ruleInfos []*assetses.RuleInfo
			var ports []*assetses.PortInfo
			gjson.Get(value.String(), "rule_tags").ForEach(func(key, val gjson.Result) bool {
				pro := gjson.Get(val.String(), "cn_product").String()
				firstTag := gjson.Get(val.String(), "cn_parent_category").String()
				secondTag := gjson.Get(val.String(), "cn_category").String()
				product = append(product, pro)
				if pro != "" {
					ruleInfos = append(ruleInfos, &assetses.RuleInfo{
						Product:   pro,
						FirstTag:  firstTag,
						SecondTag: secondTag,
						Level:     rule_infos_level.GetRuleInfoLevel(pro),
					})
				}
				return true
			})

			gjson.Get(value.String(), "host_list").ForEach(func(k, v gjson.Result) bool {
				var portInfo assetses.PortInfo

				portInfo.Port = int(gjson.Get(v.String(), "port").Int())
				portInfo.Protocol = gjson.Get(v.String(), "protocol").String()
				portInfo.Status = int(gjson.Get(v.String(), "http_status_code").Int())
				portInfo.Url = gjson.Get(v.String(), "url").String()
				portInfo.Domain = gjson.Get(v.String(), "subdomain").String()
				portInfo.Title = gjson.Get(v.String(), "title").String()
				ports = append(ports, &portInfo)
				return true
			})
			assetProcess.Product = product
			assetProcess.RuleInfos = ruleInfos
			assetProcess.Ports = ports
			lastResponseAt, _ := time.Parse(localtime.TimeFormat, gjson.Get(value.String(), "last_update_time").String())
			if !lastResponseAt.IsZero() {
				assetProcess.LastResponseAt = localtime.NewLocalTime(lastResponseAt)
			}
			ct := gjson.Get(value.String(), "createtime").String()
			if ct == "" {
				ct = gjson.Get(value.String(), "create_time").String()
			}
			if ct != "" {
				createTime, _ := time.Parse(localtime.TimeFormat, ct)
				assetProcess.CreatedAt = localtime.NewLocalTime(createTime)
			} else {
				assetProcess.CreatedAt = localtime.NewLocalTime(time.Now())
			}
			assetProcess.UpdatedAt = localtime.Now()
			assetProcessReq := elastic.NewBulkIndexRequest().Index(assetProcess.IndexName()).Id(assetProcessId).Doc(assetProcess)
			assetProcessBulkRequest = assetProcessBulkRequest.Add(assetProcessReq)
			num = num + 1
			return true
		})
		if num > 0 {
			assetProcessBulkReq, err := assetProcessBulkRequest.Refresh("true").Do(context.Background())
			if err != nil || assetProcessBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(assetses.NewProcessAssetsModel().IndexName(), err, assetProcessBulkReq)
				logs.GetSyncLogger().Errorf("Foradar syncAssets assetProcessBulkRequest.Do err:%s", errString)
				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("Foradar syncAssets assetProcessBulkRequest.Do task UpdateFailById err:%s", taskErr)
				}
				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("Foradar syncAssets assetProcessBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
				}
				return
			}

			assetTaskBulkReq, err := assetTaskBulkRequest.Do(context.Background())
			if err != nil || assetTaskBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(foradares.NewForadarTaskAssetsModel().IndexName(), err, assetTaskBulkReq)
				logs.GetSyncLogger().Errorf("Foradar syncAssets assetTaskBulkRequest.Do err:%s", errString)

				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("Foradar syncAssets assetTaskBulkRequest.Do task UpdateFailById err:%s", taskErr)
				}

				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("Foradar syncAssets assetTaskBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
				}
			}

			if err == nil {
				//更新成功数量
				err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(taskChildId, len(assetTaskBulkReq.Items))
				if err != nil {
					logs.GetSyncLogger().Errorf("Foradar syncAssets UpdateSyncDataSuccessTotal err:%s", err.Error())
				}
			}
		}
	}

	if page*constant.DefaultSize <= int(total) && total > 0 {
		syncAssets(page+1, cli, taskInfo, node, successCount)
	} else {
		logs.GetSyncLogger().Infof("SyncForadar syncAssets end taskInfo:%d,taskChildId:%d", taskId, taskChildId)
		//更新子任务完成状态
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(taskChildId, data_sync_child_task.StatusSuccess)
		if err != nil {
			logs.GetSyncLogger().Errorf("Foradar syncAssets UpdateStatusById StatusSuccess err:%s", err.Error())
		}
	}
}

// syncThreats 同步漏洞
func syncThreats(page int, cli *foradar.FORadar, taskInfo string, node *data_source.Node, successCount int) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncThreat)).Int())
	logs.GetSyncLogger().Infof("SyncForadar syncThreats start taskInfo:%d,taskChildId:%d", taskId, taskChildId)

	//获取资产
	threats, err := cli.GetThreats(page, constant.DefaultSize)

	if err != nil {
		logs.GetSyncLogger().Errorf("Foradar syncThreats GetThreats err:%s", err)

		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, "GetThreats "+err.Error())
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("Foradar syncThreats GetThreats UpdateFailById Task err:%s", taskErr.Error())
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, "GetThreats "+err.Error())
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("Foradar syncThreats GetThreats UpdateFailById childTask err:%s", childTaskErr.Error())
		}
		return
	}

	total := gjson.Get(threats, "total").Int() //数据总量
	result := gjson.Get(threats, "items")

	//子任务更新需要同步的数据总量
	if page == constant.DefaultPage { //仅第一次同步
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateById(taskChildId, data_sync_child_task.DataSyncChildTask{
			SyncDataTotal: int(total),
		})

		if err != nil {
			logs.GetSyncLogger().Errorf("Foradar syncThreats UpdateById SyncDataTotal err:%s", err.Error())
		}
	}

	// client := redis.GetRedisClient()
	// rq := &queue.RedisQueue{Client: client}
	if result.Exists() && total > 0 {
		// var threatIds []map[string]interface{}
		threatTaskBulkRequest := es.GetEsClient().Bulk()
		threatProcessBulkRequest := es.GetEsClient().Bulk()

		// 遍历 _id 字段，并根据 id 添加新的字段数据
		var num = 0
		result.ForEach(func(key, value gjson.Result) bool {
			logs.GetSyncLogger().Debugf("SyncForadar syncThreats value info:%s", value)
			id := gjson.Get(value.String(), "id").String()

			data, _ := sjson.Set(value.String(), "node_id", node.Id)
			data, _ = sjson.Delete(data, "_id")
			data, _ = sjson.Set(data, "original_id", id)

			ip := gjson.Get(value.String(), "ip").String()
			areaId := node.AreaId
			if ip != "" {
				// 获取区域
				areaId, err = node.GetAreaByIp(ip)
				if err != nil {
					logs.GetSyncLogger().Errorf("Foradar syncThreats GetAreaByIp failed,ip:%s,nodeId:%d,err:%s", ip, node.Id, err)
					r := data_sync_child_task.NewDataSyncChildTaskFailRecord()
					r.Create(&data_sync_child_task.DataSyncChildTaskFailRecord{
						ChildTaskId:  taskChildId,
						DataType:     "ip",
						DataContent:  ip,
						FailedReason: err.Error(),
					})
				}
			}
			data, _ = sjson.Set(data, "area_id", areaId)

			//写入资产库
			threatId := fmt.Sprintf("%d_%d_%s_%d_%d", node.Id, areaId, id, taskId, taskChildId)
			//写入任务库
			threatTask := foradares.NewForadarTaskThreatsModel()
			threatTaskId := fmt.Sprintf("%d_%s", taskId, threatId)
			data, _ = sjson.Set(data, "task_id", taskId)
			data, _ = sjson.Set(data, "child_task_id", taskChildId)
			threatTaskReq := elastic.NewBulkIndexRequest().Index(threatTask.IndexName()).Id(threatTaskId).Doc(data)
			threatTaskBulkRequest = threatTaskBulkRequest.Add(threatTaskReq)

			//写入过程库
			threatProcess := poc.NewProcessPocModel()
			threatProcess.Id = threatId
			threatProcess.OriginalId = id
			threatProcess.TaskDataId = threatTaskId
			threatProcess.TaskId = taskId
			threatProcess.ChildTaskId = taskChildId
			threatProcess.Area = areaId
			threatProcess.Node = node.Id
			threatProcess.Source = node.SourceId
			threatProcess.IsPoc = poc.YesPoc
			threatProcess.HasPoc = poc.YesPoc
			threatProcess.Ip = ip
			threatProcess.Port = int(gjson.Get(value.String(), "port").Int())
			threatProcess.Url = gjson.Get(value.String(), "url").String()

			var level int
			switch gjson.Get(value.String(), "level ").Int() {
			case 0:
				level = 1 // 低危
			case 1:
				level = 2 // 中危
			case 2:
				level = 3 // 高危
			case 3:
				level = 4 // 严重
			default:
				level = 5 // 未知
			}
			//漏洞等级（低危 1 中危 2 高危 3 严重 4 未知 5）
			threatProcess.Level = level
			threatProcess.Name = gjson.Get(value.String(), "common_title").String()
			threatProcess.Cve = gjson.Get(value.String(), "cve_id").String()
			threatProcess.Cnvd = gjson.Get(value.String(), "cnvd").String()
			threatProcess.Cnnvd = gjson.Get(value.String(), "cnnvd").String()
			threatProcess.HasExp = int(gjson.Get(value.String(), "has_exp").Int())
			threatProcess.Status = int(gjson.Get(value.String(), "state").Int())
			threatProcess.Describe = gjson.Get(value.String(), "common_description").String()
			threatProcess.Details = gjson.Get(value.String(), "common_impact").String()
			threatProcess.Suggestions = gjson.Get(value.String(), "recommandation").String()
			lastResponseAt, _ := time.Parse(localtime.TimeFormat, gjson.Get(value.String(), "last_update_time").String())
			if !lastResponseAt.IsZero() {
				threatProcess.LastResponseAt = localtime.NewLocalTime(lastResponseAt)
			}
			threatProcess.Level = int(gjson.Get(value.String(), "level").Int()) //漏洞等级（低危 0 中危 1 高危 2 严重 3 未知 4）
			threatProcess.VulType = gjson.Get(value.String(), "vulType").String()
			ct := gjson.Get(value.String(), "createtime").String()
			if ct == "" {
				ct = gjson.Get(value.String(), "create_time").String()
			}
			if ct != "" {
				createTime, _ := time.Parse(localtime.TimeFormat, ct)
				threatProcess.CreatedAt = localtime.NewLocalTime(createTime)
			} else {
				threatProcess.CreatedAt = localtime.NewLocalTime(time.Now())
			}
			threatProcess.UpdatedAt = localtime.Now()
			threatProcessReq := elastic.NewBulkIndexRequest().Index(threatProcess.IndexName()).Id(threatId).Doc(threatProcess)
			threatProcessBulkRequest = threatProcessBulkRequest.Add(threatProcessReq)
			// threatIds = append(threatIds, map[string]interface{}{"id": threatId, "is_poc": poc.YesPoc, "task_id": taskChildId})
			num = num + 1
			return true
		})
		if num > 0 {
			threatProcessBulkReq, err := threatProcessBulkRequest.Refresh("true").Do(context.Background())
			if err != nil || threatProcessBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(poc.NewProcessPocModel().IndexName(), err, threatProcessBulkReq)
				logs.GetSyncLogger().Errorf("Foradar syncThreats threatProcessBulkRequest.Do err:%s", errString)

				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("Foradar syncThreats threatProcessBulkRequest.Do task UpdateFailById err:%s", taskErr)
				}

				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("Foradar syncThreats threatProcessBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
				}
				return
			}

			threatTaskBulkReq, err := threatTaskBulkRequest.Do(context.Background())
			if err != nil || threatTaskBulkReq.Errors {
				errString := handle_es_bulk_error.HandleBulkResp(foradares.NewForadarTaskThreatsModel().IndexName(), err, threatTaskBulkReq)

				logs.GetSyncLogger().Errorf("Foradar syncAssets threatTaskBulkRequest.Do err:%s", errString)

				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("Foradar syncThreats threatTaskBulkRequest.Do task UpdateFailById err:%s", taskErr)
				}

				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("Foradar syncThreats threatTaskBulkRequest.Do childTask UpdateFailById err:%s", childTaskErr.Error())
				}
				return
			}

			if err == nil {
				err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(taskChildId, len(threatTaskBulkReq.Items))
				if err != nil {
					logs.GetSyncLogger().Errorf("Foradar syncThreats UpdateSyncDataSuccessTotal err:%s", err.Error())
				}

				// logs.GetSyncLogger().Infof("Foradar syncThreats threatIds info:%v", threatIds)

				// // 发送任务开始标识
				// rq.Push(cfg.LoadQueue().VulnMergeQueue, []map[string]interface{}{{"task_id": taskChildId, "task_type": "sync", "is_start": true}})
				// successCount += rq.Push(cfg.LoadQueue().VulnMergeQueue, threatIds)
				// logs.GetSyncLogger().Infof("Foradar syncThreats queue Push successCount:%d, expectCount:%d", successCount, len(threatIds))
			}
		}
	}

	if page*constant.DefaultSize <= int(total) && total > 0 {
		syncThreats(page+1, cli, taskInfo, node, successCount)
	} else {
		logs.GetSyncLogger().Infof("SyncForadar syncThreats end taskInfo:%d,taskChildId:%d", taskId, taskChildId)
		// // 发送任务结束标识
		// rq.Push(cfg.LoadQueue().VulnMergeQueue, []map[string]interface{}{{"task_id": taskChildId, "task_type": "sync", "total": successCount, "is_end": true}})
		//更新子任务完成状态
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(taskChildId, data_sync_child_task.StatusSuccess)
		if err != nil {
			logs.GetSyncLogger().Errorf("Foradar syncThreats UpdateStatusById StatusSuccess err:%s", err.Error())
		}
	}
}
