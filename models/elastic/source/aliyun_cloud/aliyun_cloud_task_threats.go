package aliyun_cloud

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/olivere/elastic/v7"
)

type (
	AliYunCloudTaskThreats struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`         // NODE+区域+IP 作为唯一值去更新，用作数据对比
		CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
		UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
	}
)

// IndexName 索引名
func (q *AliYunCloudTaskThreats) IndexName() string {
	return "aliyun_cloud_task_threats"
}

func (q *AliYunCloudTaskThreats) String() string {
	if q == nil {
		return ""
	}
	return ""
}

func NewAliYunCloudTaskThreatsModel() *AliYunCloudTaskThreats {
	return &AliYunCloudTaskThreats{}
}

func (q *AliYunCloudTaskThreats) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("ip.keyword").
		Field("name.keyword")

	return field
}

// GetAssetListFields 列表
// key A.B.C 格式 都是key
func (q *AliYunCloudTaskThreats) GetThreatListFields() []map[string]string {
	return []map[string]string{
		{"name": "CveId", "all_key": "CveId"},
		{"name": "访问者源IP地址", "all_key": "source_ip"},
		{"name": "漏洞类型", "all_key": "vul_type"},
		{"name": "漏洞名称", "all_key": "Title"},
		{"name": "漏洞等级", "all_key": "level"},
		{"name": "漏洞状态", "all_key": "status"},
		{"name": "漏洞修复紧急程度", "all_key": "necessity"},
	}
}

// GetAssetListDetailFields 详情
func (q *AliYunCloudTaskThreats) GetThreatListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "CveId", "all_key": "CveId"},
		{"name": "访问者源IP地址", "all_key": "source_ip"},
		{"name": "漏洞类型", "all_key": "vul_type"},
		{"name": "漏洞名称", "all_key": "Title"},
		{"name": "漏洞等级", "all_key": "level"},
		{"name": "漏洞状态", "all_key": "status"},
		{"name": "漏洞修复紧急程度", "all_key": "necessity"},
		{"name": "创建漏洞", "all_key": "create_ts_start"},
		{"name": "漏洞修改", "all_key": "modify_ts"},
		{"name": "最近更新时间", "all_key": "last_time"},
	}
}
