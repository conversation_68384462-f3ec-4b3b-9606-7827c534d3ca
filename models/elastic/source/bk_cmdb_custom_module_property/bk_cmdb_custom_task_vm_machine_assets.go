package bk_cmdb_custom_module_property

import (
	"fmt"
	"fobrain/fobrain/common/localtime"
	"fobrain/initialize/es"
	"git.gobies.org/caasm/fobrain-components/utils"
	"github.com/olivere/elastic/v7"
)

type (
	BkCmdbCustomTaskVmMachineAssets struct {
		*es.BaseModel `json:"-"`
		Id            string          `json:"id"`         // NODE+区域+IP 作为唯一值去更新，用作数据对比
		CreatedAt     *localtime.Time `json:"created_at"` // 创建时间
		UpdatedAt     *localtime.Time `json:"updated_at"` // 更新时间
	}
)

// IndexName 索引名
func (b *BkCmdbCustomTaskVmMachineAssets) IndexName() string {
	return "bk_cmdb_vm_machine_task_assets"
}

func (b *BkCmdbCustomTaskVmMachineAssets) String() string {
	if b == nil {
		return ""
	}
	return ""
}

func NewBkCmdbCustomTaskVmMachineAssetsModel() *BkCmdbCustomTaskVmMachineAssets {
	return &BkCmdbCustomTaskVmMachineAssets{}
}

func (b *BkCmdbCustomTaskVmMachineAssets) NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", utils.Escape(keyword))).
		Field("ip_addr.keyword").
		Field("disk.keyword").
		Field("vm_name.keyword").
		Field("sn.keyword").
		Field("mac.keyword").
		Field("bk_inst_name.keyword")
	return field
}

func (b *BkCmdbCustomTaskVmMachineAssets) GetAssetListFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ip_addr", "all_key": "ip_addr"},
		{"name": "内存", "key": "memory", "all_key": "memory"},
		{"name": "sn", "key": "sn", "all_key": "sn"},
		{"name": "mac 地址", "key": "mac", "all_key": "mac"},
	}
}

func (b *BkCmdbCustomTaskVmMachineAssets) GetAssetListDetailFields() []map[string]string {
	return []map[string]string{
		{"name": "IP地址", "key": "ip_addr", "all_key": "ip_addr"},
		{"name": "内存", "key": "memory", "all_key": "memory"},
		{"name": "sn", "key": "sn", "all_key": "sn"},
		{"name": "mac 地址", "key": "mac", "all_key": "mac"},
	}
}
