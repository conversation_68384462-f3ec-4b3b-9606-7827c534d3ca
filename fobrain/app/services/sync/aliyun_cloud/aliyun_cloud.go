package aliyun_cloud

import (
	"context"
	"fmt"
	"fobrain/pkg/utils/handle_es_bulk_error"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"

	"github.com/olivere/elastic/v7"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"

	"fobrain/fobrain/app/services/node/aliyun_cloud"
	"fobrain/fobrain/common/constant"
	"fobrain/fobrain/common/localtime"
	"fobrain/fobrain/logs"
	"fobrain/initialize/es"
	"fobrain/models/elastic/poc"
	aliyun_cloud2 "fobrain/models/elastic/source/aliyun_cloud"
	"fobrain/models/mysql/data_source"
	"fobrain/models/mysql/data_sync_child_task"
	"fobrain/models/mysql/data_sync_task"
	"fobrain/pkg/cfg"
	"fobrain/pkg/utils"
)

// SyncAliYunCloud 同步阿里云云节点数据
//
// node 参数表示要同步的阿里云云节点信息
// taskInfo 参数表示任务信息，用于记录或处理任务
//
// 返回值：
// - error：如果同步过程中发生错误，则返回错误信息；否则返回nil
func SyncAliYunCloud(node *data_source.Node, taskInfo string) error {
	logs.GetSyncLogger().Infof("SyncAliYunCloud start taskInfo:%s", taskInfo)
	//assetTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncAsset)).Int())
	threatTaskId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncThreat)).Int())
	cli := aliyun_cloud.NewAliYunCloud()
	//设置client请求的节点
	if err := cli.SetNode(node.Id); err != nil {
		return err
	}

	//if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncAsset)).Int() == data_sync_task.SyncAsset {
	//	//更新资产任务为进行中
	//	err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(assetTaskId, data_sync_child_task.StatusDoing)
	//	if err != nil {
	//		logs.GetSyncLogger().Errorf("aliyun_cloud syncAssets UpdateStatusById StatusDoing err:%s", err.Error())
	//	}
	//	syncAssets(constant.DefaultPage, cli, taskInfo, node)
	//}

	if gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.type", data_sync_task.SyncThreat)).Int() == data_sync_task.SyncThreat {
		//更新漏洞任务为进行中
		err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(threatTaskId, data_sync_child_task.StatusDoing)
		if err != nil {
			logs.GetSyncLogger().Errorf("aliyun_cloud syncThreats UpdateStatusById StatusDoing err:%s", err.Error())
		}
		go syncThreats(constant.DefaultPage, cli, taskInfo, node)
	}
	return nil
}

// syncAssets 同步阿里云资产信息
func syncAssets(page int, cli *aliyun_cloud.AliYunCloud, taskInfo string, node *data_source.Node) {

}

// syncThreats 同步阿里云资产信息
// TODO: 优化代码，后续使用协程并发
func syncThreats(page int, cli *aliyun_cloud.AliYunCloud, taskInfo string, node *data_source.Node) {
	taskId := uint64(gjson.Get(taskInfo, "Task.id").Int())
	taskChildId := uint64(gjson.Get(taskInfo, fmt.Sprintf("ChildTasks.%d.id", data_sync_task.SyncThreat)).Int())
	logs.GetSyncLogger().Infof("aliyun_cloud syncThreats start taskInfo:%d,taskChildId:%d", taskId, taskChildId)
	vulTypeCount := 0

	for vulType, _ := range aliyun_cloud.VulTypeMap {
		vul, threatCount := syncThreatsVul(page, vulTypeCount, vulType, taskId, taskChildId, cli, taskInfo, node)
		if !vul {
			break
		}
		vulTypeCount += threatCount
		time.Sleep(time.Duration(cfg.GetInstance().SourceSync.AliyunCloudTime) * time.Second)
	}

	//更新子任务完成状态
	err := data_sync_child_task.NewDataSyncChildTaskModel().UpdateStatusById(taskChildId, data_sync_child_task.StatusSuccess)
	if err != nil {
		logs.GetSyncLogger().Errorf("aliyun_cloud syncThreats UpdateStatusById StatusSuccess err:%s", err.Error())
	}
}
func syncThreatsVul(page, vulTypeCount int, vulType string, taskId, taskChildId uint64, cli *aliyun_cloud.AliYunCloud, taskInfo string, node *data_source.Node) (bool, int) {
	//获取漏洞
	threats, err := getThreatsPage(page, vulType, taskId, taskChildId, cli)
	if err != nil {
		return false, 0
	}

	total := gjson.Get(threats, "TotalCount").Int()
	if total < 1 {
		return true, 0
	}
	//子任务更新需要同步的数据总量
	if page == constant.DefaultPage { //仅第一次同步
		vulTypeCount = vulTypeCount + int(total)
		err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataTotal(taskChildId, int(total))
		if err != nil {
			logs.GetSyncLogger().Errorf("aliyun_cloud syncThreats UpdateSyncDataTotal err:%s", err.Error())
		}
		execThreats(taskId, taskChildId, threats, cli, node)
	}
	time.Sleep(time.Millisecond * 100)
	pages := calculateTotalPages(int(total), cfg.GetInstance().SourceSync.AliyunCloudSize)
	for i := 2; i <= pages; i++ {
		threats, err = getThreatsPage(i, vulType, taskId, taskChildId, cli)
		if err != nil {
			return false, 0
		}
		execThreats(taskId, taskChildId, threats, cli, node)
		time.Sleep(time.Duration(cfg.GetInstance().SourceSync.AliyunCloudTime) * time.Second)
	}
	logs.GetSyncLogger().Infof("aliyun_cloud syncThreats end taskInfo:%d,taskChildId:%d,vulType:%v", taskId, taskChildId, vulType)
	return true, vulTypeCount
}

// execThreats 执行漏洞数据同步
// threatId ID NODE_id+IP+漏洞类型+漏洞名称+cveId
func execThreats(taskId, taskChildId uint64, threats string, cli *aliyun_cloud.AliYunCloud, node *data_source.Node) bool {
	result := gjson.Get(threats, "VulRecords")
	total := gjson.Get(threats, "TotalCount").Int()
	defer func() {
		if reErr := recover(); reErr != nil {
			logs.GetCrontabLogger().Infof("execThreats taskId:%v,taskChildId:%v,threatsData:%s > FAIL, 返回结果: %v", taskId, taskChildId, threats, reErr)
		}
	}()
	if result.Exists() && total > 0 {
		var mergeIps []map[string]interface{}
		bulkRequest := es.GetEsClient().Bulk()
		taskBulkRequest := es.GetEsClient().Bulk()
		processBulkRequest := es.GetEsClient().Bulk()
		var count int
		result.ForEach(func(key, value gjson.Result) bool {
			logs.GetSyncLogger().Debugf("aliyun_cloud syncThreatsVul value info:%s", value)

			ip := gjson.Get(value.String(), "Ip").String()
			if !utils.IsValidIP(ip) {
				return true
			}

			vType := gjson.Get(value.String(), "Type").String()
			vName := gjson.Get(value.String(), "Name").String()
			vAliasName := gjson.Get(value.String(), "AliasName").String()
			time.Sleep(time.Millisecond * 100)
			threatsDetails, err := cli.GetThreatsDetail(vType, vName, vAliasName)
			if err != nil {
				//更新主任务状态为失败
				taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, err.Error())
				if taskErr != nil {
					logs.GetSyncLogger().Errorf("aliyun_cloud syncThreatsVul GetThreatsDetail UpdateFailById Task err:%s", taskErr.Error())
				}

				//更新子任务状态为失败
				childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, err.Error())
				if childTaskErr != nil {
					logs.GetSyncLogger().Errorf("aliyun_cloud syncThreatsVul GetThreatsDetail UpdateFailById childTask err:%s", childTaskErr.Error())
				}
				return false
			}
			resultThreatsDetails := gjson.Get(threatsDetails, "Cves")
			resultThreatsDetailsForEach(vType, vName, vAliasName, taskId, taskChildId, value, resultThreatsDetails, bulkRequest, taskBulkRequest, processBulkRequest, cli, node)
			// list := resultThreatsDetailsForEach(vType, vName, vAliasName, taskId, taskChildId, value, resultThreatsDetails, bulkRequest, taskBulkRequest, processBulkRequest, cli, node)
			// for _, v := range list {
			// 	mergeIps = append(mergeIps, v)
			// }
			count = count + 1
			return true
		})
		if count > 0 && processBulkRequest.NumberOfActions() > 0 && bulkRequest.NumberOfActions() > 0 && taskBulkRequest.NumberOfActions() > 0 {
			// 过程数据处理
			threatProcessBulkReq, err := processBulkRequest.Refresh("true").Do(context.Background())
			dispose := errDispose(err, threatProcessBulkReq, "threatProcessBulkReq", taskId, taskChildId)
			if !dispose {
				return false
			}

			// 漏洞数据处理
			threatBulkReq, err := bulkRequest.Refresh("true").Do(context.Background())
			dispose = errDispose(err, threatBulkReq, "threatBulkReq", taskId, taskChildId)
			if !dispose {
				return false
			}

			// 漏洞任务处理
			threatTaskBulkReq, err := taskBulkRequest.Refresh("true").Do(context.Background())
			dispose = errDispose(err, threatTaskBulkReq, "threatTaskBulkReq", taskId, taskChildId)
			if !dispose {
				return false
			}

			if err == nil {
				err = data_sync_child_task.NewDataSyncChildTaskModel().UpdateSyncDataSuccessTotal(taskChildId, len(threatTaskBulkReq.Items))
				if err != nil {
					logs.GetSyncLogger().Errorf("aliyun_cloud syncThreats UpdateSyncDataSuccessTotal err:%s", err.Error())
				}
				logs.GetSyncLogger().Infof("aliyun_cloud syncThreats threatIds info:%v", mergeIps)

				// // 发送任务开始标识
				// rq.Push(cfg.LoadQueue().VulnMergeQueue, []map[string]interface{}{{"task_id": taskChildId, "task_type": "sync", "is_start": true}})
				// successCount := rq.Push(cfg.LoadQueue().VulnMergeQueue, mergeIps)
				// logs.GetSyncLogger().Infof("aliyun_cloud syncThreats successTotalCount:%d, expectedCount:%d", successCount, len(mergeIps))
			}
		}
	}
	return true
}

// errDispose 错误处理
func errDispose(err error, req *elastic.BulkResponse, errStr string, taskId, taskChildId uint64) bool {
	if err != nil || req.Errors {
		errString := handle_es_bulk_error.HandleBulkResp("", err, req)
		logs.GetSyncLogger().Errorf("aliyun_cloud syncThreats %s.Do err:%s", errStr, errString)

		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, errString)
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("aliyun_cloud syncThreats %s.Do task UpdateFailById err:%s", errStr, taskErr)
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, errString)
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("aliyun_cloud syncThreats %s.Do childTask UpdateFailById err:%s", errStr, childTaskErr.Error())
		}
		return false
	}
	return true
}

// resultThreatsDetailsForEach 循环执行漏洞详情数据
func resultThreatsDetailsForEach(vType, vName, vAliasName string, taskId, taskChildId uint64,
	value, resultThreatsDetails gjson.Result, bulkRequest, taskBulkRequest, processBulkRequest *es.SafeBulkService,
	cli *aliyun_cloud.AliYunCloud, node *data_source.Node) []map[string]interface{} {
	var mergeIps []map[string]interface{}

	resultThreatsDetails.ForEach(func(cveKey, cveValue gjson.Result) bool {
		ip := gjson.Get(value.String(), "Ip").String()
		title := gjson.Get(cveValue.String(), "Title").String()
		if !utils.IsValidIP(ip) {
			r := data_sync_child_task.NewDataSyncChildTaskFailRecord()
			r.Create(&data_sync_child_task.DataSyncChildTaskFailRecord{
				ChildTaskId:  taskChildId,
				DataType:     "ip",
				DataContent:  fmt.Sprintf("ip:%s,title:%s", ip, title),
				FailedReason: "ip无效",
			})
			return true
		}
		areaId := node.AreaId
		// 获取区域
		area, err := node.GetAreaByIp(ip)
		if err != nil {
			logs.GetSyncLogger().Errorf("aliyun_cloud syncThreats GetAreaByIp failed,ip:%s,nodeId:%d,err:%s", ip, node.Id, err)
			r := data_sync_child_task.NewDataSyncChildTaskFailRecord()
			r.Create(&data_sync_child_task.DataSyncChildTaskFailRecord{
				ChildTaskId:  taskChildId,
				DataType:     "ip",
				DataContent:  ip,
				FailedReason: err.Error(),
			})
		}
		areaId = area

		data, _ := sjson.Set(cveValue.String(), "node_id", node.Id)

		cveId := gjson.Get(cveValue.String(), "CveId").String()
		cvssScore := gjson.Get(cveValue.String(), "CvssScore").Float()
		content := gjson.Get(cveValue.String(), "Summary").String()
		solution := gjson.Get(cveValue.String(), "Solution").String()
		releaseTime := gjson.Get(cveValue.String(), "ReleaseTime").String()
		vulLevel := gjson.Get(cveValue.String(), "VulLevel").String()
		// cve 漏洞需要单独获取
		if vType == "cve" {
			time.Sleep(time.Millisecond * 100)
			cveDetail, err := cli.GetThreatsCveDetail(gjson.Get(cveValue.String(), "CveId").String())
			if err != nil {
				return false
			}
			title = gjson.Get(cveDetail, "Title").String()
			content = gjson.Get(cveDetail, "Detail").String()
			solution = gjson.Get(cveDetail, "Solution").String()
			releaseTime = gjson.Get(cveDetail, "ReleaseTime").String()
			vulLevel = gjson.Get(cveDetail, "VulLevel").String()
			cveDetailData := map[string]interface{}{
				"Title":           title,
				"AvdId":           gjson.Get(cveDetail, "AvdId").String(),
				"RequestId":       gjson.Get(cveDetail, "RequestId").String(),
				"Reference":       gjson.Get(cveDetail, "Reference").Value(),
				"Cvss3":           gjson.Get(cveDetail, "Cvss3").String(),
				"CveId":           gjson.Get(cveDetail, "CveId").String(),
				"Cvss3Vector":     gjson.Get(cveDetail, "Cvss3Vector").String(),
				"ReleaseTime":     gjson.Get(cveDetail, "ReleaseTime").String(),
				"ShowReleaseTime": timestampToDate(gjson.Get(cveDetail, "ReleaseTime").String()),
				"Detail":          gjson.Get(cveDetail, "Detail").String(),
				"Solution":        gjson.Get(cveDetail, "Solution").String(),
				"VulLevel":        gjson.Get(cveDetail, "VulLevel").String(),
			}
			data, _ = sjson.Set(data, "cve_detail", cveDetailData)
		}

		data, _ = sjson.Set(data, "area_id", areaId)
		data, _ = sjson.Set(data, "detail", cveValue.Value())
		data, _ = sjson.Set(data, "vul_type", vType)
		data, _ = sjson.Set(data, "vul_name", vName)
		data, _ = sjson.Set(data, "vul_aliasName", vAliasName)
		data, _ = sjson.Set(data, "source_ip", ip)
		data, _ = sjson.Set(data, "level", gjson.Get(value.String(), "Level").String())
		data, _ = sjson.Set(data, "status", gjson.Get(value.String(), "Status").Int())
		data, _ = sjson.Set(data, "necessity", gjson.Get(value.String(), "Necessity").String())
		data, _ = sjson.Set(data, "create_ts_start", timestampToDate(gjson.Get(value.String(), "FirstTs").String()))
		data, _ = sjson.Set(data, "modify_ts", timestampToDate(gjson.Get(value.String(), "ModifyTs").String()))
		data, _ = sjson.Set(data, "last_time", timestampToDate(gjson.Get(value.String(), "LastTs").String()))

		threatId := strings.ReplaceAll(uuid.New().String(), "-", "")

		//写入任务库
		threatTask := aliyun_cloud2.NewAliYunCloudTaskThreatsModel()
		threatTaskId := strings.ReplaceAll(uuid.New().String(), "-", "")
		data, _ = sjson.Set(data, "task_id", taskId)
		data, _ = sjson.Set(data, "child_task_id", taskChildId)
		assetTaskReq := elastic.NewBulkIndexRequest().Index(threatTask.IndexName()).Id(threatTaskId).Doc(data)
		taskBulkRequest = taskBulkRequest.Add(assetTaskReq)

		//写入漏洞过程库
		assetProcess := poc.NewProcessPocModel()
		assetProcess.Id = threatId
		assetProcess.TaskId = taskId
		assetProcess.ChildTaskId = taskChildId
		assetProcess.TaskDataId = threatTaskId
		assetProcess.Area = areaId
		assetProcess.Node = node.Id
		assetProcess.Source = node.SourceId
		assetProcess.Ip = ip
		assetProcess.Name = title                          // 漏洞名称
		assetProcess.Level = calculateRiskLevel(cvssScore) // 风险等级
		assetProcess.Cve = cveId                           // cveid
		assetProcess.Url = gjson.Get(value.String(), "Uuid").String()
		assetProcess.Describe = content
		assetProcess.VulType = vType
		assetProcess.Suggestions = solution
		assetProcess.CreatedAt = timestampToDate(releaseTime)

		assetProcess.Hazard = vulLevel
		threatProcessReq := elastic.NewBulkIndexRequest().Index(assetProcess.IndexName()).Id(threatId).Doc(assetProcess)

		processBulkRequest = processBulkRequest.Add(threatProcessReq)
		// mergeIps = append(mergeIps, map[string]interface{}{"id": threatId, "is_poc": poc.YesPoc, "task_id": taskChildId, "area": node.AreaId})
		return true
	})
	return mergeIps
}

// 获取漏洞分页列表数据
func getThreatsPage(page int, vulType string, taskId, taskChildId uint64, cli *aliyun_cloud.AliYunCloud) (string, error) {
	//获取漏洞
	threats, err := cli.GetThreats(page, cfg.GetInstance().SourceSync.AliyunCloudSize, vulType)
	if err != nil {
		logs.GetSyncLogger().Errorf("aliyun_cloud syncAssets GetAssets err:%s", err)

		//更新主任务状态为失败
		taskErr := data_sync_task.NewDataSyncTaskModel().UpdateFailById(taskId, "GetAssets "+err.Error())
		if taskErr != nil {
			logs.GetSyncLogger().Errorf("aliyun_cloud syncThreats GetThreats Task UpdateFailById err:%s", taskErr.Error())
		}

		//更新子任务状态为失败
		childTaskErr := data_sync_child_task.NewDataSyncChildTaskModel().UpdateFailById(taskChildId, "GetAssets "+err.Error())
		if childTaskErr != nil {
			logs.GetSyncLogger().Errorf("aliyun_cloud syncThreats GetThreats childTask UpdateFailById err:%s", childTaskErr.Error())
		}
		return "", err
	}
	return threats, nil
}

func calculateRiskLevel(value float64) int {
	if value <= 3.9 {
		return 0
	} else if value >= 4.0 && value <= 6.9 {
		return 1
	} else if value >= 7.0 && value <= 8.9 {
		return 2
	} else if value >= 9.0 && value <= 10.0 {
		return 3
	}
	return 0
}

func calculateTotalPages(totalCount int, pageSize int) int {
	if totalCount == 0 {
		return 0
	}
	pages := totalCount / pageSize
	if totalCount%pageSize != 0 {
		pages++
	}
	return pages
}

func timestampToDate(timestamp string) *localtime.Time {
	if timestamp != "" {
		i64, _ := strconv.ParseInt(timestamp, 10, 64)
		t := time.UnixMilli(i64)
		tm, _ := time.Parse(localtime.TimeFormat, t.Format("2006-01-02 15:04:05"))
		return localtime.NewLocalTime(tm)
	}
	return nil
}
