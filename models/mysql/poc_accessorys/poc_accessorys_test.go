package poc_accessorys

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	testcommon "fobrain/fobrain/tests/common_test"
	"fobrain/initialize/mysql"
)

func TestFirst(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	model := NewPocAccessorysModel()
	mockDb.ExpectQuery("SELECT * FROM `poc_accessorys` ORDER BY `poc_accessorys`.`id` LIMIT 1").
		WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(1))

	var handlers []mysql.HandleFunc
	first, err := model.First(handlers...)
	assert.NoError(t, err)
	assert.NotNil(t, first)
}

func TestCreate(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	model := NewPocAccessorysModel()
	mockDb.Mock.ExpectBegin()
	mockDb.ExpectExec("INSERT INTO `poc_accessorys` (`created_at`,`updated_at`,`path`,`fid`,`user_id`,`source`,`name`) VALUES (?,?,?,?,?,?,?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mockDb.Mock.ExpectCommit()

	data := PocAccessorys{
		Path:   "/data/a/b/c.jpeg",
		Fid:    "127.0.0.1",
		UserId: 1,
		Source: 1,
	}
	err := model.CreateItem(&data)
	assert.NoError(t, err)
}

func TestList(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	model := NewPocAccessorysModel()

	mockDb.ExpectQuery("SELECT count(*) FROM `poc_accessorys`").
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	mockDb.ExpectQuery("SELECT * FROM `poc_accessorys` LIMIT 10").
		WillReturnRows(mockDb.NewRows([]string{"id"}).AddRow(1))

	var handlers []mysql.HandleFunc
	list, count, err := model.List(1, 10, handlers...)
	assert.Nil(t, err)
	assert.Equal(t, int64(1), count)
	assert.Equal(t, 1, len(list))
}

func TestUserPocAccessorysList(t *testing.T) {
	mockDb := testcommon.GetMysqlMock()
	defer mockDb.Close()
	mockDb.ExpectQuery("SELECT count(*) FROM `poc_accessorys` LEFT JOIN users  ON poc_accessorys.user_id = users.id WHERE poc_accessorys.fid = ? or poc_accessorys.fid in (?)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnRows(mockDb.NewRows([]string{"count"}).AddRow(1))
	mockDb.ExpectQuery("SELECT poc_accessorys.*,users.username as user_name FROM `poc_accessorys` LEFT JOIN users ON poc_accessorys.user_id = users.id WHERE poc_accessorys.fid = ? or poc_accessorys.fid in (?) ORDER BY poc_accessorys.id desc LIMIT 10").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnRows(mockDb.NewRows([]string{"id"}).
			AddRow(1))

	model := NewPocAccessorysModel()
	list, total, err := model.UserPocAccessorysList("127.0.0.1", []string{"xxxx"}, 1, 10)
	assert.NoError(t, err)
	assert.Equal(t, int64(1), total)
	assert.Equal(t, 1, len(list))
}
